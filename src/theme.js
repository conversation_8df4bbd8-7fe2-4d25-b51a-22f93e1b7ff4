import { createTheme } from '@mui/material/styles';

export const theme = createTheme({
  palette: {
    primary: {
      main: "#1976d2",
    },
    secondary: {
      main: "#1E3A8A",
      grey: "#6B6B6B",
    },
    custom: {
      white: "#FFFFFF",
      lightred: "#FF3951",
      subText: "#333333",
      darkBlue: "#00243D",
      darkBlue2: "#153B50",
      lightBlack: "#000311",
      lightGreen: "#00AF31",
      darkBlue3: "#05073C",
      black: "#1C1C1C",
      divider: "#CFD6DC",
      placeholder: "#706E75",
      links: "#0083E7"
    },
  },
  //custom breakpoints
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
      tb: 768, //custom breakpoint for tablet
    },
  },
});