import * as yup from "yup";

export const BrandDetailsSchema = yup.object({
  brandName: yup.string().required("Business / Entity Name is required"),
  contactPersonName: yup.string().required("Contact Name is required"),
  entityType: yup.string().required("Entity Type is required"),
  country: yup.string().required("Country is required"),
  city: yup.string().required("City is required"),
  countryCode: yup.string().required("Code is required"),
  phoneNumber: yup
    .string()
    .required("Phone number is required")
    .matches(/^\d{7,15}$/, "Please Enter a valid phone number"),
  email: yup
    .string()
    .required("Email is required")
    .email("Enter a valid email address"),
});

export const PreferencesSchema = yup.object().shape({
  primaryColor: yup.string().test(
    "primaryColor-required-based-on-preference",
    "Primary color is required",
    function (value) {
      const { preferenceType } = this.options.context || {};
      if (preferenceType === "integrateWebsite") {
        // check if value is non-empty string
        return typeof value === "string" && value.trim() !== "";
      }
      return true; // no error for other preferenceType values
    }
  ),
  primaryColor2: yup.string().test(
    "primaryColor2-required-based-on-preference",
    "Primary color is required",
    function (value) {
      const { preferenceType } = this.options.context || {};
      if (preferenceType === "integrateMobile") {
        // check if value is non-empty string
        return typeof value === "string" && value.trim() !== "";
      }
      return true; // no error for other preferenceType values
    }
  ),
  primaryColor3: yup.string().test(
    "primaryColor3-required-based-on-preference",
    "Primary color is required",
    function (value) {
      const { preferenceType } = this.options.context || {};
      if (preferenceType === "createOwnWebsite") {
        // check if value is non-empty string
        return typeof value === "string" && value.trim() !== "";
      }
      return true; // no error for other preferenceType values
    }
  ),
  customSubDomain: yup.string().test(
    "customSubDomain-required-based-on-preference",
    "Sub-domain is required",
    function (value) {
      const { preferenceType } = this.options.context || {};
      if (preferenceType === "createOwnWebsite") {
        if (!(typeof value === "string" && value.trim() !== "")) {
          return false;
        }
        
        if (/[A-Z]/.test(value)) {
          return this.createError({
            message: "Capital letters are not allowed"
          });
        }
        
        if (/\s/.test(value)) {
          return this.createError({
            message: "Spaces are not allowed"
          });
        }
        
        if (!/^[a-z0-9]+$/.test(value)) {
          return this.createError({
            message: "Only lowercase letters and numbers are allowed"
          });
        }
        
        return true;
      }
      return true; 
    }
  ),
  websiteSubDomain: yup.string().test(
    "websiteSubDomain-required-based-on-preference",
    "Sub-domain is required",
    function (value) {
      const { preferenceType } = this.options.context || {};
      if (preferenceType === "integrateWebsite") {
        if (!(typeof value === "string" && value.trim() !== "")) {
          return false;
        }
        
        if (/[A-Z]/.test(value)) {
          return this.createError({
            message: "Capital letters are not allowed"
          });
        }
        
        if (/\s/.test(value)) {
          return this.createError({
            message: "Spaces are not allowed"
          });
        }
        
        if (!/^[a-z0-9]+$/.test(value)) {
          return this.createError({
            message: "Only lowercase letters and numbers are allowed"
          });
        }
        
        return true;
      }
      return true; 
    }
  ),
  mobileSubDomain: yup.string().test(
    "mobileSubDomain-required-based-on-preference",
    "Sub-domain is required",
    function (value) {
      const { preferenceType } = this.options.context || {};
      if (preferenceType === "integrateMobile") {
        if (!(typeof value === "string" && value.trim() !== "")) {
          return false;
        }
        
        if (/[A-Z]/.test(value)) {
          return this.createError({
            message: "Capital letters are not allowed"
          });
        }
        
        if (/\s/.test(value)) {
          return this.createError({
            message: "Spaces are not allowed"
          });
        }
        
        if (!/^[a-z0-9]+$/.test(value)) {
          return this.createError({
            message: "Only lowercase letters and numbers are allowed"
          });
        }
        
        return true;
      }
      return true; 
    }
  ),
  mobileInstaUrl: yup.string().test(
    "mobileInstaUrl-url-validation",
    "Please enter a valid Instagram URL",
    function (value) {
      // If no value is provided, it's valid (not required)
      if (!value || value.trim() === "") {
        return true;
      }
      
      // URL validation regex
      const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/;
      
      if (!urlRegex.test(value)) {
        return this.createError({
          message: "Please enter a valid URL"
        });
      }

      // Check if it's an Instagram URL
      if (!/^https?:\/\/(www\.)?(instagram\.com|instagr\.am)/i.test(value)) {
        return this.createError({
          message: "Please enter a valid Instagram URL"
        });
      }
      
      return true;
    }
  ),
  websiteInstaUrl: yup.string().test(
    "websiteInstaUrl-url-validation",
    "Please enter a valid Instagram URL",
    function (value) {
      if (!value || value.trim() === "") {
        return true;
      }
      const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/;
      if (!urlRegex.test(value)) {
        return this.createError({
          message: "Please enter a valid URL"
        });
      }
      if (!/^https?:\/\/(www\.)?(instagram\.com|instagr\.am)/i.test(value)) {
        return this.createError({
          message: "Please enter a valid Instagram URL"
        });
      }
      return true;
    }
  ),
  websiteFacebookUrl: yup.string().test(
    "websiteFacebookUrl-url-validation",
    "Please enter a valid Facebook URL",
    function (value) {
      if (!value || value.trim() === "") {
        return true;
      }
      const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/;
      if (!urlRegex.test(value)) {
        return this.createError({
          message: "Please enter a valid URL"
        });
      }
      if (!/^https?:\/\/(www\.)?(facebook\.com|fb\.com)/i.test(value)) {
        return this.createError({
          message: "Please enter a valid Facebook URL"
        });
      }
      return true;
    }
  ),
  websiteTwitterUrl: yup.string().test(
    "websiteTwitterUrl-url-validation",
    "Please enter a valid Twitter/X URL",
    function (value) {
      if (!value || value.trim() === "") {
        return true;
      }
      const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/;
      if (!urlRegex.test(value)) {
        return this.createError({
          message: "Please enter a valid URL"
        });
      }
      if (!/^https?:\/\/(www\.)?(twitter\.com|x\.com)/i.test(value)) {
        return this.createError({
          message: "Please enter a valid Twitter/X URL"
        });
      }
      return true;
    }
  ),
  mobileFacebookUrl: yup.string().test(
    "mobileFacebookUrl-url-validation",
    "Please enter a valid Facebook URL",
    function (value) {
      if (!value || value.trim() === "") {
        return true;
      }
      const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/;
      if (!urlRegex.test(value)) {
        return this.createError({
          message: "Please enter a valid URL"
        });
      }
      if (!/^https?:\/\/(www\.)?(facebook\.com|fb\.com)/i.test(value)) {
        return this.createError({
          message: "Please enter a valid Facebook URL"
        });
      }
      return true;
    }
  ),
  mobileTwitterUrl: yup.string().test(
    "mobileTwitterUrl-url-validation",
    "Please enter a valid Twitter/X URL",
    function (value) {
      if (!value || value.trim() === "") {
        return true;
      }
      const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/;
      if (!urlRegex.test(value)) {
        return this.createError({
          message: "Please enter a valid URL"
        });
      }
      if (!/^https?:\/\/(www\.)?(twitter\.com|x\.com)/i.test(value)) {
        return this.createError({
          message: "Please enter a valid Twitter/X URL"
        });
      }
      return true;
    }
  ),
  customInstaUrl: yup.string().test(
    "customInstaUrl-url-validation",
    "Please enter a valid Instagram URL",
    function (value) {
      if (!value || value.trim() === "") {
        return true;
      }
      const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/;
      if (!urlRegex.test(value)) {
        return this.createError({
          message: "Please enter a valid URL"
        });
      }
      if (!/^https?:\/\/(www\.)?(instagram\.com|instagr\.am)/i.test(value)) {
        return this.createError({
          message: "Please enter a valid Instagram URL"
        });
      }
      return true;
    }
  ),
  customFacebookUrl: yup.string().test(
    "customFacebookUrl-url-validation",
    "Please enter a valid Facebook URL",
    function (value) {
      if (!value || value.trim() === "") {
        return true;
      }
      const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/;
      if (!urlRegex.test(value)) {
        return this.createError({
          message: "Please enter a valid URL"
        });
      }
      if (!/^https?:\/\/(www\.)?(facebook\.com|fb\.com)/i.test(value)) {
        return this.createError({
          message: "Please enter a valid Facebook URL"
        });
      }
      return true;
    }
  ),
  customTwitterUrl: yup.string().test(
    "customTwitterUrl-url-validation",
    "Please enter a valid Twitter/X URL",
    function (value) {
      if (!value || value.trim() === "") {
        return true;
      }
      const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/;
      if (!urlRegex.test(value)) {
        return this.createError({
          message: "Please enter a valid URL"
        });
      }
      if (!/^https?:\/\/(www\.)?(twitter\.com|x\.com)/i.test(value)) {
        return this.createError({
          message: "Please enter a valid Twitter/X URL"
        });
      }
      return true;
    }
  ),
});

export const BusinessDetailsSchema = yup.object({
  gstin: yup.string(),
  companyRegistrationNo: yup.string(),
  monthlyTransactionVolume: yup.string().required("Monthly Transaction Volume is required"),
});

export const ServicesSchema = yup.object({
  services: yup.array().of(yup.string()).required("Services are required"),
});
