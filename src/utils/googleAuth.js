// Google Sign-In utility functions using @react-oauth/google
import { useGoogleLogin } from "@react-oauth/google";
import axios from "axios";

// Exchange authorization code for tokens
export const exchangeCodeForToken = async (code) => {
  const tokenEndpoint = "https://oauth2.googleapis.com/token";
  const payload = {
    code: code,
    client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID,
    client_secret: process.env.REACT_APP_GOOGLE_CLIENT_SECRET,
    redirect_uri: process.env.REACT_APP_REDIRECT_URL,
    grant_type: "authorization_code",
  };

  try {
    const response = await axios.post(tokenEndpoint, payload);
    const { id_token } = response.data;
    return id_token;
  } catch (error) {
    console.error("Error exchanging code for token:", error);
    throw new Error("Failed to exchange code for token");
  }
};

// Google Sign-In hook
export const useGoogleSignIn = (onSuccess, onError) => {
  const handleSuccess = async (response) => {
    const authorizationCode = response.code;

    try {
      const idToken = await exchangeCodeForToken(authorizationCode);

      // Return the idToken for the component to handle with Redux thunk
      if (onSuccess) {
        onSuccess({ idToken });
      }
    } catch (error) {
      console.error("Error during Google sign-in:", error);
      if (onError) {
        onError(error);
      }
    }
  };

  return useGoogleLogin({
    onSuccess: handleSuccess,
    onError: onError,
    flow: "auth-code",
  });
};
