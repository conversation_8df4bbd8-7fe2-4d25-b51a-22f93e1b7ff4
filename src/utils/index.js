function generateCloudFrontURL({
  bucket,
  key,
  width,
  height,
  fit = "cover", // outside, inside, cover, contain, fill
  actual = false,
}) {
  const data = {
    bucket,
    key,
  };

  if (!actual) {
    data.edits = {
      resize: {
        width,
        height,
        fit,
      },
    };
  }
  const jsonString = JSON.stringify(data);
  const base64String = btoa(jsonString); // Base64 encoding
  const cloudfrontBase = "https://d3jc9og2dhsnlt.cloudfront.net/";

  return cloudfrontBase + base64String;
}

// const url = generateCloudFrontURL({
//   bucket: "dev-zuumm-public",
//   key: "package/media/28/R5qUumBB-1750144571395.jpg",
//   width: 400,
//   height: 300,
//   fit: "outside",
// });

export default generateCloudFrontURL;
