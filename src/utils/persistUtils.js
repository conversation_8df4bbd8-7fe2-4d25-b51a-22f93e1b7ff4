// Utility function to clear all persisted data
export const clearAllPersistedData = (persistorInstance = null) => {
  try {
    // Clear redux-persist data first if persistor is provided
    if (persistorInstance) {
      console.log("persistor: ", persistorInstance);
      persistorInstance.purge();
    }

    // Clear localStorage
    localStorage.clear();

    // Clear sessionStorage
    sessionStorage.clear();

    // Clear specific keys if needed
    localStorage.removeItem("zuumm_token");
    localStorage.removeItem("zuumm_refresh_token");
    localStorage.removeItem("persist:root");

    // Reset Redux store to initial state
    // We'll dispatch this action through a different mechanism
    window.dispatchEvent(new CustomEvent("RESET_STORE"));
  } catch (error) {
    console.error("Error clearing persisted data:", error);
  }
};

// Function to get persistor instance (to be called after store initialization)
export const getPersistorInstance = () => {
  try {
    // Dynamic import to avoid circular dependency
    const { persistor } = require("../store/store");
    return persistor;
  } catch (error) {
    console.error("Error getting persistor instance:", error);
    return null;
  }
};
