// Token management utilities

// Get access token from localStorage
export const getAccessToken = () => {
  return localStorage.getItem("zuumm_token");
};

// Get refresh token from localStorage
export const getRefreshToken = () => {
  return localStorage.getItem("zuumm_refresh_token");
};

// Set tokens in localStorage
export const setTokens = (accessToken, refreshToken) => {
  if (accessToken) {
    localStorage.setItem("zuumm_token", accessToken);
  }
  if (refreshToken) {
    localStorage.setItem("zuumm_refresh_token", refreshToken);
  }
};

// Clear tokens from localStorage
export const clearTokens = () => {
  localStorage.clear();
  localStorage.removeItem("persist:root");
  localStorage.removeItem("zuumm_token");
  localStorage.removeItem("zuumm_refresh_token");
};

// Check if user is authenticated
export const isAuthenticated = () => {
  const accessToken = getAccessToken();
  return !!accessToken;
};
