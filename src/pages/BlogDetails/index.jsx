/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import { Box } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import {
  fetchBlogDetails,
  fetchBlogPosts,
} from "../../store/reducers/Blog/apiThunk";
import { ToastNotifyError } from "../../components/Toast/ToastNotify";
import BreadCrumb from "../../components/common/BreadCrumb";
import BlogCard from "../Blogs/BlogCard";
import { BlogDetailsSkeleton } from "../../components/Skeleton";
import generateCloudFrontURL from "../../utils";
import moment from "moment";

const BlogDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [blogDetails, setBlogDetails] = useState(null);
  const [blogPosts, setBlogPosts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch blog details and related posts on component mount
  useEffect(() => {
    if (id) {
      getBlogDetails(id);
      getRelatedBlogPosts(id);
    }
  }, [id]);

  const getBlogDetails = async (blogId) => {
    try {
      setIsLoading(true);
      const res = await dispatch(fetchBlogDetails(blogId));
      const { data, message, status } = res?.payload?.data;
      if (status === "success") {
        const formattedData = {
          ...data,
          banner: generateCloudFrontURL({
            bucket: process.env.REACT_APP_BUCKET_NAME,
            key: data.banner,
            width: 760,
            height: 510,
            fit: "cover",
            actual: false,
          }),
        };
        setBlogDetails(formattedData);
      } else {
        ToastNotifyError(message);
      }
    } catch (err) {
      console.log(err);
    } finally {
      setTimeout(() => {
        setIsLoading(false);
      }, 1000);
    }
  };

  const getRelatedBlogPosts = async (blogId) => {
    try {
      const params = `?exclude=${blogId}`;
      const res = await dispatch(fetchBlogPosts(params));
      const { data, message, status } = res?.payload?.data;
      if (status === "success") {
        const formattedData = data?.blogs?.map((blog) => ({
          ...blog,
          banner: generateCloudFrontURL({
            bucket: process.env.REACT_APP_BUCKET_NAME,
            key: blog.banner,
            width: 410,
            height: 300,
            fit: "cover",
            actual: false,
          }),
        }));
        setBlogPosts(formattedData);
      } else {
        ToastNotifyError(message);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleCardClick = (blogId) => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
    navigate(`/wander-notes/${blogId}`);
  };

  // Show skeleton while loading
  if (isLoading) {
    return <BlogDetailsSkeleton />;
  }

  return (
    <Box>
      <Box sx={{ pt: { xs: "80px", md: "116px" } }}>
        <Box
          sx={{
            backgroundColor: "#1E3A8A",
            padding: { xs: "8px 20px", md: "13px 80px" },
          }}
        >
          <BreadCrumb
            crumbs={[
              { label: "Home", path: "/" },
              { label: "Wander Notes", path: "/wander-notes" },
              {
                label: blogDetails?.data?.title || "Blog Details",
                path: "",
                isLast: true,
              },
            ]}
          />
        </Box>
      </Box>
      <Box
        sx={{
          width: "100%",
          height: { xs: "300px", md: "600px" },
          position: "relative",
        }}
      >
        <Box
          component="img"
          src={blogDetails?.banner}
          alt={blogDetails?.data?.seo_title}
          sx={{
            width: "100%",
            height: { xs: "300px", md: "600px" },
            objectFit: "cover",
          }}
        />
        <Box
          sx={{
            position: "absolute",
            bottom: 0,
            left: 0,
            width: "100%",
            height: "fit-content",
            color: "#FFFFFF",
            background: "#0000001A",
            backdropFilter: "blur(10px)",
            p: {
              xs: "12px 16px",
              md: "28px 80px",
            },
            boxSizing: "border-box",
          }}
        >
          <Box
            sx={{
              fontFamily: "Roboto",
              fontSize: { xs: "20px", md: "27px" },
              fontWeight: "700",
              lineHeight: { xs: "24px", md: "32px" },
              mb: 1.25,
            }}
          >
            {blogDetails?.seo_title}
          </Box>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              fontFamily: "Roboto",
              fontSize: { xs: "12px", md: "14px" },
              fontWeight: "400",
              lineHeight: { xs: "16px", md: "20px" },
            }}
          >
            {`${moment(blogDetails?.published_at).format("MMM DD")} • ${blogDetails?.read_time} min read`}
          </Box>
        </Box>
      </Box>

      <Box
        sx={{
          width: "100%",
          height: "100%",
          px: { xs: "20px", md: "80px" },
          py: { xs: "30px", md: "60px" },
          boxSizing: "border-box",
        }}
      >
        <Box
          dangerouslySetInnerHTML={{ __html: blogDetails?.content || "" }}
          sx={{
            fontFamily: "Roboto",
            color: "#333333",
          }}
        />
      </Box>
      <Box
        sx={{
          width: "100%",
          height: "100%",
          px: { xs: "20px", md: "80px" },
          boxSizing: "border-box",
          pb: { xs: "20px", md: "40px" },
        }}
      >
        <Box
          sx={{
            width: "100%",
            height: "100%",
            display: "flex",
            boxSizing: "border-box",
            borderTop: "1px solid #FF39510D",
            borderBottom: "1px solid #FF39510D",
            py: { xs: "16px", md: "20px" },
            gap: { xs: "8px", md: "30px" },
          }}
        >
          <Box
            component="img"
            src={blogDetails?.writer?.profile_picture}
            sx={{
              width: { xs: "40px", md: "70px" },
              height: { xs: "40px", md: "70px" },
              objectFit: "cover",
              borderRadius: "50%",
            }}
          />
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: { xs: "8px" },
            }}
          >
            <Box
              sx={{
                fontFamily: "Roboto",
                fontSize: { xs: "14px", md: "16px" },
                fontWeight: "700",
                lineHeight: { xs: "16px", md: "20px" },
              }}
            >
              {blogDetails?.writer?.name}
            </Box>
            <Box
              sx={{
                fontFamily: "Roboto",
                fontSize: { xs: "12px", md: "14px" },
                fontWeight: "400",
                lineHeight: { xs: "16px", md: "20px" },
              }}
            >
              {blogDetails?.writer?.bio}
            </Box>
          </Box>
        </Box>
      </Box>
      {blogPosts?.length > 0 && (
        <Box
          sx={{
            width: "100%",
            height: "100%",
            background:
              "linear-gradient(99.31deg, #FFFFFF 0.33%, #FEF7F4 100%)",
            pb: { xs: "40px", md: "40px" },
            px: { xs: "20px", md: "80px" },
            boxSizing: "border-box",
          }}
        >
          <Box
            sx={{
              fontFamily: "Lora",
              fontSize: { xs: "24px", md: "32px" },
              fontWeight: "700",
              lineHeight: { xs: "32px", md: "40px" },
              mb: { xs: "20px", md: "40px" },
              textAlign: "center",
              color: "#1E3A8A",
            }}
          >
            Wander Notes
          </Box>
          <Box>
            <Swiper
              modules={[Navigation]}
              spaceBetween={24}
              slidesPerView={1}
              navigation={{
                nextEl: ".swiper-button-next-blog",
                prevEl: ".swiper-button-prev-blog",
              }}
              breakpoints={{
                640: {
                  slidesPerView: 2,
                  spaceBetween: 24,
                },
                768: {
                  slidesPerView: 3,
                  spaceBetween: 24,
                },
                1024: {
                  slidesPerView: 3,
                  spaceBetween: 24,
                },
              }}
              style={{
                width: "100%",
                paddingBottom: "16px",
              }}
            >
              {blogPosts?.map((blog) => (
                <SwiperSlide key={blog.external_id}>
                  <BlogCard
                    image={blog.banner}
                    title={blog.seo_title}
                    author={blog.writer?.name}
                    timestamp={blog.published_at}
                    category={blog.tags?.length > 0 ? blog.tags[0].name : ""}
                    onClick={() => handleCardClick(blog.slug)}
                    isHover={false}
                  />
                </SwiperSlide>
              ))}
            </Swiper>

            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                gap: 2,
              }}
            >
              <img
                className="swiper-button-prev-blog"
                src={"/static/common/slidePrev.png"}
                alt="prev"
                style={{ cursor: "pointer", width: "34px", height: "34px" }}
              />
              <img
                className="swiper-button-next-blog"
                src={"/static/common/slideNext.png"}
                alt="next"
                style={{ cursor: "pointer", width: "34px", height: "34px" }}
              />
            </Box>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default BlogDetails;
