import { styled } from '@mui/material/styles';

const StyledChoosePreferences = styled('div')(({ theme }) => `
  .choose-preferences-container {
    padding: 0px 24px 24px 24px;
    max-width: 800px;
    margin: 0 auto;
  }

  .preferences-header {
    text-align: center;
    margin-bottom: 40px;
  }

  .preferences-title {
    font-family: 'Roboto', sans-serif;
    font-size: 24px;
    font-weight: 500;
    color: #000000;
    margin-bottom: 8px;
  }

  .preferences-subtitle {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    color: #6B6B6B;
    line-height: 1.5;
  }

  .preferences-options {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-bottom: 40px;
  }

  .preference-row {
    display: flex;
    gap: 24px;
    justify-content: center;
  }

  .preference-item {
    background: ${theme.palette.custom.white};
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border: 1px solid rgba(255, 57, 81, 0.05);
    border-radius: 8px;
    min-width: 280px;

    .MuiTypography-root {
      font-family: 'Roboto', sans-serif;
      font-size: 14px;
      color: #000000;
      flex: 1;
    }

    .preference-checkbox {
      padding: 4px;
      color: #00AF31;

      &.Mui-checked {
        color: #00AF31;
      }
    }

    .info-icon {
      padding: 4px;
      color: #6B6B6B;
    }
  }

  .brand-customization {
    margin-top: 10px;
    padding: 0px 24px;
    width: 80%;
    margin: 0 auto;
  }

  .customization-row {
    display: flex;
    gap: 30px;
    align-items: center;
    justify-content: center;
  }

  .customization-row-mobile {
    display: flex;
    gap: 30px;
    align-items: flex-start;
    justify-content: center;
  }

  .logo-upload {
    display: flex;
    width: fit-content;
    flex-direction: column;
    align-items: center;

    .upload-label {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
    }

    .upload-circle {
      width: 80px;
      height: 80px;
      border: 2px dashed #1E3A8A;
      background: ${theme.palette.custom.white};
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 131, 231, 0.05);
      }

      .add-icon {
        color: #1E3A8A;
        font-size: 10px;
        width: 30px;
        height: 30px;
      }
    }

    .upload-text {
      color: #0083E7;
      font-family: 'Roboto', sans-serif;
      font-size: 12px;
      font-weight: 400;
      line-height: 1.5;
    }
  }

  .color-selection {
    width: 70%;
    display: flex;
    flex-direction: column;
    gap: 0px;
  }

  .color-field {
    .MuiTypography-root {
      font-family: 'Roboto', sans-serif;
      font-size: 14px;
      color: #000000;
      margin-bottom: 8px;
    }

    .color-input-container {
      position: relative;
    }

    .color-picker-hidden {
      position: absolute;
      visibility: hidden;
      pointer-events: none;
    }

    .color-text-field {
      width: 100%;
      cursor: pointer;

      input {
        text-transform: uppercase;
        cursor: pointer;
      }

      .color-preview-box {
        width: 24px;
        height: 20px;
      }
    }
  }

  .color-selection-mobile {
    display: flex;
    flex-direction: row;
    gap: 10px;
  }

  .navigation-buttons {
    display: flex;
    justify-content: space-around;
    margin-top: 24px;

    .previous-button {
      color: #FF3951;
      border: 1px solid #FF3951;
      padding: 8px 24px;
      text-transform: none;
      border-radius: 4px;
      font-family: 'Roboto', sans-serif;
    }

    .next-button {
      background-color: #FF3951;
      color: white;
      padding: 8px 24px;
      text-transform: none;
      border-radius: 4px;
      font-family: 'Roboto', sans-serif;
    }
  }

  .social-text-field {
    margin-bottom: 0px !important;
    
    .social-icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 45px;
      background: linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 125.89%);
      border-radius: 8px 0px 0px 8px;
      margin-right: 8px;
    }

    .social-icon {
      width: 24px;
      height: 24px;
      object-fit: cover;
    }

    .MuiOutlinedInput-root {
      padding-left: 0;
    } 
  }
  .website-text-field-sub-domain {
    width: 90%;
    margin: 0px auto;
  }
  .container-sub-domain-textfeild {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    background: var(--Icon-BG, rgba(255, 57, 81, 0.05));
    border-radius: 0px 8px 8px 0px;
    padding: 0px 10px;
    height: 45px;
  }
  .sub-domain-text {
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 100%;
    color: #FF3951;
  }

  .logo-preview-container {
    width: 80px;
      height: 80px;
      border: 1.5px dashed #1E3A8A;
      border-radius: 50%;
      display: flex;
      align-items: center;
  overflow: hidden; /* clip any overflow */
}

.logo-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  display: block; /* remove any inline spacing */
}
.logo-preview-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.change-logo-text {
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 100%;
  color: #0083E7;
}

@media (max-width: 768px) {
  .brand-customization {
    padding: 0px 0px;
    width: 100%;
    margin-top: 30px;
  }
  .preferences-title {
    font-size: 20px;
    line-height: 14px;
  }
  .preferences-subtitle {
    font-size: 12px;
  }
  .preference-row {
    flex-direction: column;
    justify-content: center;
    width: 100%;
  }  
  .preferences-options {
    gap: 10px;
  }

  .customization-row {
    flex-direction: column;
    justify-content: center;
  }
  .customization-row-mobile {
    flex-direction: column;
    justify-content: center;
    width: 100%;
    align-items: center;
  }
  .color-selection-mobile {
    flex-direction: column;
    justify-content: center;
    width: 100%;
  }
  .color-selection {
    width: 100%;
  }
  .website-text-field-sub-domain {
    width: 100%;
  }
  
  
  
`);

export default StyledChoosePreferences; 