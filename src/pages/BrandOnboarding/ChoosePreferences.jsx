/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react";
import StyledChoosePreferences from "./StyledChoosePreferences";
import { Box, Button, Typography, Checkbox } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import CustomTextField from "../../components/common/CustomTextField";
import ColorPickerPopup from "../../components/common/ColorPickerPopup";
import InstagramIcon from "../../assets/svg/orignalIntaIcon.svg";
import FacebookIcon from "../../assets/svg/orignalFacebookIcon.svg";
import TwitterIcon from "../../assets/svg/orignalTwitterIcon.svg";
import { Controller } from "react-hook-form";
import { uploadImage } from "../../store/reducers/PartnerOnboarding/apiThunk";
import { useDispatch } from "react-redux";
import { ToastNotifyError } from "../../components/Toast/ToastNotify";
import ResponsiveTooltipPopover from "../../components/common/ResponsiveTooltipPopover";

const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];

const ChoosePreferences = ({
  onNext,
  onPrevious,
  errors,
  control,
  handleSubmit,
  setPreferenceType,
  setBrandLogo,
  existingData,
  preferenceType,
  setValue,
}) => {
  const [preferences, setPreferences] = useState({
    getReferralLink: existingData?.getReferralLink || false,
    integrateWebsite: existingData?.integrateWebsite || false,
    integrateMobile: existingData?.integrateMobile || false,
    createOwnWebsite: existingData?.createOwnWebsite || false,
  });
  const [logoUploaded, setLogoUploaded] = useState(
    existingData?.logoUploaded || false
  );
  const dispatch = useDispatch();

  // Color picker popup states
  const [colorPickerOpen, setColorPickerOpen] = useState(false);
  const [currentColorField, setCurrentColorField] = useState(null);
  const [currentColorValue, setCurrentColorValue] = useState("#000000");

  const [brandCustomization, setBrandCustomization] = useState({
    websiteLogo: existingData?.websiteLogo || null,
    mobileLogo: existingData?.mobileLogo || null,
    customLogo: existingData?.customLogo || null,
  });

  const [logoPreview, setLogoPreview] = useState({
    website: existingData?.logoPreview?.website || null,
    mobile: existingData?.logoPreview?.mobile || null,
    custom: existingData?.logoPreview?.custom || null,
  });

  // Set preference type when component loads with existing data
  useEffect(() => {
    if (existingData) {
      const selectedPreference = Object.keys(preferences).find(
        (key) => preferences[key]
      );
      if (selectedPreference) {
        setPreferenceType(selectedPreference);
      }
      setValue("primaryColor", existingData?.primaryColor || "");
      setValue("secondaryColor", existingData?.secondaryColor || "");
      setValue("primaryColor2", existingData?.primaryColor2 || "");
      setValue("secondaryColor2", existingData?.secondaryColor2 || "");
      setValue("primaryColor3", existingData?.primaryColor3 || "");
      setValue("secondaryColor3", existingData?.secondaryColor3 || "");
      setValue("websiteInstaUrl", existingData?.websiteInstaUrl || "");
      setValue("websiteFacebookUrl", existingData?.websiteFacebookUrl || "");
      setValue("websiteTwitterUrl", existingData?.websiteTwitterUrl || "");
      setValue("customInstaUrl", existingData?.customInstaUrl || "");
      setValue("customFacebookUrl", existingData?.customFacebookUrl || "");
      setValue("customTwitterUrl", existingData?.customTwitterUrl || "");
      setValue("customSubDomain", existingData?.customSubDomain || "");
      setValue("websiteSubDomain", existingData?.websiteSubDomain || "");
      setValue("mobileSubDomain", existingData?.mobileSubDomain || "");
    }
  }, [existingData, preferences, setPreferenceType]);

  const handleChange = (preference) => {
    setPreferences({
      getReferralLink: false,
      integrateWebsite: false,
      integrateMobile: false,
      createOwnWebsite: false,
      [preference]: true,
    });
    setPreferenceType(preference);
  };

  const handleLogoUpload = async (event) => {
    const file = event.target.files[0];

    // Always reset the input value so the same file can be selected again
    event.target.value = "";

    if (file) {
      // Check file type
      if (!allowedTypes.includes(file.type)) {
        ToastNotifyError("Only JPG, JPEG, PNG, and WEBP files are allowed.");
        return;
      }

      const section = event.target.id.split("-")[0];

      setBrandCustomization((prev) => ({
        ...prev,
        [section + "Logo"]: file,
      }));

      await uploadTheLogo(file, section);
    }
  };

  const uploadTheLogo = async (file, section) => {
    try {
      const response = await dispatch(uploadImage(file));
      const { uploadResponse = {}, path } = response?.payload;
      if (uploadResponse?.status === 200) {
        setBrandLogo(path);
        setLogoUploaded(true);

        // Only create preview after successful upload
        const reader = new FileReader();
        reader.onloadend = () => {
          setLogoPreview((prev) => ({
            ...prev,
            [section]: reader.result,
          }));
        };
        reader.readAsDataURL(file);
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  // New color picker handlers
  const handleColorFieldClick = (fieldName, currentValue) => {
    setCurrentColorField(fieldName);
    setCurrentColorValue(currentValue || "#000000");
    setColorPickerOpen(true);
  };

  const handleColorChange = (newColor) => {
    if (currentColorField) {
      setValue(currentColorField, newColor);
    }
  };

  const handleColorPickerClose = () => {
    setColorPickerOpen(false);
    setCurrentColorField(null);
    setCurrentColorValue("#000000");
  };

  const onSubmit = (data) => {
    const completeFormData = {
      ...data,
      // Include preference selections
      getReferralLink: preferences.getReferralLink,
      integrateWebsite: preferences.integrateWebsite,
      integrateMobile: preferences.integrateMobile,
      createOwnWebsite: preferences.createOwnWebsite,
      // Include logo data
      logoUploaded,
      websiteLogo: brandCustomization.websiteLogo,
      mobileLogo: brandCustomization.mobileLogo,
      customLogo: brandCustomization.customLogo,
      logoPreview: {
        website: logoPreview.website,
        mobile: logoPreview.mobile,
        custom: logoPreview.custom,
      },
    };
    onNext(completeFormData);
  };

  const submitForm = (e) => {
    e.preventDefault();
    const submitPromise = handleSubmit(onSubmit)();
    if (submitPromise && typeof submitPromise.then === "function") {
      submitPromise
        .then(() => {})
        .catch((error) => {
          console.error("Form submission error:", error);
        });
    }
  };

  const isAnyPreferenceSelected = () => {
    return Object.values(preferences).some((value) => value === true);
  };

  return (
    <StyledChoosePreferences>
      <Box className="choose-preferences-container">
        <Box className="preferences-header">
          <Typography className="preferences-title">
            Choose your Preferences
          </Typography>
          <Typography className="preferences-subtitle">
            Let's start with the basics to get you set up on Zuumm.
          </Typography>
        </Box>
        <form onSubmit={submitForm} noValidate>
          <Box className="preferences-options">
            <Box className="preference-row">
              <Box className="preference-item">
                <Checkbox
                  checked={preferences.getReferralLink}
                  onChange={() => handleChange("getReferralLink")}
                  className="preference-checkbox"
                  sx={{
                    padding: "1px",
                    "& .MuiSvgIcon-root": {
                      fontSize: "16px",
                    },
                  }}
                />
                <Typography sx={{ fontSize: "14px" }}>
                  Get your referral link
                </Typography>
                <ResponsiveTooltipPopover
                  title={
                    "Get a unique referral link to invite others and earn commission"
                  }
                />
              </Box>

              <Box className="preference-item mobile-preference-item">
                <Checkbox
                  checked={preferences.integrateWebsite}
                  onChange={() => handleChange("integrateWebsite")}
                  className="preference-checkbox"
                  sx={{
                    padding: "1px",
                    "& .MuiSvgIcon-root": {
                      fontSize: "16px",
                    },
                  }}
                />
                <Typography sx={{ fontSize: "14px" }}>
                  Integrate with your own website
                </Typography>
                <ResponsiveTooltipPopover
                  title={
                    "Access powerful travel APIs to build your own custom website."
                  }
                />
              </Box>
            </Box>

            <Box className="preference-row">
              <Box className="preference-item">
                <Checkbox
                  checked={preferences.integrateMobile}
                  onChange={() => handleChange("integrateMobile")}
                  className="preference-checkbox"
                  sx={{
                    padding: "1px",
                    "& .MuiSvgIcon-root": {
                      fontSize: "16px",
                    },
                  }}
                />
                <Typography sx={{ fontSize: "14px" }}>
                  Integrate with your own mobile
                </Typography>
                <ResponsiveTooltipPopover
                  title={
                    "Integrate a smart travel AI chat agent using our ready-to-use SDK."
                  }
                />
              </Box>

              <Box className="preference-item">
                <Checkbox
                  checked={preferences.createOwnWebsite}
                  onChange={() => handleChange("createOwnWebsite")}
                  className="preference-checkbox"
                  sx={{
                    padding: "1px",
                    "& .MuiSvgIcon-root": {
                      fontSize: "16px",
                    },
                  }}
                />
                <Typography sx={{ fontSize: "14px" }}>
                  Get your own branded website
                </Typography>
                <ResponsiveTooltipPopover
                  title={
                    "Get your branded website in one tap — fully white-labelled"
                  }
                />
              </Box>
            </Box>
            {preferences.integrateWebsite && (
              <Box className="brand-customization">
                <Box className="customization-row-mobile">
                  <Box className="logo-upload">
                    <input
                      type="file"
                      accept="image/*"
                      id="website-logo-upload"
                      style={{ display: "none" }}
                      onChange={handleLogoUpload}
                    />
                    <label
                      htmlFor="website-logo-upload"
                      className="upload-label"
                    >
                      {logoPreview.website ? (
                        <Box className="logo-preview-wrap">
                          <Box className="logo-preview-container">
                            <img
                              src={logoPreview.website}
                              alt="Brand logo"
                              className="logo-preview"
                            />
                          </Box>
                          <Typography className="change-logo-text">
                            Change Photo
                          </Typography>
                        </Box>
                      ) : (
                        <>
                          <Box className="upload-circle">
                            <AddIcon className="add-icon" />
                          </Box>
                          <Typography className="upload-text">
                            Upload Brand logo
                          </Typography>
                        </>
                      )}
                    </label>
                  </Box>
                  <Box className="right-side-mobile">
                    <Box className="color-selection-mobile">
                      <Box className="color-field">
                        <Box className="color-input-container">
                          <Controller
                            name="primaryColor"
                            control={control}
                            render={({ field }) => (
                              <CustomTextField
                                {...field}
                                required={true}
                                label="Primary Color"
                                value={(field.value || "").toUpperCase()}
                                onClick={() =>
                                  handleColorFieldClick(
                                    "primaryColor",
                                    field.value
                                  )
                                }
                                className="color-text-field"
                                colorPreview={field.value}
                                error={!!errors.primaryColor}
                                helperText={errors.primaryColor?.message}
                                readOnly
                              />
                            )}
                          />
                        </Box>
                      </Box>
                      <Box className="color-field">
                        <Box className="color-input-container">
                          <Controller
                            name="secondaryColor"
                            control={control}
                            render={({ field }) => (
                              <CustomTextField
                                {...field}
                                label="Secondary Color"
                                value={(field.value || "").toUpperCase()}
                                onClick={() =>
                                  handleColorFieldClick(
                                    "secondaryColor",
                                    field.value
                                  )
                                }
                                className="color-text-field"
                                colorPreview={field.value}
                                error={!!errors.secondaryColor}
                                helperText={errors.secondaryColor?.message}
                                readOnly
                              />
                            )}
                          />
                        </Box>
                      </Box>
                    </Box>
                    <Box className="mobile-textfeilds">
                      <Controller
                        name="websiteInstaUrl"
                        control={control}
                        render={({ field }) => (
                          <CustomTextField
                            {...field}
                            placeholder="Enter URL"
                            className="social-text-field"
                            error={!!errors.websiteInstaUrl}
                            helperText={errors.websiteInstaUrl?.message}
                            startAdornment={
                              <Box className="social-icon-wrapper">
                                <img
                                  src={InstagramIcon}
                                  alt="Instagram"
                                  className="social-icon"
                                />
                              </Box>
                            }
                          />
                        )}
                      />
                      <Controller
                        name="websiteFacebookUrl"
                        control={control}
                        render={({ field }) => (
                          <CustomTextField
                            {...field}
                            placeholder="Enter URL"
                            className="social-text-field"
                            error={!!errors.websiteFacebookUrl}
                            helperText={errors.websiteFacebookUrl?.message}
                            startAdornment={
                              <Box className="social-icon-wrapper">
                                <img
                                  src={FacebookIcon}
                                  alt="Facebook"
                                  className="social-icon"
                                />
                              </Box>
                            }
                          />
                        )}
                      />
                      <Controller
                        name="websiteTwitterUrl"
                        control={control}
                        render={({ field }) => (
                          <CustomTextField
                            {...field}
                            placeholder="Enter URL"
                            className="social-text-field"
                            error={!!errors.websiteTwitterUrl}
                            helperText={errors.websiteTwitterUrl?.message}
                            startAdornment={
                              <Box className="social-icon-wrapper">
                                <img
                                  src={TwitterIcon}
                                  alt="Twitter"
                                  className="social-icon"
                                />
                              </Box>
                            }
                          />
                        )}
                      />
                    </Box>
                  </Box>
                </Box>
                <Box className="website-text-field-sub-domain">
                  <Controller
                    name="websiteSubDomain"
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        {...field}
                        required={true}
                        sx={{
                          "& .MuiInputBase-root.MuiOutlinedInput-root": {
                            paddingRight: "0px !important",
                          },
                        }}
                        label="Sub-Domain"
                        className="social-text-field"
                        error={!!errors.websiteSubDomain}
                        helperText={errors.websiteSubDomain?.message}
                        endAdornment={
                          <Box className="container-sub-domain-textfeild">
                            <Box className="website-text-field-sub-domain-adornment">
                              <Typography className="sub-domain-text">
                                .zuumm.com
                              </Typography>
                            </Box>
                            <ResponsiveTooltipPopover
                              title={
                                "Your personalized travel site hosted as a subdomain on zuumm.com."
                              }
                            />
                          </Box>
                        }
                      />
                    )}
                  />
                </Box>
              </Box>
            )}

            {preferences.integrateMobile && (
              <Box className="brand-customization">
                <Box className="customization-row-mobile">
                  <Box className="logo-upload">
                    <input
                      type="file"
                      accept="image/*"
                      id="mobile-logo-upload"
                      style={{ display: "none" }}
                      onChange={handleLogoUpload}
                    />
                    <label
                      htmlFor="mobile-logo-upload"
                      className="upload-label"
                    >
                      {logoPreview.mobile ? (
                        <Box className="logo-preview-wrap">
                          <Box className="logo-preview-container">
                            <img
                              src={logoPreview.mobile}
                              alt="Brand logo"
                              className="logo-preview"
                            />
                          </Box>
                          <Typography className="change-logo-text">
                            Change Photo
                          </Typography>
                        </Box>
                      ) : (
                        <>
                          <Box className="upload-circle">
                            <AddIcon className="add-icon" />
                          </Box>
                          <Typography className="upload-text">
                            Upload Brand logo
                          </Typography>
                        </>
                      )}
                    </label>
                  </Box>
                  <Box className="right-side-mobile">
                    <Box className="color-selection-mobile">
                      <Box className="color-field">
                        <Box className="color-input-container">
                          <Controller
                            name="primaryColor2"
                            control={control}
                            render={({ field }) => (
                              <CustomTextField
                                {...field}
                                required={true}
                                label="Primary Color"
                                value={(field.value || "").toUpperCase()}
                                onClick={() =>
                                  handleColorFieldClick(
                                    "primaryColor2",
                                    field.value
                                  )
                                }
                                className="color-text-field"
                                colorPreview={field.value}
                                error={!!errors.primaryColor2}
                                helperText={errors.primaryColor2?.message}
                                readOnly
                              />
                            )}
                          />
                        </Box>
                      </Box>
                      <Box className="color-field">
                        <Box className="color-input-container">
                          <Controller
                            name="secondaryColor2"
                            control={control}
                            render={({ field }) => (
                              <CustomTextField
                                {...field}
                                label="Secondary Color"
                                value={(field.value || "").toUpperCase()}
                                onClick={() =>
                                  handleColorFieldClick(
                                    "secondaryColor2",
                                    field.value
                                  )
                                }
                                className="color-text-field"
                                colorPreview={field.value}
                                error={!!errors.secondaryColor2}
                                helperText={errors.secondaryColor2?.message}
                                readOnly
                              />
                            )}
                          />
                        </Box>
                      </Box>
                    </Box>
                    <Box className="mobile-textfeilds">
                      <Controller
                        name="mobileInstaUrl"
                        control={control}
                        render={({ field }) => (
                          <CustomTextField
                            {...field}
                            placeholder="Enter URL"
                            className="social-text-field"
                            error={!!errors.mobileInstaUrl}
                            helperText={errors.mobileInstaUrl?.message}
                            startAdornment={
                              <Box className="social-icon-wrapper">
                                <img
                                  src={InstagramIcon}
                                  alt="Instagram"
                                  className="social-icon"
                                />
                              </Box>
                            }
                          />
                        )}
                      />
                      <Controller
                        name="mobileFacebookUrl"
                        control={control}
                        render={({ field }) => (
                          <CustomTextField
                            {...field}
                            placeholder="Enter URL"
                            className="social-text-field"
                            error={!!errors.mobileFacebookUrl}
                            helperText={errors.mobileFacebookUrl?.message}
                            startAdornment={
                              <Box className="social-icon-wrapper">
                                <img
                                  src={FacebookIcon}
                                  alt="Facebook"
                                  className="social-icon"
                                />
                              </Box>
                            }
                          />
                        )}
                      />
                      <Controller
                        name="mobileTwitterUrl"
                        control={control}
                        render={({ field }) => (
                          <CustomTextField
                            {...field}
                            placeholder="Enter URL"
                            className="social-text-field"
                            error={!!errors.mobileTwitterUrl}
                            helperText={errors.mobileTwitterUrl?.message}
                            startAdornment={
                              <Box className="social-icon-wrapper">
                                <img
                                  src={TwitterIcon}
                                  alt="Twitter"
                                  className="social-icon"
                                />
                              </Box>
                            }
                          />
                        )}
                      />
                    </Box>
                  </Box>
                </Box>
                <Box className="website-text-field-sub-domain">
                  <Controller
                    name="mobileSubDomain"
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        {...field}
                        required={true}
                        sx={{
                          "& .MuiInputBase-root.MuiOutlinedInput-root": {
                            paddingRight: "0px !important",
                          },
                        }}
                        label="Sub-Domain"
                        className="social-text-field"
                        error={!!errors.mobileSubDomain}
                        helperText={errors.mobileSubDomain?.message}
                        endAdornment={
                          <Box className="container-sub-domain-textfeild">
                            <Box className="website-text-field-sub-domain-adornment">
                              <Typography className="sub-domain-text">
                                .zuumm.com
                              </Typography>
                            </Box>

                            <ResponsiveTooltipPopover
                              title={
                                "Your personalized travel site hosted as a subdomain on zuumm.com."
                              }
                            />
                          </Box>
                        }
                      />
                    )}
                  />
                </Box>
              </Box>
            )}

            {preferences.createOwnWebsite && (
              <Box className="brand-customization">
                <Box className="customization-row-mobile">
                  <Box className="logo-upload">
                    <input
                      type="file"
                      accept="image/*"
                      id="custom-logo-upload"
                      style={{ display: "none" }}
                      onChange={handleLogoUpload}
                    />
                    <label
                      htmlFor="custom-logo-upload"
                      className="upload-label"
                    >
                      {logoPreview.custom ? (
                        <Box className="logo-preview-wrap">
                          <Box className="logo-preview-container">
                            <img
                              src={logoPreview.custom}
                              alt="Brand logo"
                              className="logo-preview"
                            />
                          </Box>
                          <Typography className="change-logo-text">
                            Change Photo
                          </Typography>
                        </Box>
                      ) : (
                        <>
                          <Box className="upload-circle">
                            <AddIcon className="add-icon" />
                          </Box>
                          <Typography className="upload-text">
                            Upload Brand logo
                          </Typography>
                        </>
                      )}
                    </label>
                  </Box>
                  <Box className="right-side-mobile">
                    <Box className="color-selection-mobile">
                      <Box className="color-field">
                        <Box className="color-input-container">
                          <Controller
                            name="primaryColor3"
                            control={control}
                            render={({ field }) => (
                              <CustomTextField
                                {...field}
                                required={true}
                                label="Primary Color"
                                value={(field.value || "").toUpperCase()}
                                onClick={() =>
                                  handleColorFieldClick(
                                    "primaryColor3",
                                    field.value
                                  )
                                }
                                className="color-text-field"
                                colorPreview={field.value}
                                error={!!errors.primaryColor3}
                                helperText={errors.primaryColor3?.message}
                                readOnly
                              />
                            )}
                          />
                        </Box>
                      </Box>
                      <Box className="color-field">
                        <Box className="color-input-container">
                          <Controller
                            name="secondaryColor3"
                            control={control}
                            render={({ field }) => (
                              <CustomTextField
                                {...field}
                                label="Secondary Color"
                                value={(field.value || "").toUpperCase()}
                                onClick={() =>
                                  handleColorFieldClick(
                                    "secondaryColor3",
                                    field.value
                                  )
                                }
                                className="color-text-field"
                                colorPreview={field.value}
                                error={!!errors.secondaryColor3}
                                helperText={errors.secondaryColor3?.message}
                                readOnly
                              />
                            )}
                          />
                        </Box>
                      </Box>
                    </Box>
                    <Box className="mobile-textfeilds">
                      <Controller
                        name="customInstaUrl"
                        control={control}
                        render={({ field }) => (
                          <CustomTextField
                            {...field}
                            placeholder="Enter URL"
                            className="social-text-field"
                            error={!!errors.customInstaUrl}
                            helperText={errors.customInstaUrl?.message}
                            startAdornment={
                              <Box className="social-icon-wrapper">
                                <img
                                  src={InstagramIcon}
                                  alt="Instagram"
                                  className="social-icon"
                                />
                              </Box>
                            }
                          />
                        )}
                      />
                      <Controller
                        name="customFacebookUrl"
                        control={control}
                        render={({ field }) => (
                          <CustomTextField
                            {...field}
                            placeholder="Enter URL"
                            className="social-text-field"
                            error={!!errors.customFacebookUrl}
                            helperText={errors.customFacebookUrl?.message}
                            startAdornment={
                              <Box className="social-icon-wrapper">
                                <img
                                  src={FacebookIcon}
                                  alt="Facebook"
                                  className="social-icon"
                                />
                              </Box>
                            }
                          />
                        )}
                      />
                      <Controller
                        name="customTwitterUrl"
                        control={control}
                        render={({ field }) => (
                          <CustomTextField
                            {...field}
                            placeholder="Enter URL"
                            className="social-text-field"
                            error={!!errors.customTwitterUrl}
                            helperText={errors.customTwitterUrl?.message}
                            startAdornment={
                              <Box className="social-icon-wrapper">
                                <img
                                  src={TwitterIcon}
                                  alt="Twitter"
                                  className="social-icon"
                                />
                              </Box>
                            }
                          />
                        )}
                      />
                    </Box>
                  </Box>
                </Box>
                <Box className="website-text-field-sub-domain">
                  <Controller
                    name="customSubDomain"
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        {...field}
                        required={true}
                        sx={{
                          "& .MuiInputBase-root.MuiOutlinedInput-root": {
                            paddingRight: "0px !important",
                          },
                        }}
                        label="Sub-Domain"
                        className="social-text-field"
                        error={!!errors.customSubDomain}
                        helperText={errors.customSubDomain?.message}
                        endAdornment={
                          <Box className="container-sub-domain-textfeild">
                            <Box className="website-text-field-sub-domain-adornment">
                              <Typography className="sub-domain-text">
                                .zuumm.com
                              </Typography>
                            </Box>
                            <ResponsiveTooltipPopover
                              title={
                                "Your personalized travel site hosted as a subdomain on zuumm.com."
                              }
                            />
                          </Box>
                        }
                      />
                    )}
                  />
                </Box>
              </Box>
            )}
          </Box>
          <Box className="navigation-buttons">
            <Button className="previous-button" onClick={onPrevious}>
              Previous
            </Button>
            <Button
              className="next-button"
              type="submit"
              disabled={!isAnyPreferenceSelected()}
              onClick={(e) => {
                const selectedPreference = Object.keys(preferences).find(
                  (key) => preferences[key]
                );
                let logoRequired = false;

                if (
                  selectedPreference === "integrateWebsite" &&
                  !logoPreview.website
                ) {
                  logoRequired = true;
                } else if (
                  selectedPreference === "integrateMobile" &&
                  !logoPreview.mobile
                ) {
                  logoRequired = true;
                } else if (
                  selectedPreference === "createOwnWebsite" &&
                  !logoPreview.custom
                ) {
                  logoRequired = true;
                }

                if (logoRequired) {
                  e.preventDefault();
                  ToastNotifyError("Logo not uploaded");
                  return;
                }
                submitForm(e);
              }}
              sx={{
                backgroundColor: !isAnyPreferenceSelected()
                  ? "#808080"
                  : "#007bff",
                color: "#ffffff",
                "&:hover": {
                  backgroundColor: !isAnyPreferenceSelected()
                    ? "#808080"
                    : "#0056b3",
                },
                "&.Mui-disabled": {
                  backgroundColor: "#808080 !important",
                  color: "#ffffff !important",
                  opacity: 0.7,
                },
              }}
            >
              Next
            </Button>
          </Box>
        </form>
      </Box>

      {/* Color Picker Popup */}
      <ColorPickerPopup
        open={colorPickerOpen}
        onClose={handleColorPickerClose}
        onColorChange={handleColorChange}
        initialColor={currentColorValue}
      />
    </StyledChoosePreferences>
  );
};

export default ChoosePreferences;
