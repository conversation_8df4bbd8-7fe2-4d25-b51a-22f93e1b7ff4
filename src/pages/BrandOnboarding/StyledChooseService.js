import { styled } from '@mui/material/styles';

const StyledChooseService = styled('div')(({ theme }) =>`
  .choose-service-container {
    padding: 24px;
    max-width: 600px;
    margin: 0 auto;
  }

  .choose-service-header {
    text-align: center;
    margin-bottom: 40px;
  }

  .choose-service-title {
    font-family: 'Roboto', sans-serif;
    font-size: 24px;
    font-weight: 500;
    color: #000000;
    margin-bottom: 8px;
  }

  .choose-service-subtitle {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    color: #6B6B6B;
    line-height: 1.5;
  }

  .package-selection {
    margin-top: 20px;
  }

  .section-title {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: 500;
    color: #000000;
    margin-bottom: 16px;
  }

  .radio-group {
    display: flex;
    flex-direction: row;
    gap: 10px;
    margin-bottom: 24px;
  }

  .radio-group-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    border: 1px solid rgba(255, 57, 81, 0.05);
    border-radius: 8px;
    width: 220px;
    background-color: #FFFFFF;

}

  .radio-label {
    margin: 0;
    
    .MuiTypography-root {
      font-family: 'Roboto', sans-serif;
      font-weight: 400;
      font-size: 16px;
      color: ${theme.palette.common.subText};
    }
  }

  .custom-radio {
    padding: 0;
    margin-right: 12px;
    color: #00AF31;

    &.Mui-checked {
      color: #00AF31;
    }
  }

  .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .checkbox-label {
    margin: 0;
    
    .MuiTypography-root {
      font-family: 'Roboto', sans-serif;
      font-weight: 400;
      font-size: 16px;
      color: ${theme.palette.common.subText};
    }
  }

  .custom-checkbox {
    padding: 0;
    margin-right: 12px;
    margin-left: 0 !important;
    color: #0083E7;

    &.Mui-checked {
      color: #0083E7;
    }
  }

  .terms-link {
    color: #0083E7;
    text-decoration: underline;
  }

  .choose-service-btns {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
  }

  .choose-service-btn-previous {
    color: #FF3951;
    border: 1px solid #FF3951;
    padding: 8px 24px;
    text-transform: none;
    border-radius: 4px;
    font-family: 'Roboto', sans-serif;
  }

  .choose-service-btn-submit {
    background-color: #FF3951;
    color: white;
    padding: 8px 24px;
    text-transform: none;
    border-radius: 4px;
    font-family: 'Roboto', sans-serif;
    box-shadow: none;
    &:hover {
      
    }
  }

  @media (max-width: 768px) {
    .choose-service-title {
      font-size: 20px;
    }
    .choose-service-subtitle {
      font-size: 12px;
    }
    .radio-group {
      flex-direction: column;
    }
    .radio-group-item {
      width: 100%;
    }
    
    
    
  }
  
`);

export default StyledChooseService;
