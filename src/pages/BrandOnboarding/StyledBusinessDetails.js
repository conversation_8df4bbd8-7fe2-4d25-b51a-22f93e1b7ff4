import { styled } from '@mui/material/styles';

const StyledBusinessDetails = styled('div')(({ theme }) => `
    .business-details-container {
        padding: 0px 24px 24px 24px;
        max-width: 397px;
        margin: 0 auto;
    }

  .business-details-header {
    text-align: center;
    margin-bottom: 40px;
  }

  .business-details-title {
    font-family: 'Roboto', sans-serif;
    font-size: 24px;
    font-weight: 500;
    color: #000000;
    margin-bottom: 8px;
  }

  .business-details-subtitle {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    color: #6B6B6B;
    line-height: 1.5;
  }
  .business-btns {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }  

  .business-btn-previous {
    color: #FF3951;
    border: 1px solid #FF3951;
    padding: 8px 24px;
    text-transform: none;
    border-radius: 4px;
    font-family: 'Roboto', sans-serif;
  }
  .business-btn-next {
    background-color: #FF3951;
    color: white;
    padding: 8px 24px;
    text-transform: none;
    border-radius: 4px;
    font-family: 'Roboto', sans-serif;
  }

  @media (max-width: 768px) {
    .business-details-title {
      font-size: 20px;
    }
    .business-details-subtitle {
      font-size: 12px;
    }
    
  } 
`)


export default StyledBusinessDetails;
