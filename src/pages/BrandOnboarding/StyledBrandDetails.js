import { styled } from '@mui/material/styles';

const StyledBrandDetails = styled("div")(({theme}) => `

.brand-details-container {
  width: 100%;
  height: 100%;
}
.brand-details-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin: 0 auto;
}  
.brand-details-title {
  font-family: 'Roboto', sans-serif;
  font-size: 24px;
  font-weight: 500;
  color: #000000;
  line-height: 28px;
}  
.brand-details-subtitle {
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: ${theme.palette.secondary.grey};
  line-height: 28px;
}  
.brand-details-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0px;
  width: 60%;
  margin: 20px auto;
}  
.brand-details-form-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  width: 100%;
}  
.text-field-container {
  width: 100%;
}  
.text-field-container2 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 100%;
}  
.next-button-container {
  display: flex;
  align-items: center;
  justify-content: end;
  width: 100%;
}  
.next-button {
  background-color: ${theme.palette.custom.lightred};
  color: ${theme.palette.custom.white};
  border-radius: 4px;
  padding: 12px 24px;
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 100%;
  text-align: center;

  &:hover {
    background-color: ${theme.palette.custom.lightred};
    opacity: 0.9;
  }

  &.Mui-disabled {
    background-color: #E0E0E0;
    color: #9E9E9E;
    cursor: not-allowed;
  }
}  
.get-otp-text {
  font-family: 'Roboto', sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 100%;
  text-align: center;
  color: #DB0000;
  text-decoration: underline;
} 
.get-otp-verified {
  font-family: 'Roboto', sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 100%;
  text-align: center;
  color: #00AF31;
} 
  
@media (max-width: 768px) {
  .brand-details-title {
    font-size: 20px;
    text-align: center;
    line-height: 14px;
  }

  .brand-details-subtitle {
    font-size: 12px;
    text-align: center;
    padding: 0px 20px;
  }
  .brand-details-form {
    width: 90%;
  }  
  .brand-details-form-row {
    flex-direction: column;
    gap: 0px;
  }
  .text-field-container {
    width: 100%;
  }
  .text-field-container2 {
    flex-direction: column;
  }
}





`);

export default StyledBrandDetails;
