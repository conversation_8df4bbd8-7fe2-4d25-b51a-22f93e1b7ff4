import React, { useState } from "react";
import BrandDetails from "./BrandDetails";
import { Box, Divider, Typography } from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import StyledBrandOnboarding from "./StyledBrandOnboarding";
import ChoosePreferences from "./ChoosePreferences";
import BusinessDetails from "./BusinessDetails";
import ChooseService from "./ChooseService";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  BrandDetailsSchema,
  BusinessDetailsSchema,
  PreferencesSchema,
  ServicesSchema,
} from "../../schema/partnerOnboardingSchema";
import { useDispatch } from "react-redux";
import { fetchBrandOnboarding } from "../../store/reducers/PartnerOnboarding";
import { ToastNotifyError } from "../../components/Toast/ToastNotify";
import circleCrossIcon from "../../assets/svg/circleCrossIcon.svg";

const BrandOnboarding = ({ handleSuccess, setClose }) => {
  const dispatch = useDispatch();
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [preferenceType, setPreferenceType] = useState("");
  const [brandLogo, setBrandLogo] = useState("");
  const [formData, setFormData] = useState({
    brandDetails: {},
    preferences: {},
    businessDetails: {},
    services: {},
  });

  const getDefaultValues = () => {
    const values = (() => {
      switch (currentStep) {
        case 1:
          return {
            countryCode: "+91", // Set default country code
            ...formData.brandDetails
          };
        case 2:
          return formData.preferences;
        case 3:
          return formData.businessDetails;
        case 4:
          return formData.services;
        default:
          return {};
      }
    })();
    return values;
  };

  const validationSchema = [
    BrandDetailsSchema,
    // PreferencesSchema,
    PreferencesSchema,
    BusinessDetailsSchema,
    ServicesSchema,
  ];

  const {
    control,
    handleSubmit,
    formState: { errors },
    trigger,
    reset,
    setValue,
  } = useForm({
    resolver: yupResolver(validationSchema[currentStep - 1]),
    context: { preferenceType: preferenceType },
    defaultValues: getDefaultValues(),
  });

  const handleNext = (stepData) => {
    
    setFormData((prev) => {
      const newFormData = {
        ...prev,
        [getCurrentStepKey()]: stepData,
      };
      return newFormData;
    });
    setCompletedSteps((prev) => [...prev, currentStep]);
    setCurrentStep((prev) => prev + 1);
  };

  const handlePrevious = () => {
    console.log("handlePrevious - currentStep:", formData);
    setCurrentStep((prev) => prev - 1);
  };


  const handleFinalSubmit = async (serviceData) => {

    const subDomainPrefrence = () => {
      if(preferenceType === "createOwnWebsite"){
        return formData?.preferences?.customSubDomain;
      } else if(preferenceType === "integrateWebsite"){
        return formData?.preferences?.websiteSubDomain;
      } else if(preferenceType === "integrateMobile"){
        return formData?.preferences?.mobileSubDomain;
      } else if(preferenceType === "getReferralLink"){
        return ""
      }
      return "";
    }

    const finalFormData = {
      ...formData,
      services: serviceData,
    };
    const BEPreference =
      preferenceType === "getReferralLink"
        ? "RL"
        : preferenceType === "integrateWebsite"
          ? "IW"
          : preferenceType === "integrateMobile"
            ? "IA"
            : "OW";

    const BEPackage =
      finalFormData?.services?.selectedPackage === "Sell your own packages"
        ? "OP" : "ZP"


        const getFacebookUrl = () => {
          if (preferenceType === "createOwnWebsite") {
            return finalFormData?.preferences?.customFacebookUrl;
          } else if (preferenceType === "integrateWebsite") {
            return finalFormData?.preferences?.websiteFacebookUrl;
          } else if (preferenceType === "integrateMobile") {
            return finalFormData?.preferences?.mobileFacebookUrl;
          } else if (preferenceType === "getReferralLink") {
            return "";
          }
        };

        const getInstagramUrl = () => {
          if (preferenceType === "createOwnWebsite") {
            return finalFormData?.preferences?.customInstaUrl;
          } else if (preferenceType === "integrateWebsite") {
            return finalFormData?.preferences?.websiteInstaUrl;
          } else if (preferenceType === "integrateMobile") {
            return finalFormData?.preferences?.mobileInstaUrl;
          } else if (preferenceType === "getReferralLink") {
            return "";
          }
        };

        const getTwitterUrl = () => {
          if (preferenceType === "createOwnWebsite") {
            return finalFormData?.preferences?.customTwitterUrl;
          } else if (preferenceType === "integrateWebsite") {
            return finalFormData?.preferences?.websiteTwitterUrl;
          } else if (preferenceType === "integrateMobile") {
            return finalFormData?.preferences?.mobileTwitterUrl;
          } else if (preferenceType === "getReferralLink") {
            return "";
          }
        };

        console.log("finalFormData: ", finalFormData?.preferences);
    const payload = {
      entity_name: finalFormData?.brandDetails?.brandName,
      entity_type: finalFormData?.brandDetails?.entityType,
      preference: BEPreference,
      organization_url: finalFormData?.brandDetails?.websiteUrl
        ? finalFormData?.brandDetails?.websiteUrl
        : "",
      facebook_media_link: getFacebookUrl(),
      instagram_media_link: getInstagramUrl(),
      twitter_media_link: getTwitterUrl(),
      ...(subDomainPrefrence()
        ? {
            subdomain: subDomainPrefrence() || "",
          }
        : {}),
      ...(brandLogo ? { logo: brandLogo } : {}),
      ...(preferenceType === "getReferralLink"
        ? {}
        : {
            primary_theme:
              finalFormData?.preferences?.primaryColor ||
              finalFormData?.preferences?.primaryColor2 ||
              finalFormData?.preferences?.primaryColor3 ||
              "",
          }),
      ...(preferenceType === "getReferralLink"
        ? {}
        : {
            secondary_theme:
              finalFormData?.preferences?.secondaryColor ||
              finalFormData?.preferences?.secondaryColor2 ||
              finalFormData?.preferences?.secondaryColor3 ||
              "",
          }),
      ...(finalFormData?.businessDetails?.gstin
        ? { gst_number: finalFormData?.businessDetails?.gstin }
        : {}),
      ...(finalFormData?.businessDetails?.companyRegistrationNo
        ? {
            company_registration_number:
              finalFormData?.businessDetails?.companyRegistrationNo,
          }
        : {}),
      ...(finalFormData?.businessDetails?.monthlyTransactionVolume
        ? {
            monthly_transaction_volume:
              finalFormData?.businessDetails?.monthlyTransactionVolume,
          }
        : {}),
      package_type: BEPackage,
      referral_code: "",
      update_notification_enabled: finalFormData?.services?.keepPosted,
      address: {
        city: finalFormData?.brandDetails?.city || "",
        country: finalFormData?.brandDetails?.country || "",
      },
      partner_admin: {
        full_name: finalFormData?.brandDetails?.contactPersonName,
        email: finalFormData?.brandDetails?.email,
        phone_number_country_code: finalFormData?.brandDetails?.countryCode,
        phone_number: finalFormData?.brandDetails?.phoneNumber,
      },
    };

    try {
      const res = await dispatch(fetchBrandOnboarding(payload));
      const { status, message } = res?.payload?.data;

      

      if (status === "success") {
         handleSuccess();         
      } else {
        ToastNotifyError(message);
      }
    } catch (error) {
      console.log("error", error);
    }
    // Add your API call or further processing here
  };

  const onSubmit = (data) => {
    if (currentStep === 4) {
      return; // Let the ChooseService component handle its own submission
    }
    handleNext(data);
  };

  const getCurrentStepKey = () => {
    switch (currentStep) {
      case 1:
        return "brandDetails";
      case 2:
        return "preferences";
      case 3:
        return "businessDetails";
      case 4:
        return "services";
      default:
        return "brandDetails";
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <BrandDetails
            handleSubmit={handleSubmit}
            onSubmit={onSubmit}
            errors={errors}
            control={control}
            onNext={handleNext}
            trigger={trigger}
            existingData={formData.brandDetails}
            reset={reset}
            setValue={setValue}
          />
        );
      case 2:
        return (
          <ChoosePreferences
            trigger={trigger}
            onNext={handleNext}
            onPrevious={handlePrevious}
            errors={errors}
            control={control}
            handleSubmit={handleSubmit}
            setPreferenceType={setPreferenceType}
            setBrandLogo={setBrandLogo}
            existingData={formData.preferences}
            preferenceType={preferenceType}
            setValue={setValue}
          />
        );
      case 3:
        return (
          <BusinessDetails
            onNext={handleNext}
            onPrevious={handlePrevious}
            errors={errors}
            control={control}
            handleSubmit={handleSubmit}
            existingData={formData.businessDetails}
            setValue={setValue}
          />
        );
      case 4:
        return (
          <ChooseService
            onSubmit={handleFinalSubmit}
            onPrevious={handlePrevious}
            formData={formData}
            // brandLogo={setBrandLogo}
            existingData={formData.services}
          />
        );
      default:
        return (
          <BrandDetails onNext={handleNext} errors={errors} control={control} />
        );
    }
  };

  return (
    <StyledBrandOnboarding>
      <Box className='brand-onboarding-container'>
        <Box className="circle-cross-icon-container" onClick={() => setClose()}>
        <img src={circleCrossIcon} alt="circleCrossIcon" className="circle-cross-icon" />          
        </Box>
        <Box className='brand-onboarding-header'>
          <Typography className='brand-onboarding-title'>
            With Just One Tap, Get Your Brand Live On ZUUMM.
          </Typography>
          <Box className='brand-onboarding-step-indicator'>
            <Typography
              className={`brand-onboarding-step-indicator-text ${currentStep === 1 ? "active" : ""} ${completedSteps.includes(1) ? "completed" : ""}`}
            >
              <span>1</span>
              {completedSteps.includes(1) && <CheckCircleIcon className='step-check-icon' />}
              Brand Details
            </Typography>
            <Divider 
              className={`brand-onboarding-step-indicator-divider ${currentStep > 1 ? "completed" : ""}`}
              sx={{
                backgroundColor: currentStep > 1 ? '#4CAF50' : '#ccc'
              }}
            />
            <Typography
              className={`brand-onboarding-step-indicator-text ${currentStep === 2 ? "active" : ""} ${completedSteps.includes(2) ? "completed" : ""}`}
            >
              <span>2</span>
              {completedSteps.includes(2) && <CheckCircleIcon className='step-check-icon' />}
              Choose Preferences
            </Typography>
            <Divider 
              className={`brand-onboarding-step-indicator-divider ${currentStep > 2 ? "completed" : ""}`}
              sx={{
                backgroundColor: currentStep > 2 ? '#4CAF50' : '#ccc'
              }}
            />
            <Typography
              className={`brand-onboarding-step-indicator-text ${currentStep === 3 ? "active" : ""} ${completedSteps.includes(3) ? "completed" : ""}`}
            >
              <span>3</span>
              {completedSteps.includes(3) && <CheckCircleIcon className='step-check-icon' />}
              Business Details
            </Typography>
            <Divider 
              className={`brand-onboarding-step-indicator-divider ${currentStep > 3 ? "completed" : ""}`}
              sx={{
                backgroundColor: currentStep > 3 ? '#4CAF50' : '#ccc'
              }}
            />
            <Typography
              className={`brand-onboarding-step-indicator-text ${currentStep === 4 ? "active" : ""} ${completedSteps.includes(4) ? "completed" : ""}`}
            >
              <span>4</span>
              {completedSteps.includes(4) && <CheckCircleIcon className='step-check-icon' />}
              Choose Services
            </Typography>
          </Box>
        </Box>

        {renderCurrentStep()}
      </Box>
    </StyledBrandOnboarding>
  );
};

export default BrandOnboarding;
