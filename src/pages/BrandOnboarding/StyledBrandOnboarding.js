import { styled } from "@mui/material/styles";
import smallTickIcon from "../../assets/svg/smallTickIcon2.svg";

const StyledBrandOnboarding = styled("div")(({theme}) => `
.brand-onboarding-container {
  padding: 10px 0;
  background: #FFF9F9;
  position: relative;
  height: 100%;
}
.circle-cross-icon-container {
  position: absolute;
  top: 30px;
  right: 40px;
  z-index: 1;
  cursor: pointer;
}
.brand-onboarding-header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 20px;
}  
.brand-onboarding-title {
  font-family: '<PERSON>ra', serif;
  font-size: 24px;
  font-weight: 600;
  color: ${theme.palette.secondary.main};
  line-height: 130%;
  text-transform: capitalize;
}  
.brand-onboarding-step-indicator {
  width: 70%;
  height: 80px;
  background: linear-gradient(90deg, #E6ECFF 0%, #FFECEE 100%);
  border-radius: 8px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: 20px auto;
}  
.brand-onboarding-step-indicator-divider {
  border-width: 0.5px;
  height: 60%;
  rotate: 30deg;
  background-color: ${theme.palette.custom.divider};
}

.brand-onboarding-step-indicator-divider.completed {
  background: linear-gradient(90deg, #4CAF50 0%, #4CAF50 33%, #ccc 33%, #ccc 100%);
}

.brand-onboarding-step-indicator-divider.completed:nth-of-type(2) {
  background: linear-gradient(90deg, #4CAF50 0%, #4CAF50 66%, #ccc 66%, #ccc 100%);
}

.brand-onboarding-step-indicator-divider.completed:nth-of-type(3) {
  background: linear-gradient(90deg, #4CAF50 0%, #4CAF50 100%);
}

.brand-onboarding-step-indicator-text {
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: ${theme.palette.custom.placeholder};
  line-height: 20px;
  text-transform: capitalize;
  display: flex;
  align-items: center;
  gap: 8px;
  
  span {
    padding-right: 15px;
  }
  .step-check-icon {
    width: 24px;
    height: 24px;
    color: #4CAF50;
  }

  &.active {
    color: ${theme.palette.custom.lightred};
    font-weight: 500;

    .step-number {
      background-color: ${theme.palette.custom.lightred};
    }
  }

  &.completed {
    color: ${theme.palette.custom.subtext};
    font-weight: 500;
    
    span {
      display: none;
    }
  }
}

@media (max-width: 1024px) {

  .brand-onboarding-step-indicator {
    width: 96%;
  }
  .brand-onboarding-step-indicator-text {
    gap: 0px;
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .mobile-preference-item {
    margin-bottom: 15px !important;
  }
  .brand-onboarding-title {
    font-size: 20px;
    text-align: center;
    padding: 15px 20px;
  }

  .brand-onboarding-step-indicator {
    width: 97%;
    padding: 20px 10px;
    height: auto;
    background: linear-gradient(90deg, #E6ECFF 0%, #FFECEE 100%);
    margin: 40px auto 20px;
    position: relative;
    justify-content: space-between;
    min-width: 200px;
    max-width: 600px;
  }

  .brand-onboarding-step-indicator-text {
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    gap: 8px;
    width: auto;
    z-index: 2;
    font-size: 14px;
    color: #666;
    padding: 0;

    .step-check-icon {
      display: none;
    }
    
    span {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: 2px solid #fff;
      box-shadow: 0 0 0 2px ${theme.palette.custom.placeholder};
      background: #fff;
      display: flex !important;
      align-items: center;
      justify-content: center;
      padding: 0;
      margin: 0 0 8px;
      color: transparent;
      position: relative;

      &:after {
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: ${theme.palette.custom.placeholder};
      }
    }

    &.active {
      color: ${theme.palette.custom.lightred};
      span {
        background: #fff;
        box-shadow: 0 0 0 2px ${theme.palette.custom.lightred};
        &:after {
          background: ${theme.palette.custom.lightred};
        }
      }
    }

    &.completed {
      color: ${theme.palette.custom.subtext};
      font-weight: 500;
      
      span {
        width: 28px;
        height: 28px;
        background: #4CAF50;
        box-shadow: none;
        border: none;
        color: transparent;
        font-size: 14px;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        content: '';
        text-indent: -9999px;
        overflow: hidden;
        
        &:after {
          display: none;
        }
        
        &:before {
          content: '';
          width: 12px;
          height: 12px;
          background-image: url(${smallTickIcon});
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          position: absolute;
          text-indent: 0;
        }
      }
    }
  }

  .brand-onboarding-step-indicator-divider {
    position: absolute;
    top: 34px;
    left: 40px;
    width: calc(100% - 95px);
    height: 0.5px;
    rotate: 0deg;
    margin: 0;
    z-index: 1;
    background-color: #ccc;
  }

  .brand-onboarding-step-indicator-divider.completed,
  .brand-onboarding-step-indicator-divider.completed.MuiDivider-root {
    background-color: #4CAF50 !important;
    background: #4CAF50 !important;
  }

  .brand-onboarding-step-indicator-divider.completed:nth-of-type(2),
  .brand-onboarding-step-indicator-divider.completed:nth-of-type(2).MuiDivider-root {
    background-color: #4CAF50 !important;
    background: #4CAF50 !important;
  }

  .brand-onboarding-step-indicator-divider.completed:nth-of-type(3),
  .brand-onboarding-step-indicator-divider.completed:nth-of-type(3).MuiDivider-root {
    background-color: #4CAF50 !important;
    background: #4CAF50 !important;
  }
}
@media (max-width: 600px) {

.circle-cross-icon-container {
  top: 10px;
  right: 12px;
}
}
`
);

export default StyledBrandOnboarding;

