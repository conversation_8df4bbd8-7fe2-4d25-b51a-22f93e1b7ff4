/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useMemo, useEffect } from "react";
import StyledBrandDetails from "./StyledBrandDetails";
import { Box, Button, Typography, InputAdornment } from "@mui/material";
import CustomTextField from "../../components/common/CustomTextField";
import CustomSelect from "../../components/common/CustomSelect";
import OTPVerificationModal from "../../components/common/OTPVerificationModal";
import { getCountries, getCountryCallingCode } from "libphonenumber-js";
import { countries } from "countries-list";
import { Controller } from "react-hook-form";
import { fetchVerifyOtp, fetchVerifyTestOtp } from "../../store/reducers/PartnerOnboarding";
import { useDispatch } from "react-redux";
import { ToastNotifyError, ToastNotifySuccess } from "../../components/Toast/ToastNotify";
import ResponsiveTooltipPopover from '../../components/common/ResponsiveTooltipPopover';


const BrandDetails = ({ onNext, errors, control, handleSubmit, trigger, existingData, reset }) => {
  const [countryCode, setCountryCode] = useState(existingData?.countryCode || "+91");
  const [phoneNumber, setPhoneNumber] = useState(existingData?.phoneNumber || "");
  const [email, setEmail] = useState(existingData?.email || "");
  const [websiteUrl, setWebsiteUrl] = useState(existingData?.websiteUrl || "");
  const [isOTPModalOpen, setIsOTPModalOpen] = useState(false);
  const [isEmailOTPModalOpen, setIsEmailOTPModalOpen] = useState(false);
  const [mobileOtpVerified, setMobileOtpVerified] = useState(existingData?.mobileOtpVerified || false);
  const [emailOtpVerified, setEmailOtpVerified] = useState(existingData?.emailOtpVerified || false);
  const dispatch = useDispatch();
  const [mobileOtpVerifiedStatus, setMobileOtpVerifiedStatus] = useState(false);
  const [emailOtpVerifiedStatus, setEmailOtpVerifiedStatus] = useState(false);



  useEffect(() => {
    if (existingData && Object.keys(existingData).length > 0) {
      if (existingData.countryCode) setCountryCode(existingData.countryCode);
      if (existingData.phoneNumber) setPhoneNumber(existingData.phoneNumber);
      if (existingData.email) setEmail(existingData.email);
      if (existingData.websiteUrl) setWebsiteUrl(existingData.websiteUrl);
      if (existingData.mobileOtpVerified !== undefined) setMobileOtpVerified(existingData.mobileOtpVerified);
      if (existingData.emailOtpVerified !== undefined) setEmailOtpVerified(existingData.emailOtpVerified);
    }
  }, []);

  const countryCodeOptions = useMemo(() => {
    const codes = getCountries()
      .map((country) => {
        const code = getCountryCallingCode(country);
        return {
          value: `+${code}`,
          label: `+${code}`,
        };
      })
      .sort((a, b) => a.value.localeCompare(b.value));

    // Remove duplicates based on value
    const uniqueCodes = codes.filter(
      (code, index, self) =>
        index === self.findIndex((c) => c.value === code.value)
    );

    return uniqueCodes;
  }, []);

  const countryOptions = useMemo(() => {
    return Object.entries(countries)
      .map(([code, country]) => ({
        value: country.name,
        label: country.name,
      }))
      .sort((a, b) => a.label.localeCompare(b.label));
  }, []);

  const handleVerifyOTP = async (otp) => {
    const queryParams = `?source=PARTNER_REGISTRATION&otp=${otp}&phone_number=${encodeURIComponent(countryCode + phoneNumber)}`;
    try {
      const response = await dispatch(fetchVerifyTestOtp(queryParams)).then(
        (res) => {
          return res;
        }
      );
      const { status, message, data } = response?.payload?.data;

      if (status === "success") {
        if (data?.is_valid) {
          setMobileOtpVerified(true);
          setIsOTPModalOpen(false);
        } else {
          ToastNotifyError("Please enter correct otp", "error");
        }
      } else {
        ToastNotifyError(message, "error");
      }
    } catch (error) {
      console.error("error", error);
    }
  };
  const handleVerifyEmailOTP = async (otp) => {
    const queryParams = `?source=PARTNER_REGISTRATION&otp=${otp}&email=${encodeURIComponent(email)}`
    try {
      const response = await dispatch(fetchVerifyTestOtp(queryParams));
      const { status, message, data } = response?.payload?.data;

      if (status === "success") {
        if (data?.is_valid) {
          setEmailOtpVerified(true);
          setIsEmailOTPModalOpen(false);
        } else {
          ToastNotifyError("Please enter correct otp", "error");
        }
      } else {
        ToastNotifyError(message, "error");
      }
    } catch (error) {
      console.error("error", error);
    }
  };
  const handleMobileGetOTP = async () => {
    const isValid = await trigger('phoneNumber');
    const isValidCode = await trigger('countryCode');
    if (!isValid || !isValidCode) return;

    const numberWithCode = countryCode + phoneNumber;
    const dataToSend = {
      source: "PARTNER_REGISTRATION",
      phone_number: numberWithCode,
    }
    try {
      const response = await dispatch(fetchVerifyOtp(dataToSend));
      const { status, message } = response?.payload?.data;
      setMobileOtpVerifiedStatus(status);
      if (status) {
        ToastNotifySuccess("OTP sent successfully", "success");
        setIsOTPModalOpen(true);

      } else {
        ToastNotifyError(message, "error");
      }
    } catch (error) {
      console.error("error", error);
    }
  };
  const handleEmailGetOTP = async () => {
    const isValid = await trigger('email');
    if (!isValid) return;

    const dataToSend = {
      source: "PARTNER_REGISTRATION",
      email: email,
    }
    try {
      const response = await dispatch(fetchVerifyOtp(dataToSend));
      const { status, message } = response?.payload?.data;
      setEmailOtpVerifiedStatus(status);
      if (status) {
        setIsEmailOTPModalOpen(true);
        ToastNotifySuccess("OTP sent successfully", "success");
      } else {
        ToastNotifyError(message, "error");
      }
    } catch (error) {
      console.error("error", error);
    }
  };

  const getOTPAdornment = (
    <InputAdornment
      position='end'
      sx={{
        color: "#DB0000",
      }}
      onClick={mobileOtpVerified ? () => { } : handleMobileGetOTP}
    >
      {mobileOtpVerified ? <Typography className='get-otp-verified'>Verified</Typography> : <Typography className='get-otp-text' sx={{ cursor: "pointer" }}>Get OTP</Typography>}
    </InputAdornment>
  );
  const getMailOTPAdornment = (
    <InputAdornment
      position='end'
      sx={{
        color: "#DB0000",
      }}
      onClick={emailOtpVerified ? () => { } : handleEmailGetOTP}
    >
      {emailOtpVerified ? <Typography className='get-otp-verified'>Verified</Typography> : <Typography className='get-otp-text' sx={{ cursor: "pointer" }}>Get OTP</Typography>}
    </InputAdornment>
  );

  const onSubmit = (data) => {
    const completeFormData = {
      ...data,
      countryCode,
      phoneNumber,
      email,
      websiteUrl,
      mobileOtpVerified,
      emailOtpVerified,
    };
    onNext(completeFormData);
  };

  return (
    <StyledBrandDetails>
      <Box className="brand-details-container">
        <Box className="brand-details-header">
          <Typography className="brand-details-title">
            Tell us about you
          </Typography>
          <Typography className="brand-details-subtitle">
            Let's start with the basics to get you set up on Zuumm.
          </Typography>
        </Box>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Box className="brand-details-form">
            <Box className="brand-details-form-row">
              <Box className="text-field-container">
                <Controller
                  control={control}
                  name="brandName"
                  render={({ field }) => (
                    <CustomTextField
                      label="Business /Entity Name"
                      // value={EntityName}
                      required={true}
                      // onChange={(e) => setEntityName(e.target.value)}
                      {...field}
                      error={!!errors.brandName}
                      helperText={errors.brandName?.message}
                      endAdornment={
                        <ResponsiveTooltipPopover title="Don't have a business brand? Just enter your full name" />
                      }
                    />
                  )}
                />
              </Box>
              <Box className="text-field-container">
                <Controller
                  control={control}
                  name="contactPersonName"
                  render={({ field }) => (
                    <CustomTextField
                      label="Contact Name"
                      // value={contactPersonName}
                      required={true}
                      // onChange={(e) => setContactPersonName(e.target.value)}
                      {...field}
                      error={!!errors.contactPersonName}
                      helperText={errors.contactPersonName?.message}
                    />
                  )}
                />
              </Box>
            </Box>
            <Box className="brand-details-form-row">
              <Box className="text-field-container">
                <Controller
                  control={control}
                  name="entityType"
                  render={({ field }) => (
                    <CustomSelect
                      label="Entity Type"
                      {...field}
                      options={[
                        { value: "TA", label: "Travel Agent/Tour Operators" },
                        {
                          value: "RP",
                          label: "Retail Chains and Loyalty Platforms",
                        },
                        { value: "EI", label: "Educational Institutions" },
                        {
                          value: "BP",
                          label: "Banks & Financial Institutions",
                        },
                        { value: "IC", label: "Insurance Companies" },
                        {
                          value: "RSP",
                          label: "Religious and Spiritual Organizations",
                        },
                        { value: "HEBP", label: "HR/Employee Benefit Portal" },
                        { value: "ECP", label: "E-commerce Platforms" },
                        { value: "FG", label: "Freelancers & Gig-workers" },
                        {
                          value: "SBL",
                          label: "Small Businesses & Local Shops",
                        },
                        { value: "WCC", label: "Wellness Centres & Clinics" },
                        {
                          value: "TO",
                          label: "Transport Operators (Rail, Bus, Cab)",
                        },
                      ]}
                      required
                      error={!!errors.entityType}
                      helperText={errors.entityType?.message}
                      customHeight="68px"
                      customMarginBottom="16px"
                    />
                  )}
                />
              </Box>
              <Box className="text-field-container2">
                <Box className="text-field-container">
                  <Controller
                    control={control}
                    name="country"
                    render={({ field }) => (
                      <CustomSelect
                        label="Country"
                        {...field}
                        // value={country}
                        // onChange={(e) => setCountry(e.target.value)}
                        options={countryOptions}
                        required
                        error={!!errors.country}
                        helperText={errors.country?.message}
                        customHeight="68px"
                        customMarginBottom="16px"
                      />
                    )}
                  />
                </Box>
                <Box className="text-field-container">
                  <Controller
                    control={control}
                    name="city"
                    render={({ field }) => (
                      <CustomTextField
                        label="City"
                        {...field}
                        // value={city}
                        required={true}
                        // onChange={(e) => setCity(e.target.value)}
                        error={!!errors.city}
                        helperText={errors.city?.message}
                      />
                    )}
                  />
                </Box>
              </Box>
            </Box>
            <Box className="brand-details-form-row">
              <Box className="text-field-container">
                <Box sx={{ display: "flex", gap: "12px" }}>
                  <Box sx={{ width: "80px" }}>
                    <Controller
                      control={control}
                      name="countryCode"
                      render={({ field }) => (
                        <CustomSelect
                          {...field}
                          label="Code"
                          value={field.value || countryCode}
                          onChange={async (e) => {
                            setCountryCode(e.target.value);
                            field.onChange(e.target.value);
                            if (e.target.value) {
                              await trigger("countryCode");
                            }
                          }}
                          disabled={mobileOtpVerified}
                          options={countryCodeOptions}
                          required
                          error={!!errors.countryCode}
                          helperText={errors.countryCode?.message}
                          customHeight="68px"
                          customMarginBottom="16px"
                        />
                      )}
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Controller
                      control={control}
                      name="phoneNumber"
                      render={({ field }) => (
                        <CustomTextField
                          disabled={mobileOtpVerified}
                          label="Phone Number"
                          {...field}
                          required={true}
                          onChange={async (e) => {
                            setPhoneNumber(e.target.value);
                            field.onChange(e.target.value);
                            // Trigger validation after value change
                            if (e.target.value) {
                              await trigger("phoneNumber");
                            }
                          }}
                          type="tel"
                          inputProps={{
                            maxLength: 10,
                            pattern: "[0-9]*",
                          }}
                          endAdornment={getOTPAdornment}
                          error={!!errors.phoneNumber}
                          helperText={errors.phoneNumber?.message}
                        />
                      )}
                    />
                  </Box>
                </Box>
              </Box>
              <Box className="text-field-container">
                <Controller
                  control={control}
                  name="email"
                  render={({ field }) => (
                    <CustomTextField
                      label="Email ID"
                      disabled={emailOtpVerified}
                      {...field}
                      required={true}
                      onChange={async (e) => {
                        setEmail(e.target.value);
                        field.onChange(e.target.value);
                      }}
                      endAdornment={getMailOTPAdornment}
                      error={!!errors.email}
                      helperText={errors.email?.message}
                    />
                  )}
                />
              </Box>
            </Box>
            <Box className="brand-details-form-row">
              <Box className="text-field-container">
                <Controller
                  control={control}
                  name="websiteUrl"
                  render={({ field }) => (
                    <CustomTextField
                      {...field}
                      label="Organisation Website"
                      value={websiteUrl}
                      onChange={(e) => setWebsiteUrl(e.target.value)}
                      error={!!errors.websiteUrl}
                      helperText={errors.websiteUrl?.message}
                    />
                  )}
                />
              </Box>
            </Box>
            <Box className="next-button-container">
              <Button
                className="next-button"
                disabled={!mobileOtpVerified || !emailOtpVerified}
                type="submit"
              >
                Next
              </Button>
            </Box>
          </Box>
        </form>
      </Box>
      <OTPVerificationModal
        open={isEmailOTPModalOpen}
        onClose={() => setIsEmailOTPModalOpen(false)}
        phoneNumber={email}
        onVerify={handleVerifyEmailOTP}
        handleResendOTP={handleEmailGetOTP}
        emailOtpVerified={emailOtpVerified}
        emailOtpVerifiedStatus={emailOtpVerifiedStatus}
      />
      <OTPVerificationModal
        open={isOTPModalOpen}
        onClose={() => setIsOTPModalOpen(false)}
        phoneNumber={countryCode + phoneNumber}
        onVerify={handleVerifyOTP}
        handleResendOTP={handleMobileGetOTP}
        mobileOtpVerified={mobileOtpVerified}
        mobileOtpVerifiedStatus={mobileOtpVerifiedStatus}
      />
    </StyledBrandDetails>
  );
};

export default BrandDetails;
