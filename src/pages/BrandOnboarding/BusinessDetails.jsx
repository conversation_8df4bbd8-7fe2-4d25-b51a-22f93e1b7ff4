/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect } from "react";
import StyledBusinessDetails from "./StyledBusinessDetails";
import { Box, Button, Typography } from "@mui/material";
import CustomSelect from "../../components/common/CustomSelect";
import CustomTextField from "../../components/common/CustomTextField";
import { Controller } from "react-hook-form";

const BusinessDetails = ({ onNext, onPrevious, control, errors, handleSubmit, existingData, setValue }) => {

  useEffect(() => {
    if (existingData && Object.keys(existingData).length > 0) {
      if (existingData.gstin) setValue('gstin', existingData.gstin);
      if (existingData.companyRegistrationNo) setValue('companyRegistrationNo', existingData.companyRegistrationNo);
      if (existingData.monthlyTransactionVolume) setValue('monthlyTransactionVolume', existingData.monthlyTransactionVolume);
    }
  }, [existingData]);

  const onSubmit = (data) => {
    onNext(data);
  }

  return (
    <StyledBusinessDetails>
      <Box className='business-details-container'>
        <Box className='business-details-header'>
          <Typography className='business-details-title'>
            Tell Us Your Business Details
          </Typography>
          <Typography className='business-details-subtitle'>
            Let's start with the basics to get you set up on Zuumm.
          </Typography>
        </Box>
        <form onSubmit={handleSubmit(onSubmit)}>
        <Box className='business-details-form'>
          <Controller
            name="gstin"
            control={control}
            render={({ field }) => (
              <CustomTextField
                {...field}
                label='GSTIN/Tax ID'
                fullWidth
                error={!!errors.gstin}
                helperText={errors.gstin?.message}
            />
            )}
          />
          <Controller
            name="companyRegistrationNo"
            control={control}
            render={({ field }) => (
              <CustomTextField
                {...field}
                label='Company Registration No'
                fullWidth
                error={!!errors.companyRegistrationNo}
                helperText={errors.companyRegistrationNo?.message}
              />
            )}
          />
          <Controller
            name="monthlyTransactionVolume"
            control={control}
            render={({ field }) => (
              <CustomSelect
                {...field}
            label='Monthly Transaction Volume'
            required={true}
                options={[
                  { value: "<1L", label: "Less than 1L" },
                  { value: "1-5L", label: "Between 1L to 5L" },
                  { value: "5-20L", label: "Between 5L to 20L" },
                  { value: ">20L", label: "Greater than 20L" },
                ]}
                fullWidth
                error={!!errors.monthlyTransactionVolume}
                helperText={errors.monthlyTransactionVolume?.message}
                customHeight='68px'
                customMarginBottom='16px'
              />
            )}
          />

          <Box className='business-btns'>
            <Button
              variant='outlined'
              color='primary'
              className='business-btn-previous'
              onClick={onPrevious}
            >
              Previous
            </Button>
            <Button
              type="submit"
              variant='contained'
              color='primary'
                className='business-btn-next'
              // onClick={handleSubmit(onNext)}
            >
              Next
            </Button>
          </Box>
        </Box>
        </form>
      </Box>
    </StyledBusinessDetails>
  );
};

export default BusinessDetails;
