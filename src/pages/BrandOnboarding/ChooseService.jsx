import React, { useState } from "react";
import StyledChooseService from "./StyledChooseService";
import {
  Box,
  Typography,
  Radio,
  Checkbox,
  FormControlLabel,
  Button,
} from "@mui/material";

const ChooseService = ({ onSubmit, onPrevious, existingData, formData }) => {
  const [selectedPackage, setSelectedPackage] = useState(existingData?.selectedPackage || "Sell ZUUMM packages");
  const [termsAccepted, setTermsAccepted] = useState(existingData?.termsAccepted || false);
  const [keepPosted, setKeepPosted] = useState(existingData?.keepPosted || false);


  const handleSubmit = () => {
    const formData = {
      selectedPackage,
      termsAccepted,
      keepPosted
    };
    onSubmit(formData);
  };

  return (
    <StyledChooseService>
      <Box className="choose-service-container">
        <Box className="choose-service-header">
          <Typography className="choose-service-title">
            Tell Us Your Brand Details
          </Typography>
          <Typography className="choose-service-subtitle">
            Let's start with the basics to get you set up on Zuumm.
          </Typography>
        </Box>

        <Box className="package-selection">
          <Typography className="section-title">Choose Package</Typography>

          <Box className="radio-group">
            <Box className="radio-group-item">
              <FormControlLabel
                value="Sell ZUUMM packages"
                control={
                  <Radio
                    checked={selectedPackage === "Sell ZUUMM packages"}
                    onChange={(e) => setSelectedPackage(e.target.value)}
                    className="custom-radio"
                    sx={{
                      padding: "4px",
                      "& .MuiSvgIcon-root": {
                        fontSize: "18px",
                      },
                    }}
                  />
                }
                label="Sell ZUUMM packages"
                className="radio-label"
              />
            </Box>
            {formData?.brandDetails?.entityType === "TA" && (
              <Box className="radio-group-item">
                <FormControlLabel
                  value="Sell your own packages"
                  control={
                    <Radio
                      checked={selectedPackage === "Sell your own packages"}
                      onChange={(e) => setSelectedPackage(e.target.value)}
                      className="custom-radio"
                      sx={{
                        padding: "4px",
                        "& .MuiSvgIcon-root": {
                          fontSize: "18px",
                        },
                      }}
                    />
                  }
                  label="Sell your own packages"
                  className="radio-label"
                />
              </Box>
            )}
          </Box>

          <Box className="checkbox-group">
            <FormControlLabel
              control={
                <Checkbox
                  checked={termsAccepted}
                  onChange={(e) => setTermsAccepted(e.target.checked)}
                  className="custom-checkbox"
                  sx={{
                    padding: "1px",
                    "& .MuiSvgIcon-root": {
                      fontSize: "18px",
                    },
                  }}
                />
              }
              label={
                <Typography className="checkbox-label">
                  I agree to the{" "}
                  <a href="/terms" className="terms-link" target="_blank">
                    Terms & Conditions
                  </a>
                </Typography>
              }
              sx={{
                marginLeft: "0",
                "& .MuiFormControlLabel-label": {
                  marginLeft: "8px",
                },
              }}
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={keepPosted}
                  onChange={(e) => setKeepPosted(e.target.checked)}
                  className="custom-checkbox"
                  sx={{
                    padding: "1px",
                    "& .MuiSvgIcon-root": {
                      fontSize: "18px",
                    },
                  }}
                />
              }
              label="Keep me posted with updates"
              className="checkbox-label"
            />
          </Box>
          <Box className="choose-service-btns">
            <Button
              variant="outlined"
              color="primary"
              className="choose-service-btn-previous"
              onClick={onPrevious}
              type="button"
            >
              Previous
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              color="primary"
              className="choose-service-btn-submit"
              disabled={!termsAccepted}
              sx={{
                opacity: termsAccepted ? 1 : 0.6,
                cursor: termsAccepted ? "pointer" : "not-allowed",
                "&.Mui-disabled": {
                  backgroundColor: "#808080",
                  color: "#ffffff",
                },
              }}
            >
              Submit
            </Button>
          </Box>
        </Box>
      </Box>
    </StyledChooseService>
  );
};

export default ChooseService;
