import { Box, CircularProgress, useMediaQuery } from "@mui/material";
import React, { useState, useEffect, useMemo, useLayoutEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { useTheme } from "@mui/material/styles";
import PackageDetailsLayout from "../../layout/PackageDetailsLayout";
import BreadCrumbsContainer from "../../components/PackageDetails/BreadCrumbsContainer";
import TitleContainer from "../../components/PackageDetails/TitleContainer";
import GalleryContainer from "../../components/PackageDetails/GalleryContainer";
import PackageInfoSection from "../../components/PackageDetails/PackageInfoSection";
import FeatureSection from "../../components/PackageDetails/FeatureSection";
import PackageDetailsSection from "../../components/PackageDetails/PackageDetailsSection";
import GalleryModal from "../../components/PackageDetails/GalleryModal";
import {
  fetchPackageBasicDetails,
  fetchPackageItineraryDetails,
  fetchPackageDestinationFaq,
  clearPackageDetails,
  generatePackagePdf,
} from "../../store/reducers/PackageDetails";
import { toast } from "react-toastify";
import generateCloudFrontURL from "../../utils";
import { formatIndianNumber } from "../../utils/helper";
import {
  ToastNotifyError,
  ToastNotifySuccess,
} from "../../components/Toast/ToastNotify";
import PackageDetailsMainSkeleton from "../../components/Skeleton/PackageDetails/PackageDetailsMainSkeleton";

const PackageDetails = () => {
  const dispatch = useDispatch();
  const { id: packageId } = useParams();
  const [isGalleryOpen, setIsGalleryOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isDownloading, setIsDownloading] = useState(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  useLayoutEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, []);

  // Get data from Redux store
  const { basicDetails, itineraryDetails, destinationFaq } = useSelector(
    (state) => state.packageDetails
  );

  // Fetch all required data when component mounts
  useEffect(() => {
    const fetchData = async () => {
      if (packageId) {
        setIsLoading(true);
        try {
          // Fetch package specific data
          await dispatch(fetchPackageBasicDetails(packageId)).unwrap();
          await dispatch(fetchPackageItineraryDetails(packageId)).unwrap();
          await dispatch(fetchPackageDestinationFaq(packageId)).unwrap();
        } catch (error) {
          console.error("Error fetching package details:", error);
        } finally {
          setTimeout(() => {
            setIsLoading(false);
          }, 1000);
        }
      }
    };

    fetchData();

    // Cleanup function to clear data when component unmounts
    return () => {
      dispatch(clearPackageDetails());
    };
  }, [dispatch, packageId]);

  // Extract data from API responses
  const rating = basicDetails?.data?.rating || 0;
  const title = basicDetails?.data?.title || "";

  const formattedImages = useMemo(
    () =>
      basicDetails?.data?.media?.map((img) => ({
        external_id: img.external_id,
        file: generateCloudFrontURL({
          bucket: process.env.REACT_APP_BUCKET_NAME,
          key: img.file,
          width: isMobile ? 190 : 760,
          height: isMobile ? 154 : 510,
          fit: "cover",
          actual: false,
        }),
      })),
    [basicDetails?.data?.media, isMobile]
  );

  const formattedActivities = useMemo(
    () =>
      basicDetails?.data?.activities?.map((activity) => ({
        external_id: activity.external_id,
        media:
          activity?.media?.length > 0
            ? generateCloudFrontURL({
                bucket: process.env.REACT_APP_BUCKET_NAME,
                key: activity.media[0].media,
                width: 200,
                height: 200,
                fit: "cover",
                actual: false,
              })
            : null,
        title: activity.title,
      })),
    [basicDetails?.data?.activities]
  );

  const formattedActualImages = useMemo(
    () =>
      basicDetails?.data?.media.map((img) => ({
        external_id: img.external_id,
        file: generateCloudFrontURL({
          bucket: process.env.REACT_APP_BUCKET_NAME,
          key: img.file,
          width: 760,
          height: 510,
          fit: "cover",
          actual: true,
        }),
      })),
    [basicDetails?.data?.media]
  );

  const basicData = useMemo(
    () => basicDetails?.data || {},
    [basicDetails?.data]
  );
  const itineraryData = useMemo(
    () => itineraryDetails?.data || {},
    [itineraryDetails?.data]
  );

  const infoItems = useMemo(() => {
    const items = [];

    items.push({
      type: "owner",
      title: "Owner",
      value: basicData?.owner || "-",
      icon: "/static/package/feature/owner.png",
    });

    items.push({
      type: "type",
      title: "Package Type",
      value: basicData?.type || "-",
      icon: "/static/package/feature/luggage.png",
    });

    items.push({
      type: "visa_type",
      title: "Visa Type",
      value: basicData?.visa_type?.join(", ") || "-",
      icon: "/static/package/feature/visa.png",
    });

    items.push({
      type: "best_time_to_visit_display",
      title: "Best Time to Visit",
      value: basicData?.best_time_to_visit_display || "-",
      icon: "/static/package/feature/visit.png",
    });

    items.push({
      type: "duration",
      title: "Duration",
      value: basicData?.duration || "-",
      icon: "/static/package/feature/duration.png",
    });

    items.push({
      type: "price_per_person",
      title: "Starting from",
      value:
        `${formatIndianNumber(basicData?.price_per_person || 0)}/per person` ||
        "-",
      icon: "/static/package/feature/price.png",
    });

    return items;
  }, [basicData]);

  // Handle share functionality
  const handleShare = async () => {
    const shareData = {
      title: basicData?.title || "Package Details",
      url: window.location.href,
    };

    try {
      if (navigator.share) {
        await navigator.share(shareData);
      } else {
        await navigator.clipboard.writeText(window.location.href);
        toast.success("Link copied to clipboard!");
      }
    } catch (error) {
      console.error("Error sharing:", error);
    }
  };

  // Handle download functionality
  const handleDownload = async () => {
    if (!packageId) {
      toast.error("Package ID not found");
      return;
    }

    try {
      setIsDownloading(true);
      const response = await dispatch(generatePackagePdf(packageId)).unwrap();

      if (response?.status === "success") {
        const { pdf_url = null } = response?.data || {};

        try {
          // Fetch the PDF as a blob
          const pdfResponse = await fetch(pdf_url);

          if (!pdfResponse.ok) {
            throw new Error(`Failed to fetch PDF: ${pdfResponse.status}`);
          }

          const pdfBlob = await pdfResponse.blob();

          // Create a blob URL
          const blobUrl = window.URL.createObjectURL(pdfBlob);

          // Create download link
          const link = document.createElement("a");
          link.href = blobUrl;
          link.download = `${basicData?.title || "package"}_details.pdf`;
          link.style.display = "none";

          // Append to body, click, and cleanup
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Clean up the blob URL
          window.URL.revokeObjectURL(blobUrl);

          ToastNotifySuccess("Package downloaded successfully!");
        } catch (error) {
          console.error("Error downloading PDF:", error);
          ToastNotifyError("Failed to download package. Please try again.");
        }
      } else {
        ToastNotifyError("Failed to generate PDF");
      }
    } catch (error) {
      console.error("Error downloading PDF:", error);
      toast.error("Failed to download PDF");
    } finally {
      setTimeout(() => {
        setIsDownloading(false);
      }, 1000);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <Box sx={{ ...(isLoading && { cursor: "wait" }) }}>
        <PackageDetailsLayout>
          <Box>
            <BreadCrumbsContainer />
          </Box>
          <PackageDetailsMainSkeleton />
        </PackageDetailsLayout>
      </Box>
    );
  }

  return (
    <PackageDetailsLayout>
      <Box>
        <BreadCrumbsContainer />
      </Box>
      <Box
        sx={{
          padding: { xs: "24px 20px", md: "24px 80px" },
        }}
      >
        <TitleContainer rating={rating} title={title} />
        <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
          <Box
            display="flex"
            justifyContent="flex-end"
            width="fit-content"
            ml={{ xs: "auto", md: 0 }}
            mt={{ xs: 2, md: 2.5 }}
            gap={2}
            sx={{
              fontSize: "16px",
              fontWeight: 500,
              color: "#1E3A8A",
            }}
          >
            <Box
              display="flex"
              alignItems="center"
              gap={1}
              sx={{ cursor: isDownloading ? "wait" : "pointer" }}
              onClick={() => !isDownloading && handleDownload()}
            >
              {isDownloading ? (
                <CircularProgress
                  sx={{ color: "#1E3A8A" }}
                  size={18}
                  thickness={4}
                />
              ) : (
                <>
                  <img
                    src={"/static/common/download.png"}
                    alt="Download"
                    width={24}
                    height={24}
                  />
                </>
              )}
              {isDownloading ? "Downloading..." : "Download"}
            </Box>
            <Box
              display="flex"
              alignItems="center"
              gap={1}
              sx={{ cursor: "pointer" }}
              onClick={handleShare}
            >
              <img
                src={"/static/common/share.png"}
                alt="Share"
                width={24}
                height={24}
              />
              Share
            </Box>
          </Box>
        </Box>
        <GalleryContainer
          gallery={formattedImages}
          setIsGalleryOpen={setIsGalleryOpen}
          basicData={basicData}
        />
        <PackageInfoSection infoItems={infoItems} basicData={basicData} />
        <FeatureSection basicData={basicData} />
        <PackageDetailsSection
          basicData={basicData}
          itineraryData={itineraryData}
          itineraryDetails={itineraryDetails}
          destinationFaq={destinationFaq}
          formattedActivities={formattedActivities}
        />
      </Box>

      {isGalleryOpen && (
        <GalleryModal
          open={isGalleryOpen}
          onClose={() => setIsGalleryOpen(false)}
          images={formattedActualImages}
        />
      )}
    </PackageDetailsLayout>
  );
};

export default PackageDetails;
