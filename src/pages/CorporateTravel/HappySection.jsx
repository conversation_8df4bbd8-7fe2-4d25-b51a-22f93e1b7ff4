import { Box } from "@mui/material";
import React from "react";

const HappySection = () => {
  const happyData = [
    {
      id: 1,
      heading: "For Employees",
      points: [
        "Unified platform for both personal and business travel",
        "AI assistant 'Nova' identifies lowest rates instantly",
        "Premium support for every journey",
        "Real-time reports visualizing policy compliance and savings",
      ],
    },
    {
      id: 2,
      heading: "For Travel Coordinators",
      points: [
        "Automate policy enforcement with AI",
        "Eliminate email dependency for bookings",
        "Manage all travel vendors through one relationship",
        "Streamline group booking processes",
      ],
    },
    {
      id: 3,
      heading: "For CFOs",
      points: [
        "Save 6 hours per booking and reduce costs by 20%",
        "Simplify vendor management with unified invoicing",
        "Automate GST input credit and compliance",
        "Seamlessly integrate with existing ERP, HRMS, and Payroll systems",
      ],
    },
  ];

  return (
    <Box
      sx={{
        pt: { xs: "30px", md: "30px" },
        position: "relative",
        px: { xs: 2.5, md: 10 },
        pb: { xs: "40px", md: "60px" },
        background: "#FFF9F9",
        boxSizing: "border-box",
      }}
    >
      <Box
        sx={{
          fontFamily: "Lora, serif",
          fontWeight: 700,
          fontSize: { xs: "24px", md: "32px" },
          lineHeight: { xs: "30px", md: "45px" },
          textAlign: "center",
          color: "#1E3A8A",
          mb: { xs: 3, md: 3 },
        }}
      >
        Keep Your Organization Happy
      </Box>

      <Box
        sx={{
          display: "flex",
          gap: { xs: "24px", md: "32px" },
          flexDirection: { xs: "column", md: "row" },
          justifyContent: "space-between",
        }}
      >
        {happyData.map((item) => (
          <Box
            sx={{
              width: { xs: "100%", md: "346px" },
              background: "#FFFFFF",
              borderRadius: "12px",
              p: { xs: "24px" },
              boxSizing: "border-box",
            }}
          >
            <Box>
              <Box
                sx={{
                  fontFamily: "Lora, serif",
                  fontWeight: 600,
                  fontSize: "20px",
                  lineHeight: "1.5",
                  color: "#1E3A8A",
                  mb: "16px",
                  pl: 2,
                }}
              >
                {item.heading}
              </Box>
              <Box
                component="ul"
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "1.5",
                  color: "#000000",
                  display: "flex",
                  flexDirection: "column",
                  gap: "16px",
                  pl: "20px",
                  boxSizing: "border-box",
                }}
              >
                {item.points.map((point, index) => (
                  <Box component="li" key={index}>
                    {point}
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default HappySection;
