import { Box } from "@mui/material";
import React from "react";
import SolutionCard from "./SolutionCard";

const SolutionSection = () => {
  const solutionData = [
    {
      id: 1,
      heading: "Corporate Spend Cards",
      description:
        "Enjoy 1% cashback on all travel purchases, creating immediate savings while maintaining complete spend visibility.",
      image: "/static/corporate/solution/corporateCard.png",
    },
    {
      id: 2,
      heading: "GST-Compliant Invoicing",
      description:
        "Automatic reconciliation saves hours of accounting work while ensuring full compliance with tax regulations.",
      image: "/static/corporate/solution/gst.png",
    },
    {
      id: 3,
      heading: "AI-Driven Policy Enforcement",
      description:
        "Our intelligent system automatically enforces travel policies without manual intervention, preventing out-of-policy bookings.",
      image: "/static/corporate/solution/ai.png",
    },
    {
      id: 4,
      heading: "Forex Cards",
      description:
        "Multi-currency travel cards with competitive exchange rates save on international transaction fees while providing real-time tracking of expenses.",
      image: "/static/corporate/solution/forexCard.png",
    },
    {
      id: 5,
      heading: "Amex Card with 51-Day Credit",
      description:
        "Extend your cash flow with our American Express corporate card offering an industry-leading 51-day credit period for maximum financial flexibility.",
      image: "/static/corporate/solution/amexCard.png",
    },
    {
      id: 6,
      heading: "Real-Time Analytics Dashboard",
      description:
        "Monitor travel spend patterns, track policy compliance, and identify cost-saving opportunities with customizable reports and interactive visualizations.",
      image: "/static/corporate/solution/analytic.png",
    },
    {
      id: 7,
      heading: "HRMS & Accounting Integrations",
      description:
        "Seamlessly connect with popular HRMS and accounting systems to automate expense allocation, employee data synchronization, and financial reporting without manual data entry.",
      image: "/static/corporate/solution/hrms.png",
    },
  ];

  return (
    <Box
      sx={{
        pt: { xs: "40px", md: "60px" },
        position: "relative",
        px: { xs: 2.5, md: 10 },
        pb: { xs: "40px", md: "60px" },
        background: "#FFF9F9",
        boxSizing: "border-box",
      }}
    >
      <Box
        sx={{
          fontFamily: "Lora, serif",
          fontWeight: 700,
          fontSize: { xs: "24px", md: "32px" },
          lineHeight: { xs: "30px", md: "45px" },
          textAlign: "center",
          color: "#1E3A8A",
          mb: { xs: 2, md: 2 },
        }}
      >
        Our FinTech-First Solution
      </Box>

      <Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 400,
            fontSize: { xs: "16px" },
            lineHeight: { xs: "24px" },
            textAlign: "center",
            color: "#333333",
            mb: { xs: 3, md: 5 },
            mx: "auto",
            maxWidth: "810px",
          }}
        >
          Zuumm revolutionizes corporate travel by
          <span
            style={{
              color: "#FF3951",
            }}
          >
            {" prioritizing financial efficiency "}
          </span>{" "}
          alongside traveler experience. Our platform was designed with CFOs and
          finance teams at the forefront, delivering tools that transform travel
          from a cost center to a strategic advantage.
        </Box>
      </Box>

      <Box
        sx={{
          maxWidth: "1200px",
          margin: "0 auto",
          display: "flex",
          flexWrap: "wrap",
          rowGap: { xs: 3, sm: 3, md: 4 },
          columnGap: { xs: 2, sm: 4.5, md: 7.5 },
          justifyContent: "center", // centers last row
          flexDirection: { xs: "column", sm: "row" },
        }}
      >
        {solutionData.map((item) => (
          <Box
            sx={{
              flex: "1 1 260px",
              maxWidth: {
                xs: "100%",
                sm: "calc(33.33% - 24px)",
                md: "calc(25% - 45px)",
              },
            }}
          >
            <SolutionCard key={item.id} solutionData={item} />
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default SolutionSection;
