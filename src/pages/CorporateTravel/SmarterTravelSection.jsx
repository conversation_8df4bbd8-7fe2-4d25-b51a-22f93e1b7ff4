import { Box, Button } from "@mui/material";
import React from "react";

const SmarterTravelSection = () => {
  return (
    <Box
      sx={{
        position: "relative",
        px: { xs: 2.5, md: 10 },
        py: { xs: "30px", md: "30px" },
        background: "#FFFFFF",
        boxSizing: "border-box",
      }}
    >
      <Box
        sx={{
          boxSizing: "border-box",
          backgroundImage: "url('/static/corporate/bgSmarterTravel.png')",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
          borderRadius: "16px",
          p: { xs: "24px 20px", md: "48px 24px" },
          minHeight: "274px",
          position: "relative",
        }}
      >
        <Box
          component="img"
          src="/static/corporate/robotTravel.png"
          sx={{
            zIndex: 0,
            display: { xs: "none", md: "block" },
            position: "absolute",
            bottom: "4px",
            right: "28px",
            width: "198px",
            height: "198px",
            objectFit: "cover",
          }}
        />
        <Box
          sx={{
            fontFamily: "Lora, serif",
            fontWeight: 700,
            fontSize: "24px",
            lineHeight: "40px",
            textAlign: "center",
            color: "#FF3951",
            mb: { xs: 3, md: 3.5 },
          }}
        >
          Say goodbye to complexity. Say hello to smarter travel.
        </Box>
        <Box sx={{ display: "flex", justifyContent: "center" }}>
          <Button
            variant="contained"
            sx={{
              background: "linear-gradient(90deg, #FF3951 0%, #FF3951 100%)",
              color: "#FFFFFF",
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "16px",
              minWidth: "170px",
              px: 5,
              py: "8px",
              borderRadius: "8px",
              transition: "all 0.3s ease",
              textTransform: "none",
              "&:hover": {
                background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
              },
            }}
            onClick={() => {
              window.open(
                "https://bookings.incbuddy.in/company/signup",
                "_blank",
                "noopener,noreferrer"
              );
            }}
          >
            Sign-up
          </Button>
        </Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "16px",
            lineHeight: "24px",
            textAlign: "center",
            color: "#333333",
            mt: { xs: 3, md: 5 },
          }}
        >
          Contact us directly at{" "}
          <a
            href="mailto:<EMAIL>"
            style={{
              color: "#0083E7",
              textDecoration: "none",
            }}
          >
            <EMAIL>
          </a>{" "}
          for a personalized consultation.
        </Box>
      </Box>
    </Box>
  );
};

export default SmarterTravelSection;
