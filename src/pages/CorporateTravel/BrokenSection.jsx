import { Box } from "@mui/material";
import React from "react";
import BrokenCard from "./BrokenCard";

const BrokenSection = () => {
  const brokenData = [
    {
      id: 1,
      heading: "Complex Approval process",
      description:
        "Scattered & manual approval processes create delays and frustration, reducing productivity and increasing administrative overhead.",
      image: "/static/corporate/broken/complex.png",
    },
    {
      id: 2,
      heading: "Uncontrolled Expenses",
      description:
        "Decentralized data makes tracking travel expenses nearly impossible, leaving finance teams struggling to maintain budget control.",
      image: "/static/corporate/broken/expense.png",
    },
    {
      id: 3,
      heading: "Fragmented Suppliers",
      description:
        "Employees spend too much time searching across multiple sites, or contacting multiple vendors.",
      image: "/static/corporate/broken/supplier.png",
    },
    {
      id: 4,
      heading: "Compliance Nightmares",
      description:
        "Without automation, GST reconciliation becomes a manual burden, creating tax compliance risks and wasting accounting resources.",
      image: "/static/corporate/broken/compliance.png",
    },
  ];

  return (
    <Box
      sx={{
        pt: { xs: "40px", md: "60px" },
        position: "relative",
        px: { xs: 2.5, md: 10 },
        pb: { xs: "40px", md: "60px" },
        background: "#FFF9F9",
        boxSizing: "border-box",
      }}
    >
      <Box
        sx={{
          fontFamily: "Lora, serif",
          fontWeight: 700,
          fontSize: { xs: "24px", md: "32px" },
          lineHeight: { xs: "30px", md: "45px" },
          textAlign: "center",
          color: "#1E3A8A",
          mb: { xs: 3, md: 5 },
        }}
      >
        What's Broken With Corporate Travel?
      </Box>

      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: { xs: "1fr", md: "1fr 1fr" },
          rowGap: { xs: "24px", md: "32px" },
          columnGap: { xs: "24px", md: "16px" },
          maxWidth: "1440px",
          mx: "auto",
        }}
      >
        {brokenData.map((item) => (
          <BrokenCard key={item.id} brokenData={item} />
        ))}
      </Box>
    </Box>
  );
};

export default BrokenSection;
