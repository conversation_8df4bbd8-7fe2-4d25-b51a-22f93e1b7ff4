import { Box } from "@mui/material";
import React from "react";

const GetStartedSection = () => {
  const getStartedValues = [
    {
      icon: "/static/corporate/getStarted/time.png",
      title: "5-Minute Setup",
      description: "Quick onboarding process with minimal IT involvement",
    },
    {
      icon: "/static/corporate/getStarted/fee.png",
      title: "No Hidden Fees",
      description: "Transparent pricing with no surprise charges",
    },
    {
      icon: "/static/corporate/getStarted/support.png",
      title: "24/7 Expert Help",
      description: "Dedicated implementation team and ongoing support",
    },
  ];

  return (
    <Box
      sx={{
        py: { xs: "40px", md: "60px" },
        position: "relative",
        px: { xs: 2.5, md: 10 },
        background:
          "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 125.89%)",
        boxSizing: "border-box",
      }}
    >
      <Box
        sx={{
          fontFamily: "Lora, serif",
          fontWeight: 700,
          fontSize: { xs: "24px", md: "32px" },
          lineHeight: { xs: "30px", md: "45px" },
          textAlign: "center",
          color: "#1E3A8A",
          mb: { xs: 3, md: 5 },
        }}
      >
        Get Started Today
      </Box>
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: { xs: "1fr", md: "repeat(3, 1fr)" },
          gap: { xs: 3.5, md: 3.5 },
        }}
      >
        {getStartedValues.map((value, index) => (
          <Box
            sx={{
              background: "#FFFFFF",
              borderRadius: "12px",
              padding: "24px",
              boxSizing: "border-box",
              boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
            }}
            key={index}
          >
            <Box
              component="img"
              src={value.icon}
              alt={value.title}
              sx={{
                width: { xs: "44px", md: "44px" },
                height: { xs: "44px", md: "44px" },
                marginBottom: { xs: "16px", md: "16px" },
              }}
            />
            <Box
              sx={{
                fontFamily: "Lora, serif",
                fontWeight: 600,
                fontSize: "20px",
                lineHeight: "24px",
                color: "#1E3A8A",
                mb: { xs: 1.5, md: 1.5 },
              }}
            >
              {value.title}
            </Box>
            <Box
              sx={{
                fontFamily: "Roboto, sans-serif",
                fontWeight: 400,
                fontSize: "14px",
                lineHeight: "20px",
                color: "#333333",
              }}
            >
              {value.description}
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default GetStartedSection;
