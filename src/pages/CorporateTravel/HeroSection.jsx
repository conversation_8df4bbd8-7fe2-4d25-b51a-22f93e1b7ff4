import { Box } from "@mui/material";
import React from "react";
import PrimaryButton from "../../components/common/Button/PrimaryButton";
import SecondaryButton from "../../components/common/Button/SecondaryButton";
// Fix the import path to the correct location
import corporateVideoUrl from "../../assets/video/corporate.mp4";

const HeroSection = () => {
  return (
    <Box
      sx={{
        pt: { xs: "90px", md: "140px" },
        position: "relative",
        px: { xs: 2.5, md: 10 },
        minHeight: { xs: "100%", md: "937px" },
        pb: 7,
        background: "#FFFFFF",
        boxSizing: "border-box",
      }}
    >
      <Box sx={{ position: "relative" }}>
        <Box
          component="img"
          src="/static/corporate/world.png"
          alt="world"
          sx={{
            width: "100%",
            maxWidth: "1000px",
            height: "100%",
            maxHeight: "740px",
            position: "absolute",
            bottom: "0px",
            left: { xs: "0px", md: "0px" },
            zIndex: 0,
            opacity: 0.09,
          }}
        />
        <Box
          sx={{
            fontFamily: "Lora, serif",
            fontWeight: 500,
            fontSize: { xs: "32px", md: "56px" },
            lineHeight: { xs: "40px", md: "72px" },
            color: "#1E3A8A",
            textAlign: "center",
          }}
        >
          IncBuddy By Zuumm{" "}
          <span
            style={{
              background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              color: "transparent",
              display: "inline-block",
              position: "relative",
            }}
          >
            Corporate Travel
          </span>
        </Box>
        <Box
          sx={{
            mt: { xs: 2, md: 2 },
            fontFamily: "Lora, serif",
            fontWeight: 500,
            fontSize: { xs: "24px", md: "40px" },
            lineHeight: { xs: "30px", md: "50px" },
            color: "#1E3A8A",
            textAlign: "center",
          }}
        >
          The Smarter Way To Travel For Work
        </Box>
        <Box
          sx={{
            mt: { xs: 2, md: 2 },
            maxWidth: "1013px",
            mx: "auto",
            fontFamily: "Roboto",
            fontWeight: 400,
            fontSize: "16px",
            lineHeight: "1.6",
            textAlign: "center",
          }}
        >
          Manage every business trip, streamline approvals, and control travel
          spend - all from one{" "}
          <span style={{ color: "#FF3951" }}>{" intelligent platform "}</span>{" "}
          built for modern finance and travel teams. From flights and hotels to
          visa, forex, and cabs - everything your team needs, under one roof.
        </Box>
        <Box
          sx={{
            mt: { xs: 2, md: 2 },
            fontFamily: "Roboto",
            fontWeight: 600,
            fontSize: "20px",
            lineHeight: "1.6",
            textAlign: "center",
            color: "#333333",
          }}
        >
          To Experience Smarter Business Travel
        </Box>
        <Box
          sx={{
            mt: { xs: 3, md: 4 },
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            gap: { xs: "20px", md: "42px" },
            flexDirection: { xs: "column", md: "row" },
          }}
        >
          <SecondaryButton
            onClick={() => {
              window.open("https://bookings.incbuddy.in/login", "_blank");
            }}
          >
            Login
          </SecondaryButton>
          <PrimaryButton
            onClick={() => {
              window.open(
                "https://bookings.incbuddy.in/company/signup",
                "_blank"
              );
            }}
          >
            Sign-up
          </PrimaryButton>
        </Box>
        <Box
          sx={{
            mt: { xs: 3, md: 5 },
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Box
            component={"video"}
            src={corporateVideoUrl}
            autoPlay
            controls={true}
            loop
            muted
            playsInline
            sx={{
              width: "100%",
              maxWidth: "930px",
              height: "100%",
              // maxHeight: "360px",
              borderRadius: "8px",
              objectFit: "contain",
            }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default HeroSection;
