import { Box } from "@mui/material";
import React from "react";
import ServiceCard from "./ServiceCard";

const ServiceSection = () => {
  const serviceData = [
    {
      id: 1,
      heading: "Transportation",
      points: [
        "Flights across 300+ airlines",
        "Rail bookings domestic & international",
        "Cab integration with major providers",
        "Group & MICE booking capabilities",
      ],
      image: "/static/corporate/service/transport.png",
    },
    {
      id: 2,
      heading: "Support Services",
      points: [
        "Visa processing with 99.8% guarantee",
        "Forex card issuance",
        "Travel insurance",
        "Duty of care & risk management",
        "Concierge service to attend to Post booking complexities",
      ],
      image: "/static/corporate/service/support.png",
    },
    {
      id: 3,
      heading: "Accommodations",
      points: [
        "Access to 250,000+ hotels worldwide",
        "Exclusive corporate rates",
        "Long-stay options for projects",
        "Conference & meeting space booking",
      ],
      image: "/static/corporate/service/accommodation.png",
    },
  ];

  return (
    <Box
      sx={{
        pt: { xs: "40px", md: "60px" },
        position: "relative",
        px: { xs: 2.5, md: 10 },
        pb: { xs: "40px", md: "60px" },
        background: "#FFF9F9",
        boxSizing: "border-box",
      }}
    >
      <Box
        sx={{
          fontFamily: "Lora, serif",
          fontWeight: 700,
          fontSize: { xs: "24px", md: "32px" },
          lineHeight: { xs: "30px", md: "45px" },
          textAlign: "center",
          color: "#1E3A8A",
          mb: { xs: 2, md: 2 },
        }}
      >
        One Travel Platform. Infinite Services.
      </Box>
      <Box
        sx={{
          fontFamily: "Roboto",
          fontWeight: 400,
          fontSize: "16px",
          lineHeight: "1.5",
          textAlign: "center",
          color: "#333333",
          mb: { xs: 3, md: 3 },
        }}
      >
        One login. One invoice. One support line. Total control.
      </Box>

      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: { xs: "1fr", md: "repeat(3, 1fr)" },
          gap: { xs: 2 },
        }}
      >
        {serviceData.map((item) => (
          <ServiceCard key={item.id} serviceData={item} />
        ))}
      </Box>
    </Box>
  );
};

export default ServiceSection;
