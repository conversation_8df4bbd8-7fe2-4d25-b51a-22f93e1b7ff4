import React from "react";
import { Box } from "@mui/material";

const SolutionCard = ({ solutionData }) => {
  const imageUrl = solutionData.image;
  const heading = solutionData.heading;
  const description = solutionData.description;
  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        height: "100%",
        backgroundColor: "#fff",
        borderRadius: "12px",
        overflow: "hidden",
        boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
        flexDirection: { xs: "column" },
      }}
    >
      {/* Left side - Image */}
      <Box
        component="img"
        src={imageUrl}
        alt="Complex Approval Process"
        sx={{
          width: { xs: "100%" },
          height: { xs: "162px" },
          objectFit: "cover",
          flexShrink: 0,
        }}
      />

      {/* Right side - Content */}
      <Box
        sx={{
          padding: "24px 16px 24px 16px",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
        }}
      >
        {/* Heading */}
        <Box
          sx={{
            fontFamily: "Lora, serif",
            fontWeight: 600,
            fontSize: "18px",
            lineHeight: "27px",
            letterSpacing: "0%",
            verticalAlign: "middle",
            textTransform: "capitalize",
            color: "#333333",
            mb: 2,
          }}
        >
          {heading}
        </Box>

        {/* Description */}
        <Box
          sx={{
            fontFamily: "Roboto, sans-serif",
            fontWeight: 400,
            fontSize: "16px",
            lineHeight: "28px",
            letterSpacing: "0%",
            verticalAlign: "middle",
            color: "#333333",
          }}
        >
          {description}
        </Box>
      </Box>
    </Box>
  );
};

export default SolutionCard;
