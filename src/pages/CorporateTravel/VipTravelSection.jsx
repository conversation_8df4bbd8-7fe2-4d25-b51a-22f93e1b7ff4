import { Box } from "@mui/material";
import React from "react";
import BrokenCard from "./BrokenCard";

const VipTravelSection = () => {
  const vipTravelData = [
    {
      id: 1,
      heading: "Custom Booking Service",
      description:
        "Our travel experts handle all bookings, getting you the best rooms, seats, and lounge access based on what you like.",
      image: "/static/corporate/vip/service.png",
    },
    {
      id: 2,
      heading: "Smooth Airport Experience",
      description:
        "Skip the lines with fast check-in, security, private terminals, and luxury cars, making your journey hassle-free.",
      image: "/static/corporate/vip/airport.png",
    },
    {
      id: 3,
      heading: "Tailored Luxury, every step",
      description:
        "Our luxury travel experts curate every detail to match your preferences and elevate your journey.",
      image: "/static/corporate/vip/travel.png",
    },
    {
      id: 4,
      heading: "Quick Schedule Changes",
      description:
        "When plans change, we quickly adjust everything, handling all new bookings and cancellations within minutes.",
      image: "/static/corporate/vip/schedule.png",
    },
  ];

  return (
    <Box
      sx={{
        pt: { xs: "40px", md: "60px" },
        position: "relative",
        px: { xs: 2.5, md: 10 },
        pb: { xs: "40px", md: "60px" },
        background: "linear-gradient(90deg, #FEEBEE 0%, #F2EBFA 125.89%)",
        boxSizing: "border-box",
      }}
    >
      <Box
        sx={{
          fontFamily: "Lora, serif",
          fontWeight: 700,
          fontSize: { xs: "24px", md: "32px" },
          lineHeight: { xs: "30px", md: "45px" },
          textAlign: "center",
          color: "#1E3A8A",
          mb: { xs: 3, md: 3 },
        }}
      >
        VIP Travel For High-Value Executives
      </Box>
      <Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 400,
            fontSize: { xs: "16px" },
            lineHeight: { xs: "24px" },
            textAlign: "center",
            color: "#333333",
            mb: { xs: 3, md: 3 },
            mx: "auto",
            maxWidth: "698px",
          }}
        >
          Our specialized team anticipates your needs to elevate your travel.
          Top executives / CXOs demand impeccable service with zero tolerance
          for time wastage.
        </Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: { xs: "16px" },
            lineHeight: { xs: "24px" },
            textAlign: "center",
            color: "#333333",
            mb: { xs: 3, md: 5 },
            mx: "auto",
            maxWidth: "577px",
          }}
        >
          With years of helping important travelers, our team knows what you
          need before you ask4making travel smooth and easy, with no detail
          overlooked.
        </Box>
      </Box>

      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: { xs: "1fr", md: "1fr 1fr" },
          rowGap: { xs: "24px", md: "32px" },
          columnGap: { xs: "24px", md: "16px" },
          maxWidth: "1440px",
          mx: "auto",
        }}
      >
        {vipTravelData.map((item) => (
          <BrokenCard key={item.id} brokenData={item} />
        ))}
      </Box>
    </Box>
  );
};

export default VipTravelSection;
