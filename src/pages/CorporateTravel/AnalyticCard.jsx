import { Box } from "@mui/material";
import React from "react";

const AnalyticCard = ({ analyticData }) => {
  const { value, title, description } = analyticData;
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        textAlign: "center",
        p: { xs: 3, md: 4 },
      }}
    >
      {/* Percentage */}
      <Box
        sx={{
          color: "#FF3951",
          fontFamily: "Roboto",
          fontWeight: 500,
          fontSize: "40px",
          lineHeight: "100%",
          textAlign: "center",
          mb: 2,
        }}
      >
        {value}
      </Box>

      {/* Title */}
      <Box
        sx={{
          color: "#333333",
          fontFamily: "Roboto",
          fontWeight: 600,
          fontSize: "16px",
          lineHeight: "24px",
          textAlign: "center",
          mb: 2,
        }}
      >
        {title}
      </Box>

      {/* Description */}
      <Box
        sx={{
          color: "#333333",
          fontFamily: "Roboto",
          fontWeight: 400,
          fontSize: "16px",
          lineHeight: "24px",
          textAlign: "center",
          maxWidth: "600px",
        }}
      >
        {description}
      </Box>
    </Box>
  );
};

export default AnalyticCard;
