import React, { useEffect } from "react";
import { Box } from "@mui/material";

import HeroSection from "./HeroSection";
import BrokenSection from "./BrokenSection";
import BusinessSection from "./BusinessSection";
import SolutionSection from "./SolutionSection";
import VipTravelSection from "./VipTravelSection";
import AnalyticSection from "./AnalyticSection";
import HappySection from "./HappySection";
import ServiceSection from "./ServiceSection";
import GetStartedSection from "./GetStartedSection";
import SmarterTravelSection from "./SmarterTravelSection";

const CorporateTravel = () => {
  // scroll to top
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  return (
    <Box>
      <HeroSection />
      <BrokenSection />
      <BusinessSection />
      <SolutionSection />
      <VipTravelSection />
      <AnalyticSection />
      <HappySection />
      <ServiceSection />
      <GetStartedSection />
      <SmarterTravelSection />
    </Box>
  );
};

export default CorporateTravel;
