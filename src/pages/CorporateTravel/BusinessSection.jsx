import React from "react";
import { Box } from "@mui/material";

const BusinessSection = () => {
  const businessValues = [
    {
      icon: "/static/about/core/scale.png",
      title: "Comprehensive Network",
      description:
        "Access 300+ airlines and 250,000+ hotels with exclusive corporate rates unavailable through public booking channels.",
    },
    {
      icon: "/static/about/core/innovation.png",
      title: "Built-in Compliance",
      description:
        "Automated policy enforcement and approval workflows ensure spending stays within guidelines without manual oversight.",
    },
    {
      icon: "/static/about/core/ai.png",
      title: "Concierge Support",
      description:
        "24/7 multi-channel assistance via chat, call, or WhatsApp ensures travelers are never stranded without help.",
    },
    {
      icon: "/static/about/core/trust.png",
      title: "Unified Dashboard",
      description:
        "Comprehensive visibility into all travel activities, expenses, and policy compliance from a single intuitive interface.",
    },
    {
      icon: "/static/about/core/customer.png",
      title: "Luxury travel solutions crafted for the C-suite",
      description:
        "Comprehensive visibility into all travel activities, expenses, and policy compliance from a single intuitive interface.",
    },
  ];
  return (
    <Box
      sx={{
        pt: { xs: "40px", md: "60px" },
        position: "relative",
        px: { xs: 2.5, md: 10 },
        pb: { xs: "40px", md: "60px" },
        background: "#FFF9F9",
        boxSizing: "border-box",
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          mb: { xs: 2, md: 2 },
          gap: { xs: 1, md: 1.5 },
        }}
      >
        <Box
          sx={{
            fontFamily: "Lora, serif",
            fontWeight: 700,
            fontSize: { xs: "24px", md: "32px" },
            lineHeight: { xs: "30px", md: "45px" },
            textAlign: "center",
            color: "#1E3A8A",
          }}
        >
          Why Businesses Choose
        </Box>
        <Box
          component={"img"}
          src="/static/common/logo.png"
          sx={{
            width: "170px",
            height: "34px",
          }}
        />
      </Box>
      <Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 400,
            fontSize: { xs: "16px" },
            lineHeight: { xs: "24px" },
            textAlign: "center",
            color: "#333333",
            mb: { xs: 3, md: 5 },
            mx: "auto",
            maxWidth: "816px",
          }}
        >
          Zuumm delivers{" "}
          <span
            style={{
              background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
            }}
          >
            {" enterprise-grade travel management "}
          </span>{" "}
          with exclusive rates, automated compliance, round-the-clock support,
          and comprehensive analytics in one unified platform.
        </Box>
      </Box>
      <Box
        sx={{
          maxWidth: "904px",
          margin: "0 auto",
          display: "flex",
          flexWrap: "wrap",
          rowGap: { xs: 2, md: 2 },
          columnGap: { xs: 2, md: 2 },
          justifyContent: "center", // centers last row
          flexDirection: { xs: "column", md: "row" },
        }}
      >
        {businessValues.map((value, index) => (
          <Box
            sx={{
              flex: "1 1 307px",
              maxWidth: { xs: "100%", md: "calc(33.33% - 16px)" },
              background: "#FFFFFF",
              borderRadius: "12px",
              padding: "20px",
              boxSizing: "border-box",
              boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
            }}
            key={index}
          >
            <Box
              component="img"
              src={value.icon}
              alt={value.title}
              sx={{
                width: { xs: "44px", md: "44px" },
                height: { xs: "44px", md: "44px" },
                marginBottom: { xs: "16px", md: "16px" },
              }}
            />
            <Box
              sx={{
                fontFamily: "Lora, serif",
                fontWeight: 600,
                fontSize: "20px",
                lineHeight: "24px",
                color: "#1E3A8A",
                mb: { xs: 3, md: 0.5 },
              }}
            >
              {value.title}
            </Box>
            <Box
              sx={{
                fontFamily: "Roboto, sans-serif",
                fontWeight: 400,
                fontSize: "14px",
                lineHeight: "20px",
                color: "#333333",
              }}
            >
              {value.description}
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default BusinessSection;
