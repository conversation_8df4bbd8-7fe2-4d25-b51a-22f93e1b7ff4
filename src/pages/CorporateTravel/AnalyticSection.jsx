import { Box } from "@mui/material";
import React from "react";
import AnalyticCard from "./AnalyticCard";

const AnalyticSection = () => {
  const analyticData = [
    {
      id: 1,
      value: "20%",
      title: "Cost Reduction",
      description:
        "Average savings on total travel spend through policy enforcement, exclusive rates, and automated processes",
    },
    {
      id: 2,
      value: "6hrs",
      title: "Time Saved",
      description:
        "Per booking through elimination of manual processes and approval workflows",
    },
    {
      id: 3,
      value: "100%",
      title: "GST Recovery",
      description:
        "Maximize input tax credits with compliant documentation for every transaction",
    },
  ];
  return (
    <Box
      sx={{
        position: "relative",
        px: { xs: 2.5, md: 10 },
        py: { xs: "30px", md: "30px" },
        background: "#FFFFFF",
        boxSizing: "border-box",
      }}
    >
      <Box
        sx={{
          //   display: "flex",
          //   justifyContent: "center",
          //   alignItems: "center",
          boxSizing: "border-box",
          background: "#FFFFFF",
          boxShadow: "0px 12px 35px 0px #64748C1F",
          borderRadius: "12px",
          overflow: "hidden",
          width: "100%",
          height: "100%",
          p: { xs: "24px", md: "65px 84px" },
        }}
      >
        <Box
          sx={{
            fontFamily: "Lora, serif",
            fontWeight: 700,
            fontSize: { xs: "24px", md: "32px" },
            lineHeight: { xs: "30px", md: "45px" },
            textAlign: "center",
            color: "#1E3A8A",
            mb: { xs: 3, md: 5 },
          }}
        >
          The Numbers That Matter
        </Box>
        <Box>
          <Box
            sx={{
              fontFamily: "Roboto",
              fontWeight: 400,
              fontSize: { xs: "16px" },
              lineHeight: { xs: "24px" },
              textAlign: "center",
              color: "#333333",
              mb: { xs: 3 },
              mx: "auto",
              maxWidth: "711px",
            }}
          >
            Zuumm delivers measurable results that impact your bottom line while
            improving traveler satisfaction. Our clients consistently report
            significant improvements in both financial metrics and employee
            experience scores after implementation.
          </Box>
        </Box>
        <Box>
          <Box
            sx={{
              mx: "auto",
              maxWidth: "964px",
              boxSizing: "border-box",
              display: "grid",
              gridTemplateColumns: { xs: "1fr", md: "1fr 1fr 1fr" },
              rowGap: { xs: "24px", md: "32px" },
              columnGap: { xs: "24px", md: "16px" },
            }}
          >
            {analyticData.map((item) => (
              <AnalyticCard key={item.id} analyticData={item} />
            ))}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default AnalyticSection;
