import React from "react";
import { Box } from "@mui/material";

const ServiceCard = ({ serviceData }) => {
  const imageUrl = serviceData.image;
  const heading = serviceData.heading;
  const points = serviceData.points;

  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        height: "100%",
        backgroundColor: "#fff",
        borderRadius: "12px",
        overflow: "hidden",
        boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
        flexDirection: { xs: "column" },
      }}
    >
      {/* Left side - Image */}
      <Box
        component="img"
        src={imageUrl}
        alt="Complex Approval Process"
        sx={{
          width: { xs: "100%" },
          height: { xs: "162px", sm: "316px" },
          objectFit: "cover",
          flexShrink: 0,
          borderRadius: "12px",
        }}
      />

      {/* Right side - Content */}
      <Box
        sx={{
          padding: "24px 16px 24px 16px",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
        }}
      >
        <Box
          sx={{
            fontFamily: "Lora, serif",
            fontWeight: 600,
            fontSize: "20px",
            lineHeight: "1.5",
            color: "#1E3A8A",
            mb: "0px",
            pl: 2,
          }}
        >
          {heading}
        </Box>
        <Box
          component="ul"
          sx={{
            fontFamily: "Roboto",
            fontWeight: 400,
            fontSize: "16px",
            lineHeight: "1.5",
            color: "#333333",
            display: "flex",
            flexDirection: "column",
            gap: "16px",
            pl: "20px",
            boxSizing: "border-box",
          }}
        >
          {points.map((point, index) => (
            <Box component="li" key={index}>
              {point}
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default ServiceCard;
