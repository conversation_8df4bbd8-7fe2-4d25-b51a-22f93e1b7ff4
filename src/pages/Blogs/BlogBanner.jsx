import React, { useState } from "react";
import { Box, Typography } from "@mui/material";
import { useNavigate } from "react-router-dom";
import PrimaryButton from "../../components/common/Button/PrimaryButton";
import SecondaryButton from "../../components/common/Button/SecondaryButton";
import { useSelector } from "react-redux";
import OnboardingModal from "../../components/OnboardingModal";

const BlogBanner = () => {
  const navigate = useNavigate();
  const { userDetails } = useSelector((state) => state.authentication);
  const [openOnboardingModal, setOpenOnboardingModal] = useState(false);

  const handleOpenOnboardingModal = () => {
    setOpenOnboardingModal(true);
  };

  const handleCloseOnboardingModal = () => {
    setOpenOnboardingModal(false);
  };

  return (
    <Box
      sx={{
        width: "100%",
        height: "100%",
        minHeight: {
          xs: "fit-content",
          md: "520px",
        },
        position: "relative",
        display: "flex",
        justifyContent: "center",
        alignItems: {
          xs: "center",
          md: "flex-end",
        },
        px: {
          xs: "20px",
          md: "0",
        },
        boxSizing: "border-box",
      }}
    >
      <Box
        component={"img"}
        src={"/static/blogs/banner.png"}
        sx={{
          width: {
            xs: "100%",
            md: "calc(100% - 42px)",
          },
          height: "100%",
          objectFit: "cover",
          position: "absolute",
          top: 0,
          left: {
            xs: "0px",
            md: "21px",
          },
          right: {
            xs: "0px",
            md: "21px",
          },
          bottom: 0,
          zIndex: 0,
        }}
      />
      <Box
        sx={{
          pt: {
            xs: "68px",
            md: "0",
          },
          width: {
            xs: "100%",
            md: "680px",
          },
          height: "fit-content",
          mb: {
            xs: 1,
            md: 1,
          },
        }}
      >
        <Box>
          <Typography
            sx={{
              fontFamily: "Lora",
              fontSize: {
                xs: "40px",
                md: "56px",
              },
              fontWeight: 500,
              lineHeight: 1.3,
              textAlign: "center",
              color: "#1E3A8A",
              "& span": {
                textAlign: "center",
                background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                backgroundClip: "text",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                textTransform: "capitalize",
              },
            }}
          >
            One Backpack,
            <br />
            <span>A Thousand Stories</span>
          </Typography>
        </Box>
        <Box sx={{ mt: 2, mx: "auto", maxWidth: "580px" }}>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontSize: "16px",
              fontWeight: 400,
              lineHeight: 1.6,
              textAlign: "center",
              color: "#333333",
              letterSpacing: "0.5px",
            }}
          >
            Explore the world through raw reflections, hidden gems, or the
            fearless freedom of traveling alone.
          </Typography>
        </Box>
        <Box
          sx={{
            mt: { xs: 3, md: 4 },
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            gap: {
              xs: "20px",
              md: "42px",
            },
            flexDirection: { xs: "column", md: "row" },
          }}
        >
          <SecondaryButton
            onClick={() => {
              navigate("/explore");
            }}
          >
            Explore Zuumm
          </SecondaryButton>

          {userDetails?.user ? (
            <PrimaryButton
              onClick={() => {
                const visibility = process.env.REACT_APP_ENV === "production";
                if (!visibility) {
                  navigate("/ai-chat");
                } 
              }}
            >
              Plan With AI
            </PrimaryButton>
          ) : (
            <PrimaryButton onClick={handleOpenOnboardingModal}>
              Login/Sign-up
            </PrimaryButton>
          )}
        </Box>
      </Box>
      {openOnboardingModal && (
        <OnboardingModal
          open={openOnboardingModal}
          onClose={handleCloseOnboardingModal}
        />
      )}
    </Box>
  );
};

export default BlogBanner;
