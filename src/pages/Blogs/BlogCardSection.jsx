/* eslint-disable react-hooks/exhaustive-deps */
import React, {
  useState,
  useEffect,
  useLayoutEffect,
  useCallback,
} from "react";
import { Box, Checkbox, Typography } from "@mui/material";
import BlogCard from "./BlogCard";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import {
  fetchBlogPosts,
  fetchBlogTags,
} from "../../store/reducers/Blog/apiThunk";
import { ToastNotifyError } from "../../components/Toast/ToastNotify";
import generateCloudFrontURL from "../../utils";
import { BlogGridSkeleton, FilterSkeleton } from "../../components/Skeleton";

const BlogCardSection = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [filter, setFilter] = useState([]);
  const [selectedFilter, setSelectedFilter] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [loadingTags, setLoadingTags] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [showEndMessage, setShowEndMessage] = useState(false);
  const [blogPosts, setBlogPosts] = useState([]);

  // Fetch blog posts and tags on component mount
  useLayoutEffect(() => {
    getBlogTags();
  }, []);

  const getBlogs = useCallback(() => {
    // Reset pagination when selectedFilter changes
    setCurrentPage(1);
    setHasMore(true);
    getBlogPosts(1, true); // true indicates reset
  }, [selectedFilter?.length]);

  useEffect(() => {
    getBlogs();
  }, [selectedFilter?.length]);

  // Scroll event handler for infinite scroll
  const handleScroll = useCallback(() => {
    if (
      window.innerHeight + document.documentElement.scrollTop >=
        document.documentElement.offsetHeight - 1000 && // Load more when 1000px from bottom
      !loading &&
      hasMore
    ) {
      loadMorePosts();
    }
  }, [loading, hasMore]);

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  const loadMorePosts = () => {
    if (!loading && hasMore) {
      const nextPage = currentPage + 1;
      getBlogPosts(nextPage, false); // false indicates append
    }
  };

  const getBlogPosts = async (page = 1, reset = false) => {
    let params =
      selectedFilter?.length > 0
        ? { tags: selectedFilter?.map((item) => item).join(",") }
        : {};
    params.page = page;

    const paramsURL = `?tags=${params?.tags || ""}&page=${page}`;

    setLoading(true);
    try {
      const res = await dispatch(fetchBlogPosts(paramsURL));
      const { message, status, data } = res?.payload?.data;
      if (status === "success") {
        const { pagination: paginationData, blogs = [] } = data || {};
        const newBlogs = blogs.map((blog) => ({
          ...blog,
          banner: generateCloudFrontURL({
            bucket: process.env.REACT_APP_BUCKET_NAME,
            key: blog.banner,
            width: 410,
            height: 300,
            fit: "cover",
            actual: false,
          }),
        }));

        if (page === 1) {
          setBlogPosts(newBlogs);
        } else {
          setBlogPosts([...blogPosts, ...newBlogs]);
        }

        // Update pagination state
        const newHasMore = paginationData?.has_next || false;
        setHasMore(newHasMore);
        setCurrentPage(page);

        // Show end message for 3 seconds when reaching the end
        if (!newHasMore && page > 1) {
          setShowEndMessage(true);
          setTimeout(() => {
            setShowEndMessage(false);
          }, 3000);
        }

        // If reset is true, the Redux store should replace blogs
        // If reset is false, the Redux store should append blogs
        // You may need to update your Redux reducer to handle this
      } else {
        ToastNotifyError(message);
      }
    } catch (err) {
      console.log(err);
      ToastNotifyError("Failed to load blog posts");
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    }
  };

  const getBlogTags = async () => {
    try {
      setLoadingTags(true);
      const res = await dispatch(fetchBlogTags());
      const { data, message, status } = res?.payload?.data;
      if (status === "success") {
        const filData = (data?.options || []).map((tag) => ({
          ...tag,
          checked: false,
        }));
        setFilter(filData);
        setSelectedFilter([]); // Initialize with empty array instead of all filter data
      } else {
        ToastNotifyError(message);
      }
    } catch (err) {
      console.log(err);
    } finally {
      setTimeout(() => {
        setLoadingTags(false);
      }, 1000);
    }
  };

  const handleFilterChange = (e, key) => {
    const isChecked = e.target.checked;

    if (isChecked) {
      // Add to selectedFilter when checked
      if (!selectedFilter.includes(key)) {
        setSelectedFilter((prev) => [...prev, key]);
      }
    } else {
      // Remove from selectedFilter when unchecked
      setSelectedFilter((prev) => prev.filter((item) => item !== key));
    }
  };

  const handleCardClick = (blogId) => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
    navigate(`/wander-notes/${blogId}`);
  };

  return (
    <Box
      sx={{
        width: "100%",
        height: "100%",
        boxSizing: "border-box",
        px: {
          xs: "20px",
          md: "80px",
        },
        maxWidth: "1440px",
        mx: "auto",
        mt: {
          xs: "20px",
          md: "52px",
        },
        mb: {
          xs: "40px",
          md: "60px",
        },
      }}
    >
      <Box
        sx={{
          fontSize: "24px",
          fontWeight: "700",
          lineHeight: "45px",
          color: "#1E3A8A",
          mb: 3,
        }}
      >
        Explore all Wander Notes
      </Box>
      <Box
        sx={{
          display: "flex",
          alignItems: "flex-start",
          mb: 3,
          px: {
            xs: "20px",
            md: "40px",
          },
          py: {
            xs: "12px",
            md: "19px",
          },
          borderRadius: "8px",
          backgroundColor: "#FFF9F9",
          boxSizing: "border-box",
          flexDirection: {
            xs: "column",
            md: "row",
          },
          gap: 2,
        }}
      >
        {loadingTags ? (
          <FilterSkeleton />
        ) : (
          <>
            <Box
              sx={{
                fontFamily: "Roboto",
                fontSize: "20px",
                fontWeight: "500",
                lineHeight: "32px",
                color: "#1E3A8A",
                width: {
                  xs: "100%",
                  md: "100px",
                },
              }}
            >
              Filters
            </Box>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: {
                  xs: "flex-start",
                  md: "flex-end",
                },
                gap: 1.5,
                width: {
                  xs: "100%",
                  md: "calc(100% - 100px)",
                },
                flexWrap: "wrap",
              }}
            >
              {filter?.map((item) => {
                const isSelected = selectedFilter.some(
                  (selected) => selected === item.external_id
                );
                return (
                  <Box
                    key={item.external_id}
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 1.5,
                      p: 1,
                      borderRadius: "4px",
                      backgroundColor: "#FFFFFF",
                      boxSizing: "border-box",
                    }}
                  >
                    <Checkbox
                      aria-label="filter-checkbox"
                      checked={isSelected}
                      onChange={(e) => handleFilterChange(e, item.external_id)}
                      sx={{
                        width: "18px",
                        height: "18px",
                        color: "#4CAF50",
                        padding: "0px",
                        "&.Mui-checked": {
                          color: "#4CAF50",
                        },
                        "& .MuiSvgIcon-root": {
                          fontSize: "18px",
                        },
                      }}
                    />
                    <Typography
                      sx={{
                        fontFamily: "Roboto",
                        fontSize: "14px",
                        fontWeight: "400",
                        lineHeight: "20px",
                        color: "#333333",
                      }}
                    >
                      {item.name}
                    </Typography>
                  </Box>
                );
              })}
            </Box>
          </>
        )}
      </Box>
      {loading ? (
        <Box sx={{ mt: 4 }}>
          <BlogGridSkeleton count={12} />
        </Box>
      ) : (
        <Box>
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: {
                xs: "1fr",
                sm: "repeat(2, 1fr)",
                md: "repeat(3, 1fr)",
                lg: "repeat(4, 1fr)",
              },
              rowGap: {
                xs: 3,
                md: 5,
              },
              columnGap: {
                xs: 3,
                md: 3,
              },
              width: "100%",
            }}
          >
            {blogPosts?.length > 0 &&
              blogPosts?.map((blog) => (
                <Box key={blog.external_id}>
                  <BlogCard
                    image={blog.banner}
                    title={blog.seo_title}
                    author={blog?.writer?.name || ""}
                    timestamp={blog.published_at}
                    category={blog.tags?.length > 0 ? blog.tags[0].name : ""}
                    onClick={() => handleCardClick(blog.slug)}
                  />
                </Box>
              ))}
          </Box>
          {showEndMessage && (
            <Box
              sx={{
                textAlign: "center",
                py: 4,
                color: "#666",
                fontSize: "14px",
              }}
            >
              You've reached the end of all wander notes
            </Box>
          )}
        </Box>
      )}

      {/* End of content indicator - shows for 3 seconds */}
    </Box>
  );
};

export default BlogCardSection;
