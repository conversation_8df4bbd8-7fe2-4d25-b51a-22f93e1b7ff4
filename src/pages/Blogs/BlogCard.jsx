import React from "react";
import { Box, Typography } from "@mui/material";
import moment from "moment/moment";

const BlogCard = ({
  image,
  title,
  author,
  timestamp,
  category,
  onClick,
  isHover = true,
}) => {
  return (
    <Box
      onClick={onClick}
      sx={{
        cursor: onClick ? "pointer" : "default",
        backgroundColor: "transparent",
        borderRadius: "12px",
        overflow: "hidden",
        transition: "transform 0.2s ease-in-out",
        "&:hover": isHover
          ? {
              transform: "scale(1.02)",
              transition: "transform 0.2s ease-in-out",
            }
          : {},
        boxSizing: "border-box",
        width: "100%",
        height: "100%",
      }}
    >
      {/* Top Image */}
      <Box
        sx={{
          position: "relative",
          width: "100%",
          height: "300px",
          overflow: "hidden",
        }}
      >
        <Box
          component="img"
          src={image}
          alt={title}
          sx={{
            width: "100%",
            height: "100%",
            objectFit: "cover",
            borderRadius: "12px",
          }}
        />
        {category && (
          <Box
            sx={{
              position: "absolute",
              top: "20px",
              left: "20px",
              fontFamily: "Inter",
              backgroundColor: "#FFFFFF",
              color: "#1E3A8A",
              padding: "5px 14px",
              borderRadius: "18px",
              fontSize: "14px",
              lineHeight: "26px",
              fontWeight: "500",
              backdropFilter: "blur(4px)",
            }}
          >
            {category}
          </Box>
        )}
      </Box>

      <Box
        sx={{
          pt: "16px",
        }}
      >
        {/* Timestamp and Author with Separation Line */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: "10px",
            marginBottom: "8px",
          }}
        >
          <Typography
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontSize: "14px",
              fontWeight: "400",
              color: "#333333",
              lineHeight: "20px",
            }}
          >
            {moment(timestamp).format("MMMM DD YYYY")}
          </Typography>

          {/* Separation Line */}
          <Box
            sx={{
              width: "1px",
              height: "26px",
              borderRadius: "50%",
              backgroundColor: "#E7E6E6",
            }}
          />

          <Typography
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontSize: "14px",
              fontWeight: "400",
              color: "#333333",
              lineHeight: "20px",
            }}
          >
            {`By ${author}`}
          </Typography>
        </Box>

        {/* Title */}
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontSize: "16px",
            fontWeight: "500",
            lineHeight: "27px",
            color: "#333333",
            display: "-webkit-box",
            WebkitLineClamp: 2,
            WebkitBoxOrient: "vertical",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {title}
        </Typography>
      </Box>
    </Box>
  );
};

export default BlogCard;
