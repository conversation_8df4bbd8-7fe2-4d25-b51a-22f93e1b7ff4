import React, { useEffect, useRef, useState } from "react";
import { Box, Modal, IconButton } from "@mui/material";
import LaunchAITravel from "../../components/LaunchAITravel";
import IntroducingOneTap from "../../components/IntroducingOneTap";
import WhoIsItFor from "../../components/WhoIsItFor";
import WhyPartner from "../../components/WhyPartner";
import FeaturesTailored from "../../components/FeaturesTailored";
import WhatTravelersSay from "../../components/WhatTravelersSay";
import GetStarted from "../../components/GetStarted";
import DemocratizingTravel from "../../components/DemocratizingTravel";
import FAQ from "../../components/FAQ";
import { Close } from "@mui/icons-material";

const ForPartners = () => {
  const [openCalendly, setOpenCalendly] = useState(false);
  const [calendlyLoaded, setCalendlyLoaded] = useState(false);
  const calendlyRef = useRef(null);

  // Load Calendly script
  useEffect(() => {
    const script = document.createElement("script");
    script.src = "https://assets.calendly.com/assets/external/widget.js";
    script.async = true;
    script.onload = () => {
      setTimeout(() => {
        setCalendlyLoaded(true);
      }, 1000);
    };
    document.body.appendChild(script);

    return () => {
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
    };
  }, []);

  // Initialize Calendly widget when modal opens
  useEffect(() => {
    if (openCalendly && calendlyLoaded && window.Calendly) {
      // Small delay to ensure DOM is ready
      setTimeout(() => {
        if (calendlyRef.current) {
          window.Calendly.initInlineWidget({
            url: "https://calendly.com/zuumm-ai/demo?primary_color=ff3951",
            parentElement: calendlyRef.current,
            minWidth: "320px",
            height: "100%",
          });
        }
      }, 100);
    }
  }, [openCalendly, calendlyLoaded]);

  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  const handleScheduleDemo = () => {
    const isExternalRedirection = false;
    if (isExternalRedirection) {
      window.open(
        "https://calendly.com/zuumm-ai/demo?primary_color=ff3951",
        "_blank"
      );
    } else {
      setOpenCalendly(true);
    }
  };

  const handleCalendlyClose = () => {
    setOpenCalendly(false);
  };

  return (
    <Box>
      <LaunchAITravel handleScheduleDemo={handleScheduleDemo} />
      <IntroducingOneTap />
      <WhoIsItFor />
      <WhyPartner />
      <FeaturesTailored />
      <WhatTravelersSay />
      <GetStarted handleScheduleDemo={handleScheduleDemo} />
      <DemocratizingTravel />
      <FAQ />

      {openCalendly && calendlyLoaded && (
        <Modal
          open={openCalendly}
          onClose={handleCalendlyClose}
          disableEscapeKeyDown
          BackdropProps={{
            sx: {
              backdropFilter: "blur(3px)",
            },
            onClick: handleCalendlyClose,
          }}
        >
          <Box
            sx={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              width: "90%",
              maxWidth: "1200px",
              height: "80vh",
              bgcolor: "background.paper",
              borderRadius: "12px",
              boxShadow: 24,
              outline: "none",
              overflow: "hidden",
            }}
          >
            <Box
              sx={{
                position: "absolute",
                top: "0px",
                padding: " 8px 12px",
                right: { xs: "auto", md: "0px" },
                left: { xs: "0px", md: "auto" },
                background: "#FFF9F9",
                borderBottomLeftRadius: { xs: "0px", md: "12px" },
                borderBottomRightRadius: { xs: "12px", md: "0px" },
                cursor: "pointer",
                "&:hover": {
                  background: "#f7e4e4",
                },
              }}
              onClick={handleCalendlyClose}
            >
              {/* close button */}
              <IconButton
                sx={{
                  p: 0,
                  "&:hover": {
                    background: "transparent",
                  },
                }}
              >
                <Close sx={{ color: "#FF3951" }} />
              </IconButton>
            </Box>
            <div
              ref={calendlyRef}
              style={{
                width: "100%",
                height: "100%",
                border: "none",
              }}
            />
          </Box>
        </Modal>
      )}
    </Box>
  );
};

export default ForPartners;
