import { Box } from "@mui/material";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Header from "../../components/Header";
import Footer from "../../components/Footer";
import { fetchTermsAndConditions } from "../../store/reducers/Terms/apiThunk";

const Terms = () => {
  const dispatch = useDispatch();
  const { termsData } = useSelector((state) => state.terms);
  console.log("termsData: ", termsData);

  useEffect(() => {
    dispatch(fetchTermsAndConditions());
  }, [dispatch]);

  return (
    <Box>
      <Header />
      <Box
        sx={{
          boxSizing: "border-box",
          pt: { xs: "80px", md: "154px" },
          pb: { xs: 3, md: 4 },
          px: { xs: 2.5, md: 7.5 },
        }}
      >
        <Box
          sx={{
            borderLeft: "5px solid #FF3951",
            p: { xs: 3, md: 4 },
            borderRadius: "4px",
            boxShadow: "0px 4px 8px 0px #1E3A8A0F",
            background: "#FFFFFF",
          }}
        >
          <Box
            sx={{
              fontFamily: "Lora",
              fontWeight: 600,
              fontSize: "20px",
              color: "#333333",
            }}
          >
            Terms & Conditions – ZUUMM
          </Box>
          <Box sx={{ mt: 3 }}>
            {termsData && (
              <Box
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  color: "#333333",
                  mb: 2,
                }}
                dangerouslySetInnerHTML={{ __html: termsData?.data?.content }}
              />
            )}
          </Box>
        </Box>
      </Box>
      <Footer />
    </Box>
  );
};

export default Terms;
