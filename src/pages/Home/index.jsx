import React from 'react';
import { Box } from '@mui/material';
import Hero from '../../components/Hero';
import WhyChooseZuumm from '../../components/WhyChooseZuumm';
import HowZuummWorks from '../../components/HowZuummWorks';
import TrendingTrips from '../../components/TrendingTrips';
import GlobalImpact from '../../components/GlobalImpact';
import DiscoverGetaway from '../../components/DiscoverGetaway';
import OptimizedTravel from '../../components/OptimizedTravel';
import WhatTravelersSay from '../../components/WhatTravelersSay';
import WanderNotes from '../../components/WanderNotes';
import JoinNewsletter from '../../components/JoinNewsletter';
import FAQ from "../../components/FAQ";
const Home = () => {
  return (
    <Box>
      <Hero />
      <WhyChooseZuumm />
      <HowZuummWorks />
      <TrendingTrips />
      <GlobalImpact />
      <DiscoverGetaway />
      <OptimizedTravel />
      <WhatTravelersSay />
      <WanderNotes />
      <JoinNewsletter />
      <FAQ />
    </Box>
  );
};

export default Home;
