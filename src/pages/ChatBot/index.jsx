/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import { Box, IconButton, Fade } from "@mui/material";
import { v4 as uuidv4 } from "uuid";
import AiChatLeftSidebar from "../../components/ai-chatbot/AiChatLeftSidebar/AiChatLeftSidebar";
import AiChatRightSidebar from "../../components/ai-chatbot/AiChatRightSidebar/AiChatRightSidebar";
import AiChatHeader from "../../components/ai-chatbot/AiChatHeader";
import ChatInterface from "../../components/ai-chatbot/ChatInterface";
import { Customize } from "../../components/common/SvgIcon";
import {
  createConversation,
  fetchConversationChats,
  fetchConversations,
  deleteConversation,
  renameConversation,
} from "../../store/reducers/Conversation/apiThunk";
import {
  ToastNotifyError,
  ToastNotifySuccess,
} from "../../components/Toast/ToastNotify";
import { apiChatBot } from "../../services/networkChatBot";

const ChatBot = () => {
  const dispatch = useDispatch();
  const currentRequestIdRef = useRef(null);

  const [isConversationLoading, setIsConversationLoading] = useState(false); // recent chats loading
  const [ongoingChatLoading, setOngoingChatLoading] = useState(false); // ongoing/current chat loading
  const [conversations, setConversations] = useState([]); // recent chats
  const [newChat, setNewChat] = useState(true); // new chat button clicked
  const [expandedSidebar, setExpandedSidebar] = useState(true);

  const [conversationChats, setConversationChats] = useState([]); // conversation chats

  const handleAddConversationChat = (chat) => {
    setConversationChats((prev) => [...prev, chat]);
  };

  const [conversationId, setConversationId] = useState(""); // conversation id
  const handleSetConversationId = (id) => {
    setConversationId(id);
  };

  const [canSendMessage, setCanSendMessage] = useState(true); // can send message

  const disableSendMessage = () => {
    setCanSendMessage(false);
  };

  const enableSendMessage = () => {
    setCanSendMessage(true);
  };

  const [selectedConversation, setSelectedConversation] = useState("new"); // selected conversation

  const handleSelectConversation = async (conversation) => {
    if (selectedConversation === conversation) {
      return;
    }

    // Cancel any ongoing API requests by generating a new request ID
    currentRequestIdRef.current = uuidv4();

    console.log("conversation: ", conversation);
    setSelectedConversation(conversation);
    handleSetConversationId(conversation);
    closeRightSidebar();

    if (conversation) {
      await getConversationChats(conversation);
    }
    if (newChat && conversation) {
      setNewChat(false);
    }
  };

  const [rightSidebarType, setRightSidebarType] = useState(null); // 'customize' or 'package'
  const [isRightSidebarOpen, setIsRightSidebarOpen] = useState(false);

  const [packageData, setPackageData] = useState(null); // package data

  const handleSetPackageData = (data) => {
    setPackageData(data);
    openRightSidebar("package");
  };

  const [hotelData, setHotelData] = useState(null); // hotel data

  const handleSetHotelData = (data) => {
    setHotelData(data);
    openRightSidebar("hotel");
  };

  const [preferencePackageData, setPreferencePackageData] = useState(null); // preference package data

  const [selectedPreference, setSelectedPreference] = useState(null); // selected preference

  const getConversationChats = async (conversationId) => {
    try {
      const response = await dispatch(
        fetchConversationChats(conversationId)
      ).unwrap();
      if (response?.success) {
        setConversationChats(response?.data?.messages || []);
      } else {
        setConversationChats([]);
      }
    } catch (error) {
      console.error("error: ", error);
    } finally {
      if (ongoingChatLoading) {
        setOngoingChatLoading(false);
      }
    }
  };

  const getConversations = async () => {
    try {
      setIsConversationLoading(true);
      const response = await dispatch(fetchConversations()).unwrap();
      if (response?.success) {
        setConversations(response?.data);
      } else {
        setConversations([]);
      }
    } catch (error) {
      console.error("error: ", error);
    } finally {
      setTimeout(() => {
        setIsConversationLoading(false);
      }, 1000);
    }
  };

  const handleDeleteConversation = async (conversationId) => {
    try {
      const response = await dispatch(
        deleteConversation(conversationId)
      ).unwrap();
      if (response?.success) {
        // Remove the conversation from the list
        setConversations((prev) =>
          prev.filter((conv) => conv.conversation_id !== conversationId)
        );
        ToastNotifySuccess("Conversation deleted successfully");

        // If the deleted conversation was selected, switch to new chat
        if (selectedConversation === conversationId) {
          handleNewChat();
          closeRightSidebar();
        }
      }
    } catch (error) {
      console.error("error: ", error);
    }
  };

  const handleRenameConversation = async (conversationId, newTitle) => {
    try {
      const response = await dispatch(
        renameConversation({ conversationId, title: newTitle })
      ).unwrap();
      if (response?.success) {
        // Update the conversation title in the list
        setConversations((prev) =>
          prev.map((conv) =>
            conv.conversation_id === conversationId
              ? { ...conv, title: newTitle }
              : conv
          )
        );
        ToastNotifySuccess("Conversation renamed successfully");
      }
    } catch (error) {
      console.error("error: ", error);
    }
  };

  const handleNewChat = () => {
    setNewChat(true);
    setCanSendMessage(true);
    setConversationChats([]);
    handleSelectConversation(null);
  };

  // Upload media file using presigned URL
  const uploadMediaFile = async (mediaFile, senderChatId) => {
    try {
      // Validate mediaFile
      if (!mediaFile) {
        throw new Error("No media file provided");
      }

      // Get file extension from the mediaFile (base64 data URL)
      let fileExtension = "jpeg"; // default
      if (mediaFile.startsWith("data:image/")) {
        const match = mediaFile.match(/data:image\/([^;]+)/);
        if (match) {
          fileExtension = match[1].toLowerCase();
        }
      } else if (mediaFile instanceof File) {
        // Handle File object
        const fileNameParts = mediaFile.name.split(".");
        if (fileNameParts.length > 1) {
          fileExtension = fileNameParts.pop().toLowerCase();
        }
      }

      const fileName = `${senderChatId}.${fileExtension}`;
      console.log(
        "Uploading file:",
        fileName,
        "with extension:",
        fileExtension
      );

      // Get presigned URL
      const presignedResponse = await apiChatBot.post(
        "/api/v1/upload/presigned-url",
        {
          file_name: fileName,
          file_type: "image/" + fileExtension,
        }
      );

      console.log("Presigned response:", presignedResponse);

      if (!presignedResponse.success) {
        throw new Error(
          presignedResponse.message || "Failed to get presigned URL"
        );
      }

      const { presigned_url, form_fields } = presignedResponse?.data?.data;

      if (!presigned_url || !form_fields) {
        throw new Error("Invalid presigned URL response");
      }

      console.log("Presigned URL:", presigned_url);
      console.log("Form fields:", form_fields);

      // Convert base64 to File object or use existing File
      let file;
      if (mediaFile.startsWith("data:image/")) {
        // Convert base64 to blob first
        const base64Response = await fetch(mediaFile);
        const blob = await base64Response.blob();
        console.log("blob: ", blob);

        // Create File object from blob
        file = new File([blob], fileName, { type: `image/${fileExtension}` });
      } else if (mediaFile instanceof File) {
        file = mediaFile;
      } else {
        throw new Error("Unsupported media file format");
      }

      // Create FormData with all required fields
      const formData = new FormData();

      // Add all form fields from the response
      for (const key in form_fields) {
        formData.append(key, form_fields[key]);
      }

      // Append the file last
      formData.append("file", file);

      // Upload to presigned URL
      const uploadResponse = await fetch(presigned_url, {
        method: "POST",
        body: formData,
      });

      console.log("Upload response status:", uploadResponse.status);

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        throw new Error(
          `Failed to upload file: ${uploadResponse.status} - ${errorText}`
        );
      }

      // Return the key from form_fields
      const uploadedKey = form_fields.key;
      console.log("Successfully uploaded file with key:", uploadedKey);
      return uploadedKey;
    } catch (error) {
      console.error("Error uploading media file:", error);
      throw error;
    }
  };

  const handleSendMessage = async (
    message,
    conversationId,
    message_data = {},
    mediaFile = null
  ) => {
    // Generate a new request ID for this request
    const requestId = uuidv4();
    currentRequestIdRef.current = requestId;

    const payload = {
      query: message,
      conversation_id: conversationId,
      message_data: message_data,
      // image_url: "https://example.com/image.png",
      // channel: "web",
    };
    const senderChatId = uuidv4();
    if (newChat) {
      setNewChat(false);
    }
    handleAddConversationChat({
      message_text: message,
      conversation_id: conversationId,
      id: senderChatId,
      message_data: {},
      sender: "user",
      created_at: new Date().toISOString(),
      ...(mediaFile && { image_url: mediaFile, isBeforeUpload: true }),
    });

    try {
      let media_url = null;
      if (mediaFile) {
        setCanSendMessage(false);
        try {
          // Upload the media file and get the URL
          const response = await uploadMediaFile(mediaFile, senderChatId);
          console.log("Uploaded media URL:", response);
          if (response) {
            media_url = response;
            // update the chat with the media_url and isBeforeUpload to false
            setConversationChats((prev) =>
              prev.map((chat) =>
                chat.id === senderChatId
                  ? { ...chat, image_url: media_url, isBeforeUpload: false }
                  : chat
              )
            );
          }
        } catch (uploadError) {
          console.error("Failed to upload media file:", uploadError);
          ToastNotifyError("Failed to upload image. Please try again.");
          // remove the chat from the conversation chats
          setConversationChats((prev) =>
            prev.filter((chat) => chat.id !== senderChatId)
          );
          return;
        }
      }

      setOngoingChatLoading(true);

      // Add media_url to payload if available
      if (media_url) {
        payload.image_url = media_url;
      }

      const response = await dispatch(createConversation(payload)).unwrap();

      // Check if this request was cancelled (new conversation selected)
      if (currentRequestIdRef.current !== requestId) {
        console.log("Request was cancelled - new conversation selected");
        return;
      }

      if (response?.success) {
        const agentChatId = uuidv4();
        const payload = {
          ...response?.data,
          id: agentChatId,
        };
        if (selectedConversation && selectedConversation !== "new") {
          if (selectedConversation === payload?.conversation_id) {
            await handleAddConversationChat(payload);
          }
        } else {
          await handleAddConversationChat(payload);
        }
      }
    } catch (error) {
      // Only log error if this request wasn't cancelled
      if (currentRequestIdRef.current === requestId) {
        console.error("error: ", error);
      }
    } finally {
      // Only update loading state if this request wasn't cancelled
      if (currentRequestIdRef.current === requestId) {
        setOngoingChatLoading(false);
        setCanSendMessage(true);
      }
    }
  };

  const handleStartNewConversation = async (message, mediaFile = null) => {
    // generate conversation id
    setCanSendMessage(true);
    const conversationId = uuidv4();
    await handleSendMessage(message, conversationId, {}, mediaFile);
    handleSetConversationId(conversationId);
    setSelectedConversation(conversationId);
    setConversations([
      {
        conversation_id: conversationId,
        last_message_preview: message,
      },
      ...conversations,
    ]);
  };

  // Fetch conversations on component mount
  useEffect(() => {
    getConversations();
  }, []);

  const handleSendSelectedOption = (item, chatId) => {
    if (!chatId) {
      ToastNotifyError("Conversation id is not found");
      return;
    }
    let message_data_payload = {};
    const newChats = [...conversationChats];
    //find the chat with the same conversation id
    const chat = newChats.find((chat) => chat?.id === chatId);
    if (chat) {
      message_data_payload = chat?.message_data;
      const options = message_data_payload?.ui_component?.options?.map(
        (option) => {
          if (option.id === item.id) {
            return {
              ...option,
              selected: true,
              editable: false,
            };
          }
          return {
            ...option,
            editable: false,
          };
        }
      );

      message_data_payload.ui_component.options = options;

      chat.message_data = message_data_payload;

      const finalChats = newChats.map((chat) => {
        if (chat?.id === chatId) {
          return {
            ...chat,
            message_data: message_data_payload,
          };
        }
        return chat;
      });
      setConversationChats(finalChats);
    }
    const message_text = item?.response_prompt || item?.display;
    handleSendMessage(message_text, conversationId, message_data_payload);
  };

  const handleSendCounterData = (updateCounts, chatId) => {
    if (!chatId) {
      ToastNotifyError("Conversation id is not found");
      return;
    }
    let message_data_payload = {};
    const newChats = [...conversationChats];
    //find the chat with the same conversation id
    const chat = newChats?.find((chat) => chat?.id === chatId);
    if (chat) {
      message_data_payload = chat?.message_data;
      const options = updateCounts;

      message_data_payload.ui_component.options = options;

      chat.message_data = message_data_payload;

      const finalChats = newChats.map((chat) => {
        if (chat?.id === chatId) {
          return {
            ...chat,
            message_data: message_data_payload,
          };
        }
        return chat;
      });
      setConversationChats(finalChats);
    }
    const message_text = `Sure, I'll be traveling with ${updateCounts[0]?.count} Adults, ${updateCounts[1]?.count} Children, and ${updateCounts[2]?.count} Infants.`;
    handleSendMessage(message_text, conversationId, message_data_payload);
  };

  const openRightSidebar = (type) => {
    if (!isRightSidebarOpen) {
      setRightSidebarType(type);
      setIsRightSidebarOpen(true);
    } else {
      setIsRightSidebarOpen(false);

      if (rightSidebarType === type) {
        setRightSidebarType(type);
        setIsRightSidebarOpen(true);
      } else {
        setTimeout(() => {
          setRightSidebarType(type);
          setIsRightSidebarOpen(true);
        }, 800);
      }
    }
    setExpandedSidebar(false);
  };

  const closeRightSidebar = () => {
    setIsRightSidebarOpen(false);
  };

  const handleUpdatePreferencePackage = (message) => {
    handleSendMessage(message, conversationId);
    closeRightSidebar();
  };

  useEffect(() => {
    if (conversationChats.length > 0) {
      const lastMessage = conversationChats[conversationChats.length - 1];

      // Check if last message has packages type and pan_component
      if (
        lastMessage?.message_data?.response_component?.type === "packages" &&
        lastMessage?.message_data?.pane_component
      ) {
        openRightSidebar("preference-package");
        setPreferencePackageData(lastMessage?.message_data?.pane_component);
        setSelectedPreference("preference-package");
        return;
      } else if (
        lastMessage?.message_data?.response_component?.type === "hotels" &&
        lastMessage?.message_data?.pan_component
      ) {
        openRightSidebar("preference-hotel");
        setSelectedPreference("preference-hotel");
      } else {
        if (selectedPreference) {
          setSelectedPreference(null);
        }
      }
      // if (
      //   lastMessage?.message_data?.response_component?.type === "flights" &&
      //   lastMessage?.message_data?.pan_component
      // ) {
      //   openRightSidebar("preference-flight");
      // }
      // if (
      //   lastMessage?.message_data?.response_component?.type === "transfers" &&
      //   lastMessage?.message_data?.pan_component
      // ) {
      //   openRightSidebar("preference-transfer");
      // }
    }
  }, [conversationChats]);

  const handleTogglePreference = (preference) => {
    if (preference) {
      openRightSidebar(preference);
    }
  };

  return (
    <>
      <AiChatHeader />
      <Box
        sx={{
          display: "flex",
          height: "calc(100dvh - 55px)",
          background: "#fff",
          mt: "55px",
        }}
      >
        {/* Left Sidebar */}
        <Box
          sx={{
            flexShrink: 0,
            height: "calc(100dvh - 55px)",
            boxShadow: "none !important",
            zIndex: 2,
            position: "relative",
            background: "#FFF9F9",
            overflow: "hidden",
          }}
        >
          <AiChatLeftSidebar
            conversations={conversations}
            handleNewChat={handleNewChat}
            handleSelectConversation={handleSelectConversation}
            selectedConversation={selectedConversation}
            isLoading={isConversationLoading}
            handleDeleteConversation={handleDeleteConversation}
            handleRenameConversation={handleRenameConversation}
            expanded={expandedSidebar}
            setExpanded={setExpandedSidebar}
          />
        </Box>
        {/* Center Chat Area */}
        <Box
          sx={{
            flex: 1,
            position: "relative",
            overflow: "hidden",
            height: "calc(100dvh - 55px)",
          }}
        >
          {/* Action buttons to open right sidebar */}
          {selectedPreference && (
            <Fade in={!isRightSidebarOpen} timeout={700}>
              <Box
                sx={{
                  position: "absolute",
                  top: "11px",
                  right: "0px",
                  zIndex: 10,
                  display: "flex",
                  gap: 2,
                }}
              >
                <IconButton
                  sx={{
                    display: "flex",
                    p: 0,
                    zIndex: 1000,
                    "&:hover": {
                      backgroundColor: "transparent",
                    },
                  }}
                  onClick={() => {
                    handleTogglePreference(selectedPreference);
                  }}
                >
                  <Customize />
                </IconButton>
              </Box>
            </Fade>
          )}

          {/* Center content (chat) - now with ChatInterface component */}
          <Box
            sx={{
              width: "100%",
              height: "100%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              animation: "slideInUp 1.2s cubic-bezier(0.77, 0, 0, 0.175, 1)",
              p: "0px 26px 20px 26px",
              boxSizing: "border-box",
            }}
          >
            <ChatInterface
              openRightSidebar={openRightSidebar}
              canSendMessage={canSendMessage}
              ongoingChatLoading={ongoingChatLoading}
              error={false}
              disableSendMessage={disableSendMessage}
              enableSendMessage={enableSendMessage}
              handleStartNewConversation={handleStartNewConversation}
              handleSendMessage={handleSendMessage}
              newChat={newChat}
              selectedConversation={selectedConversation}
              conversationChats={conversationChats}
              conversationId={conversationId}
              handleSendSelectedOption={handleSendSelectedOption}
              handleSetPackageData={handleSetPackageData}
              handleSetHotelData={handleSetHotelData}
              handleSendCounterData={handleSendCounterData}
            />
          </Box>
        </Box>
        {/* Right Sidebar */}
        <Box
          sx={{
            flexShrink: 0,
            height: "calc(100dvh - 55px)",
            zIndex: 2,
            position: "relative",
            background: "#FFFFFF",
            overflow: "hidden",
            boxShadow: "-4px 4px 7px 0px #00000008",
          }}
        >
          <AiChatRightSidebar
            sidebarType={rightSidebarType || "customize"}
            open={isRightSidebarOpen}
            onClose={closeRightSidebar}
            packageData={packageData}
            hotelData={hotelData}
            preferencePackageData={preferencePackageData}
            handleUpdatePreferencePackage={handleUpdatePreferencePackage}
          />
        </Box>
      </Box>
    </>
  );
};

export default ChatBot;
