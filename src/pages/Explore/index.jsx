/* eslint-disable */
import React, { useEffect, useState } from "react";
import StyledExplore from "./StyledExplore";
import { Box, TextField, Typography, Button } from "@mui/material";
import BotIcon from "../../assets/svg/explore-header-bot.svg";
import searchIcon from "../../assets/svg/searchIcon.svg";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchExploreActivities,
  fetchExploreCategories,
  fetchExploreDestinations,
} from "../../store/reducers/Explore";
import { ToastNotifyError } from "../../components/Toast/ToastNotify";
import { useNavigate } from "react-router-dom";
import ROUTE_PATH from "../../constants/route";
import generateCloudFrontURL from "../../utils";
import OnboardingModal from "../../components/OnboardingModal";
import {
  CategorySkeleton,
  ActivitySkeleton,
  DestinationSkeleton,
} from "../../components/Skeleton";

const Explore = () => {
  const [showAllCategories, setShowAllCategories] = useState(false);
  const [categories, setCategories] = useState([]);
  const [destinations, setDestinations] = useState([]);
  const [activities, setActivities] = useState([]);
  const [search, setSearch] = useState("");
  const navigate = useNavigate();
  const { userDetails } = useSelector((state) => state.authentication);

  const [openOnboardingModal, setOpenOnboardingModal] = useState(false);

  const isLogin = userDetails?.user ? true : false;

  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [destinationsLoading, setDestinationsLoading] = useState(true);
  const [activitiesLoading, setActivitiesLoading] = useState(true);

  const handleOpenOnboardingModal = () => {
    setOpenOnboardingModal(true);
  };

  const handleCloseOnboardingModal = () => {
    navigate("/ai-chat");
    setOpenOnboardingModal(false);
  };

  const handlePlanWithAI = () => {
    if (isLogin) {
      const visibility = process.env.REACT_APP_ENV === "production";
      if (!visibility) {
        navigate("/ai-chat");
      }
    } else {
      handleOpenOnboardingModal();
    }
  };

  const renderImage = (mediaKey) => {
    try {
      return generateCloudFrontURL({
        bucket: process.env.REACT_APP_BUCKET_NAME,
        key: mediaKey,
        width: 400,
        height: 300,
        fit: "cover",
      });
    } catch (error) {
      console.error("Error generating CloudFront URL:", error);
      return mediaKey; // Fallback to original key
    }
  };

  const dispatch = useDispatch();

  const getCategories = async () => {
    setCategoriesLoading(true);
    const params = `?search=${search}`;
    try {
      const res = await dispatch(fetchExploreCategories(params));
      const { data, status, message } = res?.payload?.data;

      if (status === "success") {
        setCategories(data?.categories);
      } else {
        ToastNotifyError(message);
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setTimeout(() => {
        setCategoriesLoading(false);
      }, 1000);
    }
  };

  // scroll to top
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  const getDestinations = async () => {
    setDestinationsLoading(true);
    try {
      const res = await dispatch(fetchExploreDestinations());
      const { data, status, message } = res?.payload?.data;

      if (status === "success") {
        setDestinations(data?.destinations);
      } else {
        ToastNotifyError(message);
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setDestinationsLoading(false);
    }
  };

  const getActivities = async () => {
    setActivitiesLoading(true);
    try {
      const res = await dispatch(fetchExploreActivities());
      const { data, status, message } = res?.payload?.data;

      if (status === "success") {
        setActivities(data?.activities);
      } else {
        ToastNotifyError(message);
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setActivitiesLoading(false);
    }
  };

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (search !== undefined) {
        getCategories();
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [search]);

  useEffect(() => {
    getCategories();
    getDestinations();
    getActivities();
  }, []);

  return (
    <StyledExplore>
      <Box className="content-wrapper">
        <Box className="header-container">
          <Box className="title-container">
            <Typography className="title">
              Explore by Zuumm - Discover Places & Plan Your Holidays!
            </Typography>
          </Box>
          <Box className="ai-box">
            <Box className="ai-box-title">
              <Typography className="header-ai-title">
                Not sure what to explore? Zippy is here to help you.
              </Typography>
            </Box>
            <Box
              className="ai-box-title-btn"
              sx={{ cursor: "pointer" }}
              onClick={handlePlanWithAI}
            >
              <Typography className="header-ai-title-btn">
                Plan with AI
              </Typography>
            </Box>
          </Box>

          <Box className="ai-bot-icon">
            <img src={BotIcon} alt="ai-search" className="ai-bot-icon-img" />
          </Box>
        </Box>
        <Box className="content-container">
          <Box className="top-categories-container">
            <Typography className="sub-title"> Top Categories</Typography>
            <Box className="search-container">
              <TextField
                placeholder="Search for Categories"
                variant="outlined"
                className="search-input"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <Box
                      sx={{
                        background: "white",
                        height: "100%",
                        display: "flex",
                        alignItems: "center",
                        marginRight: "8px",
                      }}
                    >
                      <Button variant="contained" className="search-button">
                        <img src={searchIcon} alt="search-Icon" />
                      </Button>
                    </Box>
                  ),
                }}
              />
            </Box>
          </Box>
          <Box
            className={`categories-container ${showAllCategories ? "show-all" : ""}`}
          >
            {categoriesLoading ? (
              <>
                {[...Array(12)].map((_, index) => (
                  <CategorySkeleton key={index} />
                ))}
              </>
            ) : (
              <>
                {categories?.length > 0 ? (
                  categories.map((category, index) => (
                    <Box
                      key={index}
                      className="category-item"
                      onClick={() =>
                        navigate(ROUTE_PATH.SELECTED_DESTINATION, {
                          state: {
                            externalid: category.external_id,
                            title: category.title,
                            hasVisibilityScroll: true,
                          },
                        })
                      }
                    >
                      <Box className="main-category-item">
                        <Box className="category-item-image">
                          <img
                            src={renderImage(category?.media?.[0]?.media)}
                            alt={category?.name}
                          />
                        </Box>
                        <Box className="category-item-content">
                          <Typography className="category-name">
                            {category?.title?.length > 22
                              ? `${category.title.substring(0, 22)}...`
                              : category?.title}
                          </Typography>
                          <Typography className="category-sub-title">
                            {category?.description || ""}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  ))
                ) : (
                  <Box className="no-categories-found">
                    <Typography className="no-categories-text">
                      No categories found
                    </Typography>
                  </Box>
                )}
              </>
            )}
          </Box>
          {categories?.length > 6 && (
            <button className="show-more-btn">
              {showAllCategories ? null : (
                <span onClick={() => setShowAllCategories(!showAllCategories)}>
                  See all
                </span>
              )}
            </button>
          )}

          <Typography
            className="section-title pp-mobile-title"
            sx={{ marginBottom: "0px !important" }}
          >
            Popular Things To Do
          </Typography>
          <Box className="activities-grid">
            {activitiesLoading ? (
              <>
                <ActivitySkeleton variant="large" />
                <Box className="activity-card2-container">
                  <ActivitySkeleton variant="small" />
                  <ActivitySkeleton variant="small" />
                </Box>
                <Box className="activity-card3-container">
                  <ActivitySkeleton variant="small" />
                  <ActivitySkeleton variant="small" />
                </Box>
              </>
            ) : (
              <>
                <Box
                  className="activity-card"
                  onClick={() =>
                    navigate(ROUTE_PATH.SELECTED_DESTINATION, {
                      state: {
                        externalid: activities?.[0]?.external_id,
                        title: activities?.[0]?.title,
                        type: "activity",
                        hasVisibilityScroll: true,
                      },
                    })
                  }
                >
                  <Box className="activity-card-overlay" />
                  <img
                    src={renderImage(activities?.[0]?.media?.[0]?.media)}
                    alt="Adventure"
                  />
                  <Typography className="activity-card-title">
                    {activities?.[0]?.title?.length > 22
                      ? `${activities?.[0]?.title.substring(0, 22)}...`
                      : activities?.[0]?.title}{" "}
                  </Typography>
                </Box>
                <Box className="activity-card2-container">
                  <Box
                    className="activity-card2"
                    onClick={() =>
                      navigate(ROUTE_PATH.SELECTED_DESTINATION, {
                        state: {
                          externalid: activities?.[1]?.external_id,
                          title: activities?.[1]?.title,
                          type: "activity",
                          hasVisibilityScroll: true,
                        },
                      })
                    }
                  >
                    <Box className="activity-card-overlay" />
                    <img
                      src={renderImage(activities?.[1]?.media?.[0]?.media)}
                      alt="Adventure"
                    />
                    <Typography className="activity-card-title">
                      {activities?.[1]?.title?.length > 22
                        ? `${activities?.[1]?.title.substring(0, 22)}...`
                        : activities?.[1]?.title}{" "}
                    </Typography>
                  </Box>
                  <Box
                    className="activity-card2"
                    onClick={() =>
                      navigate(ROUTE_PATH.SELECTED_DESTINATION, {
                        state: {
                          externalid: activities?.[2]?.external_id,
                          title: activities?.[2]?.title,
                          type: "activity",
                          hasVisibilityScroll: true,
                        },
                      })
                    }
                  >
                    <Box className="activity-card-overlay" />
                    <img
                      src={renderImage(activities?.[2]?.media?.[0]?.media)}
                      alt="Adventure"
                    />
                    <Typography className="activity-card-title">
                      {activities?.[2]?.title?.length > 22
                        ? `${activities?.[2]?.title.substring(0, 22)}...`
                        : activities?.[2]?.title}{" "}
                    </Typography>
                  </Box>
                </Box>
                <Box className="activity-card3-container">
                  <Box
                    className="activity-card3"
                    onClick={() =>
                      navigate(ROUTE_PATH.SELECTED_DESTINATION, {
                        state: {
                          externalid: activities?.[3]?.external_id,
                          title: activities?.[3]?.title,
                          type: "activity",
                          hasVisibilityScroll: true,
                        },
                      })
                    }
                  >
                    <Box className="activity-card-overlay" />
                    <img
                      src={renderImage(activities?.[3]?.media?.[0]?.media)}
                      alt="Adventure"
                    />
                    <Typography className="activity-card-title">
                      {activities?.[3]?.title?.length > 22
                        ? `${activities?.[3]?.title.substring(0, 22)}...`
                        : activities?.[3]?.title}{" "}
                    </Typography>
                  </Box>
                  <Box
                    className="activity-card3"
                    onClick={() =>
                      navigate(ROUTE_PATH.SELECTED_DESTINATION, {
                        state: {
                          externalid: activities?.[4]?.external_id,
                          title: activities?.[4]?.title,
                          type: "activity",
                          hasVisibilityScroll: true,
                        },
                      })
                    }
                  >
                    <Box className="activity-card-overlay" />
                    <img
                      src={renderImage(activities?.[4]?.media?.[0]?.media)}
                      alt="Adventure"
                    />
                    <Typography className="activity-card-title">
                      {activities?.[4]?.title?.length > 22
                        ? `${activities?.[4]?.title.substring(0, 22)}...`
                        : activities?.[4]?.title}{" "}
                    </Typography>
                  </Box>
                </Box>
              </>
            )}
          </Box>

          {(destinations?.length > 0 || destinationsLoading) && (
            <Box
              sx={{
                width: "100%",
              }}
            >
              <Typography
                className="section-title"
                sx={{ marginTop: "30px", marginBottom: "0px !important" }}
              >
                Trending Destinations
              </Typography>
              <Box className="destinations-grid">
                {destinationsLoading ? (
                  <>
                    {[...Array(4)].map((_, index) => (
                      <DestinationSkeleton key={index} />
                    ))}
                  </>
                ) : (
                  destinations.map((destination, index) => (
                    <Box
                      key={index}
                      className="destination-card"
                      onClick={() =>
                        navigate(ROUTE_PATH.SELECTED_PACKAGE, {
                          state: {
                            externalid: destination?.external_id,
                            title: destination?.title,
                            type: "destination",
                            hasVisibilityScroll: true,
                          },
                        })
                      }
                    >
                      <img
                        src={renderImage(destination?.media?.[0]?.media)}
                        alt={destination?.title}
                      />
                      <Box className="destination-overlay">
                        <Typography className="destination-title">
                          {destination?.title}
                        </Typography>
                        {/* <Typography className='destination-country'>
                      {`${destination?.other_packages_count} Tours`}
                    </Typography> */}
                      </Box>
                    </Box>
                  ))
                )}
              </Box>
            </Box>
          )}
        </Box>
      </Box>
      {openOnboardingModal && (
        <OnboardingModal
          open={openOnboardingModal}
          onClose={handleCloseOnboardingModal}
        />
      )}
    </StyledExplore>
  );
};

export default Explore;
