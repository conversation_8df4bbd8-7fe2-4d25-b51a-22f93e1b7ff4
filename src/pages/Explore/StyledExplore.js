import { styled } from "@mui/material/styles";
import exploreHeaderBg from "../../assets/png/explore-heading-bg.png";

const StyledExplore = styled("div")(
  ({ theme }) => `
  padding: 0px 0;
  background: #FFFFFF;

  .content-wrapper {
    max-width: 100%;
    margin: 0 auto;
    text-align: center;
    }
    .header-container {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: url(${exploreHeaderBg}) no-repeat center center;
      background-size: cover;
      min-height: 400px;
      position: relative;
      padding: 70px 0;
    }
    .title {
      font-family: '<PERSON>ra', serif;
      font-size: 56px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 63px;
      max-width: 900px;
      margin: 0 auto;
      text-align: center;
      margin-bottom: 30px;
      margin-top: 40px;
    }  
    .ai-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #FFFFFF;
      border-radius: 8px 0px 0px 8px;
      height: 48px;
      width: 45%;
      padding: 0 0px 0px 20px;
    }
    .ai-box-title {
      font-family: 'Roboto', sans-serif;
      font-size: 16px;
      font-weight: 500;
      color: ${theme.palette.secondary.main};
      line-height: 24px;
    }
    .ai-bot-icon-img {
      width: 180px;
      height: 194px;
    }
    .ai-box-title-btn {
      font-family: 'Roboto', sans-serif;
      font-size: 16px;
      font-weight: 500;
      color: ${theme.palette.custom.white};
      line-height: 100%;
      letter-spacing: 0.05em;
      background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
      height: 100%;
      width: 30%;
      justify-content: center;
      align-items: center;
      display: flex;

    }
    .ai-bot-icon {
      position: absolute;
      bottom: 0;
      right: 15%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .content-container {
      margin: 30px 0px;
      padding: 0px 50px 24px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 20px;
      width: 100%;
      box-sizing: border-box;
    }
    .sub-title {
      font-family: 'Lora', serif;
      font-size: 24px;
      font-weight: 700;
      color: ${theme.palette.secondary.main};
      line-height: 45px;
    }
    .categories-container {
      position: relative;
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 16px;
      padding: 0;
      box-sizing: border-box;
      
    }
    .category-item {
      flex: 0 0 calc((100% - 32px) / 3);
      height: auto;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      background-color: #FFF9F9;
      border-radius: 12px 12px 0px 0px;
      overflow: hidden;
       &:hover {
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
        cursor: pointer;
      }
    }
    .main-category-item {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      padding: 0;
      flex: 1;
      border-radius: 8px;
      box-sizing: border-box;
      // height: 100px !important;

     
      .category-item-image {
      width: 100%;
      height: 250px;
      border-radius: 12px 12px 0px 0px;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12px 12px 0px 0px;
      }
    }  
    }
    .category-item-content {
      width: 100%;
      padding: 16px 20px;
      background: #FFF9F9;
      box-sizing: border-box;
      position: relative;
      // height: 130px !important;
      flex: 1;
    }  
    .category-name {
      font-family: 'Roboto', sans-serif;
      font-size: 20px;
      font-weight: 700;
      line-height: 130%;
      letter-spacing: 0.05em;
      color: ${theme.palette.secondary.main};
      text-align: left;
      word-wrap: break-word;
      overflow-wrap: break-word;
      text-transform: capitalize;
    }
    .category-sub-title {
      margin-top: 6px;
      font-family: 'Roboto', sans-serif;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      color: #000000;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
    }
    .category-item-btn {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 12px;
      position: absolute;
      bottom: 12px;
      right: 20px;
    }
    .category-item-btn-text {
      font-family: 'Roboto', sans-serif;
      font-size: 16px;
      font-weight: 600;
      line-height: 20px;
      color: ${theme.palette.custom.links};
    }
    .section-title {
    font-family: 'Lora', serif !important;
    font-size: 28px;
    font-weight: 700;
    line-height: 45px;
      color: ${theme.palette.secondary.main};
      margin-bottom: 24px;
      text-align: left;
      width: 100%;
    }

    .activities-grid {
      width: 100%;
      display: flex;
      flex-direction: row;
      gap: 20px;
      height: 510px;
      box-sizing: border-box;
    }

    .activity-card {
      width: 40%;
      height: 100%;
      border-radius: 12px;
      background-color: #F5F5F5;
      box-sizing: border-box;
      position: relative;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12px;
      }

      .activity-card-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 50%;
        padding: 30px;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
        border-radius: 0 0 12px 12px;
      }

      }
      .activity-card-title {
        font-family: 'Roboto', sans-serif;
        font-size: 18px;
        font-weight: 500;
        color: #FFFFFF;
        text-align: left;
        position: absolute;
        bottom: 18px;
        left: 30px;
        text-transform: capitalize;
      }

      .activity-card-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 50%;
        padding: 30px;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
        border-radius: 0 0 12px 12px;
      }

    .activity-card2-container {
      width: 20%;
      display: flex;
      flex-direction: column;
      gap: 20px;
      box-sizing: border-box;
    }

    .activity-card2 {
      width: 100%;
      height: calc(50% - 10px);
      border-radius: 12px;
      background-color: #F5F5F5;
      box-sizing: border-box;
      position: relative;
      cursor: pointer;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12px;
      } 
    }

    .activity-card3-container {
      width: 40%;
      display: flex;
      flex-direction: column;
      gap: 20px;
      box-sizing: border-box;
    }

    .activity-card3 {
      width: 100%;
      height: calc(50% - 10px);
      border-radius: 12px;
      background-color: #F5F5F5;
      box-sizing: border-box;
      position: relative;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12px;
      }
    }

    .destinations-grid {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 26px;
    }

    .destination-card {
      position: relative;
      border-radius: 12px;
      width: calc((100% - 78px) / 4);
      height: 180px;
      box-sizing: border-box;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12px;
      }

      .destination-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 16px;
        background: #00000066;
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .destination-title {
        font-family: 'Roboto', sans-serif;
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        color: #FFFFFF;
        text-align: center;
        text-transform: capitalize; 
      }

      .destination-country {
        font-family: 'Roboto', sans-serif;
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 24px;
        text-align: center;
        margin-top: 4px;
        opacity: 1;
      }
    }

    .show-more-btn {
      display: none;
      width: 100%;
      padding: 12px;
      background: transparent;
      border: 1px solid ${theme.palette.secondary.main};
      border-radius: 8px;
      font-family: 'Roboto', sans-serif;
      font-size: 16px;
      font-weight: 500;
      color: ${theme.palette.secondary.main};
      cursor: pointer;
    }

    .pp-mobile-title {
      margin-top: 30px;
    }

    @media (max-width: 1024px) {
      .title {
        font-size: 40px;
        margin-bottom: 20px;
        max-width: 450px;
      }
      .ai-box {
        height: 35px;
      }
      .header-ai-title{
        font-size: 10px !important;
      }
      .header-ai-title-btn {
        font-size: 10px;
      }
      .ai-bot-icon-img {
        width: 125px;
        height: auto;
      }
      .destinations-grid {
        gap: 20px;
      }
      .destination-card {
        width: calc((100% - 20px) / 2);
      }
      .category-item {
        flex: 0 0 calc((100% - 16px) / 2);
      }
    }

    @media (max-width: 768px) {
      .content-container {
      padding: 0px 30px 24px;
}
      .ai-box {
        width: 48%;
        margin-top: 25px !important;
      }
      .activities-grid {
        flex-direction: column;
        height: auto;
        gap: 20px;
      }

      .destination-card {
        width: calc((100% - 20px) / 2);
      }
        .ai-bot-icon-img {
        width: 105px;
        height: auto;
      }

      .activity-card {
        width: 100%;
        height: 300px;
        cursor: pointer;
      }

      .activity-card2-container {
        width: 100%;
        flex-direction: column;
        gap: 20px;
      }

      .activity-card2 {
        height: 300px;
        cursor: pointer;
      }

      .activity-card3-container {
        width: 100%;
        gap: 20px;
      }

      .activity-card3 {
        height: 300px;
        cursor: pointer;
      }
      
      .category-item {
        flex: 0 0 calc((100% - 16px) / 2);
      }
    }

    @media (max-width: 480px) {
      .category-item-content {
        height: 160px !important;
      }
      .header-container {
        padding: 30px 0;
      }
      .title {
        font-size: 26px;
        max-width: 300px;
        line-height: 35px;
      }
      .activity-card-title {
        position: relative !important;
        bottom: 45px !important;
        left: 0 !important;
        right: 0 !important;
        text-align: center !important;
      }  
      .pp-mobile-title {
        margin-top: 0px !important;
      }

      .top-categories-container {
        flex-direction: column !important;
        gap: 10px !important;
      }
        .search-container {
          width: 100% !important;
        }

      .ai-box {
        flex-direction: column;
        padding: 12px 6px;
        gap: 5px;
        border-radius: 4px !important;
        max-width: 170px;
        height: 75px;
        width: 160px;
      }
      .ai-bot-icon-img {
        width: 110px !important;
      }
      .ai-bot-icon {
        right: 4% !important;
      }  
      .header-ai-title {
        font-size: 12px !important;
        font-weight: 500;
        line-height: 16px;
        max-width: 150px;
      }
        .ai-box-title-btn {
          width: 105px;
          height: 30px;
          border-radius: 4px;
        }
      .header-ai-title-btn {
        font-size: 10px;
      }
      .activities-grid, .destinations-grid {
        grid-template-columns: 1fr;
      }
      
      .destination-card {
        width: calc((100% - 20px) / 2);
      }
      
      .category-item {
        flex: 0 0 100%;
      }

      .categories-container {
        .category-item:nth-child(n+7) {
          display: none;
        }
        &.show-all .category-item:nth-child(n+7) {
          display: flex;
        }
      }
      
      .show-more-btn {
        display: block;
        outline: none !important;
        border: none !important;
      }
    }

    .top-categories-container {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
    .search-container {
      width: 40%;
      .search-input {
        width: 100%;
        
        .MuiOutlinedInput-root {
          padding-right: 8px;
          border-radius: 48px;
          overflow: hidden;
          border: none !important;
          padding: 1px;
          background: linear-gradient(90deg, rgba(255, 57, 81, 0.2) 0%, rgba(85, 48, 249, 0.2) 100%);
          position: relative;
          
          fieldset {
            border: none !important;
          }
          
          &:hover fieldset {
            border: none !important;
          }
          
          &.Mui-focused fieldset {
            border: none !important;
          }
          
          &::before {
            content: '';
            position: absolute;
            top: 1px;
            left: 1px;
            right: 1px;
            bottom: 1px;
            background: #FFFFFF;
            border-radius: 48px;
            z-index: 0;
          }
          
          input {
            border-radius: 48px;
            padding: 12px 20px;
            background: transparent;
            position: relative;
            z-index: 1;
          }
          
          .search-button {
            height: 36px;
            width: 36px;
            min-width: 36px;
            background: #FF39510D;
            border-radius: 50%;
            text-transform: none;
            color: red;
            padding: 8px;
            box-shadow: none;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 1;

            img {
              width: 20px;
              height: 20px;
            }
          }
        }
      }
    }
}
`
);

export default StyledExplore;
