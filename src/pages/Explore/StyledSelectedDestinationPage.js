import { styled } from "@mui/material/styles";
import exploreHeaderBg from "../../assets/png/explore-heading-bg.png";

const StyledSelectedDestinationPage = styled("div")(
  ({ theme }) => `
  padding: 0px 0;
 background: #FFFFFF;

 .content-wrapper {
   max-width: 100%;
   margin: 0 auto;
   text-align: center;
   }
   .header-container {
     position: relative;
     display: flex;
     flex-direction: column;
     justify-content: center;
     align-items: center;
     background: url(${exploreHeaderBg}) no-repeat center center;
     background-size: cover;
     min-height: 400px;
     position: relative;
     padding: 70px 0;
   }
   .title {
     font-family: '<PERSON>ra', serif;
     font-size: 56px;
     font-weight: 500;
     color: #FFFFFF;
     line-height: 63px;
     max-width: 900px;
     margin: 0 auto;
     text-align: center;
     margin-bottom: 30px;
     margin-top: 40px;
   }  
   .ai-box {
     display: flex;
     justify-content: space-between;
     align-items: center;
     background: #FFFFFF;
     border-radius: 8px 0px 0px 8px;
     height: 48px;
     width: 45%;
     padding: 0 0px 0px 20px;
   }
   .ai-box-title {
     font-family: 'Roboto', sans-serif;
     font-size: 16px;
     font-weight: 500;
     color: ${theme.palette.secondary.main};
     line-height: 24px;
   }
   .ai-bot-icon-img {
     width: 180px;
     height: 194px;
   }
   .ai-box-title-btn {
     font-family: 'Roboto', sans-serif;
     font-size: 16px;
     font-weight: 500;
     color: ${theme.palette.custom.white};
     line-height: 100%;
     letter-spacing: 0.05em;
     background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
     height: 100%;
     width: 30%;
     justify-content: center;
     align-items: center;
     display: flex;

   }
   .ai-bot-icon {
     position: absolute;
     bottom: 0;
     right: 15%;
     display: flex;
     justify-content: center;
     align-items: center;
   }
   .container-wrapper{
     margin: 30px 0px;
     padding: 0 50px 24px;
   }
   .destionations-title {
     display: flex;
     justify-content: space-between;
     align-items: center;
   }
   .title-text {
     font-family: 'Lora', serif;
     font-size: 24px;
     font-weight: 700;
     color: ${theme.palette.secondary.main};
     line-height: 45px;
     margin-bottom: 10px;
   }
   .destionations-title-btn {
     display: flex;
     gap: 10px;
     align-items: center;
     min-width: 300px;
   }
   .destionations-title-btn-text {
     font-family: 'Roboto', sans-serif;
     font-size: 16px;
     font-weight: 500;
     line-height: 100%;
     color: ${theme.palette.secondary.main};
     min-width: 67px;
   }
   .destionations-title-btn-select {
     width: 100%;
     height: 44px;
   }  
   .destionations-list {
     display: flex;
     flex-direction: column;
     gap: 24px;
   }
   .destionations-container-wrapper {
     margin-top: 24px !important;
     position: relative;
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 16px;
      padding: 0;
      box-sizing: border-box;
   }
    .destionations-list-item {
     flex: 0 0 calc((100% - 32px) / 3);
     height: auto;
     display: flex;
     flex-direction: column;
     justify-content: flex-start;
     align-items: flex-start;
     padding: 0;
     border-radius: 8px;
     box-sizing: border-box;
     box-shadow: 0px 4px 8px 0px #0000000D;

   }
   .destionations-list-item-img {
     position: relative;
     width: 100%;
     height: 250px;
     border-radius: 8px;
     box-sizing: border-box;
     img {
       width: 100%;
       height: 100%;
       object-fit: cover;
       border-radius: 12px 12px 0px 0px;
     }
   }  
   .destionations-list-item-img-text {
     position: absolute;
     bottom: 0;
     right: 0;
     width: fit-content;
     height: 30px;
     background: #FFF9F9;
     border-radius: 4px 0 0 0;
     box-sizing: border-box;
     padding: 8px;
   }  
   .destionations-list-item-img-text-title {
     font-family: 'Roboto', sans-serif;
     font-size: 12px;
     font-weight: 400;
     color: ${theme.palette.custom.subText};
     line-height: 120%;
     letter-spacing: 0;
     span {
       font-weight: 600;
     }
   }
   .destionations-list-item-text {
     width: 100%;
     height: 100%;
     padding: 14px 20px;
     background: ${theme.palette.custom.white};
     box-sizing: border-box;
     position: relative;
     display: flex;
     flex-direction: column;
     justify-content: space-between;
     border-radius: 8px;
   }  
   .destionations-list-item-text-title {
     font-family: 'Roboto', sans-serif;
     font-size: 20px;
     font-weight: 700;
     color: ${theme.palette.custom.subText};
     line-height: 100%;
     letter-spacing: 0;
     margin-bottom: 10px;
     text-transform: capitalize;
   }
   .destionations-list-item-text-description {
     font-family: 'Roboto', sans-serif;
     font-size: 14px;
     font-weight: 400;
     color: ${theme.palette.custom.placeholder};
     line-height: 20px;
     letter-spacing: 0;
     margin-bottom: 10px;
     overflow: hidden;
     text-overflow: ellipsis;
     display: -webkit-box;
     -webkit-line-clamp: 4;
     -webkit-box-orient: vertical;
     margin-bottom: 20px;
   }
   .destionations-list-item-text-price-container{
     display: flex;
     align-items: center;
     justify-content: space-between;
   }
   .destionations-list-item-text-price-text {
     font-family: 'Roboto', sans-serif;
     font-size: 16px;
     font-weight: 400;
     color: ${theme.palette.custom.subText};
     line-height: 132%;
     letter-spacing: 0;
     .destionations-list-item-text-price-text-price {
       font-weight: 600;
       font-size: 20px;
       color: ${theme.palette.secondary.main};
     }
     .destionations-list-item-text-price-text-price-currency {
       font-weight: 400;
       font-size: 14px;
       color: ${theme.palette.secondary.main};
     }
   }
   .destionations-list-item-text-btn {
     height: 44px;
     border: 1px solid ${theme.palette.custom.lightred};
     border-radius: 4px;
     box-sizing: border-box;
     padding: 0 20px;
     display: flex;
     align-items: center;
     justify-content: center;
     cursor: pointer;
   }    
   .destionations-list-item-text-btn-text {
     font-family: 'Roboto', sans-serif;
     font-size: 16px;
     font-weight: 500;
     color: ${theme.palette.custom.lightred};
     line-height: 100%;
   }
   .popular-international-container {
     margin-top: 30px;
   }  
   .popular-international-list {
     display: flex;
     flex-direction: row;
     flex-wrap: wrap;
     gap: 17px;
   }  
   .popular-international-list-item {
     width: calc((100% - 52px) / 4);
     height: 240px;
     border-radius: 12px;
     position: relative;
     .popular-international-list-item-img {
       width: 100%;
       height: 100%;
     }
     img {
       width: 100%;
       height: 100%;
       object-fit: cover;
       border-radius: 12px;
     }
   }   
   .popular-international-list-item-overlay {
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background: linear-gradient(180deg, rgba(5, 7, 60, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
     border-radius: 12px;
     z-index: 2;
   }
   .popular-international-list-item-img-text {
     position: absolute;
     bottom: 12px;
     left: 15px;
     font-family: 'Roboto', sans-serif;
     font-size: 18px;
     font-weight: 600;
     color: ${theme.palette.custom.white};
     line-height: 30px;
     letter-spacing: 0;
     z-index: 3;
     text-transform: capitalize;
   }  
   .popular-domestic-container {
     margin-top: 30px;
   }  
   .popular-domestic-list {
     display: flex;
     flex-direction: row;
     gap: 17px;
     flex-wrap: wrap;
   }  
   .popular-domestic-list-item {
     width: calc((100% - 52px) / 4);
     height: 240px;
     border-radius: 12px;
     position: relative;
     img {
       width: 100%;
       height: 100%;
       object-fit: cover;
       border-radius: 12px;
     }
   }  
   .popular-domestic-list-item-img {
     width: 100%;
     height: 100%;
     border-radius: 12px;
   }  
   .popular-domestic-list-item-img-text {
     position: absolute;
     bottom: 12px;
     left: 15px;
     font-family: 'Roboto', sans-serif;
     font-size: 18px;
     font-weight: 600;
     color: ${theme.palette.custom.white};
     line-height: 30px;
     letter-spacing: 0;
     z-index: 3;
     text-transform: capitalize;
   }  
   .visa-on-arrival-container {
     margin-top: 30px;
   }  
   .visa-on-arrival-list {
     display: flex;
     flex-direction: row;
     gap: 17px;
   }  
   .visa-on-arrival-list-item {
     width: calc((100% - 52px) / 4);
     height: 240px;
     border-radius: 12px;
     position: relative;
   }  
   .visa-on-arrival-list-item-img {
     width: 100%;
     height: 100%;
     border-radius: 12px;
     img {
       width: 100%;
       height: 100%;
       object-fit: cover;
       border-radius: 12px;
       z-index: 1;
     }
   }  
   .visa-on-arrival-list-item-img-text {
     position: absolute;
     bottom: 12px;
     left: 15px;
     font-family: 'Roboto', sans-serif;
     font-size: 18px;
     font-weight: 600;
     color: ${theme.palette.custom.white};
     line-height: 30px;
     letter-spacing: 0;
     z-index: 3;
     text-transform: capitalize;
   }  
   .visa-on-arrival-list-item-overlay {
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background: linear-gradient(180deg, rgba(5, 7, 60, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
     border-radius: 12px;
     z-index: 2;
   }
   .popular-domestic-list-item-overlay {
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background: linear-gradient(180deg, rgba(5, 7, 60, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
     border-radius: 12px;
     z-index: 2;
   }
   .filter-container {
     width: 100%;
     height: auto;
    }
   .selected-filter-container {
     height: 48px;
     padding: 0 50px;
     background: ${theme.palette.secondary.main};
     display: flex;
     align-items: center;
   }
   .selected-filter {
     font-family: 'Roboto', sans-serif;
     font-size: 16px;
     font-weight: 500;
     color: ${theme.palette.custom.white};
     line-height: 130%;
     letter-spacing: 0.05em;
     text-transform: capitalize;
   }
   .select-fliter-container {
     padding: 10px 50px;
     background: #FFF9F9;
     border-bottom: 1px solid var(--Color-Icon-BG, #FF39510D);
     border-radius: 0px 8px;
   }  
   .select-fliter-container-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
   }  
   .select-fliter {
     font-family: 'Roboto', sans-serif;
     font-size: 24px;
     font-weight: 500;
     color: ${theme.palette.secondary.main};
     line-height: 32px;
     letter-spacing: 0.05em;
     text-transform: capitalize;
   }  
   .filters-container {
    display: flex;
    align-items: center;
    gap: 10px;
   }  
   .filter-textfield-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 92px;
    background: white;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid var(--Color-Icon-BG, #FF39510D);
    cursor: pointer;
   }
   .filter-textfield-container.open {
    border: 1px solid #FF3951;
   }
   .filter-cross-textfield-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #F6F6F6;
    width: 100px;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
   }
   .filter-title {
    font-family: 'Roboto', sans-serif;
    font-size: 15px;
    font-weight: 400;
    color: ${theme.palette.custom.placeholder};
    line-height: 100%;
    letter-spacing: 0;
   } 
   .filter-cross-title {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: ${theme.palette.custom.subText};
    line-height: 100%;
    letter-spacing: 0;
   } 
   .dropdown-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
    width: 12px;
    height: 12px;
    }
   }
  .filter-categories-container {
    padding: 10px 40px;
    background: #FFF9F9;
    padding-bottom: 20px;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    max-height: 150px;
    overflow-y: auto;
  }  
  .filter-categories-container-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    width: fit-content;
    height: fit-content;
  }
  .filter-categories-container-item-text {
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    font-weight: 400;
    color: ${theme.palette.custom.subText};
    line-height: 100%;
    letter-spacing: 0;
    padding-right: 5px;
    text-transform: capitalize;
  }

  .filter-checkbox {
    .MuiCheckbox-root {
      font-size: 12px;
      padding: 0px;
      color: #4CAF50;
    }
    .MuiCheckbox-root.Mui-checked {
      padding: 0px;
      color: #4CAF50;
    }
    
    .MuiCheckbox-root:hover {
      background-color: rgba(76, 175, 80, 0.04);
    }
  }

  .MuiCheckbox-root {
    padding: 0px;
    color: #4CAF50;
  }
  
  .MuiCheckbox-root.Mui-checked {
    padding: 0px;
    color: #4CAF50;
  }
  
  .MuiCheckbox-root:hover {
    padding: 0px;
    background-color: rgba(76, 175, 80, 0.04);
  }

  .price-range-container-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .price-range-container {
    width: 50%;
    display: flex;
    align-items: center;
    gap: 20px;
  }
  .date-range-container-wrapper {
    width: 30%;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .price-range-container-left {
    width: 100%;
  }
  .price-range-container-left-title {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: 600;
    color: ${theme.palette.secondary.main};
    line-height: 17px;
    letter-spacing: 0;
  }
  .price-range-container-left-slider {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0px;
    border-radius: 4px;
    padding: 10px 0px !important;

    .MuiSlider-thumb {
      background: linear-gradient(#fff, #fff) padding-box,
      linear-gradient(90deg, #FF3951 0%, #5530F9 100%) border-box;
      border: 2px solid transparent; 
      width: 20px;
      height: 20px;
    }
    .MuiSlider-rail {
      background: #FFB6C1;
      opacity: 1;
    }
    .MuiSlider-track {
      background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
      border: 0px;
    }
  }

    .slider-points {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .price-range-container-left-slider-title {
      font-family: 'Roboto', sans-serif;
      font-size: 14px;
      font-weight: 700;
      color: ${theme.palette.secondary.main};
      line-height: 17px;
      letter-spacing: 0;
    }
  }
  .price-range-container-right {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .price-range-input {
    width: 120px;
    margin-left: 16px;
    background: #fff;
    border-radius: 8px;
  }

  .all-selected-filters-container {
    padding: 20px 40px;
    background: #FFF9F9;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    border-top: 1px solid #FF39510D;
  }
  .all-selected-filters-container-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 4px;
    background:#1E3A8A;
    cursor: pointer;
    border: 1px solid #1E3A8A;
    color: #FFFFFF;
  }  
  .all-selected-filters-container-item-text {
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 100%;
    text-transform: capitalize;
  }
  .destionations-list-item-no-data {
    width: 100%;
    height: 400px;
    display: flex;
    flex-direction: column;
    gap: 0px;
    align-items: center;
    justify-content: center;
  }  
  .destionations-list-item-text-title-no-data {
    font-family: 'Lora', serif;
    font-size: 28px;
    font-weight: 700;
    color: ${theme.palette.secondary.main};
    line-height: 45px;
    letter-spacing: 0;
    text-transform: capitalize;
  }  
  .mobile-select-container {
    dislpay: none;
  } 
  .activity-gallery-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }   
  .activity-gallery-item {
    width: calc((100% - 52px) / 4);
    height: 180px;
    border-radius: 4px;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
  }   

  @media (max-width: 1024px) {
     .destionations-list-item {
      flex: 0 0 calc((100% - 16px) / 2);
     }
    .ai-bot-icon-img {
      width: 90px;
      height: auto;
    }
    // .destionations-list-item {
    //   height: 430px !important;
    // }
  }

  @media (max-width: 768px) {
  .destionations-list-item {
      flex: 0 0 calc((100% - 16px) / 2);
     }
      .ai-bot-icon-img {
        width: 105px;
        height: auto;
      }
    .filter-container {
      display: none;
    }
    .destionations-title-btn {
      display: none;
    }  
      .header-container {
        padding: 30px 0;
      }
      .title {
        font-size: 26px;
        max-width: 300px;
        line-height: 35px;
      } 

      .ai-box {
        flex-direction: column;
        padding: 12px 6px;
        gap: 5px;
        border-radius: 4px !important;
        max-width: 170px;
        height: 75px;
        width: 160px;
      }
      .ai-bot-icon-img {
        width: 110px !important;
        height: auto !important;
      }
      .ai-bot-icon {
        right: 4% !important;
      }  
      .header-ai-title {
        font-size: 12px !important;
        font-weight: 500;
        line-height: 16px;
        max-width: 150px;
      }
        .ai-box-title-btn {
          width: 105px;
          height: 30px;
          border-radius: 4px;
        }
      .header-ai-title-btn {
        font-size: 10px;
      }
      .container-wrapper {
        margin: 16px 0px;
        padding: 0 20px 24px;
      }  
      .title-text {
        font-size: 20px !important;
        line-height: 100% !important;
        margin-bottom: 0px !important;
      }  
      .destionations-container-wrapper {
        margin-top: 16px !important;
      }  
      .popular-international-list {
        flex-direction: column !important;
        margin-top: 16px !important;
      }  
      .popular-international-list-item  {
        width: 100% !important;
      }  
      .popular-domestic-list {
        flex-direction: column !important;
        margin-top: 16px !important;
      }  
      .popular-domestic-list-item {
        width: 100% !important;
      }  
      .visa-on-arrival-list {
        flex-direction: column !important;
        margin-top: 16px !important;
      }  
      .visa-on-arrival-list-item {
        width: 100% !important;
      }  
      .mobile-select-container {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 32px;
        display: flex;
        align-items: center;
        padding: 10px 60px;
        gap: 10px;
        justify-content: center;
        background: #FFF9F9;
        z-index: 1000;
      } 
      .mobile-select-hr {
        width: 10%;
        border: 1px solid #FF39510D;
        rotate: 90deg;
        background: #FF39510D;
      }  
      .mobile-select-item {
        display: flex;
        align-items: center;
        gap: 10px;
      } 
      .mobile-select-text {
        font-family: 'Roboto', sans-serif;
        font-size: 16px;
        font-weight: 500;
        color: ${theme.palette.secondary.main};
        line-height: 32px;
        letter-spacing: 0.05em;
      }  
      .activity-gallery-container {
        flex-direction: column;
        gap: 16px;
      }
      .activity-gallery-item {
        width: 100%;
      }

  }
      @media (max-width: 480px) {
        .destionations-list-item {
          flex: 0 0 100%;
        }
      }
     `
);

export default StyledSelectedDestinationPage;