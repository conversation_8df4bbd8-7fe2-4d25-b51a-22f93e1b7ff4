/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef, useState } from "react";
import StyledPakagePage from "./StyledPakagePage";
import {
  Box,
  Typography,
  Checkbox,
  Slider,
  TextField,
  Button,
} from "@mui/material";
import BotIcon from "../../assets/svg/explore-header-bot.svg";
import dropdownIcon from "../../assets/svg/dropdownIcon.svg";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import CustomSelect from "../../components/common/CustomSelect";
import Rating from "@mui/material/Rating";
import StarIcon from "@mui/icons-material/Star";
import smallTickIcon from "../../assets/svg/smallTickIcon.svg";
import {
  fetchExploreActivitiesByDestination,
  fetchExploreDestinationsDropdowns,
  fetchExplorePackages,
} from "../../store/reducers/Explore";
import { useDispatch, useSelector } from "react-redux";
import { ToastNotifyError } from "../../components/Toast/ToastNotify";
import { useLocation, useNavigate } from "react-router-dom";
import Lottie from "lottie-react";
import noDataAnimation from "../../assets/animations/noDataAnimation.json";
import { useMediaQuery } from "@mui/material";
import { Drawer } from "@mui/material";
import sortIcon from "../../assets/svg/sortIcon.svg";
import filterIcon from "../../assets/svg/mobile-filter-icon.svg";
import closeIcon from "../../assets/svg/circleCrossIcon.svg";
import generateCloudFrontURL from "../../utils";
import BreadCrumb from "../../components/common/BreadCrumb";
import { formatIndianNumber } from "../../utils/helper";
import { useOnboardingModal } from "../../hooks/useOnboardingModal";
import OnboardingModal from "../../components/OnboardingModal";
import {
  PackageSkeleton,
  PackageDestinationSkeleton,
} from "../../components/Skeleton";

const monthList = [
  {
    title: "Jan",
    value: "jan",
  },
  {
    title: "Feb",
    value: "feb",
  },
  {
    title: "Mar",
    value: "mar",
  },
  {
    title: "Apr",
    value: "apr",
  },
  {
    title: "May",
    value: "may",
  },
  {
    title: "Jun",
    value: "jun",
  },
  {
    title: "Jul",
    value: "jul",
  },
  {
    title: "Aug",
    value: "aug",
  },
  {
    title: "Sep",
    value: "sep",
  },
  {
    title: "Oct",
    value: "oct",
  },
  {
    title: "Nov",
    value: "nov",
  },
  {
    title: "Dec",
    value: "dec",
  },
];

const monthValueToNumber = {
  jan: 1,
  feb: 2,
  mar: 3,
  apr: 4,
  may: 5,
  jun: 6,
  jul: 7,
  aug: 8,
  sep: 9,
  oct: 10,
  nov: 11,
  dec: 12,
};

const getPercent = (value, max = 5) => `${(value / max) * 100}%`;

const SelectedPakagePage = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const { userDetails } = useSelector((state) => state.authentication);
  const { open } = useOnboardingModal();
  const isMobile = useMediaQuery("(max-width:768px)");
  const { externalid = null, title, prevTitle, type } = location?.state || {};
  const [isCategoriesOpen, setIsCategoriesOpen] = useState(false);
  const [isActivitiesOpen, setIsActivitiesOpen] = useState(false);
  const [isVisitingMonthOpen, setIsVisitingMonthOpen] = useState(false);
  const [isPriceRangeOpen, setIsPriceRangeOpen] = useState(false);
  const [isDurationOpen, setIsDurationOpen] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [selectedActivities, setSelectedActivities] = useState([]);
  const [selectedMonths, setSelectedMonths] = useState([]);
  const [sortBy, setSortBy] = useState("priceLowToHigh");
  const [categories, setCategories] = useState([]);
  const [activities, setActivities] = useState([]);
  const [priceRange, setPriceRange] = useState([0, 500000]);
  const minPrice = 0;
  const maxPrice = 500000;
  const [nightsRange, setNightsRange] = useState([0, 50]);
  const minNights = 0;
  const maxNights = 50;
  const [packages, setPackages] = useState([]);
  const [destinationActivities, setDestinationActivities] = useState([]);
  const [priceRangeError, setPriceRangeError] = useState({
    min: false,
    max: false,
  });
  const [mobileSelectedCategories, setMobileSelectedCategories] = useState([]);
  const [mobileSelectedActivities, setMobileSelectedActivities] = useState([]);
  const [mobileSelectedMonths, setMobileSelectedMonths] = useState([]);
  const [mobilePriceRange, setMobilePriceRange] = useState([
    minPrice,
    maxPrice,
  ]);
  const [mobileNightsRange, setMobileNightsRange] = useState([
    minNights,
    maxNights,
  ]);
  const [mobilePriceRangeError, setMobilePriceRangeError] = useState({
    min: false,
    max: false,
  });
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [selectedMobileFilter, setSelectedMobileFilter] =
    useState("Categories");
  const [mobileSortOpen, setMobileSortOpen] = useState(false);

  const [openOnboardingModal, setOpenOnboardingModal] = useState(false);

  const [packagesLoading, setPackagesLoading] = useState(true);
  const [activitiesLoading, setActivitiesLoading] = useState(true);

  const handleOpenOnboardingModal = () => {
    setOpenOnboardingModal(true);
  };

  const handleCloseOnboardingModal = () => {
    setOpenOnboardingModal(false);
  };

  const handlePlanWithAI = () => {
    if (userDetails?.user) {
      const visibility = process.env.REACT_APP_ENV === "production";
      if (!visibility) {
        navigate("/ai-chat");
      }
    } else {
      handleOpenOnboardingModal();
    }
  };

  const handleCloseSort = () => {
    setMobileSortOpen(false);
  };

  const handleFilterClick = (filterType) => {
    let isCurrentlyOpen = false;
    switch (filterType) {
      case "categories":
        isCurrentlyOpen = isCategoriesOpen;
        break;
      case "activities":
        isCurrentlyOpen = isActivitiesOpen;
        break;
      case "visitingMonth":
        isCurrentlyOpen = isVisitingMonthOpen;
        break;
      case "priceRange":
        isCurrentlyOpen = isPriceRangeOpen;
        break;
      case "duration":
        isCurrentlyOpen = isDurationOpen;
        break;
      default:
        break;
    }

    setIsCategoriesOpen(false);
    setIsActivitiesOpen(false);
    setIsVisitingMonthOpen(false);
    setIsPriceRangeOpen(false);
    setIsDurationOpen(false);

    if (!isCurrentlyOpen) {
      switch (filterType) {
        case "categories":
          setIsCategoriesOpen(true);
          break;
        case "activities":
          setIsActivitiesOpen(true);
          break;
        case "visitingMonth":
          setIsVisitingMonthOpen(true);
          break;
        case "priceRange":
          setIsPriceRangeOpen(true);
          break;
        case "duration":
          setIsDurationOpen(true);
          break;
        default:
          break;
      }
    }
  };

  const handleClearFilters = () => {
    setSelectedCategories([]);
    setSelectedActivities([]);
    setSelectedMonths([]);
    setPriceRange([minPrice, maxPrice]);
    setNightsRange([minNights, maxNights]);
    setPriceRangeError({ min: false, max: false });
  };

  const handleSliderChange = (event, newValue) => {
    setPriceRange(newValue);
  };

  const handleMinInputChange = (e) => {
    const value = Number(e.target.value.replace(/[^0-9]/g, ""));
    setPriceRange([value, priceRange[1]]);

    // Validate min value
    if (value < minPrice) {
      setPriceRangeError((prev) => ({ ...prev, min: true }));
      ToastNotifyError("Minimum value should be ₹5,000 or higher");
    } else if (value > priceRange[1]) {
      setPriceRangeError((prev) => ({ ...prev, min: true }));
      ToastNotifyError("Minimum value cannot be greater than maximum value");
    } else {
      setPriceRangeError((prev) => ({ ...prev, min: false }));
    }
  };

  const handleMaxInputChange = (e) => {
    const value = Number(e.target.value.replace(/[^0-9]/g, ""));
    setPriceRange([priceRange[0], value]);

    // Validate max value
    if (value > maxPrice) {
      setPriceRangeError((prev) => ({ ...prev, max: true }));
      ToastNotifyError("Maximum value should be ₹5,00,000 or lower");
    } else if (value < priceRange[0]) {
      setPriceRangeError((prev) => ({ ...prev, max: true }));
      ToastNotifyError("Maximum value cannot be less than minimum value");
    } else {
      setPriceRangeError((prev) => ({ ...prev, max: false }));
    }
  };

  const getCategories = async () => {
    const params = "?dropdown=categories";
    try {
      const res = await dispatch(fetchExploreDestinationsDropdowns(params));
      const { data, message, status } = res?.payload?.data;
      if (status === "success") {
        setCategories(data?.options);
      } else {
        ToastNotifyError(message);
      }
    } catch (err) {
      console.log(err);
    }
  };
  const getActivities = async () => {
    const params = "?dropdown=activities";
    try {
      const res = await dispatch(fetchExploreDestinationsDropdowns(params));
      const { data, message, status } = res?.payload?.data;
      if (status === "success") {
        setActivities(data?.options);
      } else {
        ToastNotifyError(message);
      }
    } catch (err) {
      console.log(err);
    }
  };

  useEffect(() => {
    // Don't make API call if there are price range errors
    if (priceRangeError.min || priceRangeError.max) {
      return;
    }

    const getPackages = async () => {
      setPackagesLoading(true);
      const allCategoryIds = [...selectedCategories].filter(Boolean);
      const allActivitiesIds = [...selectedActivities].filter(Boolean);
      const sortByValue =
        sortBy === "priceLowToHigh"
          ? "price"
          : sortBy === "priceHighToLow"
            ? "-price"
            : "";
      const selectedMonthsNumbers = selectedMonths
        .map((m) => monthValueToNumber[m])
        .filter(Boolean);

      const paramsArr = [];
      if (externalid) paramsArr.push(`destination=${externalid}`);
      if (allCategoryIds.length)
        paramsArr.push(`categories=${allCategoryIds.join(",")}`);
      if (allActivitiesIds.length)
        paramsArr.push(`activities=${allActivitiesIds.join(",")}`);

      // Only add price range if it's different from initial state
      if (priceRange[0] !== 0) paramsArr.push(`min_price=${priceRange[0]}`);
      if (priceRange[1] !== 500000)
        paramsArr.push(`max_price=${priceRange[1]}`);

      // Only add duration range if it's different from initial state
      if (nightsRange[0] !== 0)
        paramsArr.push(`min_duration=${nightsRange[0]}`);
      if (nightsRange[1] !== 50)
        paramsArr.push(`max_duration=${nightsRange[1]}`);

      if (sortByValue) paramsArr.push(`ordering=${sortByValue}`);
      if (selectedMonthsNumbers.length)
        paramsArr.push(`visiting_months=${selectedMonthsNumbers.join(",")}`);

      const params = paramsArr.length ? `?${paramsArr.join("&")}` : "";

      try {
        const res = await dispatch(fetchExplorePackages(params));
        const { data, message, status } = res?.payload?.data;
        console.log(data);
        if (status === "success") {
          setPackages(data?.packages);
        } else {
          ToastNotifyError(message);
        }
      } catch (err) {
        console.log(err);
      } finally {
        setTimeout(() => {
          setPackagesLoading(false);
        }, 1000);
      }
    };
    getPackages();
  }, [
    sortBy,
    selectedCategories,
    selectedActivities,
    selectedMonths,
    priceRange,
    nightsRange,
    priceRangeError,
  ]);

  const handleCloseFilters = () => {
    setFiltersOpen(false);
  };

  const handleApplyFilters = () => {
    if (mobilePriceRangeError.min || mobilePriceRangeError.max) {
      ToastNotifyError(
        "Please fix the price range errors before applying filters."
      );
      return;
    }
    setSelectedCategories(mobileSelectedCategories);
    setSelectedActivities(mobileSelectedActivities);
    setSelectedMonths(mobileSelectedMonths);
    setPriceRange(mobilePriceRange);
    setNightsRange(mobileNightsRange);
    setFiltersOpen(false);
  };

  const handleOpenFilters = () => {
    setFiltersOpen(true);
  };

  const handleMobileSliderChange = (event, newValue) => {
    setMobilePriceRange(newValue);
  };

  const handleMobileMinInputChange = (e) => {
    const value = Number(e.target.value.replace(/[^0-9]/g, ""));
    setMobilePriceRange([value, mobilePriceRange[1]]);
    // Validation
    if (value < minPrice) {
      setMobilePriceRangeError((prev) => ({ ...prev, min: true }));
      ToastNotifyError("Minimum value should be ₹5,000 or higher");
    } else if (value > mobilePriceRange[1]) {
      setMobilePriceRangeError((prev) => ({ ...prev, min: true }));
      ToastNotifyError("Minimum value cannot be greater than maximum value");
    } else {
      setMobilePriceRangeError((prev) => ({ ...prev, min: false }));
    }
  };

  const handleMobileMaxInputChange = (e) => {
    const value = Number(e.target.value.replace(/[^0-9]/g, ""));
    setMobilePriceRange([mobilePriceRange[0], value]);
    // Validation
    if (value > maxPrice) {
      setMobilePriceRangeError((prev) => ({ ...prev, max: true }));
      ToastNotifyError("Maximum value should be ₹5,00,000 or lower");
    } else if (value < mobilePriceRange[0]) {
      setMobilePriceRangeError((prev) => ({ ...prev, max: true }));
      ToastNotifyError("Maximum value cannot be less than minimum value");
    } else {
      setMobilePriceRangeError((prev) => ({ ...prev, max: false }));
    }
  };

  const getActivitiesByDestination = async () => {
    if (!externalid) return;
    setActivitiesLoading(true);
    const params = `?destination_id=${externalid || ""}`;
    try {
      const res = await dispatch(fetchExploreActivitiesByDestination(params));
      const { data, message, status } = res?.payload?.data;
      if (status === "success") {
        setDestinationActivities(data?.activities);
      } else {
        ToastNotifyError(message);
      }
    } catch (err) {
      console.log(err);
    } finally {
      setActivitiesLoading(false);
    }
  };
  const renderImage = (mediaKey) => {
    try {
      return generateCloudFrontURL({
        bucket: process.env.REACT_APP_BUCKET_NAME,
        key: mediaKey,
        width: 400,
        height: 250,
        fit: "cover",
      });
    } catch (error) {
      console.error("Error generating CloudFront URL:", error);
      return mediaKey; // Fallback to original key
    }
  };

  const scrollContainerRef = useRef(null);

  useEffect(() => {
    if (location?.state?.hasVisibilityScroll) {
      scrollContainerRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [location?.state?.hasVisibilityScroll]);

  useEffect(() => {
    getCategories();
    getActivities();
    getActivitiesByDestination();
  }, []);

  // Preselect filter based on title
  useEffect(() => {
    if (title && (categories.length > 0 || activities.length > 0)) {
      // Find matching category and preselect it
      const matchingCategory = categories.find(
        (category) => category.title?.toLowerCase() === title?.toLowerCase()
      );
      if (matchingCategory) {
        setSelectedCategories([matchingCategory.external_id]);
        setMobileSelectedCategories([matchingCategory.external_id]);
      }
    }
  }, [title, categories, activities]);

  const handleMobileClearAll = () => {
    setMobileSelectedCategories([]);
    setMobileSelectedActivities([]);
    setMobileSelectedMonths([]);
    setMobilePriceRange([minPrice, maxPrice]);
    setMobileNightsRange([minNights, maxNights]);
  };

  const openChat = (id) => {
    if (window?.userlike) {
      console.log("userlike", window.userlike);
      // const getPath = window.location.pathname;
      // const text = `Hi there! 👋 I'm on the explore package page — ${getPath}. \n I want to to know more about this package id: "${id}". \n Please check the package.`;

      // Open the chat widget
      window.userlike.userlikeStartChat();
    }
  };

  return (
    <StyledPakagePage>
      <Box className="content-wrapper">
        <Box className="header-container">
          <Box className="overlay-container" />
          <Box className="title-container" sx={{ zIndex: 3 }}>
            <Typography className="title">
              Explore by Zuumm - Discover Places & Plan Your Holidays!
            </Typography>
          </Box>
          <Box className="ai-box">
            <Box className="ai-box-title">
              <Typography className="header-ai-title">
                Not sure what to explore? Zippy is here to help you.
              </Typography>
            </Box>
            <Box
              className="ai-box-title-btn"
              sx={{ cursor: "pointer" }}
              onClick={handlePlanWithAI}
            >
              <Typography className="header-ai-title-btn">
                Plan with AI
              </Typography>
            </Box>
          </Box>

          <Box className="ai-bot-icon">
            <img src={BotIcon} alt="ai-search" className="ai-bot-icon-img" />
          </Box>
        </Box>
      </Box>
      <Box
        ref={scrollContainerRef}
        sx={{ height: "0px", width: "1px", visibility: "hidden" }}
      />
      <Box
        sx={{ position: "sticky", top: "0px", zIndex: "1000" }}
        className="filter-container"
      >
        <Box className="selected-filter-container">
          <BreadCrumb
            crumbs={[
              { label: "Home", path: "/" },
              { label: "Explore", path: "/explore" },
              // {
              //   label: prevTitle,
              //   path: "",
              //   isLast: true,
              // },
              // {
              //   label: title,
              //   path: "",
              //   isLast: true,
              // },
              {
                label: "Packages",
                path: "",
                isLast: true,
              },
            ]}
          />
        </Box>
        <Box className="select-fliter-container">
          <Box className="select-fliter-container-wrapper">
            <Typography className="select-fliter">Filters</Typography>
            <Box className="filters-container">
              <Box
                className={`filter-textfield-container ${isCategoriesOpen ? "open" : ""}`}
                onClick={() => handleFilterClick("categories")}
              >
                <Typography className="filter-title">Categories</Typography>
                <Box
                  className="dropdown-icon-container"
                  style={{
                    transform: isCategoriesOpen
                      ? "rotate(180deg)"
                      : "rotate(0deg)",
                  }}
                >
                  <img
                    src={dropdownIcon}
                    alt="dropdown"
                    className="dropdown-icon"
                  />
                </Box>
              </Box>
              <Box
                className={`filter-textfield-container ${isActivitiesOpen ? "open" : ""}`}
                onClick={() => handleFilterClick("activities")}
              >
                <Typography className="filter-title">Activities</Typography>
                <Box
                  className="dropdown-icon-container"
                  style={{
                    transform: isActivitiesOpen
                      ? "rotate(180deg)"
                      : "rotate(0deg)",
                  }}
                >
                  <img
                    src={dropdownIcon}
                    alt="dropdown"
                    className="dropdown-icon"
                  />
                </Box>
              </Box>
              <Box
                className={`filter-textfield-container ${isVisitingMonthOpen ? "open" : ""}`}
                sx={{ width: "120px !important" }}
                onClick={() => handleFilterClick("visitingMonth")}
              >
                <Typography className="filter-title">Visiting month</Typography>
                <Box
                  className="dropdown-icon-container"
                  style={{
                    transform: isVisitingMonthOpen
                      ? "rotate(180deg)"
                      : "rotate(0deg)",
                  }}
                >
                  <img
                    src={dropdownIcon}
                    alt="dropdown"
                    className="dropdown-icon"
                  />
                </Box>
              </Box>
              <Box
                className={`filter-textfield-container ${isPriceRangeOpen ? "open" : ""}`}
                sx={{ width: "100px !important" }}
                onClick={() => handleFilterClick("priceRange")}
              >
                <Typography className="filter-title">Price Range</Typography>
                <Box
                  className="dropdown-icon-container"
                  style={{
                    transform: isPriceRangeOpen
                      ? "rotate(180deg)"
                      : "rotate(0deg)",
                  }}
                >
                  <img
                    src={dropdownIcon}
                    alt="dropdown"
                    className="dropdown-icon"
                  />
                </Box>
              </Box>
              <Box
                className={`filter-textfield-container ${isDurationOpen ? "open" : ""}`}
                sx={{ width: "80px !important" }}
                onClick={() => handleFilterClick("duration")}
              >
                <Typography className="filter-title">Duration</Typography>
                <Box
                  className="dropdown-icon-container"
                  style={{
                    transform: isDurationOpen
                      ? "rotate(180deg)"
                      : "rotate(0deg)",
                  }}
                >
                  <img
                    src={dropdownIcon}
                    alt="dropdown"
                    className="dropdown-icon"
                  />
                </Box>
              </Box>
              {(selectedCategories.length > 0 ||
                selectedActivities.length > 0 ||
                selectedMonths.length > 0 ||
                priceRange[0] !== minPrice ||
                priceRange[1] !== maxPrice ||
                nightsRange[0] !== minNights ||
                nightsRange[1] !== maxNights) && (
                <Box
                  className="filter-cross-textfield-container"
                  onClick={handleClearFilters}
                  style={{ cursor: "pointer" }}
                >
                  <Typography className="filter-cross-title">
                    Clear Filters
                  </Typography>
                  <Box className="dropdown-icon-container">
                    <CancelOutlinedIcon style={{ fontSize: "16px" }} />
                  </Box>
                </Box>
              )}
            </Box>
          </Box>
        </Box>
        {(isCategoriesOpen ||
          isActivitiesOpen ||
          isVisitingMonthOpen ||
          isPriceRangeOpen ||
          isDurationOpen) && (
          <Box className="filter-categories-container">
            {isCategoriesOpen &&
              categories?.map((item, index) => (
                <Box
                  className="filter-categories-container-item"
                  key={item?.external_id || index}
                >
                  <Checkbox
                    size="small"
                    className="filter-checkbox"
                    checked={selectedCategories.includes(item?.external_id)}
                    onChange={() => {
                      setSelectedCategories((prev) =>
                        prev.includes(item?.external_id)
                          ? prev.filter((v) => v !== item?.external_id)
                          : [...prev, item?.external_id]
                      );
                    }}
                  />
                  <Typography className="filter-categories-container-item-text">
                    {item?.title}
                  </Typography>
                </Box>
              ))}
            {isActivitiesOpen &&
              activities?.map((item, index) => (
                <Box
                  className="filter-categories-container-item"
                  key={item?.external_id || index}
                >
                  <Checkbox
                    size="small"
                    className="filter-checkbox"
                    checked={selectedActivities.includes(item?.external_id)}
                    onChange={() => {
                      setSelectedActivities((prev) =>
                        prev.includes(item?.external_id)
                          ? prev.filter((v) => v !== item?.external_id)
                          : [...prev, item?.external_id]
                      );
                    }}
                  />
                  <Typography className="filter-categories-container-item-text">
                    {item?.title}
                  </Typography>
                </Box>
              ))}
            {isVisitingMonthOpen && (
              <>
                {monthList?.map((item, index) => (
                  <Box className="filter-categories-container-item" key={index}>
                    <Checkbox
                      size="small"
                      className="filter-checkbox"
                      checked={selectedMonths.includes(item.value)}
                      onChange={() => {
                        setSelectedMonths((prev) =>
                          prev.includes(item.value)
                            ? prev.filter((v) => v !== item.value)
                            : [...prev, item.value]
                        );
                      }}
                    />
                    <Typography className="filter-categories-container-item-text">
                      {item?.title}
                    </Typography>
                  </Box>
                ))}
              </>
            )}
            {isPriceRangeOpen && (
              <Box className="price-range-container-wrapper">
                <Typography className="price-range-container-left-title">
                  Price range
                </Typography>
                <Box className="price-range-container">
                  <Box className="price-range-container-left">
                    <Box className="price-range-container-left-slider">
                      <Box className="slider-points">
                        <Typography className="price-range-container-left-slider-title">
                          ₹0
                        </Typography>
                        <Typography className="price-range-container-left-slider-title">
                          ₹5L
                        </Typography>
                      </Box>
                      <Slider
                        className="price-range-slider"
                        value={priceRange}
                        onChange={handleSliderChange}
                        min={minPrice}
                        max={maxPrice}
                        step={1}
                      />
                    </Box>
                  </Box>
                  <Box className="price-range-container-right">
                    <TextField
                      className="price-range-input"
                      value={priceRange[0]}
                      onChange={handleMinInputChange}
                      placeholder="Min"
                      size="small"
                      InputProps={{
                        startAdornment: (
                          <span style={{ color: "#666", marginRight: "4px" }}>
                            ₹
                          </span>
                        ),
                      }}
                      inputProps={{
                        min: minPrice,
                        max: priceRange[1],
                        type: "number",
                        style: {
                          WebkitAppearance: "none",
                          MozAppearance: "textfield",
                        },
                      }}
                      error={priceRangeError.min}
                      sx={{
                        "& .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline":
                          {
                            borderColor: "#1E3A8A",
                          },
                        "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline":
                          {
                            borderColor: "#1E3A8A",
                          },
                        "& .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline":
                          {
                            borderColor: "#1E3A8A",
                          },
                      }}
                    />
                    <TextField
                      className="price-range-input"
                      value={priceRange[1]}
                      onChange={handleMaxInputChange}
                      placeholder="Max"
                      size="small"
                      InputProps={{
                        startAdornment: (
                          <span style={{ color: "#666", marginRight: "4px" }}>
                            ₹
                          </span>
                        ),
                      }}
                      inputProps={{
                        min: priceRange[0],
                        max: maxPrice,
                        type: "number",
                        style: {
                          WebkitAppearance: "none",
                          MozAppearance: "textfield",
                        },
                      }}
                      error={priceRangeError.max}
                      sx={{
                        "& .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline":
                          {
                            borderColor: "#1E3A8A",
                          },
                        "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline":
                          {
                            borderColor: "#1E3A8A",
                          },
                        "& .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline":
                          {
                            borderColor: "#1E3A8A",
                          },
                      }}
                    />
                  </Box>
                </Box>
              </Box>
            )}
            {isDurationOpen && (
              <Box className="date-range-container-wrapper">
                <Box className="price-range-container-left">
                  <Typography className="price-range-container-left-title">
                    Duration (in Nights)
                  </Typography>
                  <Box className="price-range-container-left-slider">
                    <Box className="slider-points">
                      <Typography className="price-range-container-left-slider-title">{`${nightsRange[0]} N`}</Typography>
                      <Typography className="price-range-container-left-slider-title">{`${nightsRange[1]} N`}</Typography>
                    </Box>
                    <Slider
                      className="price-range-slider"
                      value={nightsRange}
                      onChange={(e, newValue) => setNightsRange(newValue)}
                      min={minNights}
                      max={maxNights}
                      step={1}
                    />
                  </Box>
                </Box>
              </Box>
            )}
          </Box>
        )}
        {(selectedCategories.length > 0 ||
          selectedActivities.length > 0 ||
          selectedMonths.length > 0 ||
          priceRange[0] !== minPrice ||
          priceRange[1] !== maxPrice ||
          nightsRange[0] !== minNights ||
          nightsRange[1] !== maxNights) && (
          <Box className="all-selected-filters-container">
            {selectedCategories.map((id) => {
              const cat = categories.find((c) => c.external_id === id);
              return cat ? (
                <Box className="all-selected-filters-container-item" key={id}>
                  <span className="all-selected-filters-container-item-text">
                    {cat.title}
                  </span>
                  <CancelOutlinedIcon
                    style={{
                      cursor: "pointer",
                      fontSize: 16,
                      marginLeft: 4,
                      color: "#FFFFFF",
                    }}
                    onClick={() =>
                      setSelectedCategories((prev) =>
                        prev.filter((v) => v !== id)
                      )
                    }
                  />
                </Box>
              ) : null;
            })}

            {/* Selected Activities */}
            {selectedActivities.map((id) => {
              const act = activities.find((a) => a.external_id === id);
              return act ? (
                <Box className="all-selected-filters-container-item" key={id}>
                  <span className="all-selected-filters-container-item-text">
                    {act.title}
                  </span>
                  <CancelOutlinedIcon
                    style={{
                      cursor: "pointer",
                      fontSize: 16,
                      marginLeft: 4,
                      color: "#FFFFFF",
                    }}
                    onClick={() =>
                      setSelectedActivities((prev) =>
                        prev.filter((v) => v !== id)
                      )
                    }
                  />
                </Box>
              ) : null;
            })}

            {/* Selected Months */}
            {selectedMonths.map((val) => {
              const month = monthList.find((m) => m.value === val);
              return month ? (
                <Box className="all-selected-filters-container-item" key={val}>
                  <span className="all-selected-filters-container-item-text">
                    {month.title}
                  </span>
                  <CancelOutlinedIcon
                    style={{
                      cursor: "pointer",
                      fontSize: 16,
                      marginLeft: 4,
                      color: "#FFFFFF",
                    }}
                    onClick={() =>
                      setSelectedMonths((prev) => prev.filter((v) => v !== val))
                    }
                  />
                </Box>
              ) : null;
            })}

            {/* Price Range */}
            {(priceRange[0] !== minPrice || priceRange[1] !== maxPrice) && (
              <Box
                className="all-selected-filters-container-item"
                key="priceRange"
              >
                <span className="all-selected-filters-container-item-text">
                  ₹{priceRange[0]} - ₹{priceRange[1]}
                </span>
                <CancelOutlinedIcon
                  style={{
                    cursor: "pointer",
                    fontSize: 16,
                    marginLeft: 4,
                    color: "#FFFFFF",
                  }}
                  onClick={() => setPriceRange([minPrice, maxPrice])}
                />
              </Box>
            )}

            {/* Nights Range */}
            {(nightsRange[0] !== minNights || nightsRange[1] !== maxNights) && (
              <Box
                className="all-selected-filters-container-item"
                key="nightsRange"
              >
                <span className="all-selected-filters-container-item-text">
                  {nightsRange[0]}N - {nightsRange[1]}N
                </span>
                <CancelOutlinedIcon
                  style={{
                    cursor: "pointer",
                    fontSize: 16,
                    marginLeft: 4,
                    color: "#FFFFFF",
                  }}
                  onClick={() => setNightsRange([minNights, maxNights])}
                />
              </Box>
            )}
          </Box>
        )}
      </Box>
      <Box className="container-wrapper">
        <Box className="package-title">
          <Typography className="title-text">Packages</Typography>
          <Box className="package-title-btn">
            <Typography className="package-title-btn-text">Sort By</Typography>
            <Box className="package-title-btn-select">
              <CustomSelect
                value={sortBy}
                options={[
                  { value: "priceLowToHigh", label: "Price - Low to High" },
                  { value: "priceHighToLow", label: "Price - High to Low" },
                ]}
                onChange={(e) => setSortBy(e.target.value)}
                customBG="#FFF9F9"
                customFontSize="14px"
                customFontWeight="500"
              />
            </Box>
          </Box>
        </Box>
        <Box className="package-list-container">
          {packagesLoading ? (
            <>
              {[...Array(3)].map((_, index) => (
                <PackageSkeleton key={index} />
              ))}
            </>
          ) : packages?.length > 0 ? (
            packages?.map((item, index) => (
              <Box
                className="package-list-container-item"
                sx={{
                  cursor: "pointer",
                }}
                key={index}
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/package-details/${item.external_id}`);
                }}
              >
                <Box className="package-list-container-item-img">
                  <img
                    src={renderImage(item?.media?.[0]?.file)}
                    alt="destination"
                  />
                  <Box className="package-list-container-item-img-text">
                    <Typography className="package-list-container-item-img-text-title">
                      {item?.duration}
                    </Typography>
                  </Box>
                </Box>
                <Box className="package-list-container-item-text">
                  <Box className="package-list-container-item-text-title-container">
                    <Box
                      sx={{
                        display: "inline",
                      }}
                    >
                      <Typography
                        component={"span"}
                        className="package-list-container-item-text-title"
                        sx={{ marginRight: 1 }}
                      >
                        {item?.title || ""}
                      </Typography>
                      <Box
                        component={"span"}
                        sx={{
                          height: "24px",
                        }}
                      >
                        <Box
                          component={"span"}
                          sx={{
                            position: "relative",
                            display: "inline-block",
                          }}
                        >
                          {/* <Rating
                            value={5}
                            readOnly
                            icon={
                              <StarIcon
                                sx={{ color: "#DCFFE6", fontSize: 16 }}
                              />
                            }
                            sx={{
                              position: "absolute",
                              left: 0,
                              top: 0,
                              pointerEvents: "none",
                            }}
                          /> */}
                          <Box
                            component={"span"}
                            sx={{
                              width: getPercent(item?.rating),
                              overflow: "hidden",
                              position: "relative",
                              display: "inline-block",
                              verticalAlign: "middle",
                            }}
                          >
                            <Rating
                              value={5}
                              readOnly
                              icon={
                                <StarIcon
                                  sx={{ color: "#00AF31", fontSize: 16 }}
                                />
                              }
                              emptyIcon={
                                <StarIcon
                                  sx={{ color: "green", fontSize: 16 }}
                                />
                              }
                              sx={{ pointerEvents: "none" }}
                            />
                          </Box>
                        </Box>
                      </Box>
                    </Box>

                    <Box className="package-divider" />
                    <Box
                      sx={{
                        fontSize: 12,
                        fontWeight: 500,
                        color: "#007E7D",
                      }}
                    >
                      Key Inclusions
                    </Box>
                    <Box className="package-list-container-item-text-list">
                      {item?.inclusions?.slice(0, 5).map((item, index) => (
                        <Box
                          className="package-list-container-item-text-list-item"
                          key={index}
                        >
                          <img src={smallTickIcon} alt="small-tick" />
                          <Typography className="package-price-text-value-discount-text">
                            {item?.value && item.value.length > 125
                              ? `${item.value.substring(0, 125)}...`
                              : item?.value}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </Box>
                  <Box
                    className="btn-wrapper"
                    sx={{
                      boxSizing: "border-box",
                      padding: "16px 27px",
                      alignItems: "center",
                      justifyContent: "space-between",
                      gap: "16px",
                    }}
                  >
                    {/* <Button
                      className="btn-primary"
                      onClick={() =>
                        navigate(`/package-details/${item.external_id}`)
                      }
                    >
                      View Details
                    </Button> */}
                    <Box className="package-price">
                      <Typography className="package-price-text">
                        Starting from
                      </Typography>
                      <Typography className="package-price-text-value">
                        {`₹${formatIndianNumber(
                          Math.round(item?.starting_price || 0)
                        )}`}
                        <span className="package-price-text-value-discount">
                          /per person
                        </span>
                      </Typography>
                    </Box>
                    <Button
                      className="btn-secondary"
                      onClick={(e) => {
                        e.stopPropagation();
                        openChat(item.external_id);
                      }}
                    >
                      Talk To An Expert
                    </Button>
                  </Box>
                </Box>
              </Box>
            ))
          ) : (
            <Box className="destionations-list-item-no-data">
              <Box className="logo-animation">
                <Lottie
                  animationData={noDataAnimation}
                  loop
                  autoplay
                  style={{ height: "286px", width: "286px" }}
                />
              </Box>
              <Typography className="destionations-list-item-text-title-no-data">
                No Result Found!
              </Typography>
            </Box>
          )}
        </Box>
        {(destinationActivities?.length > 0 || activitiesLoading) && (
          <Box className="activities-container-1">
            <Typography className="title-text">
              Activities in{" "}
              <span style={{ textTransform: "capitalize" }}>{title}</span>
            </Typography>
            <Box className="activities-list">
              {activitiesLoading ? (
                <>
                  {[...Array(6)].map((_, index) => (
                    <PackageDestinationSkeleton key={index} />
                  ))}
                </>
              ) : (
                destinationActivities?.length > 0 &&
                destinationActivities?.map((item, index) => (
                  <Box className="activities-list-item" key={index}>
                    <Box className="activities-list-item-img">
                      <img
                        src={renderImage(item?.media?.[0]?.media)}
                        alt="activity"
                      />
                      <Box className="activities-list-item-img-text">
                        {item?.title}
                      </Box>
                    </Box>
                    <Box className="activities-list-item-overlay"></Box>
                  </Box>
                ))
              )}
            </Box>
          </Box>
        )}
      </Box>
      {isMobile ? (
        <Box className="mobile-select-container">
          <Box
            className="mobile-select-item"
            onClick={() => setMobileSortOpen(true)}
          >
            <img src={sortIcon} alt="sort" className="mobile-select-icon" />
            <Typography className="mobile-select-text">Sort By</Typography>
          </Box>
          <hr className="mobile-select-hr" />
          <Box className="mobile-select-item" onClick={handleOpenFilters}>
            <img src={filterIcon} alt="filter" className="mobile-select-icon" />
            <Typography className="mobile-select-text">Filter</Typography>
          </Box>
        </Box>
      ) : null}
      <Drawer
        open={filtersOpen}
        onClose={handleCloseFilters}
        anchor="left"
        sx={{
          "& .MuiDrawer-paper": {
            width: "100%",
            background: "white",
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            height: "100%",
          },
        }}
      >
        <Box className="mobile-select-drawer-header">
          <Typography className="mobile-select-drawer-title">Filter</Typography>
          <Typography
            className="mobile-select-drawer-clear-all"
            onClick={handleMobileClearAll}
          >
            Clear All
          </Typography>
        </Box>
        <Box className="mobile-select-drawer-content">
          <Box className="drawer-select-filter">
            {[
              "Categories",
              "Activities",
              "Visiting Month",
              "Price Range",
              "Duration",
            ].map((filter) => (
              <Box
                key={filter}
                className="drawer-select-filter-item"
                sx={{
                  borderBottom:
                    filter === "Duration" ? "1px solid #FFF9F9" : undefined,
                  cursor: "pointer",
                }}
                onClick={() => setSelectedMobileFilter(filter)}
              >
                <Typography
                  className="drawer-select-filter-item-title"
                  sx={{
                    color:
                      selectedMobileFilter === filter
                        ? "#FF3951 !important"
                        : "#706E75 !important",
                    fontWeight: selectedMobileFilter === filter ? 500 : 400,
                  }}
                >
                  {filter}
                </Typography>
              </Box>
            ))}
          </Box>
          <Box className="drawer-Choose-filter">
            {selectedMobileFilter === "Categories" &&
              categories?.map((item, index) => (
                <Box
                  className="drawer-Choose-filter-item"
                  key={item.external_id || index}
                >
                  <Checkbox
                    size="small"
                    sx={{
                      color: "#4CAF50",
                      "&.Mui-checked": { color: "#4CAF50" },
                    }}
                    checked={mobileSelectedCategories.includes(
                      item.external_id
                    )}
                    onChange={() => {
                      setMobileSelectedCategories((prev) =>
                        prev.includes(item.external_id)
                          ? prev.filter((v) => v !== item.external_id)
                          : [...prev, item.external_id]
                      );
                    }}
                  />
                  <Typography className="drawer-select-filter-item-title2">
                    {item?.title}
                  </Typography>
                </Box>
              ))}
            {selectedMobileFilter === "Activities" &&
              activities?.map((item, index) => (
                <Box
                  className="drawer-Choose-filter-item"
                  key={item.external_id || index}
                >
                  <Checkbox
                    size="small"
                    sx={{
                      color: "#4CAF50",
                      "&.Mui-checked": { color: "#4CAF50" },
                    }}
                    checked={mobileSelectedActivities.includes(
                      item.external_id
                    )}
                    onChange={() => {
                      setMobileSelectedActivities((prev) =>
                        prev.includes(item.external_id)
                          ? prev.filter((v) => v !== item.external_id)
                          : [...prev, item.external_id]
                      );
                    }}
                  />
                  <Typography className="drawer-select-filter-item-title2">
                    {item?.title}
                  </Typography>
                </Box>
              ))}
            {selectedMobileFilter === "Visiting Month" && (
              <>
                {monthList?.map((item, index) => (
                  <Box className="drawer-Choose-filter-item" key={index}>
                    <Checkbox
                      size="small"
                      sx={{
                        color: "#4CAF50",
                        "&.Mui-checked": { color: "#4CAF50" },
                      }}
                      checked={mobileSelectedMonths.includes(item.value)}
                      onChange={() => {
                        setMobileSelectedMonths((prev) =>
                          prev.includes(item.value)
                            ? prev.filter((v) => v !== item.value)
                            : [...prev, item.value]
                        );
                      }}
                    />
                    <Typography className="drawer-select-filter-item-title2">
                      {item?.title}
                    </Typography>
                  </Box>
                ))}
              </>
            )}
            {selectedMobileFilter === "Price Range" && (
              <Box className="drawer-Choose-filter-item">
                <Box className="mobile-price-range-container">
                  <Box className="price-range-container-left">
                    <Box className="price-range-container-left-slider">
                      <Box className="mobile-slider-points">
                        <Typography className="price-range-container-left-slider-title">
                          ₹0
                        </Typography>
                        <Typography className="price-range-container-left-slider-title">
                          ₹5L
                        </Typography>
                      </Box>
                      <Slider
                        className="price-range-slider"
                        value={mobilePriceRange}
                        onChange={handleMobileSliderChange}
                        min={minPrice}
                        max={maxPrice}
                        step={1}
                      />
                    </Box>
                  </Box>
                  <Box className="mobile-price-range-container-right">
                    <TextField
                      className="price-range-input"
                      value={mobilePriceRange[0]}
                      onChange={handleMobileMinInputChange}
                      placeholder="Min"
                      size="small"
                      error={mobilePriceRangeError.min}
                      InputProps={{
                        startAdornment: (
                          <span style={{ color: "#666", marginRight: "4px" }}>
                            ₹
                          </span>
                        ),
                      }}
                      inputProps={{
                        min: minPrice,
                        max: mobilePriceRange[1],
                        type: "number",
                        style: {
                          WebkitAppearance: "none",
                          MozAppearance: "textfield",
                        },
                      }}
                    />
                    <TextField
                      className="price-range-input"
                      value={mobilePriceRange[1]}
                      onChange={handleMobileMaxInputChange}
                      placeholder="Max"
                      size="small"
                      error={mobilePriceRangeError.max}
                      InputProps={{
                        startAdornment: (
                          <span style={{ color: "#666", marginRight: "4px" }}>
                            ₹
                          </span>
                        ),
                      }}
                      inputProps={{
                        min: mobilePriceRange[0],
                        max: maxPrice,
                        type: "number",
                        style: {
                          WebkitAppearance: "none",
                          MozAppearance: "textfield",
                        },
                      }}
                    />
                  </Box>
                </Box>
              </Box>
            )}
            {selectedMobileFilter === "Duration" && (
              <Box className="drawer-Choose-filter-item">
                <Box className="mobile-date-range-container">
                  <Box className="mobile-date-range-container-left">
                    <Typography className="price-range-container-left-title">
                      Duration (in Nights)
                    </Typography>
                    <Box className="price-range-container-left-slider">
                      <Box className="mobile-slider-points">
                        <Typography className="price-range-container-left-slider-title">{`${mobileNightsRange[0]} N`}</Typography>
                        <Typography className="price-range-container-left-slider-title">{`${mobileNightsRange[1]} N`}</Typography>
                      </Box>
                      <Slider
                        className="price-range-slider"
                        value={mobileNightsRange}
                        onChange={(e, newValue) =>
                          setMobileNightsRange(newValue)
                        }
                        min={minNights}
                        max={maxNights}
                        step={1}
                      />
                    </Box>
                  </Box>
                </Box>
              </Box>
            )}
          </Box>
        </Box>
        <Box className="drawer-footer">
          <Box className="drawer-footer-item" onClick={handleCloseFilters}>
            <Typography
              className="drawer-footer-text"
              sx={{ color: "#706E75" }}
            >
              Close
            </Typography>
          </Box>
          <hr className="mobile-select-hr" />
          <Box className="drawer-footer-item" onClick={handleApplyFilters}>
            <Typography
              className="drawer-footer-text"
              sx={{ color: "#1E3A8A" }}
            >
              Apply
            </Typography>
          </Box>
        </Box>
      </Drawer>
      <Drawer
        open={mobileSortOpen}
        onClose={handleCloseSort}
        anchor="bottom"
        sx={{
          "& .MuiDrawer-paper": {
            width: "100%",
            background: "#FFF9F9",
            display: "flex",
            flexDirection: "column",
            height: "20%",
            overflow: "hidden",
          },
        }}
      >
        <Box className="mobile-sort-drawer-header">
          <Typography className="mobile-sort-drawer-title">Sort By</Typography>
          <img
            src={closeIcon}
            alt="close"
            className="mobile-sort-drawer-close"
            onClick={handleCloseSort}
          />
        </Box>
        <hr className="mobile-sort-drawer-hr" />
        <Box className="mobile-sort-drawer-content-container">
          <Box
            className="mobile-sort-drawer-content-item"
            onClick={() => {
              setSortBy("priceLowToHigh");
              setMobileSortOpen(false);
            }}
          >
            <Typography
              className="mobile-sort-drawer-content-item-title"
              sx={{
                color:
                  sortBy === "priceLowToHigh"
                    ? "#FF3951 !important"
                    : "#333333",
                fontWeight: sortBy === "priceLowToHigh" ? 600 : 400,
              }}
            >
              Price - Low to High
            </Typography>
          </Box>
          <Box
            className="mobile-sort-drawer-content-item"
            onClick={() => {
              setSortBy("priceHighToLow");
              setMobileSortOpen(false);
            }}
          >
            <Typography
              className="mobile-sort-drawer-content-item-title"
              sx={{
                color:
                  sortBy === "priceHighToLow"
                    ? "#FF3951 !important"
                    : "#333333",
                fontWeight: sortBy === "priceHighToLow" ? 600 : 400,
              }}
            >
              Price - High to Low
            </Typography>
          </Box>
        </Box>
      </Drawer>
      {openOnboardingModal && (
        <OnboardingModal
          open={openOnboardingModal}
          onClose={handleCloseOnboardingModal}
          onSuccess={() => {
            navigate("/ai-chat");
          }}
        />
      )}
    </StyledPakagePage>
  );
};

export default SelectedPakagePage;
