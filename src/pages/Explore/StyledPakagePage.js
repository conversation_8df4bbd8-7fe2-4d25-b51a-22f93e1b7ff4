import styled from "@emotion/styled";
import exploreHeaderBg from "../../assets/png/destination-pakage-Bg.png";

const StyledPakagePage = styled("div")(
  ({ theme }) => `
   padding: 0px 0;
 background: #FFFFFF;

 .content-wrapper {
   max-width: 100%;
   margin: 0 auto;
   text-align: center;
   }
   .header-container {
     position: relative;
     display: flex;
     flex-direction: column;
     justify-content: center;
     align-items: center;
     background: url(${exploreHeaderBg}) no-repeat center top;
     background-size: cover;
     min-height: 400px;
     position: relative;
     padding: 70px 0;
     z-index: 1;
   }
   .overlay-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(55, 55, 55, 0) 20.6%, #323232 100%);
    z-index: 2;
    pointer-events: none;
   }
   .title {
     font-family: 'Lora', serif;
     font-size: 56px;
     font-weight: 500;
     color: #FFFFFF;
     line-height: 63px;
     max-width: 900px;
     margin: 0 auto;
     text-align: center;
     margin-bottom: 30px;
     margin-top: 40px;
     z-index: 3;
   }  
   .ai-box {
     display: flex;
     justify-content: space-between;
     align-items: center;
     background: #FFFFFF;
     border-radius: 8px 0px 0px 8px;
     height: 48px;
     width: 45%;
     padding: 0 0px 0px 20px;
     z-index: 3;
   }
   .ai-box-title {
     font-family: 'Roboto', sans-serif;
     font-size: 16px;
     font-weight: 500;
     color: ${theme.palette.secondary.main};
     line-height: 24px;
     z-index: 3;
   }
   .ai-bot-icon-img {
     width: 180px;
     height: 194px;
     z-index: 3;
   }
   .ai-box-title-btn {
     font-family: 'Roboto', sans-serif;
     font-size: 16px;
     font-weight: 500;
     color: ${theme.palette.custom.white};
     line-height: 100%;
     letter-spacing: 0.05em;
     background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
     height: 100%;
     width: 30%;
     justify-content: center;
     align-items: center;
     display: flex;
     z-index: 3;

   }
   .ai-bot-icon {
     position: absolute;
     bottom: 0;
     right: 15%;
     display: flex;
     justify-content: center;
     align-items: center;
     z-index: 3;
   }
   .filter-container {
     width: 100%;
     height: auto;
    }
   .selected-filter-container {
     height: 48px;
     padding: 0 50px;
     background: ${theme.palette.secondary.main};
     display: flex;
     align-items: center;
   }
   .selected-filter {
     font-family: 'Roboto', sans-serif;
     font-size: 16px;
     font-weight: 500;
     color: ${theme.palette.custom.white};
     line-height: 130%;
     letter-spacing: 0.05em;
     text-transform: capitalize;
   }
   .select-fliter-container {
     padding: 10px 50px;
     background: #FFF9F9;
     border-bottom: 1px solid var(--Color-Icon-BG, #FF39510D);
     border-radius: 0px 8px;
   }  
   .select-fliter-container-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
   }  
   .select-fliter {
     font-family: 'Roboto', sans-serif;
     font-size: 24px;
     font-weight: 500;
     color: ${theme.palette.secondary.main};
     line-height: 32px;
     letter-spacing: 0.05em;
     text-transform: capitalize;
   }  
   .filters-container {
    display: flex;
    align-items: center;
    gap: 10px;
   }  
   .filter-textfield-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 92px;
    background: white;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid var(--Color-Icon-BG, #FF39510D);
    cursor: pointer;
   }
   .filter-textfield-container.open {
    border: 1px solid #FF3951;
   }
   .filter-cross-textfield-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #F6F6F6;
    width: 100px;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
   }
   .filter-title {
    font-family: 'Roboto', sans-serif;
    font-size: 15px;
    font-weight: 400;
    color: ${theme.palette.custom.placeholder};
    line-height: 100%;
    letter-spacing: 0;
   } 
   .filter-cross-title {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: ${theme.palette.custom.subText};
    line-height: 100%;
    letter-spacing: 0;
   } 
   .dropdown-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
    width: 12px;
    height: 12px;
    }
   }
  .filter-categories-container {
    padding: 10px 40px;
    background: #FFF9F9;
    padding-bottom: 20px;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    max-height: 150px;
    overflow-y: auto;
  }  
  .filter-categories-container-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    width: fit-content;
    height: fit-content;
  }
  .filter-categories-container-item-text {
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    font-weight: 400;
    color: ${theme.palette.custom.subText};
    line-height: 100%;
    letter-spacing: 0;
    padding-right: 5px;
    text-transform: capitalize;
  }

  .filter-checkbox {
    .MuiCheckbox-root {
      padding: 0px;
      color: #4CAF50;
    }
    .MuiCheckbox-root.Mui-checked {
      padding: 0px;
      color: #4CAF50;
    }
    
    .MuiCheckbox-root:hover {
      background-color: rgba(76, 175, 80, 0.04);
    }
  }

  .MuiCheckbox-root {
    padding: 0px;
    color: #4CAF50;
  }
  
  .MuiCheckbox-root.Mui-checked {
    padding: 0px;
    color: #4CAF50;
  }
  
  .MuiCheckbox-root:hover {
    padding: 0px;
    background-color: rgba(76, 175, 80, 0.04);
  }

  .price-range-container-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .price-range-container {
    width: 50%;
    display: flex;
    align-items: center;
    gap: 20px;
  }
  .date-range-container-wrapper {
    width: 30%;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .price-range-container-left {
    width: 100%;
  }
  .price-range-container-left-title {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: 600;
    color: ${theme.palette.secondary.main};
    line-height: 17px;
    letter-spacing: 0;
  }
  .price-range-container-left-slider {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0px;
    border-radius: 4px;
    padding: 10px 0px !important;

    .MuiSlider-thumb {
      background: linear-gradient(#fff, #fff) padding-box,
      linear-gradient(90deg, #FF3951 0%, #5530F9 100%) border-box;
      border: 2px solid transparent; 
      width: 20px;
      height: 20px;
    }
    .MuiSlider-rail {
      background: #FFB6C1;
      opacity: 1;
    }
    .MuiSlider-track {
      background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
      border: 0px;
    }
  }

    .slider-points {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .price-range-container-left-slider-title {
      font-family: 'Roboto', sans-serif;
      font-size: 14px;
      font-weight: 700;
      color: ${theme.palette.secondary.main};
      line-height: 17px;
      letter-spacing: 0;
    }
  }
  .price-range-container-right {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .price-range-input {
    width: 120px;
    margin-left: 16px;
    background: #fff;
    border-radius: 8px;
  } 
   .container-wrapper{
     margin: 30px 0px;
     padding: 0 50px 24px;
   }
   .package-title {
     display: flex;
     justify-content: space-between;
     align-items: center;
   }
   .title-text {
     font-family: 'Lora', serif;
     font-size: 24px;
     font-weight: 700;
     color: ${theme.palette.secondary.main};
     line-height: 45px;
     margin-bottom: 10px;
   }
   .package-title-btn {
     display: flex;
     gap: 10px;
     align-items: center;
     min-width: 300px;
   }
   .package-title-btn-text {
     font-family: 'Roboto', sans-serif;
     font-size: 16px;
     font-weight: 500;
     line-height: 100%;
     color: ${theme.palette.secondary.main};
     min-width: 67px;
   } 
   .package-title-btn-select {
     width: 100%;
     height: 44px;
   }  
   .package-list-container {
     position: relative;
     margin-top: 24px;
     width: 100%;
     display: flex;
     flex-direction: row;
     flex-wrap: wrap;
     gap: 16px;
     padding: 0;
     box-sizing: border-box;
   }  
   .package-list-container-item {
     flex: 0 0 calc((100% - 32px) / 3);
     width: 100%;
    //  height: 100%;
     display: flex;
     flex-direction: column;
     justify-content: flex-start;
     align-items: flex-start;
     padding: 0;
     border-radius: 12px;
     box-sizing: border-box;
     box-shadow: 0px 4px 8px 0px #0000000D;
   }
   .package-list-container-item-img {
     position: relative;
     width: 100%;
     height: 250px !important;
     min-height: 250px;
     overflow: hidden;
     box-sizing: border-box;
     display: block;
     border-radius: 12px 12px 0px 0px;
   }
   .package-list-container-item-img img {
     width: 100%;
     height: 100%;
     min-height: 250px;
     object-fit: cover;
     display: block;
     border-radius: 12px 12px 0px 0px;
     position: absolute;
     top: 0;
     left: 0;
   }
   .package-list-container-item-img-text {
     position: absolute;
     top: 16px;
     left: 16px;
     width: fit-content;
     height: fit-content;
     background: #FFFFFF;
     padding: 8px 22px;
     border-radius: 4px;
   }
   .package-list-container-item-img-text-title {
     font-family: 'Roboto', sans-serif;
     font-size: 14px;
     font-weight: 500;
     color: ${theme.palette.secondary.main};
     line-height: 120%;
     letter-spacing: 0;
     padding: 8px 22px:
   }
   .package-list-container-item-text {
    flex: 1;
     width: 100%;
     height: 100%;
     display: flex;
    flex-direction: column;
    justify-content: space-between;
   }  
   .package-list-container-item-text-title-container {
     height: 100%;
     padding: 16px 20px;
     display: flex;
     flex-direction: column;
     gap: 12px;
   }  
   .package-list-container-item-text-title {
     font-family: 'Roboto', sans-serif;
     font-size: 20px;
     font-weight: 700;
     color: ${theme.palette.custom.subText};
     line-height: 130%;
     letter-spacing: 0.05em;
     text-transform: capitalize;
     word-break: break-word;
   }  
   .package-price {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0px;
   }  
   .package-price-text {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: ${theme.palette.custom.subText};
    line-height: 132%;
    letter-spacing: 0;
   }  
   .package-price-text-value {
    font-family: 'Roboto', sans-serif;
    font-size: 20px;
    font-weight: 600;
    color: ${theme.palette.secondary.main};
    line-height: 132%;
    letter-spacing: 0;
   }  
   .package-price-text-value-discount {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: ${theme.palette.secondary.main};
    line-height: 132%;
    letter-spacing: 0;
   }  
   .package-divider {
     width: 100%;
     border-top: 1px dashed #706E75;
     height: 0;
     background: none;
   } 
   .package-list-container-item-text-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
   }  
  .all-selected-filters-container {
    padding: 20px 40px;
    background: #FFF9F9;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    border-top: 1px solid #FF39510D;
  }
  .all-selected-filters-container-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 4px;
    background:#1E3A8A;
    cursor: pointer;
    border: 1px solid #1E3A8A;
    color: #FFFFFF;
  }  
  .all-selected-filters-container-item-text {
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 100%;
    text-transform: capitalize;
  }
  .destionations-list-item-no-data {
    width: 100%;
    height: 400px;
    display: flex;
    flex-direction: column;
    gap: 0px;
    align-items: center;
    justify-content: center;
  }  
  .destionations-list-item-text-title-no-data {
    font-family: 'Lora', serif;
    font-size: 28px;
    font-weight: 700;
    color: ${theme.palette.secondary.main};
    line-height: 45px;
    letter-spacing: 0;
    text-transform: capitalize;
  }  
  .package-list-container-item-text-list-item {
    display: flex;
    align-items: flex-start;
    gap: 3px;
  }  
  .package-price-text-value-discount-text {
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    font-weight: 400;
    color: #007E7D;
    line-height: 132%;
    letter-spacing: 0;
    text-transform: capitalize;
  }  
  .btn-wrapper {
    width: 100%;
    background: #FFF9F9;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 0px 0px 12px 12px;
  }   
  .btn-primary {
    margin: 16px 0px 16px 28px;
    width: 100%;
    height: 44px;
    border: 1px solid #FF3951;
    border-radius: 4px;
    background: white;
    color: #FF3951;
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: 500;
    line-height: 100%;
    letter-spacing: 0.05em;
    text-transform: capitalize;
  }  
  .btn-secondary {
    width: 100%;
    max-width: 184px;
    height: 44px;
    background: #FF3951;
    border-radius: 4px;
    color: white;
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: 500;
    line-height: 100%;
    letter-spacing: 0.05em;
    text-transform: capitalize;
  }  
     .activities-list {
     display: flex;
     flex-direction: row;
     flex-wrap: wrap;
     gap: 17px;
   }  
   .activities-list-item {
     width: calc((100% - 52px) / 4);
     height: 240px;
     border-radius: 12px;
     position: relative;
     .activities-list-item-img {
       width: 100%;
       height: 100%;
     }
     img {
       width: 100%;
       height: 100%;
       object-fit: cover;
       border-radius: 12px;
     }
   }   
   .activities-list-item-overlay {
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background: linear-gradient(180deg, rgba(5, 7, 60, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
     border-radius: 12px;
     z-index: 2;
   }
   .activities-list-item-img-text {
     position: absolute;
     bottom: 12px;
     left: 15px;
     font-family: 'Roboto', sans-serif;
     font-size: 18px;
     font-weight: 600;
     color: ${theme.palette.custom.white};
     line-height: 30px;
     letter-spacing: 0;
     z-index: 3;
     text-transform: capitalize;
   }  
   .mobile-filter-container {
    display: none;
   }
    .activities-container-1 {
      margin-top: 60px;
      margin-bottom: 24px;
    }

   @media (max-width: 1024px) {
    .package-list-container-item {
      flex: 0 0 calc((100% - 17px) / 2);
    }
      .title {
        font-size: 40px;
        margin-bottom: 20px;
        max-width: 450px;
      }
      .ai-box {
        height: 35px;
      }
      .header-ai-title{
        font-size: 10px !important;
      }
      .header-ai-title-btn {
        font-size: 10px;
      }
      .ai-bot-icon-img {
        width: 125px;
        height: auto;
      }
   }

     @media (max-width: 768px) {
     .activities-container-1 {
      margin-top: 100px;
     }
     .package-list-container-item {
      flex: 0 0 calc((100% - 32px) / 2) !important;
     }
    .filter-container {
      display: none;
    }
    .package-title-btn {
      display: none;
    }  
      .header-container {
        padding: 30px 0;
      }
      .title {
        font-size: 26px;
        max-width: 300px;
        line-height: 35px;
      } 

      .ai-box {
        flex-direction: column;
        padding: 12px 6px;
        gap: 5px;
        border-radius: 4px !important;
        max-width: 170px;
        height: 75px;
        width: 160px;
      }
      .ai-bot-icon-img {
        width: 110px !important;
        height: auto !important;
      }
      .ai-bot-icon {
        right: 4% !important;
      }  
      .header-ai-title {
        font-size: 12px !important;
        font-weight: 500;
        line-height: 16px;
        max-width: 150px;
      }
        .ai-box-title-btn {
          width: 105px;
          height: 30px;
          border-radius: 4px;
        }
      .header-ai-title-btn {
        font-size: 10px;
      }
      .container-wrapper {
        margin: 16px 0px;
        padding: 0 20px 24px;
      }  
      .title-text {
        font-size: 20px !important;
        line-height: 100% !important;
        margin-bottom: 0px !important;
      }  
      .package-container-wrapper {
        margin-top: 16px !important;
      }  
      .package-list-container {
        flex-direction: column;
        gap: 16px;
      }  
      .activities-list {
        flex-direction: column;
        gap: 16px;
        margin-top: 16px;
      }  
      .activities-list-item {
        width: 100% !important;
      }  
      .mobile-select-container {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 32px;
        display: flex;
        align-items: center;
        padding: 10px 60px;
        gap: 10px;
        justify-content: center;
        background: #FFF9F9;
        z-index: 1000;
      } 
      .mobile-select-hr {
        width: 10%;
        border: 1px solid #FF39510D;
        rotate: 90deg;
        background: #FF39510D;
      }  
      .mobile-select-item {
        display: flex;
        align-items: center;
        gap: 10px;
      } 
      .mobile-select-text {
        font-family: 'Roboto', sans-serif;
        font-size: 16px;
        font-weight: 500;
        color: ${theme.palette.secondary.main};
        line-height: 32px;
        letter-spacing: 0.05em;
      }  

  }  
      
 `
);

export default StyledPakagePage;