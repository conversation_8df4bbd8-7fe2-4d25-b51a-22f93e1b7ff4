import React, { useEffect, useState } from "react";
import { <PERSON>, Grid, <PERSON>, Tabs, Tab } from "@mui/material";
import Header from "../../components/Header";
import Footer from "../../components/Footer";

const DocumentHub = () => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Technical documentation sections
  const technicalSections = [
    {
      title: "Chat SDK",
      description: "Pre-built chat widget for websites and mobile apps",
      content: {
        overview: "ZUUMM Chat SDK provides a pre-built chat package that customers can integrate into their websites or mobile applications. The chat widget connects users directly with ZUUMM's AI travel assistant for instant travel assistance and booking support.",
        features: [
          "Pre-built chat interface",
          "AI-powered travel assistant",
          "Real-time travel recommendations",
          "Booking assistance and support",
          "Customizable chat appearance",
          "Multi-platform support (Web & Mobile)"
        ],
        examples: [
          {
            title: "Web SDK - JavaScript Integration",
            code: `// Install ZUUMM Chat SDK
npm install @zuumm/chat-sdk

// Initialize Chat Widget
import { ZuummChat } from '@zuumm/chat-sdk';

const chatWidget = new ZuummChat({
  apiKey: 'your-api-key',
  environment: 'production', // or 'sandbox'
  customization: {
    primaryColor: '#1E3A8A',
    position: 'bottom-right',
    welcomeMessage: 'Hi! How can I help you plan your trip?',
    avatar: 'https://yoursite.com/avatar.png'
  }
});

// Initialize the chat widget
chatWidget.init();

// Show chat widget
chatWidget.show();

// Hide chat widget
chatWidget.hide();

// Listen to chat events
chatWidget.on('chatStarted', (data) => {
  console.log('User started chat:', data);
});

chatWidget.on('bookingInitiated', (data) => {
  console.log('User initiated booking:', data);
});`
          },
          {
            title: "Mobile SDK - React Native",
            code: `// Install ZUUMM Chat SDK for React Native
npm install @zuumm/react-native-chat-sdk

// iOS Setup (ios/Podfile)
pod 'ZuummChatSDK'

// Android Setup (android/app/build.gradle)
implementation 'com.zuumm:chat-sdk:1.0.0'

// Initialize SDK
import { ZuummChatSDK } from '@zuumm/react-native-chat-sdk';

const App = () => {
  useEffect(() => {
    ZuummChatSDK.initialize({
      apiKey: 'your-api-key',
      environment: 'production',
      theme: {
        primaryColor: '#1E3A8A',
        chatBubbleColor: '#FF3951'
      }
    });
  }, []);

  const openChat = () => {
    ZuummChatSDK.presentChat({
      welcomeMessage: 'Welcome! Let me help you plan your perfect trip.',
      onChatStarted: (data) => {
        console.log('Chat started:', data);
      },
      onBookingRequested: (data) => {
        console.log('Booking requested:', data);
      }
    });
  };

  return (
    <TouchableOpacity onPress={openChat}>
      <Text>Chat with Travel Assistant</Text>
    </TouchableOpacity>
  );
};`
          }
        ]
      }
    },
    {
      title: "Travel APIs",
      description: "Comprehensive travel industry APIs",
      content: {
        overview: "ZUUMM's REST APIs provide complete access to travel industry data and booking capabilities. Build custom travel applications with our comprehensive set of endpoints covering all aspects of travel planning and booking.",
        features: [
          "Categories and destinations",
          "Activities and experiences",
          "Travel packages and deals",
          "Booking management",
          "Booking reviews and ratings",
          "User authentication and login",
          "Payment processing",
          "Real-time availability"
        ],
        examples: [
          {
            title: "Authentication & Login API",
            code: `// User Login
POST https://api.zuumm.ai/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

// Response
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "name": "John Doe"
  },
  "expires_in": 3600
}

// Use token in subsequent requests
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
          },
          {
            title: "Categories & Destinations API",
            code: `// Get travel categories
GET https://api.zuumm.ai/v1/categories
Authorization: Bearer your-access-token

// Response
{
  "categories": [
    {
      "id": "cat_001",
      "name": "Adventure",
      "description": "Thrilling adventure activities",
      "image": "https://images.zuumm.ai/adventure.jpg"
    },
    {
      "id": "cat_002",
      "name": "Beach",
      "description": "Relaxing beach destinations",
      "image": "https://images.zuumm.ai/beach.jpg"
    }
  ]
}

// Get destinations by category
GET https://api.zuumm.ai/v1/destinations?category_id=cat_001
Authorization: Bearer your-access-token

// Response
{
  "destinations": [
    {
      "id": "dest_123",
      "name": "Manali, India",
      "category_id": "cat_001",
      "description": "Adventure capital of Himachal Pradesh",
      "image": "https://images.zuumm.ai/manali.jpg",
      "coordinates": {
        "latitude": 32.2396,
        "longitude": 77.1887
      }
    }
  ]
}`
          },
          {
            title: "Packages & Booking API",
            code: `// Get travel packages
GET https://api.zuumm.ai/v1/packages?destination_id=dest_123
Authorization: Bearer your-access-token

// Response
{
  "packages": [
    {
      "id": "pkg_456",
      "name": "Manali Adventure Package",
      "destination_id": "dest_123",
      "duration": "5 days 4 nights",
      "price": {
        "amount": 25000,
        "currency": "INR"
      },
      "inclusions": ["Hotel", "Meals", "Activities", "Transport"],
      "activities": ["River Rafting", "Paragliding", "Trekking"]
    }
  ]
}

// Create booking
POST https://api.zuumm.ai/v1/bookings
Authorization: Bearer your-access-token
Content-Type: application/json

{
  "package_id": "pkg_456",
  "travelers": 2,
  "travel_date": "2025-04-15",
  "contact": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+91-9876543210"
  }
}

// Response
{
  "booking_id": "book_789",
  "status": "confirmed",
  "total_amount": 50000,
  "payment_url": "https://payments.zuumm.ai/book_789"
}`
          }
        ]
      }
    },
    {
      title: "Chat Iframe & One-Pager",
      description: "Embeddable chat widget and branded travel pages",
      content: {
        overview: "ZUUMM offers two white-label solutions: an embeddable chat iframe for websites and complete one-pager travel websites. The one-pager displays travel categories and packages with integrated chat functionality, hosted on ZUUMM subdomains and routed to your custom domain with full branding customization.",
        features: [
          "Embeddable chat iframe for any website",
          "One-pager with categories and packages",
          "Integrated AI chat for travel assistance",
          "Custom domain mapping from ZUUMM subdomain",
          "Complete brand customization",
          "Responsive design for all devices",
          "SEO optimized pages"
        ],
        examples: [
          {
            title: "Chat Iframe Integration",
            code: `<!-- Embed ZUUMM chat widget in your website -->
<iframe
  src="https://chat.zuumm.ai/embed?partner_id=your-partner-id&theme=custom"
  width="400px"
  height="600px"
  frameborder="0"
  style="border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); position: fixed; bottom: 20px; right: 20px; z-index: 1000;"
  allow="microphone; camera">
</iframe>

<!-- Responsive chat iframe -->
<iframe
  id="zuumm-chat"
  src="https://chat.zuumm.ai/embed?partner_id=your-partner-id&welcome_msg=Hi! How can I help you plan your trip?"
  width="100%"
  height="500px"
  frameborder="0"
  style="max-width: 400px; border-radius: 12px;">
</iframe>

<!-- JavaScript for chat events -->
<script>
  window.addEventListener('message', function(event) {
    if (event.origin === 'https://chat.zuumm.ai') {
      if (event.data.type === 'chatStarted') {
        console.log('User started chat');
      }
      if (event.data.type === 'bookingRequested') {
        console.log('User requested booking:', event.data.details);
      }
    }
  });
</script>`
          },
          {
            title: "One-Pager Configuration",
            code: `// One-pager website configuration
{
  "partner_id": "your-partner-id",
  "subdomain": "yourcompany.zuumm.ai",
  "custom_domain": "travel.yourcompany.com",
  "branding": {
    "company_name": "Your Travel Company",
    "logo_url": "https://yoursite.com/logo.png",
    "primary_color": "#FF6B35",
    "secondary_color": "#1E3A8A",
    "font_family": "Montserrat",
    "favicon_url": "https://yoursite.com/favicon.ico"
  },
  "content": {
    "hero_title": "Discover Amazing Travel Experiences",
    "hero_subtitle": "Chat with our AI assistant to plan your perfect trip",
    "categories": ["Adventure", "Beach", "Cultural", "Wildlife"],
    "featured_packages": true,
    "show_chat_widget": true
  },
  "features": {
    "show_zuumm_branding": false,
    "enable_package_browsing": true,
    "enable_chat_booking": true,
    "payment_integration": true
  },
  "seo": {
    "meta_title": "Travel Packages - Your Travel Company",
    "meta_description": "Discover amazing travel packages with AI-powered assistance",
    "keywords": ["travel", "packages", "vacation", "booking"]
  }
}`
          },
          {
            title: "Domain Mapping & Styling",
            code: `/* DNS Configuration for Custom Domain */
// CNAME Record
travel.yourcompany.com -> yourcompany.zuumm.ai

/* Custom CSS for one-pager styling */
:root {
  --brand-primary: #FF6B35;
  --brand-secondary: #1E3A8A;
  --brand-font: 'Montserrat', sans-serif;
}

/* Header customization */
.travel-header {
  background: white;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.travel-logo {
  max-height: 50px;
  width: auto;
}

/* Category cards */
.category-card {
  border-radius: 12px;
  transition: transform 0.3s ease;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.category-card:hover {
  transform: translateY(-5px);
}

/* Chat widget positioning */
.chat-widget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 350px;
  height: 500px;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  z-index: 1000;
}

/* Package grid */
.packages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  padding: 24px;
}`
          }
        ]
      }
    }
  ];

  // Scroll to top on component mount
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  return (
    <div
      style={{
        background: "#FFFFFF",
        position: "relative",
      }}
    >
      <div style={{ width: "100%" }}>
        <Header />
      </div>

      {/* Hero Section */}
      <Box
        sx={{
          pt: { xs: "80px", md: "136px" },
          pb: { xs: 3, md: 4 },
          position: "relative",
          px: { xs: 2.5, md: 10 },
        }}
      >
        <Box sx={{ textAlign: "center", maxWidth: "700px", mx: "auto" }}>
          <Box
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontWeight: 700,
              fontSize: { xs: "16px", md: "20px" },
              lineHeight: "100%",
              letterSpacing: "0.2em",
              textTransform: "uppercase",
              color: "#FF3951",
              mb: 2,
              pt: { xs: 3, md: 5 },
            }}
          >
            Developer Resources
          </Box>
          <Box
            sx={{
              fontFamily: "Lora, serif",
              fontWeight: 600,
              fontSize: { xs: "28px", md: "42px" },
              lineHeight: { xs: "36px", md: "52px" },
              color: "#1E3A8A",
              mb: 2,
            }}
          >
            <span
              style={{
                background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                color: "transparent",
                display: "inline-block",
              }}
            >
              Integrate ZUUMM
            </span>{" "}
            with Ease
          </Box>
          <Box
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontWeight: 400,
              fontSize: { xs: "14px", md: "16px" },
              lineHeight: "150%",
              color: "#333333",
              maxWidth: "500px",
              mx: "auto",
            }}
          >
            Choose your preferred integration method and get started quickly.
          </Box>
        </Box>
      </Box>

      {/* Technical Documentation Section */}
      <Box
        sx={{
          py: { xs: 2, md: 3 },
          px: { xs: 2.5, md: 10 },
          background: "#FFF9F9",
        }}
      >


        {/* Navigation Tabs */}
        <Box sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            centered
            sx={{
              "& .MuiTab-root": {
                fontFamily: "Roboto, sans-serif",
                fontWeight: 500,
                fontSize: { xs: "14px", md: "16px" },
                textTransform: "none",
                color: "#333333",
                minHeight: "48px",
                "&.Mui-selected": {
                  color: "#1E3A8A",
                  fontWeight: 600,
                },
              },
              "& .MuiTabs-indicator": {
                backgroundColor: "#FF3951",
                height: "3px",
              },
            }}
          >
            {technicalSections.map((section, index) => (
              <Tab key={index} label={section.title} />
            ))}
          </Tabs>
        </Box>

        {/* Content for Active Tab */}
        <Box>
          {technicalSections.map((section, index) => (
            <Box key={index} sx={{ display: activeTab === index ? "block" : "none" }}>
              {/* Section Header */}
              <Box sx={{ textAlign: "center", mb: { xs: 3, md: 4 } }}>
                <Box
                  sx={{
                    fontFamily: "Lora, serif",
                    fontWeight: 600,
                    fontSize: { xs: "24px", md: "28px" },
                    lineHeight: { xs: "30px", md: "36px" },
                    color: "#1E3A8A",
                    mb: 2,
                  }}
                >
                  {section.title}
                </Box>
                <Box
                  sx={{
                    fontFamily: "Roboto, sans-serif",
                    fontSize: { xs: "14px", md: "16px" },
                    color: "#333333",
                    maxWidth: "600px",
                    mx: "auto",
                    lineHeight: "150%",
                    letterSpacing: "0.05em",
                  }}
                >
                  {section.description}
                </Box>
              </Box>

              {/* Overview */}
              <Box
                sx={{
                  background: "#FFFFFF",
                  borderRadius: "12px",
                  padding: { xs: "20px", md: "24px" },
                  boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
                  mb: 4,
                }}
              >
                <Box
                  sx={{
                    fontFamily: "Lora, serif",
                    fontWeight: 600,
                    fontSize: "20px",
                    lineHeight: "24px",
                    color: "#1E3A8A",
                    mb: 2,
                  }}
                >
                  Overview
                </Box>
                <Box
                  sx={{
                    fontFamily: "Roboto, sans-serif",
                    fontSize: "14px",
                    color: "#333333",
                    lineHeight: "20px",
                    mb: 3,
                  }}
                >
                  {section.content.overview}
                </Box>

                <Box
                  sx={{
                    fontFamily: "Lora, serif",
                    fontWeight: 600,
                    fontSize: "18px",
                    color: "#1E3A8A",
                    mb: 2,
                  }}
                >
                  Key Features
                </Box>
                <Box component="ul" sx={{ pl: 3, m: 0 }}>
                  {section.content.features.map((feature, featureIndex) => (
                    <Box
                      component="li"
                      key={featureIndex}
                      sx={{
                        fontFamily: "Roboto, sans-serif",
                        fontSize: "14px",
                        color: "#333333",
                        lineHeight: "20px",
                        mb: 1,
                      }}
                    >
                      {feature}
                    </Box>
                  ))}
                </Box>
              </Box>

              {/* Code Examples */}
              <Grid container spacing={3}>
                {section.content.examples.map((example, exampleIndex) => (
                  <Grid item xs={12} key={exampleIndex}>
                    <Box
                      sx={{
                        background: "#FFFFFF",
                        borderRadius: "12px",
                        boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
                        overflow: "hidden",
                        height: "500px", // Fixed height for all code boxes
                        display: "flex",
                        flexDirection: "column",
                      }}
                    >
                      <Box
                        sx={{
                          p: { xs: 2, md: 3 },
                          borderBottom: "1px solid #CFD6DC",
                          background: "#FFFFFF",
                          flexShrink: 0, // Prevent header from shrinking
                        }}
                      >
                        <Box
                          sx={{
                            fontFamily: "Lora, serif",
                            fontWeight: 600,
                            fontSize: "18px",
                            color: "#1E3A8A",
                          }}
                        >
                          {example.title}
                        </Box>
                      </Box>
                      <Box
                        sx={{
                          backgroundColor: "#00243D",
                          color: "#FFFFFF",
                          p: { xs: 2, md: 3 },
                          overflow: "auto",
                          flex: 1, // Take remaining space
                        }}
                      >
                        <Box
                          component="pre"
                          sx={{
                            fontFamily: "Monaco, Consolas, 'Courier New', monospace",
                            fontSize: { xs: "12px", md: "13px" },
                            lineHeight: "18px",
                            whiteSpace: "pre-wrap",
                            wordBreak: "break-word",
                            margin: 0,
                            color: "#FFFFFF",
                          }}
                        >
                          {example.code}
                        </Box>
                      </Box>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>
          ))}
        </Box>
      </Box>

      {/* Developer Support Section */}
      <Box
        sx={{
          py: { xs: 4, md: 6 },
          px: { xs: 2.5, md: 10 },
          background: "#FFFFFF",
        }}
      >
        <Box
          sx={{
            maxWidth: "1200px",
            margin: "0 auto",
            padding: { xs: "20px", md: "40px 20px" },
            textAlign: "center",
            boxShadow: "0px 12px 35px 0px #64748C1F",
            borderRadius: "10px",
            background: "#FFFFFF",
          }}
        >
          <Box
            sx={{
              fontFamily: "Lora, serif",
              fontWeight: 700,
              fontSize: { xs: "24px", md: "28px" },
              color: "#1E3A8A",
              mb: 1,
              lineHeight: "40px",
            }}
          >
            Need Developer Support?
          </Box>
          <Box
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontSize: "14px",
              color: "#333333",
              mb: 4,
              lineHeight: "150%",
              maxWidth: "600px",
              mx: "auto",
            }}
          >
            Our technical team is ready to help you integrate ZUUMM's platform. Get assistance with SDK implementation, API integration, or white-label customization.
          </Box>
          <Box sx={{ display: "flex", justifyContent: "center" }}>
            <Link
              href="mailto:<EMAIL>"
              sx={{
                display: "inline-block",
                px: { xs: 3, md: 4 },
                py: { xs: 1.5, md: 2 },
                backgroundColor: "#1E3A8A",
                color: "white",
                textDecoration: "none",
                borderRadius: "8px",
                fontFamily: "Roboto, sans-serif",
                fontWeight: 500,
                fontSize: { xs: "14px", md: "16px" },
                transition: "all 0.2s ease-in-out",
                "&:hover": {
                  backgroundColor: "#153B50",
                  transform: "translateY(-2px)",
                },
              }}
            >
              Contact Developers
            </Link>
          </Box>
        </Box>
      </Box>

      <Footer />
    </div>
  );
};

export default DocumentHub;
