import React, { useEffect } from "react";
import { Box, Typography, Grid, Card, CardContent, Link } from "@mui/material";
import Header from "../../components/Header";
import Footer from "../../components/Footer";
import { ReactComponent as DocumentIcon } from "../../assets/svg/logo.svg"; // Using logo as placeholder

const DocumentHub = () => {
  // Sample document categories and documents
  const documentCategories = [
    {
      title: "Travel Policies",
      description: "Guidelines and policies for travel bookings and management",
      documents: [
        {
          title: "Corporate Travel Policy",
          description: "Comprehensive guidelines for business travel arrangements",
          link: "#",
          type: "PDF"
        },
        {
          title: "Expense Reimbursement Guidelines",
          description: "How to claim and process travel-related expenses",
          link: "#",
          type: "PDF"
        },
        {
          title: "Travel Safety Protocols",
          description: "Safety measures and emergency procedures for travelers",
          link: "#",
          type: "PDF"
        }
      ]
    },
    {
      title: "User Guides",
      description: "Step-by-step guides to help you navigate our platform",
      documents: [
        {
          title: "Getting Started with ZUUMM",
          description: "A beginner's guide to using our travel platform",
          link: "#",
          type: "Guide"
        },
        {
          title: "Booking Management Tutorial",
          description: "Learn how to manage your travel bookings effectively",
          link: "#",
          type: "Video"
        },
        {
          title: "AI Assistant User Manual",
          description: "Maximize your experience with our AI travel assistant",
          link: "#",
          type: "Guide"
        }
      ]
    },
    {
      title: "Legal Documents",
      description: "Important legal information and compliance documents",
      documents: [
        {
          title: "Service Agreement",
          description: "Terms of service for using ZUUMM platform",
          link: "#",
          type: "PDF"
        },
        {
          title: "Data Processing Agreement",
          description: "How we handle and protect your personal data",
          link: "#",
          type: "PDF"
        },
        {
          title: "Compliance Certificates",
          description: "Industry certifications and compliance documentation",
          link: "#",
          type: "PDF"
        }
      ]
    },
    {
      title: "API Documentation",
      description: "Technical documentation for developers and partners",
      documents: [
        {
          title: "API Reference Guide",
          description: "Complete API documentation for integration",
          link: "#",
          type: "Documentation"
        },
        {
          title: "SDK Installation Guide",
          description: "How to integrate ZUUMM SDK into your applications",
          link: "#",
          type: "Guide"
        },
        {
          title: "Webhook Configuration",
          description: "Setting up webhooks for real-time notifications",
          link: "#",
          type: "Documentation"
        }
      ]
    }
  ];

  // Scroll to top on component mount
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  const getTypeColor = (type) => {
    switch (type) {
      case "PDF":
        return "#FF3951";
      case "Guide":
        return "#1E3A8A";
      case "Video":
        return "#10B981";
      case "Documentation":
        return "#8B5CF6";
      default:
        return "#6B7280";
    }
  };

  return (
    <div
      style={{
        background: "#FFFFFF",
        position: "relative",
      }}
    >
      <div style={{ width: "100%" }}>
        <Header />
      </div>

      {/* Hero Section */}
      <Box
        sx={{
          pt: { xs: "80px", md: "136px" },
          pb: { xs: 4, md: 6 },
          px: { xs: 2.5, md: 10 },
          background: "linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%)",
          color: "white",
        }}
      >
        <Box sx={{ textAlign: "center", maxWidth: "800px", mx: "auto" }}>
          <Typography
            variant="h1"
            sx={{
              fontFamily: "Lora, serif",
              fontWeight: 600,
              fontSize: { xs: "32px", md: "48px" },
              lineHeight: { xs: "40px", md: "56px" },
              mb: 3,
            }}
          >
            Document Hub
          </Typography>
          <Typography
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontWeight: 400,
              fontSize: { xs: "16px", md: "18px" },
              lineHeight: { xs: "24px", md: "28px" },
              opacity: 0.9,
            }}
          >
            Access all important documents, guides, and resources in one centralized location. 
            Find everything you need to make the most of your ZUUMM experience.
          </Typography>
        </Box>
      </Box>

      {/* Document Categories Section */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          px: { xs: 2.5, md: 10 },
          background: "#F8FAFC",
        }}
      >
        <Grid container spacing={4}>
          {documentCategories.map((category, categoryIndex) => (
            <Grid item xs={12} md={6} key={categoryIndex}>
              <Card
                sx={{
                  height: "100%",
                  boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
                  borderRadius: "12px",
                  transition: "transform 0.2s ease-in-out",
                  "&:hover": {
                    transform: "translateY(-4px)",
                  },
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Typography
                    variant="h5"
                    sx={{
                      fontFamily: "Lora, serif",
                      fontWeight: 600,
                      fontSize: "24px",
                      color: "#1E3A8A",
                      mb: 2,
                    }}
                  >
                    {category.title}
                  </Typography>
                  <Typography
                    sx={{
                      fontFamily: "Roboto, sans-serif",
                      fontSize: "14px",
                      color: "#6B7280",
                      mb: 3,
                      lineHeight: "20px",
                    }}
                  >
                    {category.description}
                  </Typography>
                  
                  {/* Documents List */}
                  <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                    {category.documents.map((doc, docIndex) => (
                      <Box
                        key={docIndex}
                        sx={{
                          p: 2,
                          border: "1px solid #E5E7EB",
                          borderRadius: "8px",
                          transition: "background-color 0.2s ease-in-out",
                          "&:hover": {
                            backgroundColor: "#F3F4F6",
                          },
                        }}
                      >
                        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start", mb: 1 }}>
                          <Link
                            href={doc.link}
                            sx={{
                              fontFamily: "Roboto, sans-serif",
                              fontWeight: 500,
                              fontSize: "16px",
                              color: "#1E3A8A",
                              textDecoration: "none",
                              "&:hover": {
                                textDecoration: "underline",
                              },
                            }}
                          >
                            {doc.title}
                          </Link>
                          <Box
                            sx={{
                              px: 1.5,
                              py: 0.5,
                              backgroundColor: getTypeColor(doc.type),
                              color: "white",
                              borderRadius: "12px",
                              fontSize: "12px",
                              fontWeight: 500,
                              ml: 2,
                            }}
                          >
                            {doc.type}
                          </Box>
                        </Box>
                        <Typography
                          sx={{
                            fontFamily: "Roboto, sans-serif",
                            fontSize: "14px",
                            color: "#6B7280",
                            lineHeight: "20px",
                          }}
                        >
                          {doc.description}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Contact Section */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          px: { xs: 2.5, md: 10 },
          background: "#FFFFFF",
          textAlign: "center",
        }}
      >
        <Typography
          variant="h4"
          sx={{
            fontFamily: "Lora, serif",
            fontWeight: 600,
            fontSize: { xs: "24px", md: "32px" },
            color: "#1E3A8A",
            mb: 2,
          }}
        >
          Need Additional Support?
        </Typography>
        <Typography
          sx={{
            fontFamily: "Roboto, sans-serif",
            fontSize: "16px",
            color: "#6B7280",
            mb: 4,
            maxWidth: "600px",
            mx: "auto",
          }}
        >
          Can't find what you're looking for? Our support team is here to help you with any questions or additional documentation needs.
        </Typography>
        <Link
          href="mailto:<EMAIL>"
          sx={{
            display: "inline-block",
            px: 4,
            py: 2,
            backgroundColor: "#1E3A8A",
            color: "white",
            textDecoration: "none",
            borderRadius: "8px",
            fontFamily: "Roboto, sans-serif",
            fontWeight: 500,
            fontSize: "16px",
            transition: "background-color 0.2s ease-in-out",
            "&:hover": {
              backgroundColor: "#1E40AF",
            },
          }}
        >
          Contact Support
        </Link>
      </Box>

      <Footer />
    </div>
  );
};

export default DocumentHub;
