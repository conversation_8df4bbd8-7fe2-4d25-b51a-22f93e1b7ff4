import React, { useEffect, useState } from "react";
import { <PERSON>, Grid, <PERSON>, Tabs, Tab } from "@mui/material";
import Header from "../../components/Header";
import Footer from "../../components/Footer";

const DocumentHub = () => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Technical documentation sections
  const technicalSections = [
    {
      title: "SDK Documentation",
      description: "Web and Mobile SDKs for seamless integration",
      content: {
        overview: "ZUUMM provides comprehensive SDKs for both web and mobile platforms, enabling you to integrate our AI-powered travel booking capabilities directly into your applications.",
        features: [
          "Real-time flight and hotel search",
          "AI-powered recommendations",
          "Secure payment processing",
          "Multi-language support",
          "Customizable UI components"
        ],
        examples: [
          {
            title: "Web SDK - React Integration",
            code: `// Install ZUUMM Web SDK
npm install @zuumm/web-sdk

// Initialize SDK
import { ZuummSDK } from '@zuumm/web-sdk';

const zuumm = new ZuummSDK({
  apiKey: 'your-api-key',
  environment: 'production', // or 'sandbox'
  theme: {
    primaryColor: '#1E3A8A',
    fontFamily: 'Roboto'
  }
});

// Search flights
const searchFlights = async () => {
  const results = await zuumm.flights.search({
    origin: 'BLR',
    destination: 'DEL',
    departureDate: '2025-03-15',
    passengers: 1,
    class: 'economy'
  });
  return results;
};

// Render booking widget
<ZuummBookingWidget
  onBookingComplete={(booking) => {
    console.log('Booking completed:', booking);
  }}
  customization={{
    hideHeader: false,
    showPriceBreakdown: true
  }}
/>`
          },
          {
            title: "Mobile SDK - React Native",
            code: `// Install ZUUMM Mobile SDK
npm install @zuumm/react-native-sdk

// iOS Setup (ios/Podfile)
pod 'ZuummSDK'

// Android Setup (android/app/build.gradle)
implementation 'com.zuumm:android-sdk:1.0.0'

// Initialize SDK
import { ZuummMobileSDK } from '@zuumm/react-native-sdk';

const App = () => {
  useEffect(() => {
    ZuummMobileSDK.initialize({
      apiKey: 'your-api-key',
      environment: 'production'
    });
  }, []);

  const openBookingFlow = () => {
    ZuummMobileSDK.presentBookingFlow({
      searchParams: {
        origin: 'BLR',
        destination: 'GOA'
      },
      onComplete: (result) => {
        console.log('Booking result:', result);
      }
    });
  };

  return (
    <TouchableOpacity onPress={openBookingFlow}>
      <Text>Book Travel</Text>
    </TouchableOpacity>
  );
};`
          }
        ]
      }
    },
    {
      title: "API Documentation",
      description: "RESTful APIs for custom integrations",
      content: {
        overview: "ZUUMM's REST API provides programmatic access to our travel booking platform. Build custom integrations with our comprehensive set of endpoints.",
        features: [
          "Flight search and booking",
          "Hotel search and booking",
          "User management",
          "Booking management",
          "Payment processing",
          "Real-time notifications"
        ],
        examples: [
          {
            title: "Authentication",
            code: `// Get access token
POST https://api.zuumm.ai/v1/auth/token
Content-Type: application/json

{
  "client_id": "your-client-id",
  "client_secret": "your-client-secret",
  "grant_type": "client_credentials"
}

// Response
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600
}

// Use token in subsequent requests
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
          },
          {
            title: "Flight Search API",
            code: `// Search flights
POST https://api.zuumm.ai/v1/flights/search
Authorization: Bearer your-access-token
Content-Type: application/json

{
  "origin": "BLR",
  "destination": "DEL",
  "departure_date": "2025-03-15",
  "return_date": "2025-03-20",
  "passengers": {
    "adults": 1,
    "children": 0,
    "infants": 0
  },
  "class": "economy",
  "currency": "INR"
}

// Response
{
  "search_id": "search_123456",
  "flights": [
    {
      "id": "flight_789",
      "airline": "IndiGo",
      "flight_number": "6E-123",
      "departure": {
        "airport": "BLR",
        "time": "2025-03-15T06:30:00Z"
      },
      "arrival": {
        "airport": "DEL",
        "time": "2025-03-15T09:15:00Z"
      },
      "price": {
        "amount": 4500,
        "currency": "INR"
      },
      "duration": "2h 45m"
    }
  ]
}`
          },
          {
            title: "Hotel Search API",
            code: `// Search hotels
POST https://api.zuumm.ai/v1/hotels/search
Authorization: Bearer your-access-token
Content-Type: application/json

{
  "destination": "Goa, India",
  "check_in": "2025-03-15",
  "check_out": "2025-03-18",
  "guests": {
    "adults": 2,
    "children": 0
  },
  "rooms": 1,
  "currency": "INR"
}

// Response
{
  "search_id": "hotel_search_456",
  "hotels": [
    {
      "id": "hotel_123",
      "name": "Taj Exotica Resort & Spa",
      "rating": 5,
      "location": "Benaulim, South Goa",
      "price_per_night": {
        "amount": 12000,
        "currency": "INR"
      },
      "amenities": ["Pool", "Spa", "Beach Access", "WiFi"],
      "images": ["https://images.zuumm.ai/hotel_123_1.jpg"]
    }
  ]
}`
          }
        ]
      }
    },
    {
      title: "White Label Solutions",
      description: "One-pager websites with ZUUMM branding customization",
      content: {
        overview: "Create branded travel booking experiences with our white-label solution. Get a fully functional one-page website powered by ZUUMM's technology with your custom branding.",
        features: [
          "Custom domain support",
          "Brand customization (colors, logos, fonts)",
          "Responsive design",
          "SEO optimized",
          "Analytics integration",
          "Multi-language support"
        ],
        examples: [
          {
            title: "Iframe Integration",
            code: `<!-- Embed ZUUMM booking widget in your website -->
<iframe
  src="https://booking.zuumm.ai/embed?partner_id=your-partner-id&theme=custom"
  width="100%"
  height="600"
  frameborder="0"
  style="border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);"
  allow="payment; geolocation">
</iframe>

<!-- With custom parameters -->
<iframe
  src="https://booking.zuumm.ai/embed?partner_id=your-partner-id&origin=BLR&destination=GOA&theme=dark&lang=en"
  width="100%"
  height="700"
  frameborder="0">
</iframe>

<!-- JavaScript integration for dynamic resizing -->
<script>
  window.addEventListener('message', function(event) {
    if (event.origin === 'https://booking.zuumm.ai') {
      if (event.data.type === 'resize') {
        const iframe = document.getElementById('zuumm-booking');
        iframe.style.height = event.data.height + 'px';
      }
    }
  });
</script>`
          },
          {
            title: "White Label Configuration",
            code: `// White label configuration
{
  "partner_id": "your-partner-id",
  "branding": {
    "company_name": "Your Travel Company",
    "logo_url": "https://yoursite.com/logo.png",
    "primary_color": "#FF6B35",
    "secondary_color": "#1E3A8A",
    "font_family": "Montserrat",
    "favicon_url": "https://yoursite.com/favicon.ico"
  },
  "domain": {
    "custom_domain": "travel.yourcompany.com",
    "ssl_enabled": true
  },
  "features": {
    "show_zuumm_branding": false,
    "enable_chat_support": true,
    "payment_methods": ["card", "upi", "netbanking"],
    "currencies": ["INR", "USD", "EUR"]
  },
  "analytics": {
    "google_analytics_id": "GA-XXXXXXXXX",
    "facebook_pixel_id": "*********"
  },
  "seo": {
    "meta_title": "Book Travel - Your Travel Company",
    "meta_description": "Book flights and hotels with ease",
    "keywords": ["travel", "flights", "hotels", "booking"]
  }
}`
          },
          {
            title: "Custom CSS Styling",
            code: `/* Custom CSS for white label styling */
.zuumm-booking-widget {
  --primary-color: #FF6B35;
  --secondary-color: #1E3A8A;
  --font-family: 'Montserrat', sans-serif;
  --border-radius: 12px;
  --shadow: 0 8px 24px rgba(0,0,0,0.12);
}

/* Override default button styles */
.zuumm-btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border: none;
  border-radius: var(--border-radius);
  font-family: var(--font-family);
  font-weight: 600;
  transition: transform 0.2s ease;
}

.zuumm-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Custom header styling */
.zuumm-header {
  background: white;
  border-bottom: 1px solid #E5E7EB;
  padding: 16px 24px;
}

.zuumm-logo {
  max-height: 40px;
  width: auto;
}

/* Responsive design */
@media (max-width: 768px) {
  .zuumm-booking-widget {
    padding: 16px;
    border-radius: 0;
  }
}`
          }
        ]
      }
    }
  ];

  // Scroll to top on component mount
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  return (
    <div
      style={{
        background: "#FFFFFF",
        position: "relative",
      }}
    >
      <div style={{ width: "100%" }}>
        <Header />
      </div>

      {/* Hero Section */}
      <Box
        sx={{
          pt: { xs: "80px", md: "136px" },
          position: "relative",
          px: { xs: 2.5, md: 10 },
        }}
      >
        <Box sx={{ textAlign: "center", maxWidth: "800px", mx: "auto" }}>
          <Box
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontWeight: 700,
              fontSize: { xs: "20px", md: "24px" },
              lineHeight: "100%",
              letterSpacing: "0.25em",
              textTransform: "uppercase",
              color: "#FF3951",
              mb: 1.5,
              pt: { xs: 5, md: 9 },
            }}
          >
            Technical Documentation
          </Box>
          <Box
            sx={{
              fontFamily: "Lora, serif",
              fontWeight: 600,
              fontSize: { xs: "32px", md: "56px" },
              lineHeight: { xs: "40px", md: "72px" },
              color: "#1E3A8A",
              mb: 3,
            }}
          >
            <span
              style={{
                background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                color: "transparent",
                display: "inline-block",
              }}
            >
              SDK, APIs &
            </span>{" "}
            White Label Solutions
          </Box>
          <Box
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontWeight: 400,
              fontSize: { xs: "14px", md: "16px" },
              lineHeight: "160%",
              letterSpacing: "0.05em",
              color: "#333333",
              maxWidth: "600px",
              mx: "auto",
              mb: { xs: 4, md: 6 },
            }}
          >
            Everything developers need to integrate ZUUMM's AI-powered travel booking platform.
            From SDKs and APIs to complete white-label solutions.
          </Box>
        </Box>
      </Box>

      {/* Technical Documentation Section */}
      <Box
        sx={{
          py: { xs: 4, md: 6 },
          px: { xs: 2.5, md: 10 },
          background: "#FFF9F9",
        }}
      >
        {/* Section Title */}
        <Box sx={{ textAlign: "center", mb: { xs: 4, md: 6 } }}>
          <Box
            sx={{
              fontFamily: "Lora, serif",
              fontWeight: 700,
              fontSize: { xs: "24px", md: "32px" },
              lineHeight: { xs: "30px", md: "45px" },
              color: "#1E3A8A",
              mb: 2,
            }}
          >
            Choose Your Integration Method
          </Box>
          <Box
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontWeight: 400,
              fontSize: { xs: "14px", md: "16px" },
              lineHeight: "150%",
              letterSpacing: "0.05em",
              color: "#333333",
              maxWidth: "600px",
              mx: "auto",
            }}
          >
            Select the integration approach that best fits your development needs and technical requirements.
          </Box>
        </Box>

        {/* Navigation Tabs */}
        <Box sx={{ mb: 4 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            centered
            sx={{
              "& .MuiTab-root": {
                fontFamily: "Roboto, sans-serif",
                fontWeight: 500,
                fontSize: { xs: "14px", md: "16px" },
                textTransform: "none",
                color: "#333333",
                minHeight: "48px",
                "&.Mui-selected": {
                  color: "#1E3A8A",
                  fontWeight: 600,
                },
              },
              "& .MuiTabs-indicator": {
                backgroundColor: "#FF3951",
                height: "3px",
              },
            }}
          >
            {technicalSections.map((section, index) => (
              <Tab key={index} label={section.title} />
            ))}
          </Tabs>
        </Box>

        {/* Content for Active Tab */}
        <Box>
          {technicalSections.map((section, index) => (
            <Box key={index} sx={{ display: activeTab === index ? "block" : "none" }}>
              {/* Section Header */}
              <Box sx={{ textAlign: "center", mb: { xs: 4, md: 6 } }}>
                <Box
                  sx={{
                    fontFamily: "Lora, serif",
                    fontWeight: 600,
                    fontSize: { xs: "24px", md: "28px" },
                    lineHeight: { xs: "30px", md: "36px" },
                    color: "#1E3A8A",
                    mb: 2,
                  }}
                >
                  {section.title}
                </Box>
                <Box
                  sx={{
                    fontFamily: "Roboto, sans-serif",
                    fontSize: { xs: "14px", md: "16px" },
                    color: "#333333",
                    maxWidth: "600px",
                    mx: "auto",
                    lineHeight: "150%",
                    letterSpacing: "0.05em",
                  }}
                >
                  {section.description}
                </Box>
              </Box>

              {/* Overview */}
              <Box
                sx={{
                  background: "#FFFFFF",
                  borderRadius: "12px",
                  padding: { xs: "20px", md: "24px" },
                  boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
                  mb: 4,
                }}
              >
                <Box
                  sx={{
                    fontFamily: "Lora, serif",
                    fontWeight: 600,
                    fontSize: "20px",
                    lineHeight: "24px",
                    color: "#1E3A8A",
                    mb: 2,
                  }}
                >
                  Overview
                </Box>
                <Box
                  sx={{
                    fontFamily: "Roboto, sans-serif",
                    fontSize: "14px",
                    color: "#333333",
                    lineHeight: "20px",
                    mb: 3,
                  }}
                >
                  {section.content.overview}
                </Box>

                <Box
                  sx={{
                    fontFamily: "Lora, serif",
                    fontWeight: 600,
                    fontSize: "18px",
                    color: "#1E3A8A",
                    mb: 2,
                  }}
                >
                  Key Features
                </Box>
                <Box component="ul" sx={{ pl: 3, m: 0 }}>
                  {section.content.features.map((feature, featureIndex) => (
                    <Box
                      component="li"
                      key={featureIndex}
                      sx={{
                        fontFamily: "Roboto, sans-serif",
                        fontSize: "14px",
                        color: "#333333",
                        lineHeight: "20px",
                        mb: 1,
                      }}
                    >
                      {feature}
                    </Box>
                  ))}
                </Box>
              </Box>

              {/* Code Examples */}
              <Grid container spacing={3}>
                {section.content.examples.map((example, exampleIndex) => (
                  <Grid item xs={12} key={exampleIndex}>
                    <Box
                      sx={{
                        background: "#FFFFFF",
                        borderRadius: "12px",
                        boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
                        overflow: "hidden",
                        height: "500px", // Fixed height for all code boxes
                        display: "flex",
                        flexDirection: "column",
                      }}
                    >
                      <Box
                        sx={{
                          p: { xs: 2, md: 3 },
                          borderBottom: "1px solid #CFD6DC",
                          background: "#FFFFFF",
                          flexShrink: 0, // Prevent header from shrinking
                        }}
                      >
                        <Box
                          sx={{
                            fontFamily: "Lora, serif",
                            fontWeight: 600,
                            fontSize: "18px",
                            color: "#1E3A8A",
                          }}
                        >
                          {example.title}
                        </Box>
                      </Box>
                      <Box
                        sx={{
                          backgroundColor: "#00243D",
                          color: "#FFFFFF",
                          p: { xs: 2, md: 3 },
                          overflow: "auto",
                          flex: 1, // Take remaining space
                        }}
                      >
                        <Box
                          component="pre"
                          sx={{
                            fontFamily: "Monaco, Consolas, 'Courier New', monospace",
                            fontSize: { xs: "12px", md: "13px" },
                            lineHeight: "18px",
                            whiteSpace: "pre-wrap",
                            wordBreak: "break-word",
                            margin: 0,
                            color: "#FFFFFF",
                          }}
                        >
                          {example.code}
                        </Box>
                      </Box>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>
          ))}
        </Box>
      </Box>

      {/* Developer Support Section */}
      <Box
        sx={{
          py: { xs: 4, md: 6 },
          px: { xs: 2.5, md: 10 },
          background: "#FFFFFF",
        }}
      >
        <Box
          sx={{
            maxWidth: "1200px",
            margin: "0 auto",
            padding: { xs: "20px", md: "40px 20px" },
            textAlign: "center",
            boxShadow: "0px 12px 35px 0px #64748C1F",
            borderRadius: "10px",
            background: "#FFFFFF",
          }}
        >
          <Box
            sx={{
              fontFamily: "Lora, serif",
              fontWeight: 700,
              fontSize: { xs: "24px", md: "28px" },
              color: "#1E3A8A",
              mb: 1,
              lineHeight: "40px",
            }}
          >
            Need Developer Support?
          </Box>
          <Box
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontSize: "14px",
              color: "#333333",
              mb: 4,
              lineHeight: "150%",
              maxWidth: "600px",
              mx: "auto",
            }}
          >
            Our technical team is ready to help you integrate ZUUMM's platform. Get assistance with SDK implementation, API integration, or white-label customization.
          </Box>
          <Box sx={{ display: "flex", justifyContent: "center" }}>
            <Link
              href="mailto:<EMAIL>"
              sx={{
                display: "inline-block",
                px: { xs: 3, md: 4 },
                py: { xs: 1.5, md: 2 },
                backgroundColor: "#1E3A8A",
                color: "white",
                textDecoration: "none",
                borderRadius: "8px",
                fontFamily: "Roboto, sans-serif",
                fontWeight: 500,
                fontSize: { xs: "14px", md: "16px" },
                transition: "all 0.2s ease-in-out",
                "&:hover": {
                  backgroundColor: "#153B50",
                  transform: "translateY(-2px)",
                },
              }}
            >
              Contact Developers
            </Link>
          </Box>
        </Box>
      </Box>

      <Footer />
    </div>
  );
};

export default DocumentHub;
