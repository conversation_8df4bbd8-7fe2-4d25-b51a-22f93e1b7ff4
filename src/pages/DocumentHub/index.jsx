import React, { useEffect, useState } from "react";
import { Box, Typography, Grid, Card, CardContent, Link, Tabs, Tab, Paper } from "@mui/material";
import Header from "../../components/Header";
import Footer from "../../components/Footer";

const DocumentHub = () => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Technical documentation sections
  const technicalSections = [
    {
      title: "SDK Documentation",
      description: "Web and Mobile SDKs for seamless integration",
      content: {
        overview: "ZUUMM provides comprehensive SDKs for both web and mobile platforms, enabling you to integrate our AI-powered travel booking capabilities directly into your applications.",
        features: [
          "Real-time flight and hotel search",
          "AI-powered recommendations",
          "Secure payment processing",
          "Multi-language support",
          "Customizable UI components"
        ],
        examples: [
          {
            title: "Web SDK - React Integration",
            code: `// Install ZUUMM Web SDK
npm install @zuumm/web-sdk

// Initialize SDK
import { ZuummSDK } from '@zuumm/web-sdk';

const zuumm = new ZuummSDK({
  apiKey: 'your-api-key',
  environment: 'production', // or 'sandbox'
  theme: {
    primaryColor: '#1E3A8A',
    fontFamily: 'Roboto'
  }
});

// Search flights
const searchFlights = async () => {
  const results = await zuumm.flights.search({
    origin: 'BLR',
    destination: 'DEL',
    departureDate: '2025-03-15',
    passengers: 1,
    class: 'economy'
  });
  return results;
};

// Render booking widget
<ZuummBookingWidget
  onBookingComplete={(booking) => {
    console.log('Booking completed:', booking);
  }}
  customization={{
    hideHeader: false,
    showPriceBreakdown: true
  }}
/>`
          },
          {
            title: "Mobile SDK - React Native",
            code: `// Install ZUUMM Mobile SDK
npm install @zuumm/react-native-sdk

// iOS Setup (ios/Podfile)
pod 'ZuummSDK'

// Android Setup (android/app/build.gradle)
implementation 'com.zuumm:android-sdk:1.0.0'

// Initialize SDK
import { ZuummMobileSDK } from '@zuumm/react-native-sdk';

const App = () => {
  useEffect(() => {
    ZuummMobileSDK.initialize({
      apiKey: 'your-api-key',
      environment: 'production'
    });
  }, []);

  const openBookingFlow = () => {
    ZuummMobileSDK.presentBookingFlow({
      searchParams: {
        origin: 'BLR',
        destination: 'GOA'
      },
      onComplete: (result) => {
        console.log('Booking result:', result);
      }
    });
  };

  return (
    <TouchableOpacity onPress={openBookingFlow}>
      <Text>Book Travel</Text>
    </TouchableOpacity>
  );
};`
          }
        ]
      }
    },
    {
      title: "API Documentation",
      description: "RESTful APIs for custom integrations",
      content: {
        overview: "ZUUMM's REST API provides programmatic access to our travel booking platform. Build custom integrations with our comprehensive set of endpoints.",
        features: [
          "Flight search and booking",
          "Hotel search and booking",
          "User management",
          "Booking management",
          "Payment processing",
          "Real-time notifications"
        ],
        examples: [
          {
            title: "Authentication",
            code: `// Get access token
POST https://api.zuumm.ai/v1/auth/token
Content-Type: application/json

{
  "client_id": "your-client-id",
  "client_secret": "your-client-secret",
  "grant_type": "client_credentials"
}

// Response
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600
}

// Use token in subsequent requests
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
          },
          {
            title: "Flight Search API",
            code: `// Search flights
POST https://api.zuumm.ai/v1/flights/search
Authorization: Bearer your-access-token
Content-Type: application/json

{
  "origin": "BLR",
  "destination": "DEL",
  "departure_date": "2025-03-15",
  "return_date": "2025-03-20",
  "passengers": {
    "adults": 1,
    "children": 0,
    "infants": 0
  },
  "class": "economy",
  "currency": "INR"
}

// Response
{
  "search_id": "search_123456",
  "flights": [
    {
      "id": "flight_789",
      "airline": "IndiGo",
      "flight_number": "6E-123",
      "departure": {
        "airport": "BLR",
        "time": "2025-03-15T06:30:00Z"
      },
      "arrival": {
        "airport": "DEL",
        "time": "2025-03-15T09:15:00Z"
      },
      "price": {
        "amount": 4500,
        "currency": "INR"
      },
      "duration": "2h 45m"
    }
  ]
}`
          },
          {
            title: "Hotel Search API",
            code: `// Search hotels
POST https://api.zuumm.ai/v1/hotels/search
Authorization: Bearer your-access-token
Content-Type: application/json

{
  "destination": "Goa, India",
  "check_in": "2025-03-15",
  "check_out": "2025-03-18",
  "guests": {
    "adults": 2,
    "children": 0
  },
  "rooms": 1,
  "currency": "INR"
}

// Response
{
  "search_id": "hotel_search_456",
  "hotels": [
    {
      "id": "hotel_123",
      "name": "Taj Exotica Resort & Spa",
      "rating": 5,
      "location": "Benaulim, South Goa",
      "price_per_night": {
        "amount": 12000,
        "currency": "INR"
      },
      "amenities": ["Pool", "Spa", "Beach Access", "WiFi"],
      "images": ["https://images.zuumm.ai/hotel_123_1.jpg"]
    }
  ]
}`
          }
        ]
      }
    },
    {
      title: "White Label Solutions",
      description: "One-pager websites with ZUUMM branding customization",
      content: {
        overview: "Create branded travel booking experiences with our white-label solution. Get a fully functional one-page website powered by ZUUMM's technology with your custom branding.",
        features: [
          "Custom domain support",
          "Brand customization (colors, logos, fonts)",
          "Responsive design",
          "SEO optimized",
          "Analytics integration",
          "Multi-language support"
        ],
        examples: [
          {
            title: "Iframe Integration",
            code: `<!-- Embed ZUUMM booking widget in your website -->
<iframe
  src="https://booking.zuumm.ai/embed?partner_id=your-partner-id&theme=custom"
  width="100%"
  height="600"
  frameborder="0"
  style="border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);"
  allow="payment; geolocation">
</iframe>

<!-- With custom parameters -->
<iframe
  src="https://booking.zuumm.ai/embed?partner_id=your-partner-id&origin=BLR&destination=GOA&theme=dark&lang=en"
  width="100%"
  height="700"
  frameborder="0">
</iframe>

<!-- JavaScript integration for dynamic resizing -->
<script>
  window.addEventListener('message', function(event) {
    if (event.origin === 'https://booking.zuumm.ai') {
      if (event.data.type === 'resize') {
        const iframe = document.getElementById('zuumm-booking');
        iframe.style.height = event.data.height + 'px';
      }
    }
  });
</script>`
          },
          {
            title: "White Label Configuration",
            code: `// White label configuration
{
  "partner_id": "your-partner-id",
  "branding": {
    "company_name": "Your Travel Company",
    "logo_url": "https://yoursite.com/logo.png",
    "primary_color": "#FF6B35",
    "secondary_color": "#1E3A8A",
    "font_family": "Montserrat",
    "favicon_url": "https://yoursite.com/favicon.ico"
  },
  "domain": {
    "custom_domain": "travel.yourcompany.com",
    "ssl_enabled": true
  },
  "features": {
    "show_zuumm_branding": false,
    "enable_chat_support": true,
    "payment_methods": ["card", "upi", "netbanking"],
    "currencies": ["INR", "USD", "EUR"]
  },
  "analytics": {
    "google_analytics_id": "GA-XXXXXXXXX",
    "facebook_pixel_id": "*********"
  },
  "seo": {
    "meta_title": "Book Travel - Your Travel Company",
    "meta_description": "Book flights and hotels with ease",
    "keywords": ["travel", "flights", "hotels", "booking"]
  }
}`
          },
          {
            title: "Custom CSS Styling",
            code: `/* Custom CSS for white label styling */
.zuumm-booking-widget {
  --primary-color: #FF6B35;
  --secondary-color: #1E3A8A;
  --font-family: 'Montserrat', sans-serif;
  --border-radius: 12px;
  --shadow: 0 8px 24px rgba(0,0,0,0.12);
}

/* Override default button styles */
.zuumm-btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border: none;
  border-radius: var(--border-radius);
  font-family: var(--font-family);
  font-weight: 600;
  transition: transform 0.2s ease;
}

.zuumm-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Custom header styling */
.zuumm-header {
  background: white;
  border-bottom: 1px solid #E5E7EB;
  padding: 16px 24px;
}

.zuumm-logo {
  max-height: 40px;
  width: auto;
}

/* Responsive design */
@media (max-width: 768px) {
  .zuumm-booking-widget {
    padding: 16px;
    border-radius: 0;
  }
}`
          }
        ]
      }
    }
  ];

  // Scroll to top on component mount
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  return (
    <div
      style={{
        background: "#FFFFFF",
        position: "relative",
      }}
    >
      <div style={{ width: "100%" }}>
        <Header />
      </div>

      {/* Hero Section */}
      <Box
        sx={{
          pt: { xs: "80px", md: "136px" },
          pb: { xs: 4, md: 6 },
          px: { xs: 2.5, md: 10 },
          background: "linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%)",
          color: "white",
        }}
      >
        <Box sx={{ textAlign: "center", maxWidth: "800px", mx: "auto" }}>
          <Typography
            variant="h1"
            sx={{
              fontFamily: "Lora, serif",
              fontWeight: 600,
              fontSize: { xs: "32px", md: "48px" },
              lineHeight: { xs: "40px", md: "56px" },
              mb: 3,
            }}
          >
            Technical Documents
          </Typography>
          <Typography
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontWeight: 400,
              fontSize: { xs: "16px", md: "18px" },
              lineHeight: { xs: "24px", md: "28px" },
              opacity: 0.9,
            }}
          >
            Comprehensive technical documentation for SDK integration, API usage, and white-label solutions.
            Everything developers need to integrate ZUUMM's travel booking platform.
          </Typography>
        </Box>
      </Box>

      {/* Technical Documentation Section */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          px: { xs: 2.5, md: 10 },
          background: "#F8FAFC",
        }}
      >
        {/* Navigation Tabs */}
        <Box sx={{ mb: 4 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            centered
            sx={{
              "& .MuiTab-root": {
                fontFamily: "Roboto, sans-serif",
                fontWeight: 500,
                fontSize: "16px",
                textTransform: "none",
                color: "#6B7280",
                "&.Mui-selected": {
                  color: "#1E3A8A",
                },
              },
              "& .MuiTabs-indicator": {
                backgroundColor: "#1E3A8A",
              },
            }}
          >
            {technicalSections.map((section, index) => (
              <Tab key={index} label={section.title} />
            ))}
          </Tabs>
        </Box>

        {/* Content for Active Tab */}
        <Box>
          {technicalSections.map((section, index) => (
            <Box key={index} sx={{ display: activeTab === index ? "block" : "none" }}>
              {/* Section Header */}
              <Box sx={{ textAlign: "center", mb: 6 }}>
                <Typography
                  variant="h3"
                  sx={{
                    fontFamily: "Lora, serif",
                    fontWeight: 600,
                    fontSize: { xs: "28px", md: "36px" },
                    color: "#1E3A8A",
                    mb: 2,
                  }}
                >
                  {section.title}
                </Typography>
                <Typography
                  sx={{
                    fontFamily: "Roboto, sans-serif",
                    fontSize: "18px",
                    color: "#6B7280",
                    maxWidth: "600px",
                    mx: "auto",
                  }}
                >
                  {section.description}
                </Typography>
              </Box>

              {/* Overview */}
              <Card sx={{ mb: 4, boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)" }}>
                <CardContent sx={{ p: 4 }}>
                  <Typography
                    variant="h5"
                    sx={{
                      fontFamily: "Lora, serif",
                      fontWeight: 600,
                      fontSize: "24px",
                      color: "#1E3A8A",
                      mb: 3,
                    }}
                  >
                    Overview
                  </Typography>
                  <Typography
                    sx={{
                      fontFamily: "Roboto, sans-serif",
                      fontSize: "16px",
                      color: "#374151",
                      lineHeight: "24px",
                      mb: 3,
                    }}
                  >
                    {section.content.overview}
                  </Typography>

                  <Typography
                    variant="h6"
                    sx={{
                      fontFamily: "Lora, serif",
                      fontWeight: 600,
                      fontSize: "20px",
                      color: "#1E3A8A",
                      mb: 2,
                    }}
                  >
                    Key Features
                  </Typography>
                  <Box component="ul" sx={{ pl: 3, m: 0 }}>
                    {section.content.features.map((feature, featureIndex) => (
                      <Typography
                        component="li"
                        key={featureIndex}
                        sx={{
                          fontFamily: "Roboto, sans-serif",
                          fontSize: "16px",
                          color: "#374151",
                          lineHeight: "24px",
                          mb: 1,
                        }}
                      >
                        {feature}
                      </Typography>
                    ))}
                  </Box>
                </CardContent>
              </Card>

              {/* Code Examples */}
              <Grid container spacing={4}>
                {section.content.examples.map((example, exampleIndex) => (
                  <Grid item xs={12} key={exampleIndex}>
                    <Card sx={{ boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)" }}>
                      <CardContent sx={{ p: 0 }}>
                        <Box sx={{ p: 3, borderBottom: "1px solid #E5E7EB" }}>
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Lora, serif",
                              fontWeight: 600,
                              fontSize: "20px",
                              color: "#1E3A8A",
                            }}
                          >
                            {example.title}
                          </Typography>
                        </Box>
                        <Paper
                          sx={{
                            backgroundColor: "#1F2937",
                            color: "#F9FAFB",
                            p: 3,
                            borderRadius: 0,
                            overflow: "auto",
                          }}
                        >
                          <Typography
                            component="pre"
                            sx={{
                              fontFamily: "Monaco, Consolas, 'Courier New', monospace",
                              fontSize: "14px",
                              lineHeight: "20px",
                              whiteSpace: "pre-wrap",
                              wordBreak: "break-word",
                              margin: 0,
                            }}
                          >
                            {example.code}
                          </Typography>
                        </Paper>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          ))}
        </Box>
      </Box>

      {/* Developer Support Section */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          px: { xs: 2.5, md: 10 },
          background: "#FFFFFF",
          textAlign: "center",
        }}
      >
        <Typography
          variant="h4"
          sx={{
            fontFamily: "Lora, serif",
            fontWeight: 600,
            fontSize: { xs: "24px", md: "32px" },
            color: "#1E3A8A",
            mb: 2,
          }}
        >
          Need Developer Support?
        </Typography>
        <Typography
          sx={{
            fontFamily: "Roboto, sans-serif",
            fontSize: "16px",
            color: "#6B7280",
            mb: 4,
            maxWidth: "600px",
            mx: "auto",
          }}
        >
          Our technical team is ready to help you integrate ZUUMM's platform. Get assistance with SDK implementation, API integration, or white-label customization.
        </Typography>
        <Box sx={{ display: "flex", gap: 2, justifyContent: "center", flexWrap: "wrap" }}>
          <Link
            href="mailto:<EMAIL>"
            sx={{
              display: "inline-block",
              px: 4,
              py: 2,
              backgroundColor: "#1E3A8A",
              color: "white",
              textDecoration: "none",
              borderRadius: "8px",
              fontFamily: "Roboto, sans-serif",
              fontWeight: 500,
              fontSize: "16px",
              transition: "background-color 0.2s ease-in-out",
              "&:hover": {
                backgroundColor: "#1E40AF",
              },
            }}
          >
            Contact Developers
          </Link>
          <Link
            href="https://github.com/zuumm-ai/sdk-examples"
            target="_blank"
            sx={{
              display: "inline-block",
              px: 4,
              py: 2,
              backgroundColor: "#374151",
              color: "white",
              textDecoration: "none",
              borderRadius: "8px",
              fontFamily: "Roboto, sans-serif",
              fontWeight: 500,
              fontSize: "16px",
              transition: "background-color 0.2s ease-in-out",
              "&:hover": {
                backgroundColor: "#4B5563",
              },
            }}
          >
            View Examples on GitHub
          </Link>
        </Box>
      </Box>

      <Footer />
    </div>
  );
};

export default DocumentHub;
