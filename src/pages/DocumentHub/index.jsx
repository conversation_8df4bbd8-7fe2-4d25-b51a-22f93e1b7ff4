import React, { useEffect, useState } from "react";
import { <PERSON>, Grid, <PERSON>, Tabs, Tab } from "@mui/material";
import Header from "../../components/Header";
import Footer from "../../components/Footer";

const DocumentHub = () => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Technical documentation sections
  const technicalSections = [
    {
      title: "Chat SDK",
      description: "Pre-built chat widget for websites and mobile apps",
      content: {
        overview: "ZUUMM Chat SDK provides a pre-built chat package that customers can integrate into their websites or mobile applications. The chat widget connects users directly with ZUUMM's AI travel assistant for instant travel assistance and booking support.",
        features: [
          "Pre-built chat interface",
          "AI-powered travel assistant",
          "Real-time travel recommendations",
          "Booking assistance and support",
          "Customizable chat appearance",
          "Multi-platform support (Web & Mobile)"
        ],
        examples: [
          {
            title: "Web SDK - JavaScript Integration",
            code: `// Install ZUUMM Chat SDK
npm install @zuumm/chat-sdk

// Initialize Chat Widget
import { ZuummChat } from '@zuumm/chat-sdk';

const chatWidget = new ZuummChat({
  apiKey: 'your-api-key',
  environment: 'production', // or 'sandbox'
  customization: {
    primaryColor: '#1E3A8A',
    position: 'bottom-right',
    welcomeMessage: 'Hi! How can I help you plan your trip?',
    avatar: 'https://yoursite.com/avatar.png'
  }
});

// Initialize the chat widget
chatWidget.init();

// Show chat widget
chatWidget.show();

// Hide chat widget
chatWidget.hide();

// Listen to chat events
chatWidget.on('chatStarted', (data) => {
  console.log('User started chat:', data);
});

chatWidget.on('bookingInitiated', (data) => {
  console.log('User initiated booking:', data);
});`
          },
          {
            title: "Mobile SDK - React Native",
            code: `// Install ZUUMM Chat SDK for React Native
npm install @zuumm/react-native-chat-sdk

// iOS Setup (ios/Podfile)
pod 'ZuummChatSDK'

// Android Setup (android/app/build.gradle)
implementation 'com.zuumm:chat-sdk:1.0.0'

// Initialize SDK
import { ZuummChatSDK } from '@zuumm/react-native-chat-sdk';

const App = () => {
  useEffect(() => {
    ZuummChatSDK.initialize({
      apiKey: 'your-api-key',
      environment: 'production',
      theme: {
        primaryColor: '#1E3A8A',
        chatBubbleColor: '#FF3951'
      }
    });
  }, []);

  const openChat = () => {
    ZuummChatSDK.presentChat({
      welcomeMessage: 'Welcome! Let me help you plan your perfect trip.',
      onChatStarted: (data) => {
        console.log('Chat started:', data);
      },
      onBookingRequested: (data) => {
        console.log('Booking requested:', data);
      }
    });
  };

  return (
    <TouchableOpacity onPress={openChat}>
      <Text>Chat with Travel Assistant</Text>
    </TouchableOpacity>
  );
};`
          }
        ]
      }
    },
    {
      title: "Travel APIs",
      description: "Comprehensive travel industry APIs",
      content: {
        overview: "ZUUMM's REST APIs provide complete access to travel industry data and booking capabilities. Build custom travel applications with our comprehensive set of endpoints covering all aspects of travel planning and booking.",
        features: [
          "Categories and destinations",
          "Activities and experiences",
          "Travel packages and deals",
          "Booking management",
          "Booking reviews and ratings",
          "User authentication and login",
          "Payment processing",
          "Real-time availability"
        ],
        examples: [
          {
            title: "Authentication & Login API",
            code: `// User Login
POST https://api.zuumm.ai/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

// Response
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "name": "John Doe"
  },
  "expires_in": 3600
}

// Use token in subsequent requests
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
          },
          {
            title: "Categories & Destinations API",
            code: `// Get travel categories
GET https://api.zuumm.ai/v1/categories
Authorization: Bearer your-access-token

// Response
{
  "categories": [
    {
      "id": "cat_001",
      "name": "Adventure",
      "description": "Thrilling adventure activities",
      "image": "https://images.zuumm.ai/adventure.jpg"
    },
    {
      "id": "cat_002",
      "name": "Beach",
      "description": "Relaxing beach destinations",
      "image": "https://images.zuumm.ai/beach.jpg"
    }
  ]
}

// Get destinations by category
GET https://api.zuumm.ai/v1/destinations?category_id=cat_001
Authorization: Bearer your-access-token

// Response
{
  "destinations": [
    {
      "id": "dest_123",
      "name": "Manali, India",
      "category_id": "cat_001",
      "description": "Adventure capital of Himachal Pradesh",
      "image": "https://images.zuumm.ai/manali.jpg",
      "coordinates": {
        "latitude": 32.2396,
        "longitude": 77.1887
      }
    }
  ]
}`
          },
          {
            title: "Packages & Booking API",
            code: `// Get travel packages
GET https://api.zuumm.ai/v1/packages?destination_id=dest_123
Authorization: Bearer your-access-token

// Response
{
  "packages": [
    {
      "id": "pkg_456",
      "name": "Manali Adventure Package",
      "destination_id": "dest_123",
      "duration": "5 days 4 nights",
      "price": {
        "amount": 25000,
        "currency": "INR"
      },
      "inclusions": ["Hotel", "Meals", "Activities", "Transport"],
      "activities": ["River Rafting", "Paragliding", "Trekking"]
    }
  ]
}

// Create booking
POST https://api.zuumm.ai/v1/bookings
Authorization: Bearer your-access-token
Content-Type: application/json

{
  "package_id": "pkg_456",
  "travelers": 2,
  "travel_date": "2025-04-15",
  "contact": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+91-9876543210"
  }
}

// Response
{
  "booking_id": "book_789",
  "status": "confirmed",
  "total_amount": 50000,
  "payment_url": "https://payments.zuumm.ai/book_789"
}`
          }
        ]
      }
    },
    {
      title: "Chat Iframe",
      description: "Embeddable chat widget for websites",
      content: {
        overview: "ZUUMM's Chat Iframe is an embeddable chat widget that can be integrated into any website. The iframe provides a complete chat interface where users can interact with ZUUMM's AI travel assistant for travel planning and booking assistance.",
        features: [
          "Easy iframe embedding",
          "AI-powered travel chat",
          "Customizable chat appearance",
          "Real-time travel assistance",
          "Booking support through chat",
          "Responsive design",
          "Cross-domain messaging"
        ],
        examples: [
          {
            title: "Basic Iframe Integration",
            code: `<!-- Embed ZUUMM chat widget in your website -->
<iframe
  src="https://chat.zuumm.ai/embed?partner_id=your-partner-id"
  width="400px"
  height="600px"
  frameborder="0"
  style="border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); position: fixed; bottom: 20px; right: 20px; z-index: 1000;"
  allow="microphone; camera">
</iframe>

<!-- Responsive chat iframe -->
<iframe
  id="zuumm-chat"
  src="https://chat.zuumm.ai/embed?partner_id=your-partner-id&theme=light&welcome_msg=Hi! How can I help you plan your trip?"
  width="100%"
  height="500px"
  frameborder="0"
  style="max-width: 400px; border-radius: 12px;">
</iframe>`
          },
          {
            title: "Advanced Iframe with Events",
            code: `<!-- Chat iframe with custom styling -->
<div id="chat-container">
  <iframe
    id="zuumm-chat-iframe"
    src="https://chat.zuumm.ai/embed?partner_id=your-partner-id&theme=custom&primary_color=%23FF6B35"
    width="100%"
    height="100%"
    frameborder="0">
  </iframe>
</div>

<!-- JavaScript for chat events -->
<script>
  window.addEventListener('message', function(event) {
    if (event.origin === 'https://chat.zuumm.ai') {
      switch(event.data.type) {
        case 'chatStarted':
          console.log('User started chat');
          // Track chat initiation
          gtag('event', 'chat_started');
          break;
        case 'bookingRequested':
          console.log('User requested booking:', event.data.details);
          // Track booking interest
          gtag('event', 'booking_requested', {
            'destination': event.data.details.destination
          });
          break;
        case 'chatMinimized':
          document.getElementById('chat-container').style.height = '60px';
          break;
        case 'chatMaximized':
          document.getElementById('chat-container').style.height = '600px';
          break;
      }
    }
  });
</script>`
          },
          {
            title: "Iframe Customization Options",
            code: `<!-- Chat iframe with all customization options -->
<iframe
  src="https://chat.zuumm.ai/embed?partner_id=your-partner-id&theme=custom&primary_color=%23FF6B35&secondary_color=%231E3A8A&font_family=Roboto&welcome_msg=Welcome to Your Travel Company! How can I help you today?&avatar_url=https://yoursite.com/avatar.png&show_branding=false&language=en"
  width="400px"
  height="600px"
  frameborder="0"
  style="border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
</iframe>

<!-- URL Parameters Explanation:
- partner_id: Your unique partner identifier
- theme: light, dark, or custom
- primary_color: Main brand color (URL encoded)
- secondary_color: Secondary brand color (URL encoded)
- font_family: Font family for chat interface
- welcome_msg: Custom welcome message
- avatar_url: Custom avatar image URL
- show_branding: Show/hide ZUUMM branding
- language: Interface language (en, hi, etc.)
-->`
          }
        ]
      }
    },
    {
      title: "One-Pager Website",
      description: "Complete branded travel website solution",
      content: {
        overview: "ZUUMM's One-Pager solution provides customers with a complete travel website hosted on a ZUUMM subdomain (e.g., tenant1.zuumm.ai). Customers can later map their own custom domain (e.g., tenant1.com) to this subdomain. The one-page website displays travel categories, packages, and includes integrated chat functionality for seamless user experience.",
        features: [
          "ZUUMM subdomain hosting (tenant1.zuumm.ai)",
          "Custom domain mapping (tenant1.com)",
          "Travel categories and packages display",
          "Integrated AI chat functionality",
          "Complete brand customization",
          "Responsive one-page design",
          "SEO optimized",
          "Analytics integration"
        ],
        examples: [
          {
            title: "Domain Setup Process",
            code: `// Initial Setup - ZUUMM Subdomain
// Customer receives: tenant1.zuumm.ai

// Custom Domain Mapping
// Customer adds CNAME record:
tenant1.com -> tenant1.zuumm.ai

// SSL Certificate automatically provisioned
// Website accessible at both:
// - https://tenant1.zuumm.ai
// - https://tenant1.com

// Domain Configuration API
POST https://api.zuumm.ai/v1/domains/mapping
Authorization: Bearer your-access-token
Content-Type: application/json

{
  "tenant_id": "tenant1",
  "custom_domain": "tenant1.com",
  "ssl_enabled": true,
  "redirect_subdomain": true
}

// Response
{
  "status": "success",
  "subdomain": "tenant1.zuumm.ai",
  "custom_domain": "tenant1.com",
  "ssl_status": "active",
  "dns_records": [
    {
      "type": "CNAME",
      "name": "tenant1.com",
      "value": "tenant1.zuumm.ai"
    }
  ]
}`
          },
          {
            title: "One-Pager Configuration",
            code: `// Complete one-pager website configuration
{
  "tenant_id": "tenant1",
  "subdomain": "tenant1.zuumm.ai",
  "custom_domain": "tenant1.com",
  "branding": {
    "company_name": "Amazing Travels",
    "logo_url": "https://tenant1.com/logo.png",
    "primary_color": "#FF6B35",
    "secondary_color": "#1E3A8A",
    "accent_color": "#00AF31",
    "font_family": "Montserrat",
    "favicon_url": "https://tenant1.com/favicon.ico"
  },
  "content": {
    "hero": {
      "title": "Discover Your Next Adventure",
      "subtitle": "Chat with our AI assistant to plan your perfect trip",
      "background_image": "https://tenant1.com/hero-bg.jpg",
      "cta_text": "Start Planning"
    },
    "categories": {
      "display_categories": ["Adventure", "Beach", "Cultural", "Wildlife", "Luxury"],
      "featured_category": "Adventure",
      "show_category_images": true
    },
    "packages": {
      "featured_packages": 6,
      "show_price": true,
      "show_duration": true,
      "enable_quick_chat": true
    }
  },
  "features": {
    "show_zuumm_branding": false,
    "enable_chat_widget": true,
    "chat_position": "bottom-right",
    "enable_package_browsing": true,
    "enable_category_filtering": true,
    "payment_integration": true
  }
}`
          },
          {
            title: "One-Pager Custom Styling",
            code: `/* Custom CSS for one-pager website */
:root {
  --brand-primary: #FF6B35;
  --brand-secondary: #1E3A8A;
  --brand-accent: #00AF31;
  --brand-font: 'Montserrat', sans-serif;
}

/* Header section */
.onepage-header {
  background: white;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.brand-logo {
  max-height: 50px;
  width: auto;
}

/* Hero section */
.hero-section {
  background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('hero-bg.jpg');
  background-size: cover;
  background-position: center;
  min-height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
}

/* Categories grid */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  padding: 48px 24px;
}

.category-card {
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-8px);
}

/* Packages section */
.packages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 32px;
  padding: 48px 24px;
}

.package-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

/* Chat widget integration */
.chat-widget {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 380px;
  height: 600px;
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0,0,0,0.15);
  z-index: 1000;
  background: white;
}`
          }
        ]
      }
    }
  ];

  // Scroll to top on component mount
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  return (
    <div
      style={{
        background: "#FFFFFF",
        position: "relative",
      }}
    >
      <div style={{ width: "100%" }}>
        <Header />
      </div>

      {/* Hero Section */}
      <Box
        sx={{
          pt: { xs: "80px", md: "136px" },
          pb: { xs: 3, md: 4 },
          position: "relative",
          px: { xs: 2.5, md: 10 },
        }}
      >
        <Box sx={{ textAlign: "center", maxWidth: "700px", mx: "auto" }}>
          <Box
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontWeight: 700,
              fontSize: { xs: "16px", md: "20px" },
              lineHeight: "100%",
              letterSpacing: "0.2em",
              textTransform: "uppercase",
              color: "#FF3951",
              mb: 2,
              pt: { xs: 3, md: 5 },
            }}
          >
            Developer Resources
          </Box>
          <Box
            sx={{
              fontFamily: "Lora, serif",
              fontWeight: 600,
              fontSize: { xs: "28px", md: "42px" },
              lineHeight: { xs: "36px", md: "52px" },
              color: "#1E3A8A",
              mb: 2,
            }}
          >
            <span
              style={{
                background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                color: "transparent",
                display: "inline-block",
              }}
            >
              Integrate ZUUMM
            </span>{" "}
            with Ease
          </Box>
          <Box
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontWeight: 400,
              fontSize: { xs: "14px", md: "16px" },
              lineHeight: "150%",
              color: "#333333",
              maxWidth: "500px",
              mx: "auto",
            }}
          >
            Choose your preferred integration method and get started quickly.
          </Box>
        </Box>
      </Box>

      {/* Technical Documentation Section */}
      <Box
        sx={{
          py: { xs: 2, md: 3 },
          px: { xs: 2.5, md: 10 },
          background: "#FFF9F9",
        }}
      >


        {/* Navigation Tabs */}
        <Box sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            centered
            sx={{
              "& .MuiTab-root": {
                fontFamily: "Roboto, sans-serif",
                fontWeight: 500,
                fontSize: { xs: "14px", md: "16px" },
                textTransform: "none",
                color: "#333333",
                minHeight: "48px",
                "&.Mui-selected": {
                  color: "#1E3A8A",
                  fontWeight: 600,
                },
              },
              "& .MuiTabs-indicator": {
                backgroundColor: "#FF3951",
                height: "3px",
              },
            }}
          >
            {technicalSections.map((section, index) => (
              <Tab key={index} label={section.title} />
            ))}
          </Tabs>
        </Box>

        {/* Content for Active Tab */}
        <Box>
          {technicalSections.map((section, index) => (
            <Box key={index} sx={{ display: activeTab === index ? "block" : "none" }}>
              {/* Section Header */}
              <Box sx={{ textAlign: "center", mb: { xs: 3, md: 4 } }}>
                <Box
                  sx={{
                    fontFamily: "Lora, serif",
                    fontWeight: 600,
                    fontSize: { xs: "24px", md: "28px" },
                    lineHeight: { xs: "30px", md: "36px" },
                    color: "#1E3A8A",
                    mb: 2,
                  }}
                >
                  {section.title}
                </Box>
                <Box
                  sx={{
                    fontFamily: "Roboto, sans-serif",
                    fontSize: { xs: "14px", md: "16px" },
                    color: "#333333",
                    maxWidth: "600px",
                    mx: "auto",
                    lineHeight: "150%",
                    letterSpacing: "0.05em",
                  }}
                >
                  {section.description}
                </Box>
              </Box>

              {/* Overview */}
              <Box
                sx={{
                  background: "#FFFFFF",
                  borderRadius: "12px",
                  padding: { xs: "20px", md: "24px" },
                  boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
                  mb: 4,
                }}
              >
                <Box
                  sx={{
                    fontFamily: "Lora, serif",
                    fontWeight: 600,
                    fontSize: "20px",
                    lineHeight: "24px",
                    color: "#1E3A8A",
                    mb: 2,
                  }}
                >
                  Overview
                </Box>
                <Box
                  sx={{
                    fontFamily: "Roboto, sans-serif",
                    fontSize: "14px",
                    color: "#333333",
                    lineHeight: "20px",
                    mb: 3,
                  }}
                >
                  {section.content.overview}
                </Box>

                <Box
                  sx={{
                    fontFamily: "Lora, serif",
                    fontWeight: 600,
                    fontSize: "18px",
                    color: "#1E3A8A",
                    mb: 2,
                  }}
                >
                  Key Features
                </Box>
                <Box component="ul" sx={{ pl: 3, m: 0 }}>
                  {section.content.features.map((feature, featureIndex) => (
                    <Box
                      component="li"
                      key={featureIndex}
                      sx={{
                        fontFamily: "Roboto, sans-serif",
                        fontSize: "14px",
                        color: "#333333",
                        lineHeight: "20px",
                        mb: 1,
                      }}
                    >
                      {feature}
                    </Box>
                  ))}
                </Box>
              </Box>

              {/* Code Examples */}
              <Box sx={{ display: "flex", flexDirection: "column", gap: 4 }}>
                {section.content.examples.map((example, exampleIndex) => (
                  <Box
                    key={exampleIndex}
                    sx={{
                      background: "#FFFFFF",
                      borderRadius: "12px",
                      boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
                      overflow: "hidden",
                      maxWidth: "100%",
                    }}
                  >
                    <Box
                      sx={{
                        p: { xs: 2, md: 3 },
                        borderBottom: "1px solid #CFD6DC",
                        background: "#FFFFFF",
                      }}
                    >
                      <Box
                        sx={{
                          fontFamily: "Lora, serif",
                          fontWeight: 600,
                          fontSize: "18px",
                          color: "#1E3A8A",
                        }}
                      >
                        {example.title}
                      </Box>
                    </Box>
                    <Box
                      sx={{
                        backgroundColor: "#00243D",
                        color: "#FFFFFF",
                        p: { xs: 2, md: 3 },
                        overflow: "auto",
                        maxHeight: "400px", // Maximum height with scroll
                      }}
                    >
                      <Box
                        component="pre"
                        sx={{
                          fontFamily: "Monaco, Consolas, 'Courier New', monospace",
                          fontSize: { xs: "12px", md: "13px" },
                          lineHeight: "18px",
                          whiteSpace: "pre-wrap",
                          wordBreak: "break-word",
                          margin: 0,
                          color: "#FFFFFF",
                        }}
                      >
                        {example.code}
                      </Box>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Box>
          ))}
        </Box>
      </Box>

      {/* Developer Support Section */}
      <Box
        sx={{
          py: { xs: 4, md: 6 },
          px: { xs: 2.5, md: 10 },
          background: "#FFFFFF",
        }}
      >
        <Box
          sx={{
            maxWidth: "1200px",
            margin: "0 auto",
            padding: { xs: "20px", md: "40px 20px" },
            textAlign: "center",
            boxShadow: "0px 12px 35px 0px #64748C1F",
            borderRadius: "10px",
            background: "#FFFFFF",
          }}
        >
          <Box
            sx={{
              fontFamily: "Lora, serif",
              fontWeight: 700,
              fontSize: { xs: "24px", md: "28px" },
              color: "#1E3A8A",
              mb: 1,
              lineHeight: "40px",
            }}
          >
            Need Developer Support?
          </Box>
          <Box
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontSize: "14px",
              color: "#333333",
              mb: 4,
              lineHeight: "150%",
              maxWidth: "600px",
              mx: "auto",
            }}
          >
            Our technical team is ready to help you integrate ZUUMM's platform. Get assistance with SDK implementation, API integration, or white-label customization.
          </Box>
          <Box sx={{ display: "flex", justifyContent: "center" }}>
            <Link
              href="mailto:<EMAIL>"
              sx={{
                display: "inline-block",
                px: { xs: 3, md: 4 },
                py: { xs: 1.5, md: 2 },
                backgroundColor: "#1E3A8A",
                color: "white",
                textDecoration: "none",
                borderRadius: "8px",
                fontFamily: "Roboto, sans-serif",
                fontWeight: 500,
                fontSize: { xs: "14px", md: "16px" },
                transition: "all 0.2s ease-in-out",
                "&:hover": {
                  backgroundColor: "#153B50",
                  transform: "translateY(-2px)",
                },
              }}
            >
              Contact Developers
            </Link>
          </Box>
        </Box>
      </Box>

      <Footer />
    </div>
  );
};

export default DocumentHub;
