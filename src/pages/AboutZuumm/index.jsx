import { useEffect } from "react";
import Header from "../../components/Header";
import Footer from "../../components/Footer";
import { Box } from "@mui/material";

function AboutPage() {
  const coreValues = [
    {
      icon: "/static/about/core/scale.png",
      title: "Simplicity at Scale",
      description:
        "We design experiences that make even the most complex travel needs feel effortless — from visa processing to bulk corporate bookings.",
    },
    {
      icon: "/static/about/core/innovation.png",
      title: "Intelligent Innovation",
      description:
        "We leverage AI not just to automate, but to elevate — offering smart recommendations, real-time support, and dynamic personalization.",
    },
    {
      icon: "/static/about/core/ai.png",
      title: "Empowerment for AI",
      description:
        "From first-time travelers to large enterprise teams, we believe everyone deserves access to intelligent, easy-to-use travel tools.",
    },
    {
      icon: "/static/about/core/trust.png",
      title: "Trust & Transparency",
      description:
        "Our users' journeys matter. We build with honesty, clarity, and a relentless focus on delivering value.",
    },
    {
      icon: "/static/about/core/customer.png",
      title: "Customer-Centricity",
      description:
        "We obsess over experience — delivering support, flexibility, and responsiveness every step of the way.",
    },
  ];

  // scroll to top
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  return (
    <div
      style={{
        background: "#FFFFFF",
        position: "relative",
      }}
    >
      <div style={{ width: "100%" }}>
        <Header />
      </div>

      {/* Hero Section */}
      <Box
        sx={{
          pt: { xs: "80px", md: "136px" },
          position: "relative",
          px: { xs: 2.5, md: 10 },
        }}
      >
        <Box
          component="img"
          src="/static/about/circleLeft.png"
          sx={{
            position: "absolute",
            top: "-0",
            left: "0px",
            width: { xs: "100%", md: "492px" },
            height: { xs: "100%", md: "492px" },
            zIndex: 0,
          }}
          alt="hero"
        />
        <Box
          component="img"
          src="/static/about/circleRight.png"
          sx={{
            position: "absolute",
            bottom: 0,
            right: 0,
            width: { xs: "100%", md: "534px" },
            height: { xs: "100%", md: "534px" },
            zIndex: 0,
          }}
          alt="hero"
        />
        <Box
          sx={{
            position: "relative",
            zIndex: 1,
            width: "100%",
            height: "100%",
            display: "flex",
            flexDirection: { xs: "column-reverse", md: "row" },
          }}
        >
          <Box
            sx={{
              width: { xs: "100%", md: "calc(100% - 622px)" },
              height: "100%",
              display: "flex",
              flexDirection: "column",
            }}
          >
            <Box
              sx={{
                width: "100%",
                fontFamily: "Roboto, sans-serif",
                fontWeight: 700,
                fontSize: { xs: "20px", md: "24px" },
                lineHeight: "100%",
                letterSpacing: "0.25em",
                textTransform: "uppercase",
                color: "#FF3951",
                mb: 1.5,
                pt: { xs: 5, md: 9 },
              }}
            >
              About Us
            </Box>
            <Box
              sx={{
                fontFamily: "Lora, serif",
                fontWeight: 600,
                fontSize: { xs: "32px", md: "56px" },
                lineHeight: { xs: "40px", md: "72px" },
                color: "#1E3A8A",
                mb: 1.5,
                maxWidth: "505px",
              }}
            >
              <span
                style={{
                  background:
                    "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                  color: "transparent",
                  display: "inline-block",
                }}
              >
                Travel smarter
              </span>
              <Box sx={{ position: "relative" }}>
                <Box
                  component="img"
                  src="/static/about/text_line_heading.png"
                  alt="ai"
                  sx={{
                    width: "100%",
                    maxWidth: "414px",
                    height: "19px",
                    position: "absolute",
                    top: { xs: "28px", md: "50px" },
                    left: 0,
                    zIndex: 0,
                  }}
                />
                <span
                  style={{
                    background:
                      "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    color: "transparent",
                    display: "inline-block",
                    position: "relative",
                  }}
                >
                  with AI
                </span>
                <span>{" — for Business or Leisure."}</span>
              </Box>
            </Box>

            <Box
              sx={{
                fontFamily: "Roboto, sans-serif",
                fontWeight: 400,
                fontSize: { xs: "14px", md: "16px" },
                lineHeight: { xs: "20px", md: "160%" },
                letterSpacing: "0.05em",
                color: "#333333",
              }}
            >
              ZUUMM is built for individual travelers, travel sellers, and
              organizations — offering Infrastructure-as-a-Service (IaaS) for
              visas, holidays, and employee travel solutions.
              <br /> ZUUMM is your intelligent companion for discovering,
              booking, and managing travel — all in one place. Whether you're
              planning a personal getaway, selling curated travel experiences,
              assisting others in exploring the world, or managing travel for
              your workforce,
              <span
                style={{
                  background:
                    "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                  color: "transparent",
                  fontWeight: 400,
                  fontFamily: "Roboto, sans-serif",
                  fontSize: 16,
                  lineHeight: "160%",
                  letterSpacing: "0.05em",
                  display: "inline",
                }}
              >
                {
                  " ZUUMM blends the power of AI with a deep understanding of the travel lifecycle to deliver a seamless, modern experience."
                }
              </span>
              <br /> But our journey didn't start here.
            </Box>
          </Box>
          <Box
            sx={{
              width: { xs: "100%", md: "622px" },
              height: "100%",
            }}
          >
            <img
              src="/static/about/cover.png"
              alt="about-zuumm"
              style={{ width: "100%", height: "100%" }}
            />
          </Box>
        </Box>
      </Box>

      {/* From IncBuddy to ZUUMM */}
      <Box
        sx={{
          px: { xs: 2.5, md: 10 },
          py: { xs: 5, md: 7.5 },
          position: "relative",
        }}
      >
        <Box
          component="img"
          src="/static/about/clipLeft.png"
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            width: { xs: "100px", md: "230px" },
            height: { xs: "100px", md: "116px" },
            zIndex: 0,
          }}
          alt="clipLeft"
        />
        <Box
          component="img"
          src="/static/about/clipRight.png"
          sx={{
            position: "absolute",
            top: 0,
            right: 0,
            width: { xs: "100px", md: "228px" },
            height: { xs: "100px", md: "150px" },
            zIndex: 0,
          }}
          alt="clipRight"
        />
        <Box
          sx={{
            fontFamily: "Lora, serif",
            fontWeight: 600,
            fontSize: { xs: "24px", md: "32px" },
            lineHeight: { xs: "30px", md: "40px" },
            color: "#1E3A8A",
            textAlign: "center",
            mb: 3,
          }}
        >
          From IncBuddy to ZUUMM
        </Box>
        <Box
          sx={{
            textAlign: "center",
            mb: 2,
          }}
        >
          <Box
            component="img"
            src="/static/about/buddy.png"
            alt="incbuddy"
            sx={{
              width: { xs: "100%", md: "382px" },
              height: { xs: "100%", md: "120px" },
            }}
          />
        </Box>
        <Box
          sx={{
            fontFamily: "Roboto, sans-serif",
            fontWeight: 500,
            fontSize: { xs: "14px", md: "16px" },
            lineHeight: { xs: "20px", md: "24px" },
            color: "#000000",
            textAlign: "center",
            maxWidth: "856px",
            margin: "0 auto",
            mb: 2,
          }}
        >
          In November 2022, we launched{" "}
          <span style={{ color: "#003CBE" }}>{" IncBuddy"}</span>, a corporate
          travel platform built for India's SMEs and mid-market enterprises.
          Headquartered in Bangalore, IncBuddy quickly became the preferred
          travel partner for a growing base of companies — serving over 100
          corporates within just two years.
        </Box>
        <Box
          sx={{
            fontFamily: "Roboto, sans-serif",
            fontWeight: 400,
            fontSize: { xs: "14px", md: "16px" },
            lineHeight: { xs: "20px", md: "24px" },
            color: "#000000",
            textAlign: "center",
            mb: 2,
          }}
        >
          IncBuddy integrated effortlessly into business workflows — reducing
          travel costs by 20%, improving productivity by 32%, and simplifying
          everything from policy enforcement to tax reconciliation. It wasn't
          just travel tech — it felt like magic, combining flexibility,
          automation, and human support.
          <br />
          As we worked more closely with corporate clients, one thing became
          clear — companies weren't just booking business trips. They were also
          looking for thoughtfully curated holiday packages to support MICE
          travel and to reward employees for their service and dedication.
          Gifting travel became a powerful way to celebrate loyalty and
          performance.
        </Box>
        <Box
          sx={{
            fontFamily: "Roboto, sans-serif",
            fontWeight: 600,
            fontSize: { xs: "14px", md: "16px" },
            lineHeight: { xs: "20px", md: "24px" },
            color: "#1E3A8A",
            textAlign: "center",
          }}
        >
          This demand made one thing clear — our next chapter was waiting.
        </Box>
      </Box>

      {/* Enter ZUUMM */}
      <Box
        sx={{
          background:
            "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 125.89%)",
          position: "relative",
          minHeight: { xs: "100%", md: "442px" },
        }}
      >
        <Box
          component="img"
          src="/static/about/travel2.png"
          sx={{
            position: "absolute",
            bottom: { xs: "calc(100% - 100px - 20px)", md: "0px" },
            left: { xs: "20px", md: "40px" },
            width: { xs: "100px", md: "175px" },
            height: { xs: "100px", md: "241px" },
            opacity: { xs: 0.3, md: 1 },
          }}
          alt="travel2"
        />

        <Box
          component="img"
          src="/static/about/travel1.png"
          sx={{
            position: "absolute",
            bottom: { xs: "calc(100% - 100px - 20px)", md: "0px" },
            right: { xs: "20px", md: "40px" },
            width: { xs: "100px", md: "154px" },
            height: { xs: "100px", md: "241px" },
            opacity: { xs: 0.3, md: 1 },
          }}
          alt="travel1"
        />
        <Box
          component="img"
          src="/static/common/logo.png"
          sx={{
            position: "absolute",
            top: "20px",
            right: "20px",
            width: { xs: "100px", md: "457px" },
            height: { xs: "22px", md: "90px" },
            opacity: "5%",
          }}
          alt="logo"
        />
        <Box
          sx={{
            width: "100%",
            margin: "0 auto",
            maxWidth: "753px",
            py: { xs: 5, md: 7.5 },
            px: { xs: 2.5, md: 0 },
            boxSizing: "border-box",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              gap: { xs: 1, md: 2 },
            }}
          >
            <Box
              sx={{
                fontFamily: "Lora, serif",
                fontWeight: 700,
                fontSize: { xs: "20px", md: "32px" },
                lineHeight: { xs: "24px", md: "40px" },
                color: "#1E3A8A",
              }}
            >
              Enter
            </Box>
            <Box
              component="img"
              src="/static/common/logo.png"
              sx={{
                width: { xs: "100px", md: "170px" },
                height: { xs: "22px", md: "34px" },
              }}
              alt="logo"
            />
          </Box>
          <Box
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontWeight: 500,
              fontSize: { xs: "16px", md: "24px" },
              lineHeight: { xs: "20px", md: "29px" },
              color: "#333333",
              textAlign: "center",
              mt: 3,
            }}
          >
            <span style={{ color: "#FF3951" }}>ZUUMM</span> is the natural
            evolution of our journey
          </Box>
          <Box
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontWeight: 600,
              fontSize: { xs: "14px", md: "16px" },
              lineHeight: { xs: "20px", md: "24px" },
              color: "#333333",
              textAlign: "center",
              maxWidth: "579px",
              margin: "0 auto",
              mt: 3,
            }}
          >
            <span
              style={{
                background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                color: "transparent",
              }}
            >
              A smart AI-powered travel
            </span>{" "}
            portal for everyday travelers, travel sellers, and businesses alike.
            It brings together holiday packages, visa facilitation, and
            corporate travel tools, all on a single unified platform.
          </Box>
          <Box
            sx={{
              fontFamily: "Roboto, sans-serif",
              fontWeight: 400,
              fontSize: { xs: "14px", md: "20px" },
              lineHeight: { xs: "20px", md: "29px" },
              color: "#333333",
              textAlign: "center",
              mt: 3,
            }}
          >
            Whether you're a traveler, a gig seller, a startup, or a large
            enterprise, ZUUMM empowers you to plan trips, manage bookings,
            fulfill visa needs, or even build a travel business — all with
            intelligent automation and curated experiences.
          </Box>
        </Box>
      </Box>

      {/* Mission & Vision */}
      <Box>
        <Box
          sx={{
            display: "flex",
            width: "100%",
            p: { xs: 2.5, md: "60px 80px" },
            gap: { xs: 2.5, md: 10 },
            boxSizing: "border-box",
            alignItems: "center",
            flexDirection: { xs: "column", md: "row" },
          }}
        >
          <Box
            sx={{
              width: "100%",
            }}
          >
            <Box
              sx={{
                fontFamily: "Lora, serif",
                fontWeight: 600,
                fontSize: { xs: "24px", md: "32px" },
                lineHeight: { xs: "30px", md: "40px" },
                color: "#1E3A8A",
                mb: 2.5,
                position: "relative",
              }}
            >
              Our Mission
              <Box
                component="img"
                src="/static/about/text_line_heading.png"
                alt="ai"
                sx={{
                  width: "100%",
                  maxWidth: "206px",
                  height: "10px",
                  position: "absolute",
                  top: { xs: "22px", md: "30px" },
                  left: 0,
                  zIndex: 0,
                }}
              />
            </Box>
            <Box
              sx={{
                fontFamily: "Roboto, sans-serif",
                fontWeight: 400,
                fontSize: { xs: "14px", md: "16px" },
                lineHeight: { xs: "20px", md: "24px" },
                color: "#333333",
              }}
            >
              To build the most intelligent, inclusive, and trusted travel
              infrastructure — enabling anyone, anywhere to discover, plan, and
              manage travel experiences with confidence and ease. We envision a
              world where every trip, whether for work or wonder, begins with
              just a tap.
            </Box>
          </Box>
          <Box
            component="img"
            src="/static/about/mission.png"
            alt="mission"
            sx={{
              width: { xs: "100%", md: "308px" },
              height: { xs: "100%", md: "229px" },
              maxWidth: "308px",
            }}
          />
          <Box
            sx={{
              width: "100%",
            }}
          >
            <Box
              sx={{
                fontFamily: "Lora, serif",
                fontWeight: 600,
                fontSize: { xs: "24px", md: "32px" },
                lineHeight: { xs: "30px", md: "40px" },
                color: "#1E3A8A",
                mb: 2.5,
                position: "relative",
              }}
            >
              Our Vision
              <Box
                component="img"
                src="/static/about/text_line_heading.png"
                alt="ai"
                sx={{
                  width: "100%",
                  maxWidth: "206px",
                  height: "10px",
                  position: "absolute",
                  top: { xs: "22px", md: "30px" },
                  left: 0,
                  zIndex: 0,
                }}
              />
            </Box>
            <Box
              sx={{
                fontFamily: "Roboto, sans-serif",
                fontWeight: 400,
                fontSize: { xs: "14px", md: "16px" },
                lineHeight: { xs: "20px", md: "24px" },
                color: "#333333",
              }}
            >
              To democratize access to travel through the power of AI — making
              it intuitive, affordable, and personalized for everyone — whether
              you're booking a dream getaway, offering holiday packages to your
              customers, or managing hundreds of employees on the move. With
              ZUUMM, the future of travel is fast, flexible, and always within
              reach.
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Core Values */}
      <Box
        sx={{
          background: "#FFF9F9",
          padding: { xs: "60px 20px", md: "60px 80px" },
        }}
      >
        <Box
          sx={{
            fontFamily: "Lora, serif",
            fontWeight: 700,
            fontSize: { xs: "24px", md: "32px" },
            lineHeight: { xs: "30px", md: "45px" },
            color: "#1E3A8A",
            textAlign: "center",
            mb: 3,
          }}
        >
          Our Core Values
        </Box>
        <Box
          sx={{
            maxWidth: "904px",
            margin: "0 auto",
            display: "flex",
            flexWrap: "wrap",
            rowGap: { xs: 2, md: 3 },
            columnGap: { xs: 2, md: 5 },
            justifyContent: "center", // centers last row
            flexDirection: { xs: "column", md: "row" },
          }}
        >
          {coreValues.map((value, index) => (
            <Box
              sx={{
                flex: "1 1 260px",
                maxWidth: { xs: "100%", md: "calc(33.33% - 16px)" },
                background: "#FFFFFF",
                borderRadius: "12px",
                padding: "20px",
                boxSizing: "border-box",
                boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
              }}
              key={index}
            >
              <Box
                component="img"
                src={value.icon}
                alt={value.title}
                sx={{
                  width: { xs: "44px", md: "44px" },
                  height: { xs: "44px", md: "44px" },
                  marginBottom: { xs: "16px", md: "16px" },
                }}
              />
              <Box
                sx={{
                  fontFamily: "Lora, serif",
                  fontWeight: 600,
                  fontSize: "20px",
                  lineHeight: "24px",
                  color: "#1E3A8A",
                  mb: { xs: 3, md: 0.5 },
                }}
              >
                {value.title}
              </Box>
              <Box
                sx={{
                  fontFamily: "Roboto, sans-serif",
                  fontWeight: 400,
                  fontSize: "14px",
                  lineHeight: "20px",
                  color: "#333333",
                }}
              >
                {value.description}
              </Box>
            </Box>
          ))}
        </Box>
      </Box>

      <Footer />
    </div>
  );
}

export default AboutPage;
