/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Box,
  Typography,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Pagination,
  Button,
  IconButton,
  Modal,
} from "@mui/material";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import { Copy } from "../../components/common/SvgIcon";
import {
  fetchAffiliateProfile,
  sendAffiliateRequest,
  fetchAffiliateReferralUsers,
} from "../../store/reducers/Affiliate/apiThunk";
import {
  ToastNotifyError,
  ToastNotifySuccess,
} from "../../components/Toast/ToastNotify";
import moment from "moment";
import AffiliateSkeleton from "./AffiliateSkeleton";
import AiChatHeader from "../../components/ai-chatbot/AiChatHeader";
import { Checkbox } from "@mui/material";

const PAGE_SIZE = 10;

const AffiliatePage = () => {
  const [page, setPage] = useState(0);
  const [openTermsModal, setOpenTermsModal] = useState(false);
  const dispatch = useDispatch();

  // Get affiliate data from Redux store
  const { profile, referralUsers, isLoading } = useSelector(
    (state) => state.affiliate
  );
  const { userDetails } = useSelector((state) => state.authentication);

  const [loadingUserDetails, setLoadingUserDetails] = useState(true);
  const [loadingReferralUsers, setLoadingReferralUsers] = useState(true);
  const [isAgree, setIsAgree] = useState(false);

  const [transformedReferralUsers, setTransformedReferralUsers] =
    useState(null);

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoadingUserDetails(true);
        await Promise.all([dispatch(fetchAffiliateProfile()).unwrap()]);
        setTimeout(() => {
          setLoadingUserDetails(false);
        }, 1000);
      } catch (error) {
        console.error("Failed to fetch affiliate data:", error);
      }
    };

    fetchData();
  }, []);

  // Handle affiliate request
  const handleAffiliateRequest = async () => {
    if (!isAgree) {
      ToastNotifyError(
        "Please agree to the Affiliate Program Terms & Conditions to proceed."
      );
      return;
    }
    try {
      setLoadingUserDetails(true);
      await dispatch(sendAffiliateRequest()).unwrap();
      // Refresh profile data after successful request
      await dispatch(fetchAffiliateProfile()).unwrap();
      setTimeout(() => {
        setLoadingUserDetails(false);
      }, 1000);
    } catch (error) {
      console.error("Failed to send affiliate request:", error);
    }
  };

  const fetchReferralUsers = async (page) => {
    const data = await dispatch(
      fetchAffiliateReferralUsers({ page_size: PAGE_SIZE, page: page })
    ).unwrap();
    const transformedData = data?.data?.data?.map((user, index) => ({
      id: index + 1,
      customerInfo: user?.email || user?.full_name,
      joinedOn: moment(user?.created_at).format("DD-MMMM-YYYY"),
      code: "N/A",
      purchaseCount: 0, // This might need to come from a different API
      status: user?.is_active ? "Active" : "Inactive",
    }));
    setTransformedReferralUsers(transformedData || []);
  };

  useEffect(() => {
    if (profile?.is_affiliate) {
      setLoadingReferralUsers(true);
      fetchReferralUsers(page + 1);
      setTimeout(() => {
        setLoadingReferralUsers(false);
      }, 1000);
    } else {
      setLoadingReferralUsers(false);
    }
  }, [profile?.is_affiliate]);

  // Handle copy referral link
  const handleCopyReferralLink = async () => {
    const referralLink = profile?.affiliate_url || "N/A";
    try {
      await navigator.clipboard.writeText(referralLink);
      // You could add a toast notification here to show success
      ToastNotifySuccess("Referral link copied to clipboard");
    } catch (error) {
      ToastNotifyError("Failed to copy referral link:", error);
    }
  };

  // Get user initials for avatar
  const getUserInitials = (name) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Get status color and text
  const getStatusInfo = (status) => {
    switch (status) {
      case "Active":
        return { color: "#08853A", background: "#13A60012" };
      case "Inactive":
        return { color: "#FFC001", background: "#FFC00112" };

      default:
        return { color: "#666666", backgroundColor: "#F5F5F5" };
    }
  };

  // Get user name from either userDetails or profile
  const userName =
    userDetails?.user?.name ||
    userDetails?.user?.full_name ||
    profile?.user?.full_name ||
    "User";

  const handlePageChange = (value) => {
    fetchReferralUsers(value);
  };

  // Terms and Conditions Modal
  const TermsAndConditionsModal = () => {
    const useCasesData = [
      {
        useCase: "User A signs up directly on B2C website",
        affiliate: "None",
        customer: "User A",
        attribution: 'Credit = "b2c" (organic signup)',
      },
      {
        useCase: "User A refers User B who signs up on website and books",
        affiliate: "None",
        customer: "User A = referrer, User B",
        attribution: "User A gets credit for new referral",
      },
      {
        useCase: "User A signs up via affiliate link",
        affiliate: "Referrer = Affiliate X",
        customer: "User A",
        attribution: "Credit goes to Affiliate X",
      },
      {
        useCase:
          "User A signs up via affiliate, then signs up for referral link on website, then refers User B",
        affiliate: "Affiliate X",
        customer: "User A becomes referrer",
        attribution:
          "Original attribution to Affiliate X; User B attributed to User A",
      },
      {
        useCase:
          "User A clicks own referral link to sign up again (same device/email)",
        affiliate: "User A's own referral ID",
        customer: "User A (or duplicate)",
        attribution: "Self-referral blocked; no credit given",
      },
      {
        useCase: "Affiliate X refers User B who had previously visited site",
        affiliate: "Affiliate X",
        customer: "User B",
        attribution: "If not signed up earlier → Affiliate gets credit",
      },
      {
        useCase:
          "User A signs up via affiliate, then attempts to overwrite referral ID",
        affiliate: "Affiliate X",
        customer: "User A",
        attribution: "Referral ID locked; overwrite blocked",
      },
      {
        useCase: "User A (existing customer) clicks affiliate link later",
        affiliate: "Affiliate Y",
        customer: "User A",
        attribution: "Ignored — original attribution retained",
      },
      {
        useCase: "User A becomes affiliate after joining via Affiliate X",
        affiliate: "Affiliate X",
        customer: "User A = referrer",
        attribution: "User A earns only from their own referrals going forward",
      },
      {
        useCase: "User B signs up via referral, but no booking made",
        affiliate: "Affiliate/User A",
        customer: "User B",
        attribution: "No payout — requires qualifying action",
      },
    ];

    return (
      <Modal
        open={openTermsModal}
        onClose={() => setOpenTermsModal(false)}
        aria-labelledby="terms-modal-title"
        aria-describedby="terms-modal-description"
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          p: 2,
        }}
      >
        <Box
          sx={{
            bgcolor: "background.paper",
            borderRadius: "12px",
            boxShadow: 24,
            maxWidth: "900px",
            width: "100%",
            maxHeight: "90vh",
            overflow: "auto",
            p: 3,
          }}
        >
          {/* Header */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 3,
              pb: 2,
              borderBottom: "1px solid #e0e0e0",
            }}
          >
            <Typography
              variant="h5"
              component="h2"
              sx={{
                fontFamily: "Roboto",
                fontWeight: 600,
                color: "#333333",
              }}
            >
              ZUUMM Affiliate Program - Terms and Conditions
            </Typography>
            <IconButton
              onClick={() => setOpenTermsModal(false)}
              sx={{ color: "#666666" }}
            >
              ✕
            </IconButton>
          </Box>

          {/* Content */}
          <Box
            sx={{
              fontSize: "14px",
              lineHeight: "1.6",
              color: "#333333",
              fontFamily: "Roboto",
              fontWeight: 400,
              fontStyle: "normal",
            }}
          >
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, mt: 3 }}>
              1. Affiliate Eligibility and Registration
            </Typography>
            <Typography paragraph>
              1.1. Any individual or business entity is eligible to apply as an
              affiliate partner ('Affiliate').
            </Typography>
            <Typography paragraph>
              1.2. Upon successful registration, each Affiliate will receive a
              unique referral code/link via email.
            </Typography>
            <Typography paragraph>
              1.3. The referral link will follow a format such as:
              https://zuumm.ai/packages?ref=yourcode123 This link is exclusively
              assigned to the Affiliate and must not be misused, shared publicly
              in spammy or deceptive ways, or tampered with.
            </Typography>
            <Typography paragraph>
              1.4. Affiliates can be based in any country. However, compliance
              with local tax and financial regulations is the responsibility of
              the Affiliate.
            </Typography>

            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, mt: 4 }}>
              2. Commission Structure and Attribution
            </Typography>
            <Typography paragraph>
              2.1. Affiliates will earn a fixed commission of Rs. 1,000/- (INR
              One Thousand) per package (or equivalent in any other currency)
              sold and materialized through ZUUMM via their referral code.
            </Typography>
            <Typography paragraph>
              2.2. ZUUMM follows a last-click attribution model. The most recent
              referral link used by the end user will determine the Affiliate
              who receives credit for the sale.
            </Typography>
            <Typography paragraph>
              2.3. The sale must be completed, the journey undertaken, and no
              refund or cancellation requested, for the commission to be
              considered valid and payable.
            </Typography>

            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, mt: 4 }}>
              Table of Use Cases
            </Typography>
            <TableContainer
              component={Paper}
              sx={{ mb: 3, boxShadow: "none", border: "1px solid #e0e0e0" }}
            >
              <Table size="small">
                <TableHead>
                  <TableRow sx={{ backgroundColor: "#f5f5f5" }}>
                    <TableCell sx={{ fontWeight: 600, fontSize: "12px" }}>
                      Use Case
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, fontSize: "12px" }}>
                      Affiliate
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, fontSize: "12px" }}>
                      Customer
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, fontSize: "12px" }}>
                      Attribution
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {useCasesData.map((row, index) => (
                    <TableRow
                      key={index}
                      sx={{
                        "&:nth-of-type(odd)": { backgroundColor: "#fafafa" },
                      }}
                    >
                      <TableCell sx={{ fontSize: "11px", py: 1 }}>
                        {row.useCase}
                      </TableCell>
                      <TableCell sx={{ fontSize: "11px", py: 1 }}>
                        {row.affiliate}
                      </TableCell>
                      <TableCell sx={{ fontSize: "11px", py: 1 }}>
                        {row.customer}
                      </TableCell>
                      <TableCell sx={{ fontSize: "11px", py: 1 }}>
                        {row.attribution}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, mt: 4 }}>
              3. Payments and Disbursement
            </Typography>
            <Typography paragraph>
              3.1. Commissions will be eligible for payout only after the
              customer's travel has been completed (materialized) and no refunds
              are issued.
            </Typography>
            <Typography paragraph>
              3.2. Payouts will be processed at the end of each calendar month.
            </Typography>
            <Typography paragraph>
              3.3. Only amounts exceeding Rs. 10,000/- in total will be eligible
              for disbursement at the end of the month.
            </Typography>
            <Typography paragraph>
              3.4. Affiliates can initiate a claim from their dashboard for
              eligible amounts; ZUUMM does not automatically initiate payments.
            </Typography>
            <Typography paragraph>
              3.5. Payments will be processed in INR for Indian referrers. For
              international Affiliates, the amount will be converted at the
              prevailing exchange rate and paid via appropriate international
              remittance channels.
            </Typography>
            <Typography paragraph>
              3.6. RBI and FEMA regulations shall govern international
              transactions. Affiliates agree to provide required KYC and
              compliance documentation if requested.
            </Typography>

            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, mt: 4 }}>
              4. Tax and Deductions
            </Typography>
            <Typography paragraph>
              4.1. For Indian referrers Tax Deducted at Source (TDS) will be
              applicable as per Indian Income Tax laws.
            </Typography>
            <Typography paragraph>
              4.2. Affiliates will be provided with necessary tax documents
              (e.g., Form 16A) for filed deductions.
            </Typography>
            <Typography paragraph>
              4.3. Affiliates are responsible for declaring and paying taxes in
              their home country (if applicable).
            </Typography>

            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, mt: 4 }}>
              5. Dashboard and Reporting
            </Typography>
            <Typography paragraph>
              5.1. Affiliates will have access to a secure dashboard to view
              real-time sales, commissions, and disbursement history.
            </Typography>
            <Typography paragraph>
              5.2. All claims must be initiated via the dashboard interface.
            </Typography>
            <Typography paragraph>
              5.3. Any discrepancies should be reported within 7 days of the
              month-end closing <NAME_EMAIL>.
            </Typography>

            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, mt: 4 }}>
              6. Program Changes and Termination
            </Typography>
            <Typography paragraph>
              6.1. ZUUMM reserves the right to modify, suspend, or terminate the
              Program at any time with notice via email or the Affiliate
              dashboard.
            </Typography>
            <Typography paragraph>
              6.2. Any commissions accrued up to the date of termination will be
              honored if valid.
            </Typography>

            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, mt: 4 }}>
              7. Limitation of Liability and Dispute Resolution
            </Typography>
            <Typography paragraph>
              7.1. ZUUMM shall not be liable for any indirect, incidental, or
              consequential damages arising from participation in this Program.
            </Typography>
            <Typography paragraph>
              7.2. Participation in the Program does not create any partnership,
              joint venture, or employment relationship between the Affiliate
              and ZUUMM.
            </Typography>
            <Typography paragraph>
              7.3. ZUUMM reserves the right to interpret and enforce these Terms
              at its sole discretion.
            </Typography>
            <Typography paragraph>
              7.4. No disputes regarding commission, attribution, or eligibility
              will be entertained. ZUUMM's decision in all matters will be final
              and binding.
            </Typography>

            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, mt: 4 }}>
              8. Contact Information
            </Typography>
            <Typography paragraph>
              For any queries or concerns regarding the Affiliate Program,
              please contact us at: <EMAIL>
            </Typography>

            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, mt: 4 }}>
              9. Payment Information Requirements
            </Typography>
            <Typography paragraph>
              9.1. To receive payouts, Affiliates must provide accurate and
              complete payment details, including:
            </Typography>
            <Box component="ul" sx={{ pl: 3, mb: 2 }}>
              <li>Full Name (as per bank or PayPal account)</li>
              <li>Bank Account Number (for INR payouts in India)</li>
              <li>IFSC Code (for Indian banks)</li>
              <li>
                SWIFT/BIC Code, Bank Name, Bank Address, and Country (for
                international wire transfers)
              </li>
              <li>Valid government-issued identification (if requested)</li>
              <li>Tax Identification Number or PAN (for TDS compliance)</li>
            </Box>
            <Typography paragraph>
              9.2. PayPal may be offered for international affiliates. A valid
              PayPal email address must be provided.
            </Typography>
            <Typography paragraph>
              9.3. ZUUMM is not responsible for delays due to incorrect payment
              information.
            </Typography>
            <Typography paragraph>
              9.4. ZUUMM reserves the right to reject non-compliant payment
              methods.
            </Typography>

            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, mt: 4 }}>
              10. Cookie and Tracking Policy
            </Typography>
            <Typography paragraph>
              10.1. ZUUMM uses cookies and tracking technologies to attribute
              affiliate sales accurately.
            </Typography>
            <Typography paragraph>
              10.2. A 30-day cookie is placed when users click an affiliate
              link.
            </Typography>
            <Typography paragraph>
              10.3. If the purchase is completed within this window, the
              affiliate will receive credit.
            </Typography>
            <Typography paragraph>
              10.4. Clearing cookies, using a different device/browser may
              affect attribution.
            </Typography>
            <Typography paragraph>
              10.5. Affiliates must disclose use of affiliate links and comply
              with privacy laws.
            </Typography>

            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, mt: 4 }}>
              11. Final Provisions and Compliance
            </Typography>
            <Typography paragraph>
              11.1. Affiliates must comply with all laws and regulations
              including advertising and tax laws.
            </Typography>
            <Typography paragraph>
              11.2. ZUUMM may audit affiliate activity and suspend violators.
            </Typography>
            <Typography paragraph>
              11.3. These Terms are governed by Indian law and subject to
              Bangalore jurisdiction.
            </Typography>
            <Typography paragraph>
              11.4. ZUUMM may update these Terms and will notify affiliates of
              significant changes.
            </Typography>
            <Typography paragraph>
              11.5. All customer service related issues and questions in respect
              of the Service will be handled by ZUUMM.
            </Typography>
            <Typography paragraph>
              11.6. Affiliates are liable for any financial losses that ZUUMM
              might incur as a consequence of inaccurate or incomplete
              information submitted by them, including but not limited to
              payments issued to incorrect bank accounts provided by the
              Affiliates.
            </Typography>
            <Typography paragraph>
              11.7. Affiliates must maintain the confidentiality and security of
              its Login Credentials for the ZUUMM Portal. Affiliates must not
              disclose the Login Credentials to any third party under any
              circumstances. Affiliates must notify ZUUMM immediately if it
              knows or suspects that (i) its Login Credentials have been lost,
              stolen, misappropriated, or otherwise compromised, or (ii) there
              has been unauthorized use of its account.
            </Typography>
            <Typography paragraph>
              11.8. Affiliates shall not authorize, facilitate, or engage in any
              form of data scraping, data mining, or any other activity that
              involves programmatically evaluating or extracting data or content
              from ZUUMM's website. This prohibition extends to the use of
              automated systems, artificial intelligence tools, or any other
              technology designed to replicate or utilize ZUUMM's content
              outside the scope of the agreed partnership.
            </Typography>
            <Typography paragraph>
              11.9. Content Creators: In case Affiliate is a Content Creator,
              the Content Creator represents and warrants that: (i) the
              Affiliate Content does not violate any applicable law or the
              intellectual property rights or privacy rights of any third party;
              (iii) the Affiliate Content does not contain or reference
              inappropriate content (such as pornography, hate speech, or
              discriminatory content).
            </Typography>
            <Typography paragraph>
              11.10. ZUUMM may terminate these Affiliates TCs for cause
              immediately upon written notice to the other Party, if the other
              party loses any necessary government licenses, permits or
              approvals that enable them to fulfil their obligations under these
              Partner TCs.
            </Typography>
            <Typography paragraph>
              11.11. Affiliate shall not register, use, or attempt to register
              any trademarks, domain names, social media handles, or other
              identifiers that are identical or confusingly similar to ZUUMM's
              Brand Elements. If Affiliate has registered or used any such
              identifiers, Affiliate agrees to immediately transfer them to
              ZUUMM upon request and at no cost to ZUUMM.
            </Typography>
            <Typography paragraph>
              11.12. NOTWITHSTANDING ANYTHING TO THE CONTRARY HEREIN, NEITHER
              PARTY SHALL BE LIABLE FOR ANY INDIRECT, SPECIAL, INCIDENTAL,
              CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING DAMAGES FOR LOST
              DATA, LOST PROFITS, LOST REVENUE OR COSTS OF PROCUREMENT OF
              SUBSTITUTE GOODS OR SERVICES, HOWEVER CAUSED AND UNDER ANY THEORY
              OF LIABILITY, INCLUDING CONTRACT OR TORT (INCLUDING PRODUCTS
              LIABILITY, STRICT LIABILITY AND NEGLIGENCE), AND WHETHER OR NOT
              THAT PARTY WAS OR SHOULD HAVE BEEN AWARE OR ADVISED OF THE
              POSSIBILITY OF SUCH DAMAGE EXCEPT IN CASES OF THE AFFILIATE'S
              GROSS NEGLIGENCE OR WILFUL MISCONDUCT.
            </Typography>
            <Typography paragraph>
              11.13. ZUUMM may change the Service or the Affiliate TCs. In the
              event of a change to the Affiliate TCs, ZUUMM will notify
              Affiliate of the changes to the Affiliate TCs in textform (e.g. by
              email).
            </Typography>
          </Box>

          {/* Close Button */}
          <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
            <Button
              variant="contained"
              onClick={() => setOpenTermsModal(false)}
              sx={{
                backgroundColor: "#1E3A8A",
                "&:hover": { backgroundColor: "#1E3A8A" },
              }}
            >
              Close
            </Button>
          </Box>
        </Box>
      </Modal>
    );
  };

  return (
    <Box
      sx={{
        backgroundColor: "#FBFBFB",
        height: "100dvh",
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
        width: "100%",
        boxSizing: "border-box",
      }}
    >
      <AiChatHeader />
      <Box
        sx={{
          display: "flex",
          height: "calc(100dvh - 55px)",
          width: "100%",
          mt: "56px",
          maxWidth: "1440px",
          padding: { xs: "20px", md: "20px 24px" },
          mx: "auto",
          boxSizing: "border-box",
        }}
      >
        <Box
          sx={{
            width: "100%",
            height: "100%",
            display: "flex",
            flexDirection: "column",
            boxSizing: "border-box",
          }}
        >
          {/* Heading */}
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontStyle: "normal",
              fontSize: "20px",
              lineHeight: "23px",
              letterSpacing: "0%",
              color: "#333333",
              marginBottom: "20px",
              textAlign: "left",
            }}
          >
            Affiliate
          </Typography>

          <Box
            sx={{
              display: "flex",
              width: "100%",
              height: "calc(100% - 43px)",
              boxSizing: "border-box",
              gap: "24px",
              alignItems: "flex-start",
              "@media (max-width: 900px)": {
                flexDirection: "column",
              },
            }}
          >
            {/* Left Side */}
            <Box sx={{ width: { xs: "100%", md: "220px" }, flexShrink: 0 }}>
              {loadingUserDetails ? (
                <AffiliateSkeleton type="profile" />
              ) : (
                <Box
                  sx={{
                    width: { xs: "100%", md: "220px" },

                    border: "1px solid #FF39510D",
                    borderRadius: "4px",
                    overflow: "hidden",
                    flexShrink: 0,
                    "@media (max-width: 900px)": {
                      width: "100%",
                    },
                  }}
                >
                  <Box
                    sx={{
                      backgroundColor: "#FFF9F9",
                      padding: "24px",
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      gap: "12px",
                    }}
                  >
                    <Avatar
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: "#FFFFFF",
                        color: "#FF3951",
                        fontFamily: "Roboto",
                        fontWeight: 700,
                        fontSize: "20px",
                        lineHeight: "23px",
                      }}
                    >
                      {getUserInitials(userName)}
                    </Avatar>
                    <Typography
                      sx={{
                        fontFamily: "Roboto",
                        fontWeight: 500,
                        fontStyle: "normal",
                        fontSize: "16px",
                        lineHeight: "100%",
                        color: "#333333",
                        textTransform: "capitalize",
                      }}
                    >
                      {userName}
                    </Typography>
                  </Box>
                  {!profile?.is_affiliate && profile?.status !== "Pending" && (
                    <Box
                      sx={{
                        padding: "16px 12px ",
                        backgroundColor: "#FFFFFF",
                        boxSizing: "border-box",
                      }}
                    >
                      <Typography
                        sx={{
                          fontFamily: "Roboto",
                          fontWeight: 400,
                          fontStyle: "normal",
                          fontSize: "14px",
                          lineHeight: "21px",
                          color: "#1E3A8A",
                          marginBottom: "16px",
                          // border: "1px solid #FF39510D",
                          // borderRadius: "4px",
                          // padding: "4px 8px",
                          boxSizing: "border-box",
                        }}
                      >
                        Become an affiliate member by generating your own
                        referral link.
                      </Typography>

                      <Box
                        sx={{
                          display: "inline-flex",
                          // alignItems: "center",
                          gap: "8px",
                          mb: "12px",
                        }}
                      >
                        <Checkbox
                          checked={isAgree}
                          onChange={(e) => setIsAgree(e.target.checked)}
                          sx={{
                            mt: "-2px",
                            width: "14px",
                            height: "14px",
                            "& .MuiSvgIcon-root": {
                              width: "14px",
                              height: "14px",
                            },
                            "& .MuiCheckbox-root": {
                              padding: "0px",
                            },
                            color: "#FF3951",
                            "&.Mui-checked": {
                              color: "#FF3951",
                            },
                          }}
                        />
                        <Typography
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 400,
                            fontStyle: "normal",
                            fontSize: "12px",
                            lineHeight: "14px",
                            color: "#333333",
                          }}
                        >
                          I agree to the{" "}
                          <span
                            style={{
                              color: "#007aff",
                              cursor: "pointer",
                              textDecoration: "underline",
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              setOpenTermsModal(true);
                            }}
                          >
                            Affiliate Program Terms and Conditions
                          </span>
                          {"."}
                        </Typography>
                      </Box>

                      <Box
                        onClick={handleAffiliateRequest}
                        sx={{
                          width: "100%",
                          boxSizing: "border-box",
                          background:
                            "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                          "&:hover": {
                            background:
                              "linear-gradient(90deg, #5530F9 0%, #FF3951 100%)",
                            ".generate-referral-link": {
                              background:
                                "linear-gradient(90deg, #5530F9 0%, #FF3951 100%)",
                              backgroundClip: "text",
                              WebkitBackgroundClip: "text",
                              WebkitTextFillColor: "transparent",
                            },
                          },
                          borderRadius: "4.5px",
                          padding: "1px",
                          display: "flex",
                          cursor: "pointer",
                        }}
                      >
                        <Box
                          sx={{
                            width: "100%",
                            height: "100%",
                            background: "#FFFFFF",
                            borderRadius: "4px",
                          }}
                        >
                          <Button
                            disabled={isLoading}
                            sx={{
                              width: "100%",
                              height: "fit-content",
                              boxSizing: "border-box",
                              background:
                                "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)",
                              borderRadius: "4px",
                              padding: "7px 11px",
                              display: "flex",
                              justifyContent: "center",
                              textTransform: "none",
                              "&:hover": {
                                background:
                                  "linear-gradient(90deg, rgba(255, 57, 81, 0.2) 0%, rgba(85, 48, 249, 0.2) 100%)",
                              },
                            }}
                          >
                            <Typography
                              className="generate-referral-link"
                              sx={{
                                fontFamily: "Roboto",
                                fontWeight: 500,
                                fontStyle: "normal",
                                fontSize: "14px",
                                lineHeight: "16px",
                                background:
                                  "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                                backgroundClip: "text",
                                WebkitBackgroundClip: "text",
                                WebkitTextFillColor: "transparent",
                              }}
                            >
                              {isLoading
                                ? "Processing..."
                                : "Generate Referral Link"}
                            </Typography>
                          </Button>
                        </Box>
                      </Box>
                    </Box>
                  )}

                  {profile?.status === "Pending" && (
                    <Box
                      sx={{
                        padding: "16px 12px ",
                        backgroundColor: "#FFFFFF",
                        boxSizing: "border-box",
                      }}
                    >
                      <Typography
                        sx={{
                          fontFamily: "Roboto",
                          fontWeight: 400,
                          fontStyle: "normal",
                          fontSize: "14px",
                          lineHeight: "21px",
                          color: "#1E3A8A",
                          marginBottom: "16px",
                          // border: "1px solid #FF39510D",
                          // borderRadius: "4px",
                          // padding: "4px 8px",
                          boxSizing: "border-box",
                        }}
                      >
                        Your affiliate request is pending admin approval.
                        Details will appear here once approved.
                      </Typography>

                      {profile?.status === "Pending" && (
                        <Box
                          sx={{
                            padding: "8px 12px",
                            backgroundColor: "#F9F9F9",
                            borderRadius: "4px",
                            textAlign: "center",
                          }}
                        >
                          <Typography
                            sx={{
                              fontFamily: "Roboto",
                              fontWeight: 500,
                              fontStyle: "Medium",
                              fontSize: "14px",
                              lineHeight: "16px",
                              letterSpacing: "0.02em",
                              textTransform: "capitalize",
                              color: "#706E75",
                            }}
                          >
                            Request sent to Admin
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  )}
                  {profile?.is_affiliate && (
                    <Box
                      sx={{
                        padding: "8px",
                        backgroundColor: "#FFFFFF",
                        boxSizing: "border-box",
                      }}
                    >
                      <Box
                        sx={{
                          border: "1px solid #FF39510D",
                          borderRadius: "4px",
                          padding: "12px 8px",
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            gap: "12px",
                            alignItems: "center",
                            justifyContent: "space-between",
                            mb: "8px",
                          }}
                        >
                          <Typography
                            sx={{
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontStyle: "normal",
                              fontSize: "14px",
                              lineHeight: "21px",
                              color: "#1E3A8A",

                              boxSizing: "border-box",
                            }}
                          >
                            Your referral link
                          </Typography>
                          <IconButton
                            sx={{
                              padding: "0px",
                            }}
                            onClick={handleCopyReferralLink}
                          >
                            <Copy width="20px" height="20px" />
                          </IconButton>
                        </Box>

                        <Typography
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 400,
                            fontStyle: "normal",
                            fontSize: "14px",
                            lineHeight: "21px",
                            color: "#0083E7",
                            wordBreak: "break-all",
                            boxSizing: "border-box",
                          }}
                        >
                          {profile?.affiliate_url || "N/A"}
                        </Typography>
                      </Box>
                    </Box>
                  )}
                </Box>
              )}

              {!loadingUserDetails && profile?.status && (
                <Box
                  sx={{
                    width: { xs: "100%", md: "220px" },
                    marginTop: "12px",
                    borderRadius: "4px",
                    overflow: "hidden",
                    flexShrink: 0,
                    backgroundColor: "#FFFFFF",
                    display: "flex",
                    gap: "12px",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: "12px",
                    mx: { xs: "auto", md: "0px" },
                    boxSizing: "border-box",
                    "@media (max-width: 900px)": {
                      width: "100%",
                    },
                  }}
                >
                  <Typography
                    sx={{
                      fontFamily: "Roboto",
                      fontWeight: 400,
                      fontStyle: "normal",
                      fontSize: "14px",
                      lineHeight: "16px",
                      color: "#333333",
                    }}
                  >
                    Status
                  </Typography>
                  {profile?.status === "Pending" ? (
                    <Box
                      sx={{
                        padding: "4px 14px",
                        borderRadius: "4px",
                        background: "#FFC00112",
                        border: "1px solid #FFC001",
                        boxSizing: "border-box",
                        color: "#FFC001",
                        fontFamily: "Roboto",
                        fontWeight: 500,
                        fontSize: "12px",
                        lineHeight: "14px",
                      }}
                    >
                      Pending
                    </Box>
                  ) : profile?.is_affiliate ? (
                    <Box
                      sx={{
                        padding: "4px 14px",
                        borderRadius: "4px",
                        background: "#13A60012",
                        border: "1px solid #13A600",
                        boxSizing: "border-box",
                        fontFamily: "Roboto",
                        fontWeight: 500,
                        fontSize: "12px",
                        lineHeight: "14px",
                        color: "#13A600",
                      }}
                    >
                      Approved
                    </Box>
                  ) : profile?.status === "Rejected" ? (
                    <Box
                      sx={{
                        padding: "4px 14px",
                        borderRadius: "4px",
                        background: "#FF395112",
                        border: "1px solid #FF3951",
                        boxSizing: "border-box",
                        color: "#FF3951",
                        fontFamily: "Roboto",
                        fontWeight: 500,
                        fontSize: "12px",
                        lineHeight: "14px",
                      }}
                    >
                      Rejected
                    </Box>
                  ) : null}
                </Box>
              )}
            </Box>

            {/* Right Side */}
            <Box
              sx={{
                flex: 1,
                display: "flex",
                height: "100%",
                width: "100%",
                boxSizing: "border-box",
                flexDirection: "column",
              }}
            >
              {loadingReferralUsers ? (
                <AffiliateSkeleton type="table" />
              ) : (
                <>
                  <TableContainer
                    component={Paper}
                    sx={{
                      borderRadius: "12px",
                      overflow: "auto",
                      boxShadow: "none",
                      border: "1px solid #F6F6F6",
                      height: "auto",
                      minWidth: { xs: "calc(100% - 40px)", md: "100%" },
                      maxHeight: "calc(100% - 60px)",
                    }}
                  >
                    <Table stickyHeader sx={{ minWidth: 650 }}>
                      <TableHead>
                        <TableRow>
                          <TableCell
                            sx={{
                              backgroundColor: "#F9F9F9",
                              borderBottom: "1px solid #F6F6F6",
                              padding: "14px",
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontStyle: "normal",
                              fontSize: "14px",
                              lineHeight: "100%",
                              letterSpacing: "0%",
                              color: "#333333",
                              verticalAlign: "middle",
                              textAlign: "center",
                              width: "max-content",
                            }}
                          >
                            Customer Info
                          </TableCell>
                          <TableCell
                            sx={{
                              backgroundColor: "#F9F9F9",
                              borderBottom: "1px solid #F6F6F6",
                              padding: "14px",
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontStyle: "normal",
                              fontSize: "14px",
                              lineHeight: "100%",
                              letterSpacing: "0%",
                              color: "#333333",
                              verticalAlign: "middle",
                              textAlign: "center",
                              width: "max-content",
                            }}
                          >
                            Joined on
                          </TableCell>
                          <TableCell
                            sx={{
                              backgroundColor: "#F9F9F9",
                              borderBottom: "1px solid #F6F6F6",
                              padding: "14px",
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontStyle: "normal",
                              fontSize: "14px",
                              lineHeight: "100%",
                              letterSpacing: "0%",
                              color: "#333333",
                              verticalAlign: "middle",
                              textAlign: "center",
                            }}
                          >
                            Code
                          </TableCell>
                          <TableCell
                            sx={{
                              backgroundColor: "#F9F9F9",
                              borderBottom: "1px solid #F6F6F6",
                              padding: "14px",
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontStyle: "normal",
                              fontSize: "14px",
                              lineHeight: "100%",
                              letterSpacing: "0%",
                              color: "#333333",
                              verticalAlign: "middle",
                              textAlign: "center",
                            }}
                          >
                            Purchase count
                          </TableCell>
                          <TableCell
                            sx={{
                              backgroundColor: "#F9F9F9",
                              borderBottom: "1px solid #F6F6F6",
                              padding: "14px",
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontStyle: "normal",
                              fontSize: "14px",
                              lineHeight: "100%",
                              letterSpacing: "0%",
                              color: "#333333",
                              verticalAlign: "middle",
                              textAlign: "center",
                            }}
                          >
                            Status
                          </TableCell>
                          <TableCell
                            sx={{
                              backgroundColor: "#F9F9F9",
                              borderBottom: "1px solid #F6F6F6",
                              padding: "14px",
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontStyle: "normal",
                              fontSize: "14px",
                              lineHeight: "100%",
                              letterSpacing: "0%",
                              color: "#333333",
                              verticalAlign: "middle",
                              textAlign: "center",
                              maxWidth: "50px",
                              width: "50px",
                            }}
                          ></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {transformedReferralUsers?.map((row) => (
                          <TableRow
                            key={row.id}
                            sx={{
                              backgroundColor: "#FFFFFF",
                              transition: "background-color 0.3s ease",
                              "&:hover": {
                                backgroundColor: "#FFF9F9",
                              },
                            }}
                          >
                            <TableCell
                              sx={{
                                borderBottom: "1px solid #FF39510D",
                                padding: "20px",
                                fontFamily: "Roboto",
                                fontWeight: 400,
                                fontStyle: "normal",
                                fontSize: "14px",
                                lineHeight: "16px",
                                color: "#000311",
                                textAlign: "center",
                              }}
                            >
                              {row.customerInfo}
                            </TableCell>
                            <TableCell
                              sx={{
                                borderBottom: "1px solid #FF39510D",
                                padding: "20px",
                                fontFamily: "Roboto",
                                fontWeight: 400,
                                fontStyle: "normal",
                                fontSize: "14px",
                                lineHeight: "16px",
                                color: "#000311",
                                textAlign: "center",
                              }}
                            >
                              {row.joinedOn}
                            </TableCell>
                            <TableCell
                              sx={{
                                borderBottom: "1px solid #FF39510D",
                                padding: "20px",
                                fontFamily: "Roboto",
                                fontWeight: 400,
                                fontStyle: "normal",
                                fontSize: "14px",
                                lineHeight: "16px",
                                color: "#000311",
                                textAlign: "center",
                              }}
                            >
                              {row.code}
                            </TableCell>
                            <TableCell
                              sx={{
                                borderBottom: "1px solid #FF39510D",
                                padding: "20px",
                                fontFamily: "Roboto",
                                fontWeight: 400,
                                fontStyle: "normal",
                                fontSize: "14px",
                                lineHeight: "16px",
                                color: "#000311",
                                textAlign: "center",
                              }}
                            >
                              {row.purchaseCount}
                            </TableCell>
                            <TableCell
                              sx={{
                                borderBottom: "1px solid #FF39510D",
                                padding: "20px",
                                fontFamily: "Roboto",
                                fontWeight: 400,
                                fontStyle: "normal",
                                fontSize: "14px",
                                lineHeight: "16px",
                                color: "#000311",
                                textAlign: "center",
                              }}
                            >
                              <Box
                                sx={{
                                  display: "inline-block",
                                  padding: "4px 12px",
                                  borderRadius: "4px",
                                  fontSize: "12px",
                                  fontWeight: 500,
                                  textTransform: "uppercase",
                                  ...getStatusInfo(row.status),
                                }}
                              >
                                {row.status}
                              </Box>
                            </TableCell>
                            <TableCell
                              sx={{
                                borderBottom: "1px solid #FF39510D",
                                padding: "20px",
                                fontFamily: "Roboto",
                                fontWeight: 400,
                                fontStyle: "normal",
                                fontSize: "14px",
                                lineHeight: "16px",
                                color: "#000311",
                                textAlign: "center",
                              }}
                            >
                              <KeyboardArrowRightIcon
                                sx={{
                                  color: "#333333",
                                  fontSize: "16px",
                                }}
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  {(transformedReferralUsers?.length === 0 ||
                    !transformedReferralUsers) && (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        height: "100%",
                      }}
                    >
                      <Typography>No data found</Typography>
                    </Box>
                  )}

                  {/* Pagination */}
                  {transformedReferralUsers?.length > 0 && (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        boxSizing: "border-box",
                        paddingTop: "16px",
                        paddingBottom: { xs: "0px", md: "12px" },
                        marginTop: "auto",
                        py: "16px",
                      }}
                    >
                      <Typography
                        sx={{
                          fontFamily: "Roboto",
                          fontWeight: 400,
                          fontSize: "14px",
                          color: "#666666",
                        }}
                      >
                        {page * PAGE_SIZE + 1}-
                        {Math.min(
                          (page + 1) * PAGE_SIZE,
                          referralUsers?.data?.pagination?.count || 0
                        )}{" "}
                        of {referralUsers?.data?.pagination?.count || 0}
                      </Typography>
                      <Pagination
                        count={Math.ceil(
                          referralUsers?.data?.pagination?.count / PAGE_SIZE
                        )}
                        variant="outlined"
                        shape="rounded"
                        page={page + 1}
                        onChange={(event, value) => {
                          setPage(value - 1);
                          handlePageChange(value);
                        }}
                        sx={{
                          paddingRight: { xs: "0px", md: "60px" },
                          "& .MuiPaginationItem-root": {
                            fontFamily: "Roboto",
                            fontWeight: 600,
                            fontStyle: "normal",
                            fontSize: "12px",
                            lineHeight: "20px",
                            letterSpacing: "0%",
                            color: "#333333",
                            backgroundColor: "#FFFFFF",
                            border: "1px solid #FF39510D",
                            minWidth: "32px",
                            height: "32px",
                            margin: "0 2px",
                            "&:hover": {
                              backgroundColor: "#F9F9F9",
                              borderColor: "#FF39510D",
                            },
                            "&.Mui-selected": {
                              backgroundColor: "#1E3A8A",
                              color: "#FFFFFF",
                              border: "1px solid #1E3A8A",
                              "&:hover": {
                                backgroundColor: "#1E3A8A",
                                borderColor: "#1E3A8A",
                              },
                            },
                            "&.MuiPaginationItem-ellipsis": {
                              border: "none",
                              backgroundColor: "transparent",
                            },
                          },
                          "& .MuiPaginationItem-previousNext": {
                            color: "#333333",
                            "&:hover": {
                              backgroundColor: "#F9F9F9",
                            },
                            "&.Mui-disabled": {
                              color: "#CCCCCC",
                              backgroundColor: "#F6F6F6",
                            },
                          },
                        }}
                      />
                    </Box>
                  )}
                </>
              )}
            </Box>
          </Box>
        </Box>
      </Box>
      <TermsAndConditionsModal />
    </Box>
  );
};

export default AffiliatePage;
