/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Box,
  Typography,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Pagination,
  Button,
  IconButton,
} from "@mui/material";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import { Copy } from "../../components/common/SvgIcon";
import {
  fetchAffiliateProfile,
  sendAffiliateRequest,
  fetchAffiliateReferralUsers,
} from "../../store/reducers/Affiliate/apiThunk";
import {
  ToastNotifyError,
  ToastNotifySuccess,
} from "../../components/Toast/ToastNotify";
import moment from "moment";
import AffiliateSkeleton from "./AffiliateSkeleton";
import AiChatHeader from "../../components/ai-chatbot/AiChatHeader";

const PAGE_SIZE = 10;

const AffiliatePage = () => {
  const [page, setPage] = useState(0);
  const dispatch = useDispatch();

  // Get affiliate data from Redux store
  const { profile, referralUsers, isLoading } = useSelector(
    (state) => state.affiliate
  );
  const { userDetails } = useSelector((state) => state.authentication);

  const [loadingUserDetails, setLoadingUserDetails] = useState(true);
  const [loadingReferralUsers, setLoadingReferralUsers] = useState(true);

  const [transformedReferralUsers, setTransformedReferralUsers] =
    useState(null);

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoadingUserDetails(true);
        await Promise.all([dispatch(fetchAffiliateProfile()).unwrap()]);
        setTimeout(() => {
          setLoadingUserDetails(false);
        }, 1000);
      } catch (error) {
        console.error("Failed to fetch affiliate data:", error);
      }
    };

    fetchData();
  }, []);

  // Handle affiliate request
  const handleAffiliateRequest = async () => {
    try {
      setLoadingUserDetails(true);
      await dispatch(sendAffiliateRequest()).unwrap();
      // Refresh profile data after successful request
      await dispatch(fetchAffiliateProfile()).unwrap();
      setTimeout(() => {
        setLoadingUserDetails(false);
      }, 1000);
    } catch (error) {
      console.error("Failed to send affiliate request:", error);
    }
  };

  const fetchReferralUsers = async (page) => {
    const data = await dispatch(
      fetchAffiliateReferralUsers({ page_size: PAGE_SIZE, page: page })
    ).unwrap();
    const transformedData = data?.data?.data?.map((user, index) => ({
      id: index + 1,
      customerInfo: user?.email || user?.full_name,
      joinedOn: moment(user?.created_at).format("DD-MMMM-YYYY"),
      code: "N/A",
      purchaseCount: 0, // This might need to come from a different API
      status: user?.is_active ? "Active" : "Inactive",
    }));
    setTransformedReferralUsers(transformedData || []);
  };

  useEffect(() => {
    if (profile?.is_affiliate) {
      setLoadingReferralUsers(true);
      fetchReferralUsers(page + 1);
      setTimeout(() => {
        setLoadingReferralUsers(false);
      }, 1000);
    } else {
      setLoadingReferralUsers(false);
    }
  }, [profile?.is_affiliate]);

  // Handle copy referral link
  const handleCopyReferralLink = async () => {
    const referralLink = profile?.affiliate_url || "N/A";
    try {
      await navigator.clipboard.writeText(referralLink);
      // You could add a toast notification here to show success
      ToastNotifySuccess("Referral link copied to clipboard");
    } catch (error) {
      ToastNotifyError("Failed to copy referral link:", error);
    }
  };

  // Get user initials for avatar
  const getUserInitials = (name) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Get status color and text
  const getStatusInfo = (status) => {
    switch (status) {
      case "Active":
        return { color: "#08853A", background: "#13A60012" };
      case "Inactive":
        return { color: "#FFC001", background: "#FFC00112" };

      default:
        return { color: "#666666", backgroundColor: "#F5F5F5" };
    }
  };

  // Get user name from either userDetails or profile
  const userName =
    userDetails?.user?.name ||
    userDetails?.user?.full_name ||
    profile?.user?.full_name ||
    "User";

  const handlePageChange = (value) => {
    fetchReferralUsers(value);
  };

  return (
    <Box
      sx={{
        backgroundColor: "#FBFBFB",
        height: "100dvh",
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
        width: "100%",
        boxSizing: "border-box",
      }}
    >
      <AiChatHeader />
      <Box
        sx={{
          display: "flex",
          height: "calc(100dvh - 55px)",
          width: "100%",
          mt: "56px",
          maxWidth: "1440px",
          padding: { xs: "20px", md: "20px 24px" },
          mx: "auto",
          boxSizing: "border-box",
        }}
      >
        <Box
          sx={{
            width: "100%",
            height: "100%",
            display: "flex",
            flexDirection: "column",
            boxSizing: "border-box",
          }}
        >
          {/* Heading */}
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontStyle: "normal",
              fontSize: "20px",
              lineHeight: "23px",
              letterSpacing: "0%",
              color: "#333333",
              marginBottom: "20px",
              textAlign: "left",
            }}
          >
            Affiliate
          </Typography>

          <Box
            sx={{
              display: "flex",
              width: "100%",
              height: "calc(100% - 43px)",
              boxSizing: "border-box",
              gap: "24px",
              alignItems: "flex-start",
              "@media (max-width: 900px)": {
                flexDirection: "column",
              },
            }}
          >
            {/* Left Side */}
            <Box sx={{ width: { xs: "100%", md: "220px" }, flexShrink: 0 }}>
              {loadingUserDetails ? (
                <AffiliateSkeleton type="profile" />
              ) : (
                <Box
                  sx={{
                    width: { xs: "100%", md: "220px" },

                    border: "1px solid #FF39510D",
                    borderRadius: "4px",
                    overflow: "hidden",
                    flexShrink: 0,
                    "@media (max-width: 900px)": {
                      width: "100%",
                    },
                  }}
                >
                  <Box
                    sx={{
                      backgroundColor: "#FFF9F9",
                      padding: "24px",
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      gap: "12px",
                    }}
                  >
                    <Avatar
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: "#FFFFFF",
                        color: "#FF3951",
                        fontFamily: "Roboto",
                        fontWeight: 700,
                        fontSize: "20px",
                        lineHeight: "23px",
                      }}
                    >
                      {getUserInitials(userName)}
                    </Avatar>
                    <Typography
                      sx={{
                        fontFamily: "Roboto",
                        fontWeight: 500,
                        fontStyle: "normal",
                        fontSize: "16px",
                        lineHeight: "100%",
                        color: "#333333",
                        textTransform: "capitalize",
                      }}
                    >
                      {userName}
                    </Typography>
                  </Box>
                  {!profile?.is_affiliate && profile?.status !== "Pending" && (
                    <Box
                      sx={{
                        padding: "16px 12px ",
                        backgroundColor: "#FFFFFF",
                        boxSizing: "border-box",
                      }}
                    >
                      <Typography
                        sx={{
                          fontFamily: "Roboto",
                          fontWeight: 400,
                          fontStyle: "normal",
                          fontSize: "14px",
                          lineHeight: "21px",
                          color: "#1E3A8A",
                          marginBottom: "16px",
                          // border: "1px solid #FF39510D",
                          // borderRadius: "4px",
                          // padding: "4px 8px",
                          boxSizing: "border-box",
                        }}
                      >
                        Become an affiliate member by generating your own
                        referral link
                      </Typography>

                      <Box
                        onClick={handleAffiliateRequest}
                        sx={{
                          width: "100%",
                          boxSizing: "border-box",
                          background:
                            "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                          "&:hover": {
                            background:
                              "linear-gradient(90deg, #5530F9 0%, #FF3951 100%)",
                            ".generate-referral-link": {
                              background:
                                "linear-gradient(90deg, #5530F9 0%, #FF3951 100%)",
                              backgroundClip: "text",
                              WebkitBackgroundClip: "text",
                              WebkitTextFillColor: "transparent",
                            },
                          },
                          borderRadius: "4.5px",
                          padding: "1px",
                          display: "flex",
                          cursor: "pointer",
                        }}
                      >
                        <Box
                          sx={{
                            width: "100%",
                            height: "100%",
                            background: "#FFFFFF",
                            borderRadius: "4px",
                          }}
                        >
                          <Button
                            disabled={isLoading}
                            sx={{
                              width: "100%",
                              height: "fit-content",
                              boxSizing: "border-box",
                              background:
                                "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)",
                              borderRadius: "4px",
                              padding: "7px 11px",
                              display: "flex",
                              justifyContent: "center",
                              textTransform: "none",
                              "&:hover": {
                                background:
                                  "linear-gradient(90deg, rgba(255, 57, 81, 0.2) 0%, rgba(85, 48, 249, 0.2) 100%)",
                              },
                            }}
                          >
                            <Typography
                              className="generate-referral-link"
                              sx={{
                                fontFamily: "Roboto",
                                fontWeight: 500,
                                fontStyle: "normal",
                                fontSize: "14px",
                                lineHeight: "16px",
                                background:
                                  "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                                backgroundClip: "text",
                                WebkitBackgroundClip: "text",
                                WebkitTextFillColor: "transparent",
                              }}
                            >
                              {isLoading
                                ? "Processing..."
                                : "Generate Referral Link"}
                            </Typography>
                          </Button>
                        </Box>
                      </Box>
                    </Box>
                  )}

                  {profile?.status === "Pending" && (
                    <Box
                      sx={{
                        padding: "16px 12px ",
                        backgroundColor: "#FFFFFF",
                        boxSizing: "border-box",
                      }}
                    >
                      <Typography
                        sx={{
                          fontFamily: "Roboto",
                          fontWeight: 400,
                          fontStyle: "normal",
                          fontSize: "14px",
                          lineHeight: "21px",
                          color: "#1E3A8A",
                          marginBottom: "16px",
                          // border: "1px solid #FF39510D",
                          // borderRadius: "4px",
                          // padding: "4px 8px",
                          boxSizing: "border-box",
                        }}
                      >
                        Become an affiliate member by generating your own
                        referral link
                      </Typography>

                      {profile?.status === "Pending" && (
                        <Box
                          sx={{
                            padding: "8px 12px",
                            backgroundColor: "#F9F9F9",
                            borderRadius: "4px",
                            textAlign: "center",
                          }}
                        >
                          <Typography
                            sx={{
                              fontFamily: "Roboto",
                              fontWeight: 500,
                              fontStyle: "Medium",
                              fontSize: "14px",
                              lineHeight: "16px",
                              letterSpacing: "0.02em",
                              textTransform: "capitalize",
                              color: "#706E75",
                            }}
                          >
                            Request sent to Admin
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  )}
                  {profile?.is_affiliate && (
                    <Box
                      sx={{
                        padding: "8px",
                        backgroundColor: "#FFFFFF",
                        boxSizing: "border-box",
                      }}
                    >
                      <Box
                        sx={{
                          border: "1px solid #FF39510D",
                          borderRadius: "4px",
                          padding: "12px 8px",
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            gap: "12px",
                            alignItems: "center",
                            justifyContent: "space-between",
                            mb: "8px",
                          }}
                        >
                          <Typography
                            sx={{
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontStyle: "normal",
                              fontSize: "14px",
                              lineHeight: "21px",
                              color: "#1E3A8A",

                              boxSizing: "border-box",
                            }}
                          >
                            Referral Link
                          </Typography>
                          <IconButton
                            sx={{
                              padding: "0px",
                            }}
                            onClick={handleCopyReferralLink}
                          >
                            <Copy width="20px" height="20px" />
                          </IconButton>
                        </Box>

                        <Typography
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 400,
                            fontStyle: "normal",
                            fontSize: "14px",
                            lineHeight: "21px",
                            color: "#0083E7",
                            wordBreak: "break-all",
                            boxSizing: "border-box",
                          }}
                        >
                          {profile?.affiliate_url || "N/A"}
                        </Typography>
                      </Box>
                    </Box>
                  )}
                </Box>
              )}

              {!loadingUserDetails && profile?.status && (
                <Box
                  sx={{
                    width: { xs: "100%", md: "220px" },
                    marginTop: "12px",
                    borderRadius: "4px",
                    overflow: "hidden",
                    flexShrink: 0,
                    backgroundColor: "#FFFFFF",
                    display: "flex",
                    gap: "12px",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: "12px",
                    mx: { xs: "auto", md: "0px" },
                    boxSizing: "border-box",
                    "@media (max-width: 900px)": {
                      width: "100%",
                    },
                  }}
                >
                  <Typography
                    sx={{
                      fontFamily: "Roboto",
                      fontWeight: 400,
                      fontStyle: "normal",
                      fontSize: "14px",
                      lineHeight: "16px",
                      color: "#333333",
                    }}
                  >
                    Status
                  </Typography>
                  {profile?.status === "Pending" ? (
                    <Box
                      sx={{
                        padding: "4px 14px",
                        borderRadius: "4px",
                        background: "#FFC00112",
                        border: "1px solid #FFC001",
                        boxSizing: "border-box",
                        color: "#FFC001",
                        fontFamily: "Roboto",
                        fontWeight: 500,
                        fontSize: "12px",
                        lineHeight: "14px",
                      }}
                    >
                      Pending
                    </Box>
                  ) : profile?.is_affiliate ? (
                    <Box
                      sx={{
                        padding: "4px 14px",
                        borderRadius: "4px",
                        background: "#13A60012",
                        border: "1px solid #13A600",
                        boxSizing: "border-box",
                        fontFamily: "Roboto",
                        fontWeight: 500,
                        fontSize: "12px",
                        lineHeight: "14px",
                        color: "#13A600",
                      }}
                    >
                      Approved
                    </Box>
                  ) : profile?.status === "Rejected" ? (
                    <Box
                      sx={{
                        padding: "4px 14px",
                        borderRadius: "4px",
                        background: "#FF395112",
                        border: "1px solid #FF3951",
                        boxSizing: "border-box",
                        color: "#FF3951",
                        fontFamily: "Roboto",
                        fontWeight: 500,
                        fontSize: "12px",
                        lineHeight: "14px",
                      }}
                    >
                      Rejected
                    </Box>
                  ) : null}
                </Box>
              )}
            </Box>

            {/* Right Side */}
            <Box
              sx={{
                flex: 1,
                display: "flex",
                height: "100%",
                width: "100%",
                boxSizing: "border-box",
                flexDirection: "column",
              }}
            >
              {loadingReferralUsers ? (
                <AffiliateSkeleton type="table" />
              ) : (
                <>
                  <TableContainer
                    component={Paper}
                    sx={{
                      borderRadius: "12px",
                      overflow: "auto",
                      boxShadow: "none",
                      border: "1px solid #F6F6F6",
                      height: "auto",
                      minWidth: { xs: "calc(100% - 40px)", md: "100%" },
                      maxHeight: "calc(100% - 60px)",
                    }}
                  >
                    <Table stickyHeader sx={{ minWidth: 650 }}>
                      <TableHead>
                        <TableRow>
                          <TableCell
                            sx={{
                              backgroundColor: "#F9F9F9",
                              borderBottom: "1px solid #F6F6F6",
                              padding: "14px",
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontStyle: "normal",
                              fontSize: "14px",
                              lineHeight: "100%",
                              letterSpacing: "0%",
                              color: "#333333",
                              verticalAlign: "middle",
                              textAlign: "center",
                              width: "max-content",
                            }}
                          >
                            Customer Info
                          </TableCell>
                          <TableCell
                            sx={{
                              backgroundColor: "#F9F9F9",
                              borderBottom: "1px solid #F6F6F6",
                              padding: "14px",
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontStyle: "normal",
                              fontSize: "14px",
                              lineHeight: "100%",
                              letterSpacing: "0%",
                              color: "#333333",
                              verticalAlign: "middle",
                              textAlign: "center",
                              width: "max-content",
                            }}
                          >
                            Joined on
                          </TableCell>
                          <TableCell
                            sx={{
                              backgroundColor: "#F9F9F9",
                              borderBottom: "1px solid #F6F6F6",
                              padding: "14px",
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontStyle: "normal",
                              fontSize: "14px",
                              lineHeight: "100%",
                              letterSpacing: "0%",
                              color: "#333333",
                              verticalAlign: "middle",
                              textAlign: "center",
                            }}
                          >
                            Code
                          </TableCell>
                          <TableCell
                            sx={{
                              backgroundColor: "#F9F9F9",
                              borderBottom: "1px solid #F6F6F6",
                              padding: "14px",
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontStyle: "normal",
                              fontSize: "14px",
                              lineHeight: "100%",
                              letterSpacing: "0%",
                              color: "#333333",
                              verticalAlign: "middle",
                              textAlign: "center",
                            }}
                          >
                            Purchase count
                          </TableCell>
                          <TableCell
                            sx={{
                              backgroundColor: "#F9F9F9",
                              borderBottom: "1px solid #F6F6F6",
                              padding: "14px",
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontStyle: "normal",
                              fontSize: "14px",
                              lineHeight: "100%",
                              letterSpacing: "0%",
                              color: "#333333",
                              verticalAlign: "middle",
                              textAlign: "center",
                            }}
                          >
                            Status
                          </TableCell>
                          <TableCell
                            sx={{
                              backgroundColor: "#F9F9F9",
                              borderBottom: "1px solid #F6F6F6",
                              padding: "14px",
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontStyle: "normal",
                              fontSize: "14px",
                              lineHeight: "100%",
                              letterSpacing: "0%",
                              color: "#333333",
                              verticalAlign: "middle",
                              textAlign: "center",
                              maxWidth: "50px",
                              width: "50px",
                            }}
                          ></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {transformedReferralUsers?.map((row) => (
                          <TableRow
                            key={row.id}
                            sx={{
                              backgroundColor: "#FFFFFF",
                              transition: "background-color 0.3s ease",
                              "&:hover": {
                                backgroundColor: "#FFF9F9",
                              },
                            }}
                          >
                            <TableCell
                              sx={{
                                borderBottom: "1px solid #FF39510D",
                                padding: "20px",
                                fontFamily: "Roboto",
                                fontWeight: 400,
                                fontStyle: "normal",
                                fontSize: "14px",
                                lineHeight: "16px",
                                color: "#000311",
                                textAlign: "center",
                              }}
                            >
                              {row.customerInfo}
                            </TableCell>
                            <TableCell
                              sx={{
                                borderBottom: "1px solid #FF39510D",
                                padding: "20px",
                                fontFamily: "Roboto",
                                fontWeight: 400,
                                fontStyle: "normal",
                                fontSize: "14px",
                                lineHeight: "16px",
                                color: "#000311",
                                textAlign: "center",
                              }}
                            >
                              {row.joinedOn}
                            </TableCell>
                            <TableCell
                              sx={{
                                borderBottom: "1px solid #FF39510D",
                                padding: "20px",
                                fontFamily: "Roboto",
                                fontWeight: 400,
                                fontStyle: "normal",
                                fontSize: "14px",
                                lineHeight: "16px",
                                color: "#000311",
                                textAlign: "center",
                              }}
                            >
                              {row.code}
                            </TableCell>
                            <TableCell
                              sx={{
                                borderBottom: "1px solid #FF39510D",
                                padding: "20px",
                                fontFamily: "Roboto",
                                fontWeight: 400,
                                fontStyle: "normal",
                                fontSize: "14px",
                                lineHeight: "16px",
                                color: "#000311",
                                textAlign: "center",
                              }}
                            >
                              {row.purchaseCount}
                            </TableCell>
                            <TableCell
                              sx={{
                                borderBottom: "1px solid #FF39510D",
                                padding: "20px",
                                fontFamily: "Roboto",
                                fontWeight: 400,
                                fontStyle: "normal",
                                fontSize: "14px",
                                lineHeight: "16px",
                                color: "#000311",
                                textAlign: "center",
                              }}
                            >
                              <Box
                                sx={{
                                  display: "inline-block",
                                  padding: "4px 12px",
                                  borderRadius: "4px",
                                  fontSize: "12px",
                                  fontWeight: 500,
                                  textTransform: "uppercase",
                                  ...getStatusInfo(row.status),
                                }}
                              >
                                {row.status}
                              </Box>
                            </TableCell>
                            <TableCell
                              sx={{
                                borderBottom: "1px solid #FF39510D",
                                padding: "20px",
                                fontFamily: "Roboto",
                                fontWeight: 400,
                                fontStyle: "normal",
                                fontSize: "14px",
                                lineHeight: "16px",
                                color: "#000311",
                                textAlign: "center",
                              }}
                            >
                              <KeyboardArrowRightIcon
                                sx={{
                                  color: "#333333",
                                  fontSize: "16px",
                                }}
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  {(transformedReferralUsers?.length === 0 ||
                    !transformedReferralUsers) && (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        height: "100%",
                      }}
                    >
                      <Typography>No data found</Typography>
                    </Box>
                  )}

                  {/* Pagination */}
                  {transformedReferralUsers?.length > 0 && (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        boxSizing: "border-box",
                        paddingTop: "16px",
                        paddingBottom: { xs: "0px", md: "12px" },
                        marginTop: "auto",
                        py: "16px",
                      }}
                    >
                      <Typography
                        sx={{
                          fontFamily: "Roboto",
                          fontWeight: 400,
                          fontSize: "14px",
                          color: "#666666",
                        }}
                      >
                        {page * PAGE_SIZE + 1}-
                        {Math.min(
                          (page + 1) * PAGE_SIZE,
                          referralUsers?.data?.pagination?.count || 0
                        )}{" "}
                        of {referralUsers?.data?.pagination?.count || 0}
                      </Typography>
                      <Pagination
                        count={Math.ceil(
                          referralUsers?.data?.pagination?.count / PAGE_SIZE
                        )}
                        variant="outlined"
                        shape="rounded"
                        page={page + 1}
                        onChange={(event, value) => {
                          setPage(value - 1);
                          handlePageChange(value);
                        }}
                        sx={{
                          paddingRight: { xs: "0px", md: "60px" },
                          "& .MuiPaginationItem-root": {
                            fontFamily: "Roboto",
                            fontWeight: 600,
                            fontStyle: "normal",
                            fontSize: "12px",
                            lineHeight: "20px",
                            letterSpacing: "0%",
                            color: "#333333",
                            backgroundColor: "#FFFFFF",
                            border: "1px solid #FF39510D",
                            minWidth: "32px",
                            height: "32px",
                            margin: "0 2px",
                            "&:hover": {
                              backgroundColor: "#F9F9F9",
                              borderColor: "#FF39510D",
                            },
                            "&.Mui-selected": {
                              backgroundColor: "#1E3A8A",
                              color: "#FFFFFF",
                              border: "1px solid #1E3A8A",
                              "&:hover": {
                                backgroundColor: "#1E3A8A",
                                borderColor: "#1E3A8A",
                              },
                            },
                            "&.MuiPaginationItem-ellipsis": {
                              border: "none",
                              backgroundColor: "transparent",
                            },
                          },
                          "& .MuiPaginationItem-previousNext": {
                            color: "#333333",
                            "&:hover": {
                              backgroundColor: "#F9F9F9",
                            },
                            "&.Mui-disabled": {
                              color: "#CCCCCC",
                              backgroundColor: "#F6F6F6",
                            },
                          },
                        }}
                      />
                    </Box>
                  )}
                </>
              )}
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default AffiliatePage;
