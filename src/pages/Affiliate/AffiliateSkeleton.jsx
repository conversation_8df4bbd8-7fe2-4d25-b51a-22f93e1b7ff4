import React from "react";
import {
  Box,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";

const AffiliateSkeleton = ({ type = "profile" }) => {
  if (type === "profile") {
    return (
      <Box
        sx={{
          width: { xs: "100%", md: "220px" },
          border: "1px solid #FF39510D",
          borderRadius: "4px",
          overflow: "hidden",
          flexShrink: 0,
          "@media (max-width: 900px)": {
            width: "100%",
          },
        }}
      >
        <Box
          sx={{
            backgroundColor: "#FFF9F9",
            padding: "24px",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: "12px",
          }}
        >
          <Skeleton
            variant="circular"
            width={60}
            height={60}
            sx={{ bgcolor: "#F0F0F0" }}
          />
          <Skeleton
            variant="text"
            width={120}
            height={20}
            sx={{ bgcolor: "#F0F0F0" }}
          />
        </Box>
        <Box
          sx={{
            padding: "16px 12px",
            backgroundColor: "#FFFFFF",
            boxSizing: "border-box",
          }}
        >
          <Skeleton
            variant="text"
            width="100%"
            height={40}
            sx={{ bgcolor: "#F0F0F0", mb: 2 }}
          />
          <Skeleton
            variant="rectangular"
            width="100%"
            height={40}
            sx={{ bgcolor: "#F0F0F0", borderRadius: "4px" }}
          />
        </Box>
        <Box
          sx={{
            width: "220px",
            marginTop: "12px",
            borderRadius: "4px",
            overflow: "hidden",
            flexShrink: 0,
            backgroundColor: "#FFFFFF",
            display: "flex",
            gap: "12px",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "12px",
            boxSizing: "border-box",
            "@media (max-width: 900px)": {
              width: "100%",
            },
          }}
        >
          <Skeleton
            variant="text"
            width={60}
            height={16}
            sx={{ bgcolor: "#F0F0F0" }}
          />
          <Skeleton
            variant="rectangular"
            width={80}
            height={24}
            sx={{ bgcolor: "#F0F0F0", borderRadius: "4px" }}
          />
        </Box>
      </Box>
    );
  }

  if (type === "table") {
    return (
      <TableContainer
        component={Paper}
        sx={{
          borderRadius: "12px",
          overflowY: "auto",
          boxShadow: "none",
          border: "1px solid #F6F6F6",
          height: "auto",
          maxHeight: "calc(100% - 60px)",
          overflow: "hidden",
        }}
      >
        <Table stickyHeader sx={{ minWidth: 650 }}>
          <TableHead>
            <TableRow>
              {[1, 2, 3, 4, 5, 6].map((index) => (
                <TableCell
                  key={index}
                  sx={{
                    backgroundColor: "#F9F9F9",
                    borderBottom: "1px solid #F6F6F6",
                    padding: "14px",
                    fontFamily: "Roboto",
                    fontWeight: 400,
                    fontStyle: "normal",
                    fontSize: "14px",
                    lineHeight: "100%",
                    letterSpacing: "0%",
                    color: "#333333",
                    verticalAlign: "middle",
                    textAlign: "center",
                    ...(index === 6 && {
                      maxWidth: "50px",
                      width: "50px",
                    }),
                  }}
                >
                  <Skeleton
                    variant="text"
                    width={index === 6 ? 20 : 80}
                    height={16}
                    sx={{ bgcolor: "#F0F0F0", mx: "auto" }}
                  />
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {[1, 2, 3, 4, 5].map((rowIndex) => (
              <TableRow
                key={rowIndex}
                sx={{
                  backgroundColor: "#FFFFFF",
                  "&:hover": {
                    backgroundColor: "#FFF9F9",
                  },
                }}
              >
                {[1, 2, 3, 4, 5, 6].map((cellIndex) => (
                  <TableCell
                    key={cellIndex}
                    sx={{
                      borderBottom: "1px solid #FF39510D",
                      padding: "20px",
                      fontFamily: "Roboto",
                      fontWeight: 400,
                      fontStyle: "normal",
                      fontSize: "14px",
                      lineHeight: "16px",
                      color: "#000311",
                      textAlign: "center",
                      ...(cellIndex === 6 && {
                        maxWidth: "50px",
                        width: "50px",
                      }),
                    }}
                  >
                    {cellIndex === 5 ? (
                      <Skeleton
                        variant="rectangular"
                        width={80}
                        height={24}
                        sx={{
                          bgcolor: "#F0F0F0",
                          borderRadius: "4px",
                          mx: "auto",
                        }}
                      />
                    ) : cellIndex === 6 ? (
                      <Skeleton
                        variant="circular"
                        width={16}
                        height={16}
                        sx={{ bgcolor: "#F0F0F0", mx: "auto" }}
                      />
                    ) : (
                      <Skeleton
                        variant="text"
                        width={cellIndex === 1 ? 100 : 80}
                        height={16}
                        sx={{ bgcolor: "#F0F0F0", mx: "auto" }}
                      />
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  }

  return null;
};

export default AffiliateSkeleton;
