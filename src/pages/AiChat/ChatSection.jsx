import React, { Fragment } from "react";
import { Box } from "@mui/material";
import NormalMessage from "./messageTypes/NormalMessage";
import PackageMessage from "./messageTypes/PackageMessage";
import SelectMessage from "./messageTypes/SelectMessage";
import CenterMessage from "./messageTypes/CenterMessage";
import CounterMessage from "./messageTypes/CounterMessage";
import WeatherMultiMessage from "./messageTypes/WeatherMultiMessage";
import WeatherSingleMessage from "./messageTypes/WeatherSingleMessage";
import DestinationMessage from "./messageTypes/DestinationMessage";

const ChatSection = ({ chats = [], isPackageOpen, setIsPackageOpen }) => {
  return (
    <Box
      sx={{
        width: "100%",
        height: "auto",
        minHeight: "100%",
        display: "flex",
        flexDirection: "column-reverse",
        gap: "24px",
        boxSizing: "border-box",
      }}
    >
      {chats.map((chat) => (
        <Fragment key={chat.id}>
          {chat.messageType === "text" ? (
            <NormalMessage
              message={chat.message}
              sender={chat.sender}
              time={chat.timestamp}
            />
          ) : chat.messageType === "package" ? (
            <PackageMessage
              list={chat.list}
              sender={chat.sender}
              time={chat.timestamp}
              isPackageOpen={isPackageOpen}
              setIsPackageOpen={setIsPackageOpen}
            />
          ) : chat.messageType === "select" ? (
            <SelectMessage
              message={chat.message}
              sender={chat.sender}
              time={chat.timestamp}
            />
          ) : chat.messageType === "center" ? (
            <CenterMessage
              message={chat.message}
              sender={chat.sender}
              time={chat.timestamp}
            />
          ) : chat.messageType === "counter" ? (
            <CounterMessage
              message={chat.message}
              sender={chat.sender}
              time={chat.timestamp}
            />
          ) : chat.messageType === "weather_multi" ? (
            <WeatherMultiMessage
              message={chat.message}
              sender={chat.sender}
              time={chat.timestamp}
            />
          ) : chat.messageType === "weather_single" ? (
            <WeatherSingleMessage
              message={chat.message}
              sender={chat.sender}
              time={chat.timestamp}
            />
          ) : chat.messageType === "destination" ? (
            <DestinationMessage
              list={chat.list}
              sender={chat.sender}
              time={chat.timestamp}
            />
          ) : null}
        </Fragment>
      ))}
    </Box>
  );
};

export default ChatSection;
