import { Box, Typography } from "@mui/material";
import { AIRobotHead, WeatherIcon } from "../../../components/common/SvgIcon";
import moment from "moment/moment";

const WeatherMultiMessage = ({ message, sender, time }) => {
  const isAI = sender === "ai";
  return (
    <Box
      sx={{
        width: "fit-content",
        maxWidth: "88%",
        height: "fit-content",
        display: "flex",
        flexDirection: "row",
        gap: "12px",
        alignSelf: isAI ? "flex-start" : "flex-end",
      }}
    >
      {isAI && (
        <Box sx={{ width: "40px", height: "40px" }}>
          <AIRobotHead />
        </Box>
      )}
      <Box display="flex" flexDirection="column" gap="8px">
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            background: isAI
              ? "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)"
              : "#FF39510D",
            borderRadius: isAI ? "0px 12px 12px 12px" : "12px 12px 0px 12px",
            p: "9.5px 24px",
            boxSizing: "border-box",
            whiteSpace: "pre-wrap",
            wordBreak: "break-word",
            fontFamily: "Roboto",
            fontSize: "14px",
            fontWeight: 400,
            lineHeight: "16px",
            color: "#333333",
          }}
        >
          {message?.message || ""}
        </Box>

        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            fontSize: "12px",
            fontWeight: 400,
            lineHeight: "14px",
            color: "#706E75",
            alignSelf: isAI ? "flex-start" : "flex-end",
          }}
        >
          {moment(time).isSame(moment(), "day")
            ? `Today, ${moment(time).format("HH:mm")}`
            : moment(time).format("DD MMM YYYY, HH:mm")}
        </Box>

        <Box>
          <Typography
            sx={{
              fontSize: "14px",
              fontWeight: 500,
              lineHeight: "16px",
              color: "#333333",
            }}
          >
            Currently 84° · Light fog
            <br />
            <span
              style={{
                fontSize: "12px",
                fontWeight: 400,
                lineHeight: "14px",
              }}
            >
              Panaji, India
            </span>
          </Typography>
        </Box>
        <Box>
          {message?.forecast?.map((item, index) => (
            <Box key={index + "weather"}>
              <Box
                sx={{
                  display: "flex",
                  gap: "16px",
                  alignItems: "center",
                  fontFamily: "Roboto",
                  fontSize: "12px",
                  fontWeight: 400,
                  lineHeight: "14px",
                  color: "#333333",
                }}
              >
                <Box>
                  <WeatherIcon width="24px" height="24px" />
                </Box>
                <Box
                  sx={{
                    flex: 1,
                    display: "flex",
                    gap: "16px",
                    alignItems: "center",
                    padding: "4px 0px",
                  }}
                >
                  <Typography
                    sx={{
                      minWidth: "64px",
                      flexShrink: 0,
                      fontFamily: "Roboto",
                      fontSize: "12px",
                      fontWeight: 400,
                      lineHeight: "14px",
                      color: "#333333",
                    }}
                  >
                    {moment(item.date).format("dddd")}
                  </Typography>
                  <Typography
                    sx={{
                      fontFamily: "Roboto",
                      fontSize: "12px",
                      fontWeight: 400,
                      lineHeight: "14px",
                      color: "#333333",
                    }}
                  >
                    {item.temperature}°
                  </Typography>
                  <Typography
                    sx={{
                      fontFamily: "Roboto",
                      fontSize: "12px",
                      fontWeight: 400,
                      lineHeight: "14px",
                      color: "#333333",
                    }}
                  >
                    {item.description}
                  </Typography>
                </Box>
              </Box>
              <Box
                sx={{
                  width: "100%",
                  height: "2px",
                  m: "6px 0px",
                  backgroundColor: "rgba(51, 51, 51, 0.05)",
                }}
              ></Box>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default WeatherMultiMessage;
