import { Box } from "@mui/material";
import { Location } from "../../../components/common/SvgIcon";

const CenterMessage = ({ message, sender, time }) => {
  const isAI = sender === "ai";
  return (
    <Box
      sx={{
        width: "fit-content",
        maxWidth: "536px",
        height: "fit-content",
        display: "flex",
        flexDirection: "column",
        alignSelf: isAI ? "flex-start" : "center",
      }}
    >
      <Box
        sx={{
          fontFamily: "Roboto",
          fontWeight: 400,
          fontSize: "24px",
          lineHeight: "28px",
          color: "#1E3A8A",
        }}
      >
        Hello{" "}
        <Box component="span" sx={{ fontWeight: 700 }}>
          Jenny,
        </Box>
      </Box>
      <Box
        sx={{
          mt: "8px",
          fontFamily: "Roboto",
          background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
          backgroundClip: "text",
          WebkitBackgroundClip: "text",
          WebkitTextFillColor: "transparent",
          fontSize: "24px",
          lineHeight: "28px",
          fontWeight: 500,
        }}
      >
        How can I help you today?
      </Box>
      <Box
        sx={{
          mt: "8px",
          fontFamily: "Roboto",
          fontSize: "16px",
          lineHeight: "19px",
          color: "#333333",
        }}
      >
        I am here to assist you in planning your experience. Ask me anything.
      </Box>
      <Box
        sx={{
          mt: "36px",
          display: "flex",
          flexDirection: "column",
          gap: "12px",
        }}
      >
        {[1, 2, 3].map((item, index) => (
          <Box
            key={index + "center"}
            sx={{
              width: "100%",
              background: "#F9F9F9",
              borderRadius: "8px",
              padding: "18px 12px",
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              gap: "12px",
            }}
          >
            <Location />
            <Box>
              <Box
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 500,
                  fontSize: "14px",
                  lineHeight: "16px",
                  color: "#333333",
                }}
              >
                Plan a trip
              </Box>
              <Box
                sx={{
                  mt: "4px",
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "12px",
                  lineHeight: "14px",
                  color: "#706E75",
                }}
              >
                I'm here to help you explore exciting travel options. Feel free
                to ask me anything about your next adventure!
              </Box>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default CenterMessage;
