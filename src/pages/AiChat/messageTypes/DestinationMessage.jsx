import { Box, Typography } from "@mui/material";
import { AIRobotHead } from "../../../components/common/SvgIcon";
import moment from "moment/moment";
import SecondaryButton from "../../../components/common/Button/SecondaryButton";
import { formatIndianNumber } from "../../../utils/helper";

const DestinationExploreCard = ({ item }) => {
  return (
    <Box
      sx={{
        width: "100%",
        height: "190px",
        background: "#FF39510D",
        borderRadius: "4px",
        boxSizing: "border-box",
        position: "relative",
        boxShadow: "0px 2.18px 4.37px 0px #0000000D",
        overflow: "hidden",
      }}
    >
      <Box
        component={"img"}
        src={item.image}
        alt="destination-image"
        sx={{
          width: "100%",
          height: "100%",
          objectFit: "cover",
          // borderRadius: "4px",
        }}
      />
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",

          boxSizing: "border-box",
          background:
            "linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #333333 100%)",
          zIndex: 1,
        }}
      >
        <Box
          sx={{
            display: "flex",
            width: "100%",
            height: "100%",
            boxSizing: "border-box",
            position: "relative",
            padding: "12px",
          }}
        >
          <Box
            sx={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "fit-content",
              height: "fit-content",
              fontSize: "10px",
              fontWeight: 400,
              lineHeight: "12px",
              color: "#333333",
              background: "#FFF9F9",
              p: "4px",
              zIndex: 2,
            }}
          >
            {`Best Time to visit - `}
            <Box
              component={"span"}
              sx={{
                fontWeight: 600,
              }}
            >
              {item?.bestTimeToVisit || ""}
            </Box>
          </Box>
          <Box
            sx={{
              mt: "auto",
              width: "100%",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "flex-end",
              gap: "4px",
            }}
          >
            <Box>
              <Typography
                sx={{
                  fontSize: "14px",
                  fontWeight: 700,
                  lineHeight: "16px",
                  color: "#FFFFFF",
                  mb: "4px",
                }}
              >
                {item?.title || "Paris"}
              </Typography>
              <Typography
                sx={{
                  fontSize: "10px",
                  fontWeight: 400,
                  lineHeight: "12px",
                  color: "#FFFFFF",
                }}
              >
                Starting{" "}
                <span style={{ fontWeight: 500 }}>
                  ₹{formatIndianNumber(Math.round(item?.price || 0))}
                </span>
                /per person
              </Typography>
            </Box>
            <SecondaryButton
              px="3px"
              py="3px"
              fontSize="12px"
              fontWeight={500}
              lineHeight="14px"
              width="fit-content"
              borderRadius="2px"
              borderColor="#FFFFFF"
              hoverBackgroundColor="#ffffff37"
              color="#FFFFFF"
              sx={{
                wordBreak: "keep-all",
                height: "fit-content",
                flexShrink: 0,
              }}
            >
              Customize
            </SecondaryButton>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

const DestinationMessage = ({ list, sender, time, setIsPackageOpen }) => {
  const isAI = sender === "ai";
  return (
    <Box
      sx={{
        width: "fit-content",
        maxWidth: "88%",
        height: "fit-content",
        display: "flex",
        flexDirection: "row",
        gap: "12px",
        alignSelf: isAI ? "flex-start" : "flex-end",
      }}
    >
      {isAI && (
        <Box sx={{ width: "40px", height: "40px" }}>
          <AIRobotHead />
        </Box>
      )}
      <Box display="flex" flexDirection="column" gap="8px">
        <Box
          sx={{
            width: "fit-content",
            // maxWidth: {
            //   xs: "calc(90dvw - 40px 40px - 12px)",
            //   sm: "498px",
            // },
            height: "fit-content",
            background: isAI
              ? "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)"
              : "#FF39510D",
            borderRadius: isAI ? "0px 12px 12px 12px" : "12px 12px 0px 12px",
            p: "9.5px 9.5px",
            boxSizing: "border-box",
            whiteSpace: "pre-wrap",
            wordBreak: "break-word",
            fontFamily: "Roboto",
            fontSize: "14px",
            fontWeight: 400,
            lineHeight: "16px",
            color: "#333333",
          }}
        >
          <Box
            sx={{ width: "100%", display: "flex", justifyContent: "flex-end" }}
          >
            <Box
              sx={{
                width: "fit-content",
                fontSize: 10,
                fontWeight: 400,
                lineHeight: "12px",
                color: "#0083E7",
                letterSpacing: "0.02em",
                cursor: "pointer",
                mb: 1,
                "&:hover": {
                  textDecoration: "underline",
                },
              }}
            >
              See all
            </Box>
          </Box>
          <Box
            sx={{
              width: "100%",
              display: "flex",
              alignItems: "center",
              gap: "7.5px",
            }}
          >
            {list?.slice(0, 2)?.map((item, index) => (
              <DestinationExploreCard key={index + "destination"} item={item} />
            ))}
          </Box>
        </Box>
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            fontSize: "12px",
            fontWeight: 400,
            lineHeight: "14px",
            color: "#706E75",
            alignSelf: isAI ? "flex-start" : "flex-end",
          }}
        >
          {moment(time).isSame(moment(), "day")
            ? `Today, ${moment(time).format("HH:mm")}`
            : moment(time).format("DD MMM YYYY, HH:mm")}
        </Box>
      </Box>
    </Box>
  );
};

export default DestinationMessage;
