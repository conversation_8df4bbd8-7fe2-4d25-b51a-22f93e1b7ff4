import { Box, List, ListItem, Typography, ListItemIcon } from "@mui/material";
import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";
import { AIRobotHead, WeatherIcon } from "../../../components/common/SvgIcon";
import moment from "moment/moment";

const WeatherSingleMessage = ({ message, sender, time }) => {
  const isAI = sender === "ai";
  return (
    <Box
      sx={{
        width: "fit-content",
        maxWidth: "88%",
        height: "fit-content",
        display: "flex",
        flexDirection: "row",
        gap: "12px",
        alignSelf: isAI ? "flex-start" : "flex-end",
      }}
    >
      {isAI && (
        <Box sx={{ width: "40px", height: "40px" }}>
          <AIRobotHead />
        </Box>
      )}
      <Box display="flex" flexDirection="column" gap="8px">
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            background: isAI
              ? "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)"
              : "#FF39510D",
            borderRadius: isAI ? "0px 12px 12px 12px" : "12px 12px 0px 12px",
            p: "9.5px 24px",
            boxSizing: "border-box",
            whiteSpace: "pre-wrap",
            wordBreak: "break-word",
            fontFamily: "Roboto",
            fontSize: "14px",
            fontWeight: 400,
            lineHeight: "16px",
            color: "#333333",
          }}
        >
          {message?.message || ""}
        </Box>
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            fontSize: "12px",
            fontWeight: 400,
            lineHeight: "14px",
            color: "#706E75",
            alignSelf: isAI ? "flex-start" : "flex-end",
          }}
        >
          {moment(time).isSame(moment(), "day")
            ? `Today, ${moment(time).format("HH:mm")}`
            : moment(time).format("DD MMM YYYY, HH:mm")}
        </Box>
        <Box
          sx={{
            display: "flex",
            gap: "14px",
            boxShadow: "0px 1px 10px 1px #0000000D",
            borderRadius: "8px",
            p: "16px 16px 8px 16px",
            backgroundColor: "#FFFFFF",
          }}
        >
          <Box>
            <WeatherIcon />
          </Box>
          <Box>
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontSize: "12px",
                fontWeight: 600,
                lineHeight: "14px",
                color: "#333333",
              }}
            >
              🌤️ Weather Overview:
            </Typography>
            <List>
              {message?.overview?.map((item, index) => (
                <ListItem sx={{ p: "2px" }} key={index + "weather-single"}>
                  <ListItemIcon sx={{ minWidth: 16 }}>
                    <FiberManualRecordIcon
                      sx={{ fontSize: 6, color: "#333333" }}
                    />
                  </ListItemIcon>
                  <Typography
                    sx={{
                      fontFamily: "Roboto",
                      fontSize: "12px",
                      fontWeight: 400,
                      lineHeight: "14px",
                      color: "#333333",
                    }}
                  >
                    {item.description}
                  </Typography>
                </ListItem>
              ))}
            </List>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default WeatherSingleMessage;
