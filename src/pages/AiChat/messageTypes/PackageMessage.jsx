import { Box, Typography } from "@mui/material";
import { AIRobotHead } from "../../../components/common/SvgIcon";
import moment from "moment/moment";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";

const PackageMessage = ({ list, sender, time, setIsPackageOpen }) => {
  const isAI = sender === "ai";
  return (
    <Box
      sx={{
        width: "fit-content",
        maxWidth: "88%",
        height: "fit-content",
        display: "flex",
        flexDirection: "row",
        gap: "12px",
        alignSelf: isAI ? "flex-start" : "flex-end",
      }}
    >
      {isAI && (
        <Box sx={{ width: "40px", height: "40px" }}>
          <AIRobotHead />
        </Box>
      )}
      <Box display="flex" flexDirection="column" gap="8px">
        <Box
          sx={{
            width: "fit-content",
            maxWidth: {
              xs: "calc(90dvw - 40px 40px - 12px)",
              sm: "498px",
            },
            height: "fit-content",
            background: isAI
              ? "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)"
              : "#FF39510D",
            borderRadius: isAI ? "0px 12px 12px 12px" : "12px 12px 0px 12px",
            p: "9.5px 9.5px",
            boxSizing: "border-box",
            whiteSpace: "pre-wrap",
            wordBreak: "break-word",
            fontFamily: "Roboto",
            fontSize: "14px",
            fontWeight: 400,
            lineHeight: "16px",
            color: "#333333",
          }}
        >
          <Box
            sx={{ width: "100%", display: "flex", justifyContent: "flex-end" }}
          >
            <Box
              sx={{
                width: "fit-content",
                fontSize: 10,
                fontWeight: 400,
                lineHeight: "12px",
                color: "#0083E7",
                letterSpacing: "0.02em",
                cursor: "pointer",
                mb: 1,
                mr: "22.5px",
                "&:hover": {
                  textDecoration: "underline",
                },
              }}
            >
              See all
            </Box>
          </Box>
          <Box
            sx={{
              width: "100%",
              display: "flex",
              alignItems: "center",
              gap: "7.5px",
            }}
          >
            <Box>
              <img
                className="swiper-button-prev-package-chat"
                src={"/static/common/slidePrev.png"}
                alt="prev"
                style={{ cursor: "pointer", width: "15px", height: "15px" }}
              />
            </Box>
            <Swiper
              modules={[Navigation]}
              spaceBetween={7.5}
              slidesPerView={1}
              navigation={{
                nextEl: ".swiper-button-next-package-chat",
                prevEl: ".swiper-button-prev-package-chat",
              }}
              breakpoints={{
                640: {
                  slidesPerView: 2,
                  spaceBetween: 7.5,
                },
                768: {
                  slidesPerView: 3,
                  spaceBetween: 7.5,
                },
                1024: {
                  slidesPerView: 3,
                  spaceBetween: 7.5,
                },
              }}
              style={{
                width: "100%",
              }}
            >
              {list?.map((item, index) => (
                <SwiperSlide key={index + "package"}>
                  <Box
                    sx={{
                      width: "100%",
                      height: "100%",
                      background: "#FFFFFF",
                      borderRadius: "7.5px",
                      overflow: "hidden",
                    }}
                  >
                    <Box
                      sx={{
                        width: "100%",
                        aspectRatio: "141/80",
                        overflow: "hidden",
                      }}
                    >
                      <Box
                        component="img"
                        src={item.image}
                        sx={{
                          width: "100%",
                          height: "100%",
                          objectFit: "cover",
                        }}
                        alt={"package image"}
                      />
                    </Box>
                    <Box sx={{ p: "7.5px" }}>
                      <Typography
                        sx={{
                          fontSize: 10,
                          fontWeight: 500,
                          lineHeight: "12px",
                          color: "#333333",
                          letterSpacing: "0.02em",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          width: "100%",
                          mb: 0.5,
                        }}
                      >
                        {item.title}
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: 10,
                          fontWeight: 500,
                          lineHeight: "12px",
                          color: "#333333",
                          mb: 0.5,
                        }}
                      >
                        {item.duration}
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: 10,
                          fontWeight: 500,
                          lineHeight: "12px",
                          color: "#1E3A8A",
                        }}
                      >
                        {`INR ${item.price}`}
                      </Typography>
                      <Box
                        sx={{
                          width: "100%",
                          height: "1px",
                          borderBottom: "1px dashed #706E75",
                          my: "7.5px",
                        }}
                      />
                      <Box
                        sx={{
                          width: "100%",
                          boxSizing: "border-box",
                          display: "flex",
                          justifyContent: "center",
                        }}
                      >
                        <Box
                          sx={{
                            width: "fit-content",
                            fontSize: 10,
                            fontWeight: 500,
                            lineHeight: "12px",
                            color: "#0083E7",
                            cursor: "pointer",
                            "&:hover": {
                              textDecoration: "underline",
                            },
                          }}
                          onClick={() => setIsPackageOpen(true)}
                        >
                          View Detail
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                </SwiperSlide>
              ))}
            </Swiper>

            <Box>
              <img
                className="swiper-button-next-package-chat"
                src={"/static/common/slideNext.png"}
                alt="next"
                style={{ cursor: "pointer", width: "15px", height: "15px" }}
              />
            </Box>
          </Box>
        </Box>
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            fontSize: "12px",
            fontWeight: 400,
            lineHeight: "14px",
            color: "#706E75",
            alignSelf: isAI ? "flex-start" : "flex-end",
          }}
        >
          {moment(time).isSame(moment(), "day")
            ? `Today, ${moment(time).format("HH:mm")}`
            : moment(time).format("DD MMM YYYY, HH:mm")}
        </Box>
      </Box>
    </Box>
  );
};

export default PackageMessage;
