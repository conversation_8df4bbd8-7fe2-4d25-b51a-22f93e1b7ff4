import { Box } from "@mui/material";
import { AIRobotHead } from "../../../components/common/SvgIcon";
import moment from "moment/moment";

const SelectMessage = ({ message, sender, time }) => {
  const isAI = sender === "ai";
  return (
    <Box
      sx={{
        width: "fit-content",
        maxWidth: "88%",
        height: "fit-content",
        display: "flex",
        flexDirection: "row",
        gap: "12px",
        alignSelf: isAI ? "flex-start" : "flex-end",
      }}
    >
      {isAI && (
        <Box sx={{ width: "40px", height: "40px" }}>
          <AIRobotHead />
        </Box>
      )}
      <Box display="flex" flexDirection="column" gap="8px">
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            background: isAI
              ? "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)"
              : "#FF39510D",
            borderRadius: isAI ? "0px 12px 12px 12px" : "12px 12px 0px 12px",
            p: "9.5px 24px",
            boxSizing: "border-box",
            whiteSpace: "pre-wrap",
            wordBreak: "break-word",
            fontFamily: "Roboto",
            fontSize: "14px",
            fontWeight: 400,
            lineHeight: "16px",
            color: "#333333",
          }}
        >
          {message?.message || ""}
        </Box>
        <Box
          sx={{
            width: "100%",
            display: "flex",
            flexDirection: "row",
            gap: "12px",
            flexWrap: "wrap",
          }}
        >
          {message?.options?.map((item, index) => (
            <Box
              key={index + "select"}
              className={item?.isSelected ? "active" : ""}
              sx={{
                border: "1px solid #FF39514D",
                background: "#FFF",
                borderRadius: "8px",
                padding: "7px 11px",
                display: "flex",
                alignItems: "center",
                maxWidth: "160px",
                boxSizing: "border-box",
                justifyContent: "space-between",
                gap: "8px",
                cursor: "pointer",
                transition: "all 0.3s ease",
                "&:hover:not(.active)": {
                  background: "#FF39510D",
                  border: "1px solid #FF3951",
                },
                "&.active": {
                  background: "#FF39510D",
                  border: "1px solid #FF39510D",
                  cursor: "default",
                },
              }}
            >
              <Box
                sx={{
                  color: "#333333",
                  fontFamily: "Roboto",
                  fontSize: "12px",
                  fontWeight: 400,
                  lineHeight: "14px",
                }}
              >
                {item?.label || ""}
              </Box>
            </Box>
          ))}
        </Box>
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            fontSize: "12px",
            fontWeight: 400,
            lineHeight: "14px",
            color: "#706E75",
            alignSelf: isAI ? "flex-start" : "flex-end",
          }}
        >
          {moment(time).isSame(moment(), "day")
            ? `Today, ${moment(time).format("HH:mm")}`
            : moment(time).format("DD MMM YYYY, HH:mm")}
        </Box>
      </Box>
    </Box>
  );
};

export default SelectMessage;
