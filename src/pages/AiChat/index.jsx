import React, { useState, useRef, useEffect } from "react";
import { Box, IconButton, TextField } from "@mui/material";
import SpeechRecognition, {
  useSpeechRecognition,
} from "react-speech-recognition";
import AiChatLayout from "../../layout/aiChatLayout";
import { Add, Mic, Send } from "../../components/common/SvgIcon";
import ChatSection from "./ChatSection";
import { chatsData, fixedKeyWords } from "./chatData";
import FixedKeyWords from "./suggesions/FixedKeyWords";

const AiChat = () => {
  const isInputActive = true;
  const isAiSuggestionActive = true;
  const [chats, setChats] = useState(chatsData);
  const [inputValue, setInputValue] = useState("");
  const [isListening, setIsListening] = useState(false);
  const chatContainerRef = useRef(null);
  const [isPackageOpen, setIsPackageOpen] = useState(false);
  const [isCustomizeOpen, setIsCustomizeOpen] = useState(false);

  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition,
  } = useSpeechRecognition();

  // Update input value when transcript changes
  useEffect(() => {
    if (transcript) {
      setInputValue(transcript);
    }
  }, [transcript]);

  // Update listening state when speech recognition listening state changes
  useEffect(() => {
    setIsListening(listening);
  }, [listening]);

  const toggleListening = () => {
    if (listening) {
      SpeechRecognition.stopListening();
    } else {
      SpeechRecognition.startListening({ continuous: true });
    }
  };

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  };

  const sendMessage = () => {
    if (inputValue.trim() === "") return;

    // Stop listening if currently listening
    if (listening) {
      SpeechRecognition.stopListening();
    }

    setChats([
      {
        id: chats.length + 1,
        message: inputValue,
        sender: "user",
        messageType: "text",
      },
      ...chats,
    ]);
    setInputValue("");
    // Reset transcript after sending message
    resetTranscript();
  };

  const handleKeyDown = (e) => {
    // Stop listening if currently listening and any key is pressed
    if (listening) {
      SpeechRecognition.stopListening();
    }

    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleInputFocus = () => {
    // Stop listening when input is focused
    if (listening) {
      SpeechRecognition.stopListening();
    }
  };

  const handleInputChange = (e) => {
    // Stop listening when user starts typing
    if (listening) {
      SpeechRecognition.stopListening();
    }
    setInputValue(e.target.value);
  };

  // Scroll to bottom on first load
  useEffect(() => {
    scrollToBottom();
  }, []);

  // Scroll to bottom when new messages are added
  useEffect(() => {
    scrollToBottom();
  }, [chats]);

  return (
    <AiChatLayout
      isPackageOpen={isPackageOpen}
      setIsPackageOpen={setIsPackageOpen}
      isCustomizeOpen={isCustomizeOpen}
      setIsCustomizeOpen={setIsCustomizeOpen}
    >
      {/* top section: chat history */}
      <Box
        sx={{
          width: "100%",
          height: "100%",
          display: "flex",
          flexDirection: "column",
          maxWidth: "776px",
          boxSizing: "border-box",
          mx: "auto",
        }}
      >
        <Box
          ref={chatContainerRef}
          sx={{
            height: isAiSuggestionActive
              ? "calc(100% - 208px - 16px)"
              : "calc(100% - 116px - 16px)",
            px: 2,
            boxSizing: "border-box",
            overflowY: "auto",
          }}
        >
          <ChatSection
            chats={chats}
            isPackageOpen={isPackageOpen}
            setIsPackageOpen={setIsPackageOpen}
          />
        </Box>

        {/* bottom section: input */}
        <Box
          sx={{
            height: isAiSuggestionActive ? "208px" : "116px",
            pt: 2,
            display: "flex",
            flexDirection: "column",
            width: "100%",
            px: 2,
            boxSizing: "border-box",
          }}
        >
          {isAiSuggestionActive && (
            <Box
              sx={{
                mb: 1.5,
                width: "fit-content",
                maxWidth: "100%",
                mx: "auto",
                display: "flex",
                gap: "8px",
                boxSizing: "border-box",
                overflowX: "auto",
                scrollbarWidth: "none",
                "&::-webkit-scrollbar": {
                  display: "block",
                },
              }}
            >
              {fixedKeyWords?.slice(0, 30).map((item, index) => (
                <FixedKeyWords key={index} item={item} />
              ))}
            </Box>
          )}

          <Box
            sx={{
              padding: "1px", // border thickness
              borderRadius: "12.5px",
              background: isInputActive
                ? "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)"
                : "#F6F6F6",
              width: "100%",
              height: "100%",
              boxSizing: "border-box",
            }}
          >
            <Box
              sx={{
                background: isInputActive ? "#FFFFFF" : "#F6F6F6",
                borderRadius: "11.5px",
                width: "100%",
                height: "100%",
                overflow: "hidden",
                p: 1.5,
                boxSizing: "border-box",
                display: "flex",
                gap: 1.5,
              }}
            >
              <Box
                sx={{
                  width: "calc(100% - 40px - 12px)",
                  height: "100%",
                  display: "flex",
                  flexDirection: "column",
                }}
              >
                <Box
                  sx={{
                    width: "100%",
                    height: "calc(100% - 24px)",
                    display: "flex",
                    flexDirection: "column",
                    boxSizing: "border-box",
                  }}
                >
                  {/* add text field mui */}
                  <TextField
                    sx={{
                      height: "100%",
                      boxSizing: "border-box",
                      width: "100%",
                      "& .MuiInputBase-root": {
                        backgroundColor: "transparent",
                        height: "100%",
                        p: "2px",
                        display: "block",
                        overflowY: "auto",
                        fontFamily: "Roboto",
                        fontSize: "14px",
                        fontWeight: 400,
                        lineHeight: "16px",
                        color: "#000000",
                        "&::placeholder": {
                          color: "#706E75",
                        },
                      },

                      "& .MuiOutlinedInput-notchedOutline": {
                        border: "none",
                      },
                    }}
                    multiline
                    placeholder="Ask anything"
                    disabled={!isInputActive}
                    value={inputValue}
                    onChange={handleInputChange}
                    onKeyDown={handleKeyDown}
                    onFocus={handleInputFocus}
                  />
                </Box>
                <Box sx={{ width: "100%", height: "24px" }}>
                  <IconButton sx={{ p: 0 }} disabled={!isInputActive}>
                    <Add width="20px" height="20px" />
                  </IconButton>
                </Box>
              </Box>
              <Box
                sx={{
                  width: "40px",
                  height: "100%",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <IconButton
                  sx={{ p: 0 }}
                  disabled={!isInputActive}
                  onClick={sendMessage}
                >
                  <Send active={isInputActive} />
                </IconButton>
                <IconButton
                  sx={{
                    p: 0,
                    ...(isListening && {
                      animation: "pulse 1.5s ease-in-out infinite",
                      "@keyframes pulse": {
                        "0%": {
                          transform: "scale(1)",
                          opacity: 1,
                        },
                        "50%": {
                          transform: "scale(1.1)",
                          opacity: 0.7,
                        },
                        "100%": {
                          transform: "scale(1)",
                          opacity: 1,
                        },
                      },
                    }),
                  }}
                  disabled={!isInputActive || !browserSupportsSpeechRecognition}
                  onClick={toggleListening}
                >
                  <Mic active={isInputActive && isListening} />
                </IconButton>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </AiChatLayout>
  );
};

export default AiChat;
