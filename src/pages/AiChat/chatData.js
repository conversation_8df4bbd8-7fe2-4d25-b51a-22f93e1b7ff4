export const chatsData = [
  {
    id: "dest-16",
    message: null,
    sender: "ai",
    timestamp: "2025-07-02T09:01:30Z",
    messageType: "destination",
    list: [
      {
        id: "destination-1",
        title: "Paris",
        image:
          "https://images.pexels.com/photos/32505992/pexels-photo-32505992.jpeg",
        price: 100000,
        bestTimeToVisit: "Nov to Feb",
      },
      {
        id: "destination-2",
        title: "India",
        image:
          "https://images.pexels.com/photos/32169331/pexels-photo-32169331.jpeg",
        price: 500000,
        bestTimeToVisit: "May to July",
      },
      {
        id: "destination-3",
        title: "Paris",
        image:
          "https://images.pexels.com/photos/32505992/pexels-photo-32505992.jpeg",
        price: 100000,
        bestTimeToVisit: "May to July",
      },
      {
        id: "destination-4",
        title: "Paris",
        image:
          "https://images.pexels.com/photos/32169331/pexels-photo-32169331.jpeg",
        price: 100000,
        bestTimeToVisit: "Nov to Feb",
      },
      {
        id: "destination-5",
        title: "Paris",
        image:
          "https://images.pexels.com/photos/32505992/pexels-photo-32505992.jpeg",
        price: 100000,
        bestTimeToVisit: "Nov to Feb",
      },
    ],
  },
  {
    id: "weather_multi",
    message: {
      message: "What's the weather like in Jaipur?",
      currentWeather: {
        temperature: 20,
        description: "Sunny",
        icon: "sunny",
        location: "Jaipur, Rajasthan, India",
      },
      forecast: [
        {
          icon: "rain",
          date: "2025-07-22",
          temperature: 83,
          description: "Breezy with periods of rain",
        },
        {
          icon: "rain",
          date: "2025-07-23",
          temperature: 83,
          description: "Periods of rain",
        },
        {
          icon: "rain",
          date: "2025-07-24",
          temperature: 83,
          description:
            "Cloudy and breezy; a shower in the morning followed by a little rain in the afternoon",
        },
        {
          icon: "rain",
          date: "2025-07-25",
          temperature: 83,
          description: "Breezy with occasional rain",
        },
        {
          icon: "rain",
          date: "2025-07-26",
          temperature: 83,
          description: "Rather cloudy and warmer with a touch of rain",
        },
        {
          icon: "thunderstorm",
          date: "2025-07-27",
          temperature: 83,
          description:
            "Mostly cloudy; a little morning rain followed by a couple of thunderstorms in the afternoon",
        },
        {
          icon: "thunderstorm",
          date: "2025-07-28",
          temperature: 83,
          description: "Cloudy with a couple of thunderstorms",
        },
      ],
    },
    sender: "ai",
    timestamp: "2025-07-02T09:00:00Z",
    messageType: "weather_multi",
  },
  {
    id: "weather_single",
    message: {
      message: "What's the weather like in Paris?",
      overview: [
        {
          description: "Temperature: 20°C to 32°C",
          id: "temperature",
        },
        {
          description: "Humidity: 40%",
          id: "humidity",
        },
        {
          description: "Wind: 10 km/h",
          id: "wind",
        },
        {
          description: "Sunrise: 05:00 AM",
          id: "sunrise",
        },
        {
          description: "Sunset: 06:00 PM",
          id: "sunset",
        },
      ],
    },
    sender: "ai",
    timestamp: "2025-07-02T09:00:00Z",
    messageType: "weather_single",
  },
  {
    id: "ai-select-3",
    message: {
      message:
        "Love that! Trips like these are always memorable. 😍, Quick one — how many of you are going?",
      options: [
        {
          id: "adult",
          label: "Adult",
          description: "Ages 13 or above",
          count: 1,
          min: 1,
          max: 10,
        },
        {
          id: "children",
          label: "Children",
          description: "Ages 2–12",
          count: 1,
          min: 0,
          max: 10,
        },
        {
          id: "infants",
          label: "Infants",
          description: "Under 2",
          count: 0,
          min: 0,
          max: 10,
        },
      ],
    },
    sender: "ai",
    timestamp: "2025-07-02T09:00:00Z",
    messageType: "counter",
  },
  {
    id: "ai-select-1",
    message: {
      message: "Or just tell me… are you leaning towards something?",
      options: [
        {
          id: "domestic",
          label: "Domestic",
          isSelected: false,
        },
        {
          id: "international",
          label: "International",
          isSelected: true,
        },
      ],
    },
    sender: "ai",
    timestamp: "2025-07-02T09:00:00Z",
    messageType: "select",
  },
  {
    id: "ai-select-2",
    message: {
      message:
        "Nice choice! That sounds exciting. 🙌 And who’s joining you on this trip?",
      options: [
        {
          id: "solo",
          label: "Solo",
          isSelected: false,
        },
        {
          id: "couple",
          label: "Couple",
          isSelected: true,
        },
        {
          id: "family",
          label: "Family",
          isSelected: false,
        },
        {
          id: "friends",
          label: "Friends",
          isSelected: false,
        },
      ],
    },
    sender: "ai",
    timestamp: "2025-07-02T09:00:00Z",
    messageType: "select",
  },

  {
    id: "center-1",
    message: "Plan a 7-day trip",
    sender: "default",
    timestamp: "2025-07-02T09:00:00Z",
    messageType: "center",
  },
  {
    id: 15,
    message: null,
    sender: "ai",
    timestamp: "2025-07-02T09:01:30Z",
    messageType: "package",
    list: [
      {
        id: "package-1",
        title: "Paris",
        image:
          "https://images.pexels.com/photos/32505992/pexels-photo-32505992.jpeg",
        price: 100000,
        rating: 4.5,
        startingPrice: 100000,
        duration: "4D 5N",
        activities: ["Eiffel Tower", "Louvre Museum", "Montmartre"],
      },
      {
        id: "package-2",
        title: "Paris",
        image:
          "https://images.pexels.com/photos/32169331/pexels-photo-32169331.jpeg",
        price: 100000,
        rating: 4.5,
        startingPrice: 100000,
        duration: "4D 5N",
        activities: ["Eiffel Tower", "Louvre Museum", "Montmartre"],
      },
      {
        id: "package-3",
        title: "Paris",
        image:
          "https://images.pexels.com/photos/32505992/pexels-photo-32505992.jpeg",
        price: 100000,
        rating: 4.5,
        startingPrice: 100000,
        duration: "4D 5N",
        activities: ["Eiffel Tower", "Louvre Museum", "Montmartre"],
      },
      {
        id: "package-4",
        title: "Paris",
        image:
          "https://images.pexels.com/photos/32169331/pexels-photo-32169331.jpeg",
        price: 100000,
        rating: 4.5,
        startingPrice: 100000,
        duration: "4D 5N",
        activities: ["Eiffel Tower", "Louvre Museum", "Montmartre"],
      },
      {
        id: "package-5",
        title: "Paris",
        image:
          "https://images.pexels.com/photos/32505992/pexels-photo-32505992.jpeg",
        price: 100000,
        rating: 4.5,
        startingPrice: 100000,
        duration: "4D 5N",
        activities: ["Eiffel Tower", "Louvre Museum", "Montmartre"],
      },
    ],
  },

  {
    id: 2,
    message: `Hi, I’m Zippy your travel assistant!\n\nI’m here to help you plan the perfect holiday.`,
    sender: "ai",
    timestamp: "2025-07-02T09:00:02Z",
    messageType: "text",
  },
  {
    id: 3,
    message: "I want to visit Europe. Can you suggest an itinerary?",
    sender: "user",
    timestamp: "2025-07-02T09:00:10Z",
    messageType: "text",
  },
  {
    id: 4,
    message: `Absolutely! Here's a quick suggestion:\n\n• Day 1-2: Paris\n• Day 3-4: Amsterdam\n• Day 5: Berlin\n• Day 6-7: Prague`,
    sender: "ai",
    timestamp: "2025-07-02T09:00:15Z",
    messageType: "text",
  },
  {
    id: 5,
    message: "Sounds good! What should I pack for this trip?",
    sender: "user",
    timestamp: "2025-07-02T09:00:25Z",
    messageType: "text",
  },
  {
    id: 6,
    message: `Great question!\n\n✅ Light clothing\n✅ Travel adapter\n✅ Power bank\n✅ Passport & visa\n✅ Good walking shoes`,
    sender: "ai",
    timestamp: "2025-07-02T09:00:30Z",
    messageType: "text",
  },
  {
    id: 7,
    message: "What's the best time to travel to these cities?",
    sender: "user",
    timestamp: "2025-07-02T09:00:40Z",
    messageType: "text",
  },
  {
    id: 8,
    message: `May to early July and September are best!\n\nYou’ll avoid the tourist rush and enjoy mild weather.`,
    sender: "ai",
    timestamp: "2025-07-02T09:00:45Z",
    messageType: "text",
  },
  {
    id: 9,
    message: "Can you also help me find budget-friendly hotels?",
    sender: "user",
    timestamp: "2025-07-02T09:00:55Z",
    messageType: "text",
  },
  {
    id: 10,
    message: `Sure! Do you prefer:\n\n• Hostels 🛏️\n• Budget Hotels 🏨\n• Airbnbs 🏠?`,
    sender: "ai",
    timestamp: "2025-07-02T09:01:00Z",
    messageType: "text",
  },
  {
    id: 11,
    message: "Airbnb would be great. Also, I want to try local food!",
    sender: "user",
    timestamp: "2025-07-02T09:01:08Z",
    messageType: "text",
  },
  {
    id: 12,
    message: `Noted!\n\n🍽️ Paris: Croissants, crepes\n🍽️ Amsterdam: Stroopwafels\n🍽️ Berlin: Currywurst\n🍽️ Prague: Trdelník\n\nI’ll include these in your travel notes.`,
    sender: "ai",
    timestamp: "2025-07-02T09:01:15Z",
    messageType: "text",
  },
  {
    id: 13,
    message: "This is really helpful, thank you!",
    sender: "user",
    timestamp: "2025-07-02T09:01:22Z",
    messageType: "text",
  },
  {
    id: 14,
    message: `You're very welcome!\n\nLet me know if you'd like me to book anything or create a printable plan.`,
    sender: "ai",
    timestamp: "2025-06-03T09:01:27Z",
    messageType: "text",
  },
];

export const fixedKeyWords = [
  {
    id: 1,
    label: "Flights",
    icon: "/static/package/highlight/flight.png",
  },
  {
    id: 2,
    label: "Holiday Packages",
    icon: "/static/package/highlight/common.png",
  },
  {
    id: 3,
    label: "Hotels",
    icon: "/static/package/highlight/hotel.png",
  },
  {
    id: 4,
    label: "Visa",
    icon: "/static/package/highlight/visa.png",
  },
  {
    id: 1,
    label: "Flights",
    icon: "/static/package/highlight/flight.png",
  },
  {
    id: 2,
    label: "Holiday Packages",
    icon: "/static/package/highlight/common.png",
  },
  {
    id: 3,
    label: "Hotels",
    icon: "/static/package/highlight/hotel.png",
  },
  {
    id: 4,
    label: "Visa",
    icon: "/static/package/highlight/visa.png",
  },
  {
    id: 1,
    label: "Flights",
    icon: "/static/package/highlight/flight.png",
  },
  {
    id: 2,
    label: "Holiday Packages",
    icon: "/static/package/highlight/common.png",
  },
  {
    id: 3,
    label: "Hotels",
    icon: "/static/package/highlight/hotel.png",
  },
  {
    id: 4,
    label: "Visa",
    icon: "/static/package/highlight/visa.png",
  },
];