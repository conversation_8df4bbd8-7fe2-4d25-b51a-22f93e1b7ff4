import { Box } from "@mui/material";
import React from "react";

const FixedKeyWords = ({ item }) => {
  return (
    <Box
      className={item?.isSelected ? "active" : ""}
      sx={{
        border: "1px solid #FF39514D",
        background: "#FFF",
        borderRadius: "8px",
        padding: "7px 11px",
        display: "flex",
        alignItems: "center",
        maxWidth: "160px",
        boxSizing: "border-box",
        justifyContent: "space-between",
        gap: "4px",
        cursor: "pointer",
        transition: "all 0.3s ease",
        "&:hover:not(.active)": {
          background: "#FF39510D",
          border: "1px solid #FF3951",
        },
        "&.active": {
          background: "#FF39510D",
          border: "1px solid #FF39510D",
          cursor: "default",
        },
      }}
    >
      {item?.icon && (
        <Box
          component={"img"}
          src={item?.icon}
          alt={item?.label}
          sx={{
            width: "14px",
            height: "14px",
          }}
        />
      )}
      <Box
        sx={{
          color: "#333333",
          fontFamily: "Roboto",
          fontSize: "12px",
          fontWeight: 400,
          lineHeight: "14px",
          width: "max-content",
        }}
      >
        {item?.label || ""}
      </Box>
    </Box>
  );
};

export default FixedKeyWords;
