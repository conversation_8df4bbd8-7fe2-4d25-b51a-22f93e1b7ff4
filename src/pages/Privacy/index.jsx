import { Box } from "@mui/material";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Header from "../../components/Header";
import Footer from "../../components/Footer";
import { fetchPrivacyPolicy } from "../../store/reducers/Terms/apiThunk";

const Privacy = () => {
  const dispatch = useDispatch();
  const { privacyData } = useSelector((state) => state.terms);

  useEffect(() => {
    dispatch(fetchPrivacyPolicy());
  }, [dispatch]);

  return (
    <Box>
      <Header />
      <Box
        sx={{
          boxSizing: "border-box",
          pt: { xs: "80px", md: "154px" },
          pb: { xs: 3, md: 4 },
          px: { xs: 2.5, md: 7.5 },
        }}
      >
        <Box
          sx={{
            borderLeft: "5px solid #FF3951",
            p: { xs: 3, md: 4 },
            borderRadius: "4px",
            boxShadow: "0px 4px 8px 0px #1E3A8A0F",
            background: "#FFFFFF",
          }}
        >
          <Box
            sx={{
              fontFamily: "Lora",
              fontWeight: 600,
              fontSize: "20px",
              color: "#333333",
            }}
          >
            Privacy Policy – ZUUMM
          </Box>
          <Box sx={{ mt: 3 }}>
            {privacyData && (
              <Box
                sx={{
                  "& h1, & h2, & h3, & h4, & h5, & h6": {
                    fontFamily: "Lora",
                    fontWeight: 600,
                    color: "#333333",
                    mb: 2,
                  },
                  "& p": {
                    mb: 2,
                    lineHeight: 1.6,
                  },
                  "& ul, & ol": {
                    mb: 2,
                    pl: 3,
                  },
                  "& li": {
                    mb: 1,
                    lineHeight: 1.6,
                  },
                }}
                dangerouslySetInnerHTML={{ __html: privacyData?.data?.content }}
              />
            )}
          </Box>
        </Box>
      </Box>
      <Footer />
    </Box>
  );
};

export default Privacy;
