import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { logout } from "../store/reducers/Authentication";
import { ToastNotifySuccess } from "../components/Toast/ToastNotify";
import ROUTE_PATH from "../constants/route";

export const useLogout = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isLoading } = useSelector((state) => state.authentication);

  const handleLogout = async () => {
    try {
      await dispatch(logout()).unwrap();
      ToastNotifySuccess("Logged out successfully");

      // Navigate to home page after successful logout
      navigate(ROUTE_PATH.HOME, { replace: true });
    } catch (error) {
      console.error("Logout error:", error);
      // Even if there's an error, navigate to home page
      navigate(ROUTE_PATH.HOME, { replace: true });
    }
  };

  return {
    logout: handleLogout,
    isLoading,
  };
};
