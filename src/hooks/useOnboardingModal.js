import { useDispatch, useSelector } from "react-redux";
import {
  openOnboardingModal,
  closeOnboardingModal,
  toggleOnboardingModal,
} from "../store/reducers/onboardingModal";

export const useOnboardingModal = () => {
  const dispatch = useDispatch();
  const { isOpen } = useSelector((state) => state.onboardingModal);

  const open = () => dispatch(openOnboardingModal());
  const close = () => dispatch(closeOnboardingModal());
  const toggle = () => dispatch(toggleOnboardingModal());

  return {
    isOpen,
    open,
    close,
    toggle,
  };
};
