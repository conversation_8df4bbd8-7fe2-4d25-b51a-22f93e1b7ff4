export const BRAND_ONBOARDING = {
  verify_otp: "brand-onboarding/verify-otp",
  verify_test_otp: "brand-onboarding/verify-test-otp",
};

export const FAQ = {
  get_faq: "faq/get-faq",
};

export const EXPLORE = {
  get_explore_categories: "explore/get-explore-categories",
  get_explore_destinations: "explore/get-explore-destinations",
  get_explore_activities: "explore/get-explore-activities",
  get_explore_destinations_dropdowns:
    "explore/get-explore-destinations-dropdowns",
  get_explore_destinations_by_category:
    "explore/get-explore-destinations-by-category",
  get_explore_destinations_popular: "explore/get-explore-destinations-popular",
  get_explore_visa_on_arrival: "explore/get-explore-visa-on-arrival",
  get_explore_packages: "explore/get-explore-packages",
  get_explore_activities_by_destination:
    "explore/get-explore-activities-by-destination",
  get_explore_activities_gallery: "explore/get-explore-activities-gallery",
};

export const COMMON = {
  get_terms_and_conditions: "terms-and-conditions/get-terms-and-conditions",
  get_privacy_policy: "privacy-policy/get-privacy-policy",
};

export const PACKAGE_DETAILS = {
  get_package_basic_details: "package-details/get-package-basic-details",
  get_package_itinerary_details:
    "package-details/get-package-itinerary-details",
  get_package_destination_faq: "package-details/get-package-destination-faq",
  get_terms_and_conditions: "package-details/get-terms-and-conditions",
  get_privacy_policy: "package-details/get-privacy-policy",
  generate_package_pdf: "package-details/generate-package-pdf",
};

export const HOME = {
  get_trending_destinations: "home/get-trending-destinations",
  get_destinations: "home/get-destinations",
  get_join_newsletter: "home/get-join-newsletter",
};

export const BLOG = {
  get_blog_posts: "blog/get-blog-posts",
  get_blog_details: "blog/get-blog-details",
  get_blog_tags: "blog/get-blog-tags",
  get_blog_podcasts: "blog/get-blog-podcasts",
};

export const CONVERSATION = {
  get_conversations: "conversation/get-conversations",
  create_conversation: "chat",
  update_conversation: "conversation/update-conversation",
  delete_conversation: "conversation/delete-conversation",
  rename_conversation: "conversation/rename-conversation",
};