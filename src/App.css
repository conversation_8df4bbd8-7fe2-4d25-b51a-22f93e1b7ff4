/* .App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
} */

.onboarding-modal-container {
  width: 90%;
  height: 90%;
  margin: 30px auto;
  overflow-y: auto;
  background-color: #FFF9F9;
  border-radius: 10px;
}

.onboarding-success-modal {
  width: 40%;
  height: auto;
  margin: 120px auto;
  padding: 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border-radius: 10px;
  text-align: center;
  border: none !important;
  @media (max-width: 480px) {
    width: 70%;
    height: auto;
    margin: 120px auto;
    padding: 30px;
  }
}

.success-content {
  margin: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.success-title {
  font-family: "Roboto", sans-serif !important;
  font-size: 24px !important;
  line-height: 28px !important;
  font-weight: 500 !important;
  color: #333333 !important;
}

.success-description {
  font-family: "Roboto", sans-serif !important;
  font-size: 16px !important;
  line-height: 20px !important;
  font-weight: 400 !important;
  color: #706E75 !important;
  max-width: 300px !important;
}

.close-btn {
  width: 100%;
  background-color: #FF3951 !important;
  color: #FFF9F9 !important;
  border-radius: 4px !important;
  height: 44px !important;
  padding: 12px 24px !important;
  font-family: "Roboto", sans-serif !important; 
  font-size: 16px !important;
  font-weight: 500 !important;
    line-height: 100% !important;
  text-transform: capitalize !important;
}

.mobile-menu-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  width: 100% !important;
}

.mobile-menu-close-button {
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #FFF9F9;
  border-radius: 0px 5px 5px 0px;
}

.mobile-menu-divider {
  width: 100vw;
  height: 2px;
  background-color: #F6F6F6;
  margin: 20px 0;
  border: none;
  opacity: 1;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  overflow: hidden;
}

.talk-to-expert-button-mobile{
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  position: relative !important;
  background: #FF3951 !important;
  color: white !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  padding: 6px 30px !important;
  font-family: 'Roboto', sans-serif !important;
  border-radius: 4px !important;
  text-transform: none !important;
  font-weight: 500 !important;
  line-height: 100% !important;
  letter-spacing: 0.05em !important;
  height: 44px !important;
  cursor: pointer !important;
  z-index: 1 !important;
  box-shadow: none !important;
}

.talk-to-expert-button-mobile::before {
content: "";
position: absolute;
top: 0;
left: 0;
height: 100%;
width: 100%;
background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
background-size: 100% 200%;
background-position: left center;
transition: background-position 4s ease, opacity 0.5s ease;
z-index: -1;
opacity: 0;
border-radius: 4px;
box-shadow: none !important;
}
.mobile-select-drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 16px;
  background: white;
  height: 16px;
} 
.mobile-select-drawer-title {
  font-family: 'Roboto', sans-serif !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #1E3A8A !important;
  line-height: 100% !important;
  letter-spacing: 0.05em !important;
}
.mobile-select-drawer-clear-all {
  font-family: 'Roboto', sans-serif !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #706E75 !important;
  line-height: 100% !important;
  letter-spacing: 0.05em !important;
}
.drawer-footer {
  position: absolute;
  bottom: 0;
  width: 72%;
  display: flex;
  flex-direction: row;
  padding: 10px 60px;
  height: 32px;
  justify-content: center;
  align-items: center;
  border-top: 1px solid #FFF9F9;
  gap: 10px;
  background-color: #FFFFFF;
}
.drawer-footer-text {
  font-family: 'Roboto', sans-serif !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  line-height: 100% !important;
  letter-spacing: 0.05em !important;
}
.mobile-select-hr {
  width: 10% !important;
  border: 1px solid #FF39510D !important;
  rotate: 90deg !important;
  background: #FF39510D !important;
  @media (max-width: 768px) {
    width: 4% !important;
  }
  @media (max-width: 480px) {
    width: 8% !important;
  }
}  
.mobile-select-drawer-content {
  display: flex;
  gap: 0px;
  background: white;
  overflow: hidden;
  height: 100% !important;
}
.drawer-select-filter {
  display: flex;
  flex-direction: column;
  gap: 0px;
  background: white;
  width: 40%;
}
.drawer-Choose-filter {
  display: flex;
  flex-direction: column;
  gap: 0px;
  padding: 16px 16px 55px;
  background: #FFF9F9;
  width: 60%;
  overflow-y: auto !important;
}
.drawer-select-filter-item {
  padding: 24px 16px !important;
  display: flex;
  justify-content: flex-start;
  border-top: 1px solid #FFF9F9;
  background: white;
}
.drawer-select-filter-item-title {
  font-family: 'Roboto', sans-serif !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  color: #706E75 !important;
  line-height: 100% !important;
}
.drawer-Choose-filter-item {
  display: flex;
  align-items: center;
  gap: 2px;
  background: transparent;
}
.drawer-select-filter-item-title2 {
  font-family: 'Roboto', sans-serif !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  color: #333333 !important;
  line-height: 15px !important;
  text-transform: capitalize !important;
}
.price-range-container-left-title {
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #1E3A8A;
  line-height: 17px;
  letter-spacing: 0;
}

.price-range-container-left-slider {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0px;
  border-radius: 4px;
  padding: 10px 0px !important;

  .MuiSlider-thumb {
    background: linear-gradient(#fff, #fff) padding-box,
    linear-gradient(90deg, #FF3951 0%, #5530F9 100%) border-box;
    border: 2px solid transparent; 
    width: 20px;
    height: 20px;
  }
  .MuiSlider-rail {
    background: #FFB6C1;
    opacity: 1;
  }
  .MuiSlider-track {
    background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
    border: 0px;
  }
}
.mobile-slider-points {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: 'Roboto', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #1E3A8A;
}
.mobile-price-range-container-right {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.mobile-price-range-container {
  width: 100% !important;
}
.mobile-date-range-container-left {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100% !important;
}
.mobile-date-range-container {
  width: 100% !important;
}
.mobile-sort-drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 16px 0px;
}
.mobile-sort-drawer-title {
  font-family: 'Roboto', sans-serif !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #1E3A8A !important;
  line-height: 100% !important;
  letter-spacing: 0.05em !important;
}
.mobile-sort-drawer-close {
  width: 20px;
  height: 20px;
}
.mobile-sort-drawer-hr {
  width: 100% !important;
  border: 1px solid #FF39510D !important;
  background: #FF39510D !important;
}
.mobile-sort-drawer-content-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 10px 16px;
}
.mobile-sort-drawer-content-item {
  display: flex;
}
.mobile-sort-drawer-content-item-title {
  font-family: 'Roboto', sans-serif !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  color: #333333 !important;
  line-height: 100% !important;
}





