import axios from "axios";
import { ToastNotifyError } from "../components/Toast/ToastNotify";
import { getAccessToken } from "../utils/tokenUtils";
import {
  clearAllPersistedData,
  getPersistorInstance,
} from "../utils/persistUtils";

const client = () => {
  const defaultOptions = {
    headers: { "Content-Type": "application/json" },
  };

  const API = axios.create({
    baseURL: process.env.REACT_APP_BASE_URL,
  });

  const auth_token = getAccessToken();

  API.interceptors.request.use(
    (config) => {
      if (auth_token) {
        config.headers.Authorization = `Bearer ${auth_token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  API.interceptors.response.use(
    (response) => response,
    (error) => {
      const { response: { data: { message } = {}, status } = {} } = error;
      if (status === 401) {
        ToastNotifyError(message);
        // Clear all persisted data including redux-persist
        const persistorInstance = getPersistorInstance();
        clearAllPersistedData(persistorInstance);
      } else if (parseInt(status / 100) === 5) {
        ToastNotifyError(message);
      } else if (status === 400) {
        ToastNotifyError(message);
      }
      return Promise.reject(error);
    }
  );

  return {
    get: (url, options = {}) => API.get(url, { ...defaultOptions, ...options }),
    post: (url, data, options = {}) =>
      API.post(url, data, { ...defaultOptions, ...options }),
    patch: (url, data, options = {}) =>
      API.patch(url, data, { ...defaultOptions, ...options }),
    put: (url, data, options = {}) =>
      API.put(url, data, { ...defaultOptions, ...options }),
    delete: (url, options = {}) =>
      API.delete(url, { ...defaultOptions, ...options }),
  };
};

const getAuthorization = (getState, dispatch, ...restProps) => {
  const {
    authentication: {
      auth: { data: { data: { auth_token = "" } = {} } = {} } = {} 
    } = {},
  } = getState() 

  return client({ auth_token, dispatch, ...restProps[0] });
};

export { getAuthorization };
export default client;
