import axios from "axios";
import { ToastNotifyError } from "../components/Toast/ToastNotify";
import { getAccessToken } from "../utils/tokenUtils";
import {
  clearAllPersistedData,
  getPersistorInstance,
} from "../utils/persistUtils";

// API Response Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
};

// Request timeout in milliseconds
const REQUEST_TIMEOUT = 120000;

// Create axios instance with default configuration
const createAxiosInstance = (baseURL, authToken = null) => {
  const instance = axios.create({
    baseURL: baseURL || process.env.REACT_APP_AI_BASE_URL,
    timeout: REQUEST_TIMEOUT,
    headers: {
      "Content-Type": "application/json",
    },
  });

  // Request interceptor
  instance.interceptors.request.use(
    (config) => {
      // Get access token from localStorage or use provided authToken
      const token = authToken || getAccessToken();

      // Add Bearer token if available
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      return config;
    },
    (error) => {
      console.error("Request interceptor error:", error);
      return Promise.reject(error);
    }
  );

  // Response interceptor
  instance.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      // Handle different error scenarios
      handleApiError(error);
      return Promise.reject(error);
    }
  );

  return instance;
};

// Error handling function
const handleApiError = (error) => {
  const { response } = error;

  if (!response) {
    // Network error or timeout
    ToastNotifyError("Network error. Please check your connection.");
    return;
  }

  const { status, data } = response;
  const message =
    data?.message || data?.error || "An unexpected error occurred";

  switch (status) {
    case HTTP_STATUS.UNAUTHORIZED:
      ToastNotifyError("Authentication required. Please log in again.");
      // Clear all persisted data including redux-persist
      const persistorInstance = getPersistorInstance();
      clearAllPersistedData(persistorInstance);

      break;
    case HTTP_STATUS.FORBIDDEN:
      ToastNotifyError(
        "Access denied. You don't have permission for this action."
      );
      break;
    case HTTP_STATUS.NOT_FOUND:
      ToastNotifyError("Resource not found.");
      break;
    case HTTP_STATUS.CONFLICT:
      ToastNotifyError(message);
      break;
    case HTTP_STATUS.UNPROCESSABLE_ENTITY:
      ToastNotifyError(message);
      break;
    case HTTP_STATUS.INTERNAL_SERVER_ERROR:
    case HTTP_STATUS.BAD_GATEWAY:
    case HTTP_STATUS.SERVICE_UNAVAILABLE:
      ToastNotifyError("Server error. Please try again later.");
      break;
    default:
      ToastNotifyError(message);
  }
};

// Main API service class
class ApiService {
  constructor(baseURL = null, authToken = null) {
    this.client = createAxiosInstance(baseURL, authToken);
  }

  // Generic request method
  async request(config) {
    try {
      const response = await this.client.request(config);
      return {
        success: true,
        data: response.data,
        status: response.status,
        headers: response.headers,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || error.message,
        status: error.response?.status,
        message: error.response?.data?.message || error.message,
      };
    }
  }

  // GET request
  async get(url, params = {}, options = {}) {
    return this.request({
      method: "GET",
      url,
      params,
      ...options,
    });
  }

  // POST request
  async post(url, data = {}, options = {}) {
    return this.request({
      method: "POST",
      url,
      data,
      ...options,
    });
  }

  // PUT request
  async put(url, data = {}, options = {}) {
    return this.request({
      method: "PUT",
      url,
      data,
      ...options,
    });
  }

  // PATCH request
  async patch(url, data = {}, options = {}) {
    return this.request({
      method: "PATCH",
      url,
      data,
      ...options,
    });
  }

  // DELETE request
  async delete(url, options = {}) {
    return this.request({
      method: "DELETE",
      url,
      ...options,
    });
  }

  // Upload file
  async upload(url, file, options = {}) {
    const formData = new FormData();
    formData.append("file", file);

    return this.request({
      method: "POST",
      url,
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      ...options,
    });
  }

  // Upload multiple files
  async uploadMultiple(url, files, options = {}) {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });

    return this.request({
      method: "POST",
      url,
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      ...options,
    });
  }

  // Download file
  async download(url, filename = "download", options = {}) {
    try {
      const response = await this.client.get(url, {
        responseType: "blob",
        ...options,
      });

      // Create download link
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);

      return {
        success: true,
        message: "File downloaded successfully",
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || error.message,
        message: "Download failed",
      };
    }
  }
}

// Factory function to create API service with auth token
export const createApiService = (authToken = null, baseURL = null) => {
  return new ApiService(baseURL, authToken);
};

// Default API service instance
export const apiServiceChatBot = new ApiService();

// Convenience functions for common operations
export const apiChatBot = {
  // GET requests
  get: (url, params, options) => apiServiceChatBot.get(url, params, options),

  // POST requests
  post: (url, data, options) => apiServiceChatBot.post(url, data, options),

  // PUT requests
  put: (url, data, options) => apiServiceChatBot.put(url, data, options),

  // PATCH requests
  patch: (url, data, options) => apiServiceChatBot.patch(url, data, options),

  // DELETE requests
  delete: (url, options) => apiServiceChatBot.delete(url, options),

  // File operations
  upload: (url, file, options) => apiServiceChatBot.upload(url, file, options),
  uploadMultiple: (url, files, options) =>
    apiServiceChatBot.uploadMultiple(url, files, options),
  download: (url, filename, options) =>
    apiServiceChatBot.download(url, filename, options),
};

// Export the main class and constants
export default ApiService;
