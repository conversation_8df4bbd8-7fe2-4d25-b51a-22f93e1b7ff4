import { createSlice } from "@reduxjs/toolkit";
import {
  resendOtp,
  sendOtp,
  verifyOtp,
  signup,
  storeUserDetails,
  logout,
  fetchSocialLogin,
  updateProfile,
} from "./apiThunk";

const initialState = {
  authData: {
    token: "",
    name: "",
  },
  userDetails: {
    user: null,
    token: null,
  },
  isLoading: false,
  error: null,
  otpSent: false,
  otpVerified: false,
};

const authenticationSlice = createSlice({
  name: "authentication",
  initialState,
  reducers: {
    setAuthData: (state, action) => {
      state.authData = action.payload;
    },
    clearAuthData: (state) => {
      state.authData = {
        token: "",
        name: "",
      };
    },
    setUserDetails: (state, action) => {
      state.userDetails = action.payload;
    },
    clearUserDetails: (state) => {
      state.userDetails = {
        user: null,
        token: null,
      };
    },
    setOtpSent: (state, action) => {
      state.otpSent = action.payload;
    },
    setOtpVerified: (state, action) => {
      state.otpVerified = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Send OTP
    builder
      .addCase(sendOtp.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(sendOtp.fulfilled, (state, action) => {
        state.isLoading = false;
        state.otpSent = true;
      })
      .addCase(sendOtp.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Verify OTP
    builder
      .addCase(verifyOtp.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifyOtp.fulfilled, (state, action) => {
        state.isLoading = false;
        state.otpVerified = true;
        // Handle different possible response structures
        const responseData = action.payload;
        state.authData = {
          token: responseData?.token || responseData?.data?.token || "",
          name: responseData?.name || responseData?.data?.name || "",
        };
      })
      .addCase(verifyOtp.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Resend OTP
    builder
      .addCase(resendOtp.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resendOtp.fulfilled, (state, action) => {
        state.isLoading = false;
      })
      .addCase(resendOtp.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Signup
    builder
      .addCase(signup.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(signup.fulfilled, (state, action) => {
        state.isLoading = false;
        // Handle signup response
        const responseData = action.payload;
        state.authData = {
          token: responseData?.token || responseData?.data?.token || "",
          name: responseData?.name || responseData?.data?.name || "",
        };
      })
      .addCase(signup.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Store User Details
    builder
      .addCase(storeUserDetails.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(storeUserDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.userDetails = action.payload;
      })
      .addCase(storeUserDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Logout
    builder
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        // Reset all state to initial values
        state.authData = {
          token: "",
          name: "",
        };
        state.userDetails = {
          user: null,
          token: null,
        };
        state.otpSent = false;
        state.otpVerified = false;
        state.error = null;
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
        // Even on rejection, clear the auth state
        state.authData = {
          token: "",
          name: "",
        };
        state.userDetails = {
          user: null,
          token: null,
        };
        state.otpSent = false;
        state.otpVerified = false;
      });

    // Social Login
    builder
      .addCase(fetchSocialLogin.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSocialLogin.fulfilled, (state, action) => {
        state.isLoading = false;
        // Handle social login response
        const responseData = action.payload;
        state.authData = {
          token: responseData?.token || responseData?.data?.token || "",
          name: responseData?.name || responseData?.data?.name || "",
        };
      })
      .addCase(fetchSocialLogin.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Update Profile
    builder
      .addCase(updateProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        // Handle update profile response
        const responseData = action.payload;
        state.userDetails = {
          user: responseData?.user || state.userDetails.user,
          token: null,
        };
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });
  },
});

const { reducer } = authenticationSlice;
export const {
  setAuthData,
  clearAuthData,
  setUserDetails,
  clearUserDetails,
  setOtpSent,
  setOtpVerified,
  clearError,
} = authenticationSlice.actions;

// Export the API thunks
export {
  sendOtp,
  verifyOtp,
  resendOtp,
  signup,
  storeUserDetails,
  logout,
  fetchSocialLogin,
  updateProfile,
};

export default reducer;
