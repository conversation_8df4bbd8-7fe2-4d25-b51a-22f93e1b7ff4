import { createAsyncThunk } from "@reduxjs/toolkit";
import { api } from "../../../services/network";
import { ToastNotifyError } from "../../../components/Toast/ToastNotify";
import { updateLoading } from "../loader";
import { clearAllPersistedData } from "../../../utils/persistUtils";
import { getRefreshToken, setTokens } from "../../../utils/tokenUtils";

// Send OTP
const sendOtp = createAsyncThunk(
  "authentication/send-otp",
  async (payload, { dispatch }) => {
    dispatch(updateLoading(true));
    try {
      const response = await api.post("/api/accounts/v1/otp/", payload);
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || "Failed to send OTP");
      }
    } catch (err) {
      ToastNotifyError(err.message || "Failed to send OTP");
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

// Verify OTP
const verifyOtp = createAsyncThunk(
  "authentication/verify-otp",
  async (payload, { dispatch }) => {
    dispatch(updateLoading(true));
    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append("source", "LOGIN_SIGNUP");

      if (payload.email) {
        params.append("email", payload.email);
      }
      if (payload.phone_number) {
        params.append("phone_number", payload.phone_number);
      }
      if (payload.otp) {
        params.append("otp", payload.otp);
      }

      const response = await api.get(
        `/api/accounts/v1/otp/?${params.toString()}`
      );
      if (response?.data?.status === "success") {
        if (response?.data?.data?.is_valid) {
          console.log("response?.data?.data: ", response?.data?.data);
          if (
            response?.data?.data?.user_token?.access &&
            response?.data?.data?.user?.is_profile_completed
          ) {
            const token = response?.data?.data?.user_token?.access;
            const user = response?.data?.data?.user;
            const refreshToken = response?.data?.data?.user_token?.refresh;
            await dispatch(storeUserDetails({ user, token, refreshToken }));
          }
          return response.data;
        } else {
          throw new Error("Failed to verify OTP");
        }
      } else {
        throw new Error("Failed to verify OTP");
      }
    } catch (err) {
      ToastNotifyError(err.message || "Failed to verify OTP");
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

// Resend OTP
const resendOtp = createAsyncThunk(
  "authentication/resend-otp",
  async (payload, { dispatch }) => {
    dispatch(updateLoading(true));
    try {
      const response = await api.post("/api/accounts/v1/otp/", payload);
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || "Failed to resend OTP");
      }
    } catch (err) {
      ToastNotifyError(err.message || "Failed to resend OTP");
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

// Signup
const signup = createAsyncThunk(
  "authentication/signup",
  async (payload, { dispatch }) => {
    dispatch(updateLoading(true));
    try {
      const response = await api.post("/api/accounts/v1/signup/", payload);
      if (response?.data?.status === "success") {
        const token = response?.data?.data?.token?.access;
        const user = response?.data?.data?.user;
        const refreshToken = response?.data?.data?.token?.refresh;

        // Store user details and token using the new thunk
        await dispatch(storeUserDetails({ user, token, refreshToken }));

        return response.data;
      } else {
        throw new Error(response.message || "Failed to signup");
      }
    } catch (err) {
      ToastNotifyError(err.message || "Failed to signup");
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

// Store User Details and Token
const storeUserDetails = createAsyncThunk(
  "authentication/userDetails",
  async (payload, { dispatch }) => {
    try {
      const { user, token, refreshToken } = payload;

      // Store tokens in localStorage using utility function
      setTokens(token, refreshToken);

      // Return the data to be stored in Redux
      return {
        user: user || null,
        token: token || null,
      };
    } catch (err) {
      ToastNotifyError(err.message || "Failed to store user details");
      throw err;
    }
  }
);

// Logout
const logout = createAsyncThunk(
  "authentication/logout",
  async (_, { dispatch }) => {
    dispatch(updateLoading(true));
    try {
      // Get refresh token from localStorage using utility function
      const refreshToken = getRefreshToken();

      if (refreshToken) {
        // Call logout API
        const response = await api.post("/api/accounts/v1/logout/", {
          refresh_token: refreshToken,
        });

        if (response?.data?.status === "success") {
          // Clear all persisted data
          clearAllPersistedData();

          return response.data;
        } else {
          throw new Error(response?.message || "Failed to logout");
        }
      } else {
        // Clear all persisted data even if no token
        clearAllPersistedData();

        return { status: "success", message: "Logged out successfully" };
      }
    } catch (err) {
      // Clear all persisted data even on error
      clearAllPersistedData();
      ToastNotifyError("Failed to logout, Logout forcefully");
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

// Social Login
const fetchSocialLogin = createAsyncThunk(
  "authentication/social-login",
  async (payload, { dispatch }) => {
    dispatch(updateLoading(true));
    try {
      const response = await api.post(
        "/api/accounts/v1/social-login/",
        payload
      );
      if (response?.data?.status === "success") {
        return response.data;
      } else {
        throw new Error(
          response?.message || "Failed to authenticate with social login"
        );
      }
    } catch (err) {
      ToastNotifyError(
        err.message || "Failed to authenticate with social login"
      );
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

// Update Profile
const updateProfile = createAsyncThunk(
  "authentication/update-profile",
  async (payload, { dispatch }) => {
    const { token = null, ...rest } = payload;
    dispatch(updateLoading(true));
    try {
      const response = await api.patch(
        "/api/accounts/v1/profile/",
        rest,
        token
          ? {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          : {}
      );
      if (response?.data?.status === "success") {
        return response.data;
      } else {
        throw new Error(response?.message || "Failed to update profile");
      }
    } catch (err) {
      ToastNotifyError(err.message || "Failed to update profile");
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

export {
  sendOtp,
  verifyOtp,
  resendOtp,
  signup,
  storeUserDetails,
  logout,
  fetchSocialLogin,
  updateProfile,
};
