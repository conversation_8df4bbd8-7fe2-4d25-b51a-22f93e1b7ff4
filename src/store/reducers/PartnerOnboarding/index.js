import { createSlice } from "@reduxjs/toolkit";
import { fetchVerifyOtp, fetchVerifyTestOtp, uploadImage, fetchBrandOnboarding } from "./apiThunk";

const initialState = {
    onBoardingData: {},
    isLoading: false,
    error: null,
};

const partnerOnboardingSlice = createSlice({
    name: "partnerOnboarding",
    initialState, // ✅ Use correct variable
    reducers: {
        setOnBoardingData: (state, action) => {
            state.onBoardingData = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder.addCase(fetchVerifyOtp.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchVerifyOtp.fulfilled, (state, action) => {
            state.isLoading = false;
            state.onBoardingData = action.payload;
        });
        builder.addCase(fetchVerifyOtp.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        });
        builder.addCase(fetchVerifyTestOtp.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchVerifyTestOtp.fulfilled, (state, action) => {
            state.isLoading = false;
            state.onBoardingData = action.payload;
        });
        builder.addCase(fetchVerifyTestOtp.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        });
        builder.addCase(uploadImage.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(uploadImage.fulfilled, (state, action) => {
            state.isLoading = false;
            state.onBoardingData = action.payload;
        });
        builder.addCase(uploadImage.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        });
        builder.addCase(fetchBrandOnboarding.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchBrandOnboarding.fulfilled, (state, action) => {
            state.isLoading = false;
            state.onBoardingData = action.payload;
        });
        builder.addCase(fetchBrandOnboarding.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.payload;   
        });
    },
});
const { reducer } = partnerOnboardingSlice;
export const { setOnBoardingData } = partnerOnboardingSlice.actions;

export { fetchVerifyOtp, fetchVerifyTestOtp, uploadImage, fetchBrandOnboarding };

export default reducer;
