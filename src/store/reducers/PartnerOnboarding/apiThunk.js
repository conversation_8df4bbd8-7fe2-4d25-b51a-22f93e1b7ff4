import { createAsyncThunk } from "@reduxjs/toolkit";
import { getAuthorization } from "../../../services/config";
import { BRAND_ONBOARDING } from "../../../constants/reduxConstants";
import API_ROUTES from "../../../constants/apiRoutes";
import { ToastNotifyError } from "../../../components/Toast/ToastNotify";
import { updateLoading } from "../loader";

const { verify_otp = "", verify_test_otp = "" } = BRAND_ONBOARDING;
const {
  V1: { GET_OTP, VERIFY_TEST_OTP, PRE_SIGNED_URL, BRAND_ONBOARDING_FORM } = {},
} = API_ROUTES;

const fetchVerifyOtp = createAsyncThunk(
  verify_otp,
  async (payload, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    try {
      dispatch(updateLoading(true));
      const response = await API.post(GET_OTP, payload);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);
const fetchVerifyTestOtp = createAsyncThunk(
  verify_test_otp,
  async (payload, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    try {
      dispatch(updateLoading(true));
      const response = await API.get(VERIFY_TEST_OTP + payload);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);
const uploadImage = createAsyncThunk(
  "auth/upload-Image",
  async (binaryData, { dispatch, getState }) => {
    const APIWithAuth = getAuthorization(getState, dispatch);
    try {
      dispatch(updateLoading(true));
      const preSignedResponse = await APIWithAuth.get(
        PRE_SIGNED_URL + binaryData?.name + "/"
      );
      const { status } = preSignedResponse?.data;
      const { url, path } = preSignedResponse?.data?.data;


      if (status === "success") {
        const getUploadResponse = await fetch(url, {
          method: "PUT",
          body: binaryData,
          headers: {
            "Content-Type": binaryData?.type,
          },
        });

        dispatch(updateLoading(false));
        return {
          uploadResponse: {
            status: getUploadResponse?.status,
            statusText: getUploadResponse?.statusText,
          },
          path,
        };
      }

      dispatch(updateLoading(false));
      throw new Error("Failed to get pre-signed URL");
    } catch (err) {
      dispatch(updateLoading(false));
      ToastNotifyError("Error uploading image");
      return Promise.reject(err);
    }
  }
);

const fetchBrandOnboarding = createAsyncThunk(
  "brandOnboarding",
  async (payload, { dispatch, getState }) => {
    dispatch(updateLoading(true));
    const API = getAuthorization(getState, dispatch);
    try {
      const response = await API.post(BRAND_ONBOARDING_FORM, payload);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

export {
  fetchVerifyOtp,
  fetchVerifyTestOtp,
  uploadImage,
  fetchBrandOnboarding,
};
