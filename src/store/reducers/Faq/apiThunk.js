import { createAsyncThunk } from "@reduxjs/toolkit";
import { getAuthorization } from "../../../services/config";
import { FAQ } from "../../../constants/reduxConstants";
import API_ROUTES from "../../../constants/apiRoutes";
import { ToastNotifyError } from "../../../components/Toast/ToastNotify";

const { get_faq = "" } = FAQ;
const { V2: { GET_FAQ } = {} } = API_ROUTES;

const fetchFaq = createAsyncThunk(
  get_faq,
  async (_, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    try {
      const response = await API.get(GET_FAQ);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    }
  }
);

export { fetchFaq };
