import { createSlice } from "@reduxjs/toolkit";
import { fetchFaq } from "./apiThunk";

const initialState = {
    faqData: {},
    isLoading: false,
    error: null,
};

const faqSlice = createSlice({
    name: "faq",
    initialState, // ✅ Use correct variable
    reducers: {
        setFaqData: (state, action) => {
            state.faqData = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder.addCase(fetchFaq.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchFaq.fulfilled, (state, action) => {
            state.isLoading = false;
            state.faqData = action.payload;
        });
        builder.addCase(fetchFaq.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.error.message;
        });
    },
});

const { reducer } = faqSlice;
export const { setFaqData } = faqSlice.actions;

export { fetchFaq };

export default reducer;




