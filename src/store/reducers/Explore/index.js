import { createSlice } from "@reduxjs/toolkit";
import { fetchExploreCategories, fetchExploreDestinations, fetchExploreActivities, fetchExploreDestinationsDropdowns, fetchExploreDestinationsByCategory, fetchExplorePopularDestinations, fetchExploreVisaOnArrival, fetchExplorePackages, fetchExploreActivitiesByDestination, fetchExploreActivitiesGallery } from "./apiThunk";

const initialState = {
    categories: [],
    destinations: [],
    activities: [],
    isLoading: false,
    error: null,
};

const exploreSlice = createSlice({
    name: "explore",
    initialState,
    reducers: {
        setExploreCategories: (state, action) => {
            state.categories = action.payload;
        },
        setExploreDestinations: (state, action) => {
            state.destinations = action.payload;
        },
        setExploreActivities: (state, action) => {
            state.activities = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder.addCase(fetchExploreCategories.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchExploreCategories.fulfilled, (state, action) => {
            state.isLoading = false;
            state.categories = action.payload;
        });
        builder.addCase(fetchExploreCategories.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        });
        builder.addCase(fetchExploreDestinations.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchExploreDestinations.fulfilled, (state, action) => {
            state.isLoading = false;
            state.destinations = action.payload;
        });
        builder.addCase(fetchExploreDestinations.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        });
        builder.addCase(fetchExploreActivities.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchExploreActivities.fulfilled, (state, action) => {
            state.isLoading = false;
            state.activities = action.payload;
        });
        builder.addCase(fetchExploreActivities.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        });
        builder.addCase(fetchExploreDestinationsDropdowns.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchExploreDestinationsDropdowns.fulfilled, (state, action) => {
            state.isLoading = false;
            state.destinationsDropdowns = action.payload;
        });
        builder.addCase(fetchExploreDestinationsDropdowns.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        });
        builder.addCase(fetchExploreDestinationsByCategory.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchExploreDestinationsByCategory.fulfilled, (state, action) => {
            state.isLoading = false;
            state.destinationsByCategory = action.payload;
        });
        builder.addCase(fetchExploreDestinationsByCategory.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        });
        builder.addCase(fetchExplorePopularDestinations.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchExplorePopularDestinations.fulfilled, (state, action) => {
            state.isLoading = false;
            state.popularDestinations = action.payload;
        });
        builder.addCase(fetchExplorePopularDestinations.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        });
        builder.addCase(fetchExploreVisaOnArrival.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchExploreVisaOnArrival.fulfilled, (state, action) => {
            state.isLoading = false;
            state.visaOnArrival = action.payload;
        });
        builder.addCase(fetchExploreVisaOnArrival.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        });
        builder.addCase(fetchExplorePackages.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchExplorePackages.fulfilled, (state, action) => {
            state.isLoading = false;
            state.packages = action.payload;
        });
        builder.addCase(fetchExplorePackages.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        });
        builder.addCase(fetchExploreActivitiesByDestination.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchExploreActivitiesByDestination.fulfilled, (state, action) => {
            state.isLoading = false;
            state.activitiesByDestination = action.payload;
        });
        builder.addCase(fetchExploreActivitiesByDestination.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        });
        builder.addCase(fetchExploreActivitiesGallery.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchExploreActivitiesGallery.fulfilled, (state, action) => {
            state.isLoading = false;
            state.activitiesGallery = action.payload;
        });
        builder.addCase(fetchExploreActivitiesGallery.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        });
    },
});

const { reducer } = exploreSlice;
export const { setExploreCategories, setExploreDestinations, setExploreActivities } = exploreSlice.actions;

export { fetchExploreCategories, fetchExploreDestinations, fetchExploreActivities, fetchExploreDestinationsDropdowns, fetchExploreDestinationsByCategory, fetchExplorePopularDestinations, fetchExploreVisaOnArrival, fetchExplorePackages, fetchExploreActivitiesByDestination, fetchExploreActivitiesGallery };

export default reducer;