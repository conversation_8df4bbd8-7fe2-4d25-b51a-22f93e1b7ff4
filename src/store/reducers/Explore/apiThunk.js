import { createAsyncThunk } from "@reduxjs/toolkit";
import { getAuthorization } from "../../../services/config";
import { EXPLORE } from "../../../constants/reduxConstants";
import API_ROUTES from "../../../constants/apiRoutes";
import { ToastNotifyError } from "../../../components/Toast/ToastNotify";
import { updateLoading } from "../loader";

const { get_explore_categories = "", get_explore_destinations = "", get_explore_activities = "", get_explore_destinations_dropdowns = "", get_explore_destinations_by_category = "", get_explore_destinations_popular = "", get_explore_visa_on_arrival = "", get_explore_packages = "", get_explore_activities_by_destination = "", get_explore_activities_gallery = "" } = EXPLORE;
const { V3: { GET_EXPLORE_CATEGORIES, GET_EXPLORE_DESTINATIONS, GET_EXPLORE_ACTIVITIES, GET_EXPLORE_DESTINATIONS_DROPDOWNS, GET_EXPLORE_DESTINATIONS_BY_CATEGORY, GET_EXPLORE_DESTINATIONS_POPULAR, GET_EXPLORE_VISA_ON_ARRIVAL, GET_EXPLORE_PACKAGES, GET_EXPLORE_ACTIVITIES_BY_DESTINATION, GET_EXPLORE_ACTIVITIES_GALLERY } = {} } = API_ROUTES;

const fetchExploreCategories = createAsyncThunk(
  get_explore_categories,
  async (params, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_EXPLORE_CATEGORIES + params);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const fetchExploreDestinations = createAsyncThunk(
  get_explore_destinations,
  async (_, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_EXPLORE_DESTINATIONS);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const fetchExploreActivities = createAsyncThunk(
  get_explore_activities,
  async (_, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_EXPLORE_ACTIVITIES);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const fetchExploreDestinationsDropdowns = createAsyncThunk(
  get_explore_destinations_dropdowns,
  async (params, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_EXPLORE_DESTINATIONS_DROPDOWNS + params);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const fetchExploreDestinationsByCategory = createAsyncThunk(
  get_explore_destinations_by_category,
  async (params, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_EXPLORE_DESTINATIONS_BY_CATEGORY + params);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);
const fetchExplorePopularDestinations = createAsyncThunk(
  get_explore_destinations_popular,
  async (params, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_EXPLORE_DESTINATIONS_POPULAR + params);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);
const fetchExploreVisaOnArrival = createAsyncThunk(
  get_explore_visa_on_arrival,
  async (params, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_EXPLORE_VISA_ON_ARRIVAL + params);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);
const fetchExplorePackages = createAsyncThunk(
  get_explore_packages,
  async (params, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_EXPLORE_PACKAGES + params);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);
const fetchExploreActivitiesByDestination = createAsyncThunk(
  get_explore_activities_by_destination,
  async (params, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_EXPLORE_ACTIVITIES_BY_DESTINATION + params);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);
const fetchExploreActivitiesGallery = createAsyncThunk(
  get_explore_activities_gallery,
  async (id, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_EXPLORE_ACTIVITIES_GALLERY + id + "/");
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

export { fetchExploreCategories, fetchExploreDestinations, fetchExploreActivities, fetchExploreDestinationsDropdowns, fetchExploreDestinationsByCategory, fetchExplorePopularDestinations, fetchExploreVisaOnArrival, fetchExplorePackages, fetchExploreActivitiesByDestination, fetchExploreActivitiesGallery };