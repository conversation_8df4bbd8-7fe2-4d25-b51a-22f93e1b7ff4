import { createAsyncThunk } from "@reduxjs/toolkit";
import { api } from "../../../services/network";
import {
  ToastNotifyError,
  ToastNotifySuccess,
} from "../../../components/Toast/ToastNotify";
import { updateLoading } from "../loader";

// Get affiliate profile
const fetchAffiliateProfile = createAsyncThunk(
  "affiliate/fetch-profile",
  async (_, { dispatch }) => {
    dispatch(updateLoading(true));
    try {
      const response = await api.get("/api/accounts/v1/affiliate-profile/");
      if (response?.data?.status === "success") {
        return response.data?.data || response.data;
      } else {
        throw new Error(
          response.message || "Failed to fetch affiliate profile"
        );
      }
    } catch (err) {
      ToastNotifyError(err.message || "Failed to fetch affiliate profile");
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

// Send affiliate request
const sendAffiliateRequest = createAsyncThunk(
  "affiliate/send-request",
  async (_, { dispatch }) => {
    dispatch(updateLoading(true));
    try {
      const response = await api.post("/api/accounts/v1/affiliate-request/");
      if (response?.data?.status === "success") {
        ToastNotifySuccess("Affiliate request sent successfully");
        return response.data?.data || response.data;
      } else {
        throw new Error(response.message || "Failed to send affiliate request");
      }
    } catch (err) {
      ToastNotifyError(err.message || "Failed to send affiliate request");
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

// Get affiliate referral users
const fetchAffiliateReferralUsers = createAsyncThunk(
  "affiliate/fetch-referral-users",
  async (data, { dispatch }) => {
    dispatch(updateLoading(true));
    try {
      const response = await api.get(
        `/api/accounts/v1/affiliate-referral-users/?page_size=${data?.page_size || 10}&page=${data?.page || 1}`
      );
      if (response?.data?.status === "success") {
        return response.data?.data || response.data;
      } else {
        throw new Error(response.message || "Failed to fetch referral users");
      }
    } catch (err) {
      ToastNotifyError(err.message || "Failed to fetch referral users");
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

export {
  fetchAffiliateProfile,
  sendAffiliateRequest,
  fetchAffiliateReferralUsers,
};
