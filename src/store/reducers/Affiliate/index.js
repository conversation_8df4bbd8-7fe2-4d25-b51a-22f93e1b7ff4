import { createSlice } from "@reduxjs/toolkit";
import {
  fetchAffiliateProfile,
  sendAffiliateRequest,
  fetchAffiliateReferralUsers,
} from "./apiThunk";

const initialState = {
  profile: null,
  referralUsers: [],
  isLoading: false,
  error: null,
};

const affiliateSlice = createSlice({
  name: "affiliate",
  initialState,
  reducers: {
    clearAffiliateData: (state) => {
      state.profile = null;
      state.referralUsers = [];
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch affiliate profile
    builder
      .addCase(fetchAffiliateProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAffiliateProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.profile = action.payload;
      })
      .addCase(fetchAffiliateProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Send affiliate request
    builder
      .addCase(sendAffiliateRequest.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(sendAffiliateRequest.fulfilled, (state, action) => {
        state.isLoading = false;
        // Update profile with new request data
        if (state.profile) {
          state.profile.status = action.payload.status;
          state.profile.external_id = action.payload.external_id;
          state.profile.requested_at = action.payload.requested_at;
        }
      })
      .addCase(sendAffiliateRequest.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Fetch affiliate referral users
    builder
      .addCase(fetchAffiliateReferralUsers.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAffiliateReferralUsers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.referralUsers = action.payload;
      })
      .addCase(fetchAffiliateReferralUsers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });
  },
});

const { reducer } = affiliateSlice;
export const { clearAffiliateData, clearError } = affiliateSlice.actions;
export default reducer;
