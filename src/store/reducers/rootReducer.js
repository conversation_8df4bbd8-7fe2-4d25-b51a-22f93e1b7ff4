import { combineReducers } from '@reduxjs/toolkit';
import partnerOnboarding from './PartnerOnboarding';
import loader from './loader';
import explore from './Explore';
import terms from "./Terms";
import packageDetails from "./PackageDetails";
import home from "./Home";
import blog from "./Blog";
import authentication from "./Authentication";
import onboardingModal from "./onboardingModal";
import conversation from "./Conversation";
import affiliate from "./Affiliate";

const appReducer = combineReducers({
  partnerOnboarding,
  loader,
  explore,
  terms,
  packageDetails,
  home,
  blog,
  authentication,
  onboardingModal,
  conversation,
  affiliate,
});

const rootReducer = (state, action) => {
  // Reset all state when RESET_STORE action is dispatched
  if (action.type === "RESET_STORE") {
    return appReducer(undefined, action);
  }

  return appReducer(state, action);
};

export default rootReducer;

