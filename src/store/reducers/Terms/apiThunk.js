import { createAsyncThunk } from "@reduxjs/toolkit";
import { getAuthorization } from "../../../services/config";
import { COMMON } from "../../../constants/reduxConstants";
import { ToastNotifyError } from "../../../components/Toast/ToastNotify";
import { updateLoading } from "../loader";

const { get_terms_and_conditions = "", get_privacy_policy = "" } = COMMON;

const fetchTermsAndConditions = createAsyncThunk(
  get_terms_and_conditions,
  async (_, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get("/api/modules/v1/terms-and-conditions/");
      return response.data;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const fetchPrivacyPolicy = createAsyncThunk(
  get_privacy_policy,
  async (_, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get("/api/modules/v1/privacy-policy/");
      return response.data;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

export { fetchTermsAndConditions, fetchPrivacyPolicy };
