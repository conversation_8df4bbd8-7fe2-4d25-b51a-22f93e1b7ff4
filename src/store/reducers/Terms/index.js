import { createSlice } from "@reduxjs/toolkit";
import { fetchTermsAndConditions, fetchPrivacyPolicy } from "./apiThunk";

const initialState = {
  termsData: null,
  privacyData: null,
  isLoading: false,
  error: null,
};

const termsSlice = createSlice({
  name: "terms",
  initialState,
  reducers: {
    setTermsData: (state, action) => {
      state.termsData = action.payload;
    },
    setPrivacyData: (state, action) => {
      state.privacyData = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchTermsAndConditions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchTermsAndConditions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.termsData = action.payload;
      })
      .addCase(fetchTermsAndConditions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      })
      .addCase(fetchPrivacyPolicy.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchPrivacyPolicy.fulfilled, (state, action) => {
        state.isLoading = false;
        state.privacyData = action.payload;
      })
      .addCase(fetchPrivacyPolicy.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });
  },
});

const { reducer } = termsSlice;
export const { setTermsData, setPrivacyData } = termsSlice.actions;
export default reducer;
