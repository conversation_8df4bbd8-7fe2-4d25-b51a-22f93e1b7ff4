import { createSlice } from "@reduxjs/toolkit";
import { fetchConversations } from "./apiThunk";

const initialState = {
  conversations: [],
  currentConversation: null,
  isLoading: false,
  error: null,
};

const conversationSlice = createSlice({
  name: "conversation",
  initialState,
  reducers: {
    setConversations: (state, action) => {
      state.conversations = action.payload;
    },
    setCurrentConversation: (state, action) => {
      state.currentConversation = action.payload;
    },
    addConversation: (state, action) => {
      state.conversations.unshift(action.payload);
    },
    updateConversation: (state, action) => {
      const { id, updates } = action.payload;
      const index = state.conversations.findIndex((conv) => conv.id === id);
      if (index !== -1) {
        state.conversations[index] = {
          ...state.conversations[index],
          ...updates,
        };
      }
    },
    deleteConversation: (state, action) => {
      const id = action.payload;
      state.conversations = state.conversations.filter(
        (conv) => conv.id !== id
      );
      if (state.currentConversation?.id === id) {
        state.currentConversation = null;
      }
    },
    clearConversations: (state) => {
      state.conversations = [];
      state.currentConversation = null;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch Conversations
    builder
      .addCase(fetchConversations.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchConversations.fulfilled, (state, action) => {
        state.isLoading = false;
        state.conversations = action.payload;
      })
      .addCase(fetchConversations.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });
  },
});

const { reducer } = conversationSlice;
export const {
  setConversations,
  setCurrentConversation,
  addConversation,
  updateConversation,
  deleteConversation,
  clearConversations,
  clearError,
} = conversationSlice.actions;

export default reducer;
