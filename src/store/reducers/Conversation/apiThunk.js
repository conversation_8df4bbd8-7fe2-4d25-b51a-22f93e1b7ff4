import { createAsyncThunk } from "@reduxjs/toolkit";
import { apiChatBot } from "../../../services/networkChatBot";
import { CONVERSATION } from "../../../constants/reduxConstants";
import API_ROUTES from "../../../constants/apiRoutes";
import { ToastNotifyError } from "../../../components/Toast/ToastNotify";
import { updateLoading } from "../loader";

const {
  get_conversations = "",
  create_conversation = "",
  get_conversation_chats = "",
  delete_conversation = "",
  rename_conversation = "",
} = CONVERSATION;
const {
  V4: {
    GET_CONVERSATIONS,
    CREATE_CONVERSATION,
    GET_CONVERSATION_CHATS,
    DELETE_CONVERSATION,
    RENAME_CONVERSATION,
  } = {},
} = API_ROUTES;

const fetchConversations = createAsyncThunk(
  get_conversations,
  async (_, { dispatch }) => {
    dispatch(updateLoading(true));
    try {
      const response = await apiChatBot.get(GET_CONVERSATIONS);
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || "Failed to fetch conversations");
      }
    } catch (err) {
      ToastNotifyError(err.message || "Failed to fetch conversations");
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const fetchConversationChats = createAsyncThunk(
  get_conversation_chats,
  async (conversationId, { dispatch }) => {
    dispatch(updateLoading(true));
    try {
      const response = await apiChatBot.get(
        GET_CONVERSATION_CHATS + "/" + conversationId
      );
      if (response.success) {
        return response.data;
      } else {
        throw new Error(
          response.message || "Failed to fetch conversation chats"
        );
      }
    } catch (error) {
      console.error("error: ", error);
    }
  }
);

const createConversation = createAsyncThunk(
  create_conversation,
  async (payload, { dispatch }) => {
    dispatch(updateLoading(true));
    try {
      const response = await apiChatBot.post(CREATE_CONVERSATION, payload);
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || "Failed to create conversation");
      }
    } catch (err) {
      ToastNotifyError(err.message || "Failed to create conversation");
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const deleteConversation = createAsyncThunk(
  delete_conversation,
  async (conversationId, { dispatch }) => {
    dispatch(updateLoading(true));
    try {
      const response = await apiChatBot.delete(
        DELETE_CONVERSATION + "/" + conversationId
      );
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || "Failed to delete conversation");
      }
    } catch (err) {
      ToastNotifyError(err.message || "Failed to delete conversation");
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const renameConversation = createAsyncThunk(
  rename_conversation,
  async (payload, { dispatch }) => {
    const { conversationId, title } = payload;
    dispatch(updateLoading(true));
    try {
      const response = await apiChatBot.patch(
        RENAME_CONVERSATION + "/" + conversationId + "/rename",
        { title }
      );
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || "Failed to rename conversation");
      }
    } catch (err) {
      ToastNotifyError(err.message || "Failed to rename conversation");
      throw err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

export {
  fetchConversations,
  fetchConversationChats,
  createConversation,
  deleteConversation,
  renameConversation,
};
