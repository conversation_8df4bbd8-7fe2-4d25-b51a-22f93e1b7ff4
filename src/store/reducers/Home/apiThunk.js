import { createAsyncThunk } from "@reduxjs/toolkit";
import { getAuthorization } from "../../../services/config";
import { HOME } from "../../../constants/reduxConstants";
import API_ROUTES from "../../../constants/apiRoutes";
import { ToastNotifyError } from "../../../components/Toast/ToastNotify";
import { updateLoading } from "../loader";


const { get_trending_destinations = "", get_destinations = "", get_join_newsletter = "" } = HOME;
const { V3: { GET_TRENDING_DESTINATIONS, GET_DESTINATIONS } = {} } = API_ROUTES;
const { V2: { GET_JOIN_NEWSLETTER } = {} } = API_ROUTES;



const fetchTrendingDestinations = createAsyncThunk(
  get_trending_destinations,
  async (_, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_TRENDING_DESTINATIONS);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);
const fetchDestinations = createAsyncThunk(
  get_destinations,
  async (_, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_DESTINATIONS);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);
const fetchJoinNewsletter = createAsyncThunk(
  get_join_newsletter,
  async (data, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.post(GET_JOIN_NEWSLETTER, data);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

export { fetchTrendingDestinations, fetchDestinations, fetchJoinNewsletter };