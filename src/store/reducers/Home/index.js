import { createSlice } from "@reduxjs/toolkit";
import { fetchTrendingDestinations, fetchDestinations, fetchJoinNewsletter } from "./apiThunk";

const initialState = {
    trendingDestinations: [],
    destinations: [],
    isLoading: false,
    error: null,
};

const homeSlice = createSlice({
    name: "home",
    initialState, // ✅ Use correct variable
    reducers: {
        setTrendingDestinations: (state, action) => {
            state.trendingDestinations = action.payload;
        },
        setDestinations: (state, action) => {
            state.destinations = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder.addCase(fetchTrendingDestinations.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchTrendingDestinations.fulfilled, (state, action) => {
            state.isLoading = false;
            state.trendingDestinations = action.payload;
        });
        builder.addCase(fetchTrendingDestinations.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.error.message;
        });
        builder.addCase(fetchDestinations.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchDestinations.fulfilled, (state, action) => {
            state.isLoading = false;
            state.destinations = action.payload;
        });
        builder.addCase(fetchDestinations.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.error.message;
        });
        builder.addCase(fetchJoinNewsletter.pending, (state) => {
            state.isLoading = true;
        });
        builder.addCase(fetchJoinNewsletter.fulfilled, (state, action) => {
            state.isLoading = false;
            state.joinNewsletter = action.payload;
        });
        builder.addCase(fetchJoinNewsletter.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.error.message;
        });
    },
});

const { reducer } = homeSlice;
export const { setTrendingDestinations, setDestinations } = homeSlice.actions;

export { fetchTrendingDestinations, fetchDestinations, fetchJoinNewsletter };

export default reducer;




