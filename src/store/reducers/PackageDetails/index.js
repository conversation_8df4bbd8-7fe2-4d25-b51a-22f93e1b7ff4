import { createSlice } from "@reduxjs/toolkit";
import {
  fetchPackageBasicDetails,
  fetchPackageItineraryDetails,
  fetchPackageDestinationFaq,
  fetchTermsAndConditions,
  fetchPrivacyPolicy,
  generatePackagePdf,
} from "./apiThunk";

const initialState = {
  basicDetails: null,
  itineraryDetails: null,
  destinationFaq: null,
  termsAndConditions: null,
  privacyPolicy: null,
  isLoading: false,
  error: null,
};

const packageDetailsSlice = createSlice({
  name: "packageDetails",
  initialState,
  reducers: {
    setBasicDetails: (state, action) => {
      state.basicDetails = action.payload;
    },
    setItineraryDetails: (state, action) => {
      state.itineraryDetails = action.payload;
    },
    setDestinationFaq: (state, action) => {
      state.destinationFaq = action.payload;
    },
    setTermsAndConditions: (state, action) => {
      state.termsAndConditions = action.payload;
    },
    setPrivacyPolicy: (state, action) => {
      state.privacyPolicy = action.payload;
    },
    clearPackageDetails: (state) => {
      state.basicDetails = null;
      state.itineraryDetails = null;
      state.destinationFaq = null;
      state.termsAndConditions = null;
      state.privacyPolicy = null;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Basic Details
    builder
      .addCase(fetchPackageBasicDetails.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPackageBasicDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.basicDetails = action.payload;
      })
      .addCase(fetchPackageBasicDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Itinerary Details
    builder
      .addCase(fetchPackageItineraryDetails.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPackageItineraryDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.itineraryDetails = action.payload;
      })
      .addCase(fetchPackageItineraryDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Destination FAQ
    builder
      .addCase(fetchPackageDestinationFaq.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPackageDestinationFaq.fulfilled, (state, action) => {
        state.isLoading = false;
        state.destinationFaq = action.payload;
      })
      .addCase(fetchPackageDestinationFaq.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Terms and Conditions
    builder
      .addCase(fetchTermsAndConditions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTermsAndConditions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.termsAndConditions = action.payload;
      })
      .addCase(fetchTermsAndConditions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Privacy Policy
    builder
      .addCase(fetchPrivacyPolicy.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPrivacyPolicy.fulfilled, (state, action) => {
        state.isLoading = false;
        state.privacyPolicy = action.payload;
      })
      .addCase(fetchPrivacyPolicy.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });
  },
});

const { reducer } = packageDetailsSlice;
export const {
  setBasicDetails,
  setItineraryDetails,
  setDestinationFaq,
  setTermsAndConditions,
  setPrivacyPolicy,
  clearPackageDetails,
} = packageDetailsSlice.actions;

export {
  fetchPackageBasicDetails,
  fetchPackageItineraryDetails,
  fetchPackageDestinationFaq,
  fetchTermsAndConditions,
  fetchPrivacyPolicy,
  generatePackagePdf,
};

export default reducer;
