import { createAsyncThunk } from "@reduxjs/toolkit";
import { getAuthorization } from "../../../services/config";
import { PACKAGE_DETAILS } from "../../../constants/reduxConstants";
import { ToastNotifyError } from "../../../components/Toast/ToastNotify";
import { updateLoading } from "../loader";

const {
  get_package_basic_details = "",
  get_package_itinerary_details = "",
  get_package_destination_faq = "",
  get_terms_and_conditions = "",
  get_privacy_policy = "",
  generate_package_pdf = "",
} = PACKAGE_DETAILS;

const fetchPackageBasicDetails = createAsyncThunk(
  get_package_basic_details,
  async (packageId, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(
        `/api/packages/v1/package/${packageId}/basic-details/`
      );
      return response.data;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const fetchPackageItineraryDetails = createAsyncThunk(
  get_package_itinerary_details,
  async (packageId, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(
        `/api/packages/v1/package/${packageId}/itinerary-details/`
      );
      return response.data;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const fetchPackageDestinationFaq = createAsyncThunk(
  get_package_destination_faq,
  async (packageId, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(
        `/api/packages/v1/package/${packageId}/destination-faq/`
      );
      return response.data;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const fetchTermsAndConditions = createAsyncThunk(
  get_terms_and_conditions,
  async (_, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get("/api/modules/v1/terms-and-conditions/");
      return response.data;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const fetchPrivacyPolicy = createAsyncThunk(
  get_privacy_policy,
  async (_, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get("/api/modules/v1/privacy-policy/");
      return response.data;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const generatePackagePdf = createAsyncThunk(
  generate_package_pdf,
  async (packageId, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(
        `/api/packages/v1/package/${packageId}/generate-pdf/`
      );
      return response.data;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

export {
  fetchPackageBasicDetails,
  fetchPackageItineraryDetails,
  fetchPackageDestinationFaq,
  fetchTermsAndConditions,
  fetchPrivacyPolicy,
  generatePackagePdf,
};
