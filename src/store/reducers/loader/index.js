import { createSlice } from "@reduxjs/toolkit";

const loaderSlice = createSlice({
  name: "loader",
  initialState: {
    isLoading: false,
  },
  reducers: {
    updateLoading: (state, action) => {
      state.isLoading = action.payload;
    },
  },
});

const {
  actions: { updateLoading },
  reducer,
} = loaderSlice;

const selectorLoader = (state) => state.loader;
const selectorHomeLoader = (state) => state?.loader?.isLoading;

export { updateLoading, selectorLoader, selectorHomeLoader };
export default reducer;
