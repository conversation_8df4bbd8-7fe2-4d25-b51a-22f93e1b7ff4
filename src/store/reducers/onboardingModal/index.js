import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  isOpen: false,
};

const onboardingModalSlice = createSlice({
  name: "onboardingModal",
  initialState,
  reducers: {
    openOnboardingModal: (state) => {
      state.isOpen = true;
    },
    closeOnboardingModal: (state) => {
      state.isOpen = false;
    },
    toggleOnboardingModal: (state) => {
      state.isOpen = !state.isOpen;
    },
  },
});

export const {
  openOnboardingModal,
  closeOnboardingModal,
  toggleOnboardingModal,
} = onboardingModalSlice.actions;

export default onboardingModalSlice.reducer;
