import { createSlice } from "@reduxjs/toolkit";
import {
  fetchBlogPosts,
  fetchBlogDetails,
  fetchBlogTags,
  fetchBlogPodcasts,
} from "./apiThunk";

const initialState = {
  blogPosts: [],
  blogDetails: null,
  blogTags: [],
  blogPodcasts: [],
  isLoading: false,
  error: null,
};

const blogSlice = createSlice({
  name: "blog",
  initialState,
  reducers: {
    setBlogPosts: (state, action) => {
      state.blogPosts = action.payload;
    },
    setBlogDetails: (state, action) => {
      state.blogDetails = action.payload;
    },
    setBlogTags: (state, action) => {
      state.blogTags = action.payload;
    },
    setBlogPodcasts: (state, action) => {
      state.blogPodcasts = action.payload;
    },
    clearBlogDetails: (state) => {
      state.blogDetails = null;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Blog Posts
    builder
      .addCase(fetchBlogPosts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchBlogPosts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.blogPosts = action.payload;
      })
      .addCase(fetchBlogPosts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Blog Details
    builder
      .addCase(fetchBlogDetails.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchBlogDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.blogDetails = action.payload;
      })
      .addCase(fetchBlogDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Blog Tags
    builder
      .addCase(fetchBlogTags.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchBlogTags.fulfilled, (state, action) => {
        state.isLoading = false;
        state.blogTags = action.payload;
      })
      .addCase(fetchBlogTags.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });

    // Blog Podcasts
    builder
      .addCase(fetchBlogPodcasts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchBlogPodcasts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.blogPodcasts = action.payload;
      })
      .addCase(fetchBlogPodcasts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });
  },
});

const { reducer } = blogSlice;
export const {
  setBlogPosts,
  setBlogDetails,
  setBlogTags,
  setBlogPodcasts,
  clearBlogDetails,
} = blogSlice.actions;
export default reducer;
