import { createAsyncThunk } from "@reduxjs/toolkit";
import { getAuthorization } from "../../../services/config";
import { BLOG } from "../../../constants/reduxConstants";
import API_ROUTES from "../../../constants/apiRoutes";
import { ToastNotifyError } from "../../../components/Toast/ToastNotify";
import { updateLoading } from "../loader";

const {
  get_blog_posts = "",
  get_blog_details = "",
  get_blog_tags = "",
  get_blog_podcasts = "",
} = BLOG;
const { V1: { GET_BLOG_POSTS, GET_BLOG_DETAILS, GET_BLOG_PODCASTS } = {} } =
  API_ROUTES;
const { V3: { GET_BLOG_TAGS } = {} } = API_ROUTES;

const fetchBlogPosts = createAsyncThunk(
  get_blog_posts,
  async (params = "", { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_BLOG_POSTS + params);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const fetchBlogDetails = createAsyncThunk(
  get_blog_details,
  async (blogId, { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_BLOG_DETAILS + blogId + "/");
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const fetchBlogTags = createAsyncThunk(
  get_blog_tags,
  async (params = "?dropdown=tags", { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_BLOG_TAGS + params);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

const fetchBlogPodcasts = createAsyncThunk(
  get_blog_podcasts,
  async (params = "", { dispatch, getState }) => {
    const API = getAuthorization(getState, dispatch);
    dispatch(updateLoading(true));
    try {
      const response = await API.get(GET_BLOG_PODCASTS + params);
      return response;
    } catch (err) {
      ToastNotifyError(err);
      return err;
    } finally {
      dispatch(updateLoading(false));
    }
  }
);

export { fetchBlogPosts, fetchBlogDetails, fetchBlogTags, fetchBlogPodcasts };
