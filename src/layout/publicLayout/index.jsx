import React from "react";
import Header from "../../components/Header";
import Footer from "../../components/Footer";
import { Box } from "@mui/material";

const PublicLayout = ({ children, stickyHeader = true }) => {
  return (
    <Box sx={{ overflow: "visible" }}>
      <Header isSticky={stickyHeader} />
      <Box>{children}</Box>
      <Footer />
    </Box>
  );
};

export default PublicLayout;
