import React, { useState } from "react";
import { Box, Typography } from "@mui/material";
import CustomSelect from "../../../components/common/CustomSelect";
import PrimaryButton from "../../../components/common/Button/PrimaryButton";

export const CustomizePackage = () => {
  const [formData, setFormData] = useState({
    destination: "",
    travelType: "",
    adults: "",
    children: "",
    infants: "",
    comfortZone: "",
    numberOfDays: "",
    category: "",
  });

  const handleFormChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Dummy data for dropdowns
  const destinationOptions = [
    { value: "bali", label: "Bali, Indonesia" },
    { value: "thailand", label: "Thailand" },
    { value: "singapore", label: "Singapore" },
    { value: "malaysia", label: "Malaysia" },
    { value: "dubai", label: "Dubai, UAE" },
    { value: "europe", label: "Europe" },
    { value: "usa", label: "United States" },
    { value: "australia", label: "Australia" },
  ];

  const travelTypeOptions = [
    { value: "leisure", label: "Leisure" },
    { value: "business", label: "Business" },
    { value: "adventure", label: "Adventure" },
    { value: "cultural", label: "Cultural" },
    { value: "honeymoon", label: "Honeymoon" },
    { value: "family", label: "Family" },
  ];

  const personOptions = [
    { value: "1", label: "1" },
    { value: "2", label: "2" },
    { value: "3", label: "3" },
    { value: "4", label: "4" },
    { value: "5", label: "5" },
    { value: "6", label: "6" },
    { value: "7", label: "7" },
    { value: "8", label: "8" },
    { value: "9", label: "9" },
    { value: "10", label: "10" },
  ];

  const comfortZoneOptions = [
    { value: "budget", label: "Budget" },
    { value: "economy", label: "Economy" },
    { value: "standard", label: "Standard" },
    { value: "premium", label: "Premium" },
    { value: "luxury", label: "Luxury" },
  ];

  const daysOptions = [
    { value: "1-3", label: "1-3 Days" },
    { value: "4-7", label: "4-7 Days" },
    { value: "8-14", label: "8-14 Days" },
    { value: "15-21", label: "15-21 Days" },
    { value: "22+", label: "22+ Days" },
  ];

  const categoryOptions = [
    { value: "beach", label: "Beach" },
    { value: "mountain", label: "Mountain" },
    { value: "city", label: "City" },
    { value: "wildlife", label: "Wildlife" },
    { value: "heritage", label: "Heritage" },
    { value: "adventure", label: "Adventure" },
    { value: "wellness", label: "Wellness" },
    { value: "food", label: "Food & Culture" },
  ];

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        position: "relative",
      }}
    >
      {/* Header Section */}
      <Box sx={{ mb: 3 }}>
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontStyle: "normal",
            fontSize: "16px",
            lineHeight: "20px",
            letterSpacing: "0.02em",
            textTransform: "capitalize",
            color: "#1E3A8A",
            mb: 1,
          }}
        >
          Start With Your Preferences – You're in Control
        </Typography>

        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 400,
            fontStyle: "normal",
            fontSize: "12px",
            lineHeight: "18px",
            letterSpacing: "0.02em",
            textTransform: "capitalize",
            color: "#333333",
          }}
        >
          From beach type to travel dates, set your preferences now and update
          them anytime.
        </Typography>
      </Box>

      {/* Form Section */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          flex: 1,
          paddingBottom: "20px",
        }}
      >
        {/* Destination Selection */}
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              lineHeight: "16px",
              color: "#333333",
            }}
          >
            Destination Selection
          </Typography>
          <CustomSelect
            label=""
            value={formData.destination}
            onChange={(e) => handleFormChange("destination", e.target.value)}
            options={destinationOptions}
            placeholder="Select destination"
            customMarginBottom="0px"
          />
        </Box>

        {/* Travel Type */}
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              lineHeight: "16px",
              color: "#333333",
            }}
          >
            Travel Type
          </Typography>
          <CustomSelect
            label=""
            value={formData.travelType}
            onChange={(e) => handleFormChange("travelType", e.target.value)}
            options={travelTypeOptions}
            placeholder="Select travel type"
            customMarginBottom="0px"
          />
        </Box>

        {/* Number of Persons Travelling */}
        <Box>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              color: "#333333",
              mb: 1,
            }}
          >
            No of Person Traveling
          </Typography>
          <Box
            sx={{
              display: "flex",
              gap: 2,
              alignItems: "center",
            }}
          >
            <Box sx={{ flex: 1 }}>
              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "12px",
                  lineHeight: "14px",
                  color: "#333333",
                  mb: 1,
                }}
              >
                Adults
              </Typography>
              <CustomSelect
                label=""
                value={formData.adults}
                onChange={(e) => handleFormChange("adults", e.target.value)}
                options={personOptions}
                placeholder="0"
                customMarginBottom="0px"
              />
            </Box>
            <Box sx={{ flex: 1 }}>
              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "12px",
                  lineHeight: "14px",
                  color: "#333333",
                  mb: 1,
                }}
              >
                Children
              </Typography>
              <CustomSelect
                label=""
                value={formData.children}
                onChange={(e) => handleFormChange("children", e.target.value)}
                options={personOptions}
                placeholder="0"
                customMarginBottom="0px"
              />
            </Box>
            <Box sx={{ flex: 1 }}>
              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "12px",
                  lineHeight: "14px",
                  color: "#333333",
                  mb: 1,
                }}
              >
                Infants
              </Typography>
              <CustomSelect
                label=""
                value={formData.infants}
                onChange={(e) => handleFormChange("infants", e.target.value)}
                options={personOptions}
                placeholder="0"
                customMarginBottom="0px"
              />
            </Box>
          </Box>
        </Box>

        {/* Comfort Zone */}
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              color: "#333333",
            }}
          >
            Comfort Zone
          </Typography>
          <CustomSelect
            label=""
            value={formData.comfortZone}
            onChange={(e) => handleFormChange("comfortZone", e.target.value)}
            options={comfortZoneOptions}
            placeholder="Select comfort level"
            customMarginBottom="0px"
          />
        </Box>

        {/* Number of Days */}
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              color: "#333333",
            }}
          >
            No of Days
          </Typography>
          <CustomSelect
            label=""
            value={formData.numberOfDays}
            onChange={(e) => handleFormChange("numberOfDays", e.target.value)}
            options={daysOptions}
            placeholder="Select duration"
            customMarginBottom="0px"
          />
        </Box>

        {/* Pick a Category */}
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              color: "#333333",
            }}
          >
            Pick a Category
          </Typography>
          <CustomSelect
            label=""
            value={formData.category}
            onChange={(e) => handleFormChange("category", e.target.value)}
            options={categoryOptions}
            placeholder="Select category"
            customMarginBottom="0px"
          />
        </Box>
      </Box>

      {/* footer */}
      <Box
        sx={{
          position: "sticky",
          bottom: "-20px",
          background: "#FFF9F9",
          width: "100%",
          p: "20px 0px",
          boxSizing: "border-box",
        }}
      >
        <PrimaryButton width="100%">Update</PrimaryButton>
      </Box>
    </Box>
  );
};
