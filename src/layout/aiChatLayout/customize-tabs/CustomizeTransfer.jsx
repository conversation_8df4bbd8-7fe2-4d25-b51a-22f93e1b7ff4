import { Box } from "@mui/material";

export const CustomizeTransfer = () => {
  const itineraryData =
    "<h3>Day 1 - Arrive in Geneva</h3><ul><li>Transfer to hotel and check-in.</li></ul><h3>Day 2 - Glacier 3000 & Gstaad</h3><ul><li>Visit Glacier 3000 via cable car, enjoy snow activities like Peak Walk and Dog Sledding.</li><li>Explore the town of Gstaad, a celebrity-favourite Swiss town.</li></ul><h3>Day 3 - Lavaux Vineyards & Montreux</h3><ul><li>Tour Lavaux Vineyards, a UNESCO World Heritage Site.</li><li>Stop at Montreux and see Château de Chillon (outside).</li></ul><h3>Day 4 - Zurich via Bern</h3><ul><li>Drive to Zurich via Bern, with photo stops at the Clock Tower and Einstein's house.</li><li>Evening free in Zurich.</li></ul><h3>Day 5 - Mt. Titlis & Lucerne</h3><ul><li>Visit Mt. Titlis with Rotair cable car and Cliff Walk.</li><li>Tour Lucerne's Lion Monument and Chapel Bridge.</li></ul><h3>Day 6 - Jungfraujoch (Optional)</h3><ul><li>Optional excursion to Jungfraujoch – Top of Europe.</li><li>Visit Ice Palace and Sphinx Observatory.</li></ul><h3>Day 7 - Rhine Falls & Paris</h3><ul><li>Visit Rhine Falls in Schaffhausen.</li><li>Drive to Paris, evening at leisure.</li></ul><h3>Day 8 - Paris City Tour</h3><ul><li>Guided tour of Paris, Eiffel Tower (2nd level), Seine River Cruise.</li></ul><h3>Day 9 - Disneyland Paris</h3><ul><li>Full-day at Disneyland Park or Walt Disney Studios Park.</li></ul><h3>Day 10 - Departure</h3><ul><li>Free time, transfer to airport for departure.</li></ul>";
  return (
    <Box>
      <Box
        sx={{
          fontFamily: "Roboto",
          fontSize: 12,
          fontWeight: 400,
          lineHeight: "18px",
          color: "#333333",
          "& h3, & h4, & h5, & h6": {
            fontSize: 12,
            fontFamily: "Roboto",
            fontWeight: 500,
            color: "#000000",
            mb: 1,
            mt: 1,
          },
          "& ul, & ol": {
            fontSize: 12,
            fontFamily: "Roboto",
            fontWeight: 400,
            color: "#000000",
            mb: 1,
            mt: 1,
          },
        }}
        dangerouslySetInnerHTML={{ __html: itineraryData }}
      />
    </Box>
  );
};
