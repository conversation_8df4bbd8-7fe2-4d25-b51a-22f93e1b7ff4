import { Box, Stack, Typography } from "@mui/material";
import { useMemo } from "react";
import styled from "styled-components";

const highlightsIconUrl = "/static/package/highlight";

const highlightsIcons = [
  "island",
  "beach",
  "ship",
  "passenger",
  "breakfast",
  "taxi",
  "bus",
  "hotel",
  "flight",
  "visa",
  "forex_card",
  "insurance",
  "common",
];

const getHighlightIconUrl = (icon) => {
  let url = "";
  if (highlightsIcons.includes(icon)) {
    url = `${highlightsIconUrl}/${icon}.png`;
  } else {
    url = `${highlightsIconUrl}/common.png`;
  }
  return url;
};

const Separator = styled(Box)(({ theme }) => ({
  width: "100%",
  height: "2px",
  background: "#333333",
  opacity: 0.05,
  marginTop: "16px",
  marginBottom: "16px",
}));

export const OverviewTab = () => {
  const highlightsData = [
    {
      icon_class: "island",
      value:
        "Dune bashing and sandboarding in Dubai's desert with a BBQ dinner.",
    },
    {
      icon_class: "beach",
      value: "Skydiving over Palm Jumeirah",
    },
    {
      icon_class: "ship",
      value: "XLine zip-lining at Dubai Marina",
    },
    {
      icon_class: "passenger",
      value: "Visit Burj Khalifa (124th floor)",
    },
    {
      icon_class: "breakfast",
      value:
        "City tour covering Jumeirah Mosque, Atlantis, Dubai Frame, and Dubai Mall Fountain Show",
    },
    {
      icon_class: "taxi",
      value: "Ferrari World",
    },
    {
      icon_class: "bus",
      value: "Sheikh Zayed Grand Mosque",
    },
    {
      icon_class: "hotel",
      value: "Skydiving over Palm Jumeirah with panoramic views of Dubai.",
    },
    {
      icon_class: "flight",
      value: "Visit Sheikh Zayed Grand Mosque",
    },
    {
      icon_class: "visa",
      value: "Visit Sheikh Zayed Grand Mosque",
    },
    {
      icon_class: "forex_card",
      value:
        "Skydiving over Palm Jumeirah with panoramic views of Dubai. Visit Sheikh Zayed Grand Mosque",
    },
    {
      icon_class: "insurance",
      value: "Visit Sheikh Zayed Grand Mosque",
    },
  ];

  return (
    <Box>
      <Box mb={1.5}>
        <Box
          component="img"
          src={
            "https://images.pexels.com/photos/32505992/pexels-photo-32505992.jpeg"
          }
          sx={{
            width: "100%",
            height: "146px",
            objectFit: "cover",
            borderRadius: "12px",
          }}
        />
      </Box>
      <Box
        mb={1.5}
        sx={{
          fontFamily: "Roboto",
          fontSize: 12,
          fontWeight: 400,
          lineHeight: "18px",
          color: "#333333",
        }}
      >
        Unleash your adventurous spirit with the "Extreme UAE 4N/5D Tour
        Package," a 4-night, 5-day journey through the UAE's most exhilarating
        experiences. From the towering dunes of Dubai's desert to the urban
        thrills of its skyscrapers and the cultural wonders of Abu Dhabi, this
        package is tailored for thrill-seekers. Expect heart-pounding activities
        like dune bashing, skydiving over Palm Jumeirah, and the world's longest
        urban zip-line, paired with visits to iconic landmarks like Burj Khalifa
        and Ferrari World. Perfect for adrenaline junkies and explorers, this
        tour delivers extreme fun with premium comfort and seamless logistics.
      </Box>
      <Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontSize: 12,
            fontWeight: 500,
            lineHeight: "14px",
            color: "#333333",
            mb: 1,
          }}
        >
          Tour Highlights
        </Box>
        <Stack direction="column" spacing={"6px"}>
          {highlightsData?.map((item, index) => (
            <Box key={index + "highlights"} sx={{ display: "flex", gap: 0.5 }}>
              <img
                src={getHighlightIconUrl(item.icon_class)}
                alt="A"
                style={{ width: "16px", height: "16px" }}
              />
              <Box
                sx={{
                  fontSize: 12,
                  fontWeight: 400,
                  lineHeight: "14px",
                  color: "#333333",
                }}
              >
                {item.value}
              </Box>
            </Box>
          ))}
        </Stack>
      </Box>
    </Box>
  );
};

export const InclusionsTab = () => {
  const inclusionsData = [
    {
      icon_class: "island",
      value:
        "Dune bashing and sandboarding in Dubai's desert with a BBQ dinner.",
    },
    {
      icon_class: "beach",
      value: "Skydiving over Palm Jumeirah",
    },
    {
      icon_class: "ship",
      value: "XLine zip-lining at Dubai Marina",
    },
    {
      icon_class: "passenger",
      value: "Visit Burj Khalifa (124th floor)",
    },
    {
      icon_class: "breakfast",
      value:
        "City tour covering Jumeirah Mosque, Atlantis, Dubai Frame, and Dubai Mall Fountain Show",
    },
    {
      icon_class: "taxi",
      value: "Ferrari World",
    },
    {
      icon_class: "bus",
      value: "Sheikh Zayed Grand Mosque",
    },
    {
      icon_class: "hotel",
      value: "Skydiving over Palm Jumeirah with panoramic views of Dubai.",
    },
    {
      icon_class: "flight",
      value: "Visit Sheikh Zayed Grand Mosque",
    },
    {
      icon_class: "visa",
      value: "Visit Sheikh Zayed Grand Mosque",
    },
    {
      icon_class: "forex_card",
      value:
        "Skydiving over Palm Jumeirah with panoramic views of Dubai. Visit Sheikh Zayed Grand Mosque",
    },
    {
      icon_class: "insurance",
      value: "Visit Sheikh Zayed Grand Mosque",
    },
  ];

  const exclusionsData = [
    {
      icon_class: "island",
      value:
        "Dune bashing and sandboarding in Dubai's desert with a BBQ dinner.",
    },
    {
      icon_class: "beach",
      value: "Skydiving over Palm Jumeirah",
    },
    {
      icon_class: "ship",
      value: "XLine zip-lining at Dubai Marina",
    },
    {
      icon_class: "passenger",
      value: "Visit Burj Khalifa (124th floor)",
    },
    {
      icon_class: "breakfast",
      value:
        "City tour covering Jumeirah Mosque, Atlantis, Dubai Frame, and Dubai Mall Fountain Show",
    },
    {
      icon_class: "taxi",
      value: "Ferrari World",
    },
    {
      icon_class: "bus",
      value: "Sheikh Zayed Grand Mosque",
    },
    {
      icon_class: "hotel",
      value: "Skydiving over Palm Jumeirah with panoramic views of Dubai.",
    },
    {
      icon_class: "flight",
      value: "Visit Sheikh Zayed Grand Mosque",
    },
    {
      icon_class: "visa",
      value: "Visit Sheikh Zayed Grand Mosque",
    },
    {
      icon_class: "forex_card",
      value:
        "Skydiving over Palm Jumeirah with panoramic views of Dubai. Visit Sheikh Zayed Grand Mosque",
    },
    {
      icon_class: "insurance",
      value: "Visit Sheikh Zayed Grand Mosque",
    },
  ];
  return (
    <Box>
      <Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontSize: 12,
            fontWeight: 500,
            lineHeight: "14px",
            color: "#333333",
            mb: 1,
          }}
        >
          Inclusions
        </Box>
        <Stack direction="column" spacing={"6px"}>
          {inclusionsData?.map((item, index) => (
            <Box key={index + "highlights"} sx={{ display: "flex", gap: 0.5 }}>
              <img
                src={getHighlightIconUrl(item.icon_class)}
                alt="A"
                style={{ width: "16px", height: "16px" }}
              />
              <Box
                sx={{
                  fontSize: 12,
                  fontWeight: 400,
                  lineHeight: "14px",
                  color: "#333333",
                }}
              >
                {item.value}
              </Box>
            </Box>
          ))}
        </Stack>
      </Box>
      <Box mt={1.5}>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontSize: 12,
            fontWeight: 500,
            lineHeight: "14px",
            color: "#333333",
            mb: 1,
          }}
        >
          Exclusions
        </Box>
        <Stack direction="column" spacing={"6px"}>
          {exclusionsData?.map((item, index) => (
            <Box key={index + "highlights"} sx={{ display: "flex", gap: 0.5 }}>
              <img
                src={"/static/common/close.png"}
                alt="exclusions"
                style={{ width: "14px", height: "14px" }}
              />
              <Box
                sx={{
                  fontSize: 12,
                  fontWeight: 400,
                  lineHeight: "14px",
                  color: "#333333",
                }}
              >
                {item.value}
              </Box>
            </Box>
          ))}
        </Stack>
      </Box>
    </Box>
  );
};

export const ItineraryTab = () => {
  const itineraryData =
    "<h3>Day 1 - Arrive in Geneva</h3><ul><li>Transfer to hotel and check-in.</li></ul><h3>Day 2 - Glacier 3000 & Gstaad</h3><ul><li>Visit Glacier 3000 via cable car, enjoy snow activities like Peak Walk and Dog Sledding.</li><li>Explore the town of Gstaad, a celebrity-favourite Swiss town.</li></ul><h3>Day 3 - Lavaux Vineyards & Montreux</h3><ul><li>Tour Lavaux Vineyards, a UNESCO World Heritage Site.</li><li>Stop at Montreux and see Château de Chillon (outside).</li></ul><h3>Day 4 - Zurich via Bern</h3><ul><li>Drive to Zurich via Bern, with photo stops at the Clock Tower and Einstein's house.</li><li>Evening free in Zurich.</li></ul><h3>Day 5 - Mt. Titlis & Lucerne</h3><ul><li>Visit Mt. Titlis with Rotair cable car and Cliff Walk.</li><li>Tour Lucerne's Lion Monument and Chapel Bridge.</li></ul><h3>Day 6 - Jungfraujoch (Optional)</h3><ul><li>Optional excursion to Jungfraujoch – Top of Europe.</li><li>Visit Ice Palace and Sphinx Observatory.</li></ul><h3>Day 7 - Rhine Falls & Paris</h3><ul><li>Visit Rhine Falls in Schaffhausen.</li><li>Drive to Paris, evening at leisure.</li></ul><h3>Day 8 - Paris City Tour</h3><ul><li>Guided tour of Paris, Eiffel Tower (2nd level), Seine River Cruise.</li></ul><h3>Day 9 - Disneyland Paris</h3><ul><li>Full-day at Disneyland Park or Walt Disney Studios Park.</li></ul><h3>Day 10 - Departure</h3><ul><li>Free time, transfer to airport for departure.</li></ul>";
  return (
    <Box>
      <Box
        sx={{
          fontFamily: "Roboto",
          fontSize: 12,
          fontWeight: 400,
          lineHeight: "18px",
          color: "#333333",
          "& h3, & h4, & h5, & h6": {
            fontSize: 12,
            fontFamily: "Roboto",
            fontWeight: 500,
            color: "#000000",
            mb: 1,
            mt: 1,
          },
          "& ul, & ol": {
            fontSize: 12,
            fontFamily: "Roboto",
            fontWeight: 400,
            color: "#000000",
            mb: 1,
            mt: 1,
          },
        }}
        dangerouslySetInnerHTML={{ __html: itineraryData }}
      />
    </Box>
  );
};

export const HotelsTab = () => {
  const hotelsData = [
    "4 star Hotel",
    "Mövenpick Hotel & Casino Geneva or Similar",
    "Sihlpark Hotel and Spa or Similar",
    "The Atrium Hotel & Conference Centre Paris CDG Airport, by Penta or Similar",
  ];

  return (
    <Box
      sx={{
        width: "100%",
      }}
    >
      <ul style={{ paddingLeft: "0px", listStyle: "none" }}>
        {hotelsData.map((hotel, index) => (
          <li key={index + "hotel"}>
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 500,
                fontSize: 12,
                lineHeight: "18px",
                color: "#333333",
              }}
            >
              {hotel}
            </Typography>
            <Separator />
          </li>
        ))}
      </ul>
    </Box>
  );
};

export const AddOnsTab = () => {
  const addOnsData = [
    {
      icon_class: "island",
      value: "Island Tour",
    },
    {
      icon_class: "beach",
      value: "Beach Tour",
    },
    {
      icon_class: "ship",
      value: "City Tour",
    },
    {
      icon_class: "passenger",
      value: "Dune Bashing",
    },
    {
      icon_class: "breakfast",
      value: "Sandboarding",
    },
    {
      icon_class: "taxi",
      value: "Skydiving",
    },
    {
      icon_class: "bus",
      value: "XLine Zip-lining",
    },
    {
      icon_class: "hotel",
      value: "Ferrari World",
    },
    {
      icon_class: "flight",
      value: "Burj Khalifa",
    },
    {
      icon_class: "visa",
      value: "Ferrari World",
    },
    {
      icon_class: "forex_card",
      value: "Grand Mosque",
    },
    {
      icon_class: "insurance",
      value: "Ferrari World",
    },
  ];

  return (
    <Box
      sx={{
        width: "100%",
        height: "fit-content",
        display: "flex",
        gap: 2,
        flexWrap: "wrap",
      }}
    >
      {addOnsData.map((item, index) => (
        <Box
          key={index + "add-on"}
          sx={{
            display: "flex",
            height: "fit-content",
            gap: "6px",
            alignItems: "center",
            borderRadius: "8px",
            background: "#FFF9F9",
            padding: 1,
          }}
        >
          <img
            src={getHighlightIconUrl(item.icon_class)}
            alt="add-on"
            style={{ width: "24px", height: "24px" }}
          />
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 400,
              fontSize: 12,
              lineHeight: "14px",
              color: "#333333",
              textTransform: "capitalize",
            }}
          >
            {item.value}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

export const ThreeSixtyViewTab = () => {
  return (
    <Box>
      <Box
        component="video"
        src="https://videos.pexels.com/video-files/8996454/8996454-uhd_2560_1440_24fps.mp4"
        autoPlay
        controls
        loop
        muted
        playsInline
        sx={{
          width: "100%",
          height: "267px",
          objectFit: "cover",
          borderRadius: "8px",
        }}
      />
    </Box>
  );
};
export const PolicyTab = () => {
  return (
    <Box width="100%">
      <Box>
        <Box>
          {/* Terms & Conditions */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 500,
                fontFamily: "Roboto",
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                mb: 2,
              }}
            >
              Zuumm – Terms & Conditions & Important Information
            </Typography>
            <ol
              style={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                paddingLeft: "20px",
                marginTop: "0px",
              }}
            >
              <li>
                The package price is calculated per person, based on a minimum
                of two travelers.
              </li>
              <li>
                Visa Assistance: Zuumm supports with visa applications, but
                final approval is at the sole discretion of the
                embassy/consulate.
              </li>
              <li>
                Passport Validity: Ensure your passport is valid for at least 6
                months from your return date.
              </li>
              <li>
                Flight Schedule: Always check flight status 24 hours before
                departure as schedules can change without notice.
              </li>
              <li>
                Correct Names: Names on bookings must exactly match the travel
                documents (passport, etc.).
              </li>
              <li>
                Airport Transfers: After baggage claim, head to the assigned
                exit and meet our representative within 60 minutes of landing.
              </li>
              <li>
                Hotel Check-In: Standard check-in is 2 PM, check-out is 11 AM.
                Early/late access is subject to hotel availability and may be
                chargeable.
              </li>
              <li>
                Hotel Deposits: Some hotels may ask for a refundable security
                deposit at check-in.
              </li>
              <li>
                Package Exclusions:
                <ul style={{ paddingLeft: "20px", marginTop: "8px" }}>
                  <li>Visa fee is not included.</li>
                  <li>
                    Gala dinner charges (e.g., Christmas/New Year) are not
                    included.
                  </li>
                  <li>
                    Personal expenses like room service, laundry, or mini-bar
                    are excluded.
                  </li>
                </ul>
              </li>
              <li>
                Customizations & Vouchers: Packages can be customized. Final
                itinerary and vouchers will be shared 72 hours prior to
                departure after full payment.
              </li>
              <li>
                Natural Disruptions: In case of delays or cancellations due to
                weather or other unavoidable issues, refunds will be processed
                as per vendor policies.
              </li>
              <li>
                Low-Cost Flights: For low-cost airlines, baggage is not included
                unless added separately during booking. So ensure baggage policy
                before confirming the flights.
              </li>
              <li>
                Hotel Substitution: If the selected hotel is unavailable, a
                similar category hotel will be provided.
              </li>
              <li>
                Punctuality: Be on time for activities and transfers. Delays may
                result in missed services without refund.
              </li>
              <li>
                Special Event Charges: Additional charges may apply at hotels
                during festivals/peak dates. Zuumm will inform you wherever
                possible.
              </li>
              <li>
                Activity Refunds: Paid activities that are cancelled will be
                refunded within 30 days. Complimentary activities are
                non-refundable.
              </li>
              <li>
                Itinerary Changes: Zuumm may adjust the itinerary due to flight
                delays, closures, weather, or other disruptions.
              </li>
            </ol>
          </Box>

          {/* Cancellation Policy */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 500,
                fontFamily: "Roboto",
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                mb: 2,
              }}
            >
              Zuumm Holiday Packages – Cancellation Policy
            </Typography>
            <ol
              style={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                paddingLeft: "20px",
                marginTop: "0px",
              }}
            >
              <li>
                Without Flights: Cancellations allowed only if made 30+ days
                before the travel date.
              </li>
              <li>
                Less than 30 Days: Bookings made less than 30 days before travel
                are non-refundable.
              </li>
              <li>
                With Flights: Cancellations/reschedules follow the airline's
                policy.
              </li>
              <li>
                Land Services: Hotels, transfers, and activities follow Zuumm's
                standard cancellation policy.
              </li>
              <li>
                TCS (Tax Collected at Source) is non-refundable, but can be
                claimed when filing your income tax return.
              </li>
              <li>
                A 5% GST will be charged on the land package value, excluding
                any flight costs.
              </li>
            </ol>
          </Box>

          {/* TCS Conditions */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 500,
                fontFamily: "Roboto",
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                mb: 2,
              }}
            >
              TCS Conditions
            </Typography>
            <ol
              style={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                paddingLeft: "20px",
                marginTop: "0px",
              }}
            >
              <li>
                If you haven't filed ITRs for the past 2 years and your TDS/TCS
                exceeds ₹50,000, TCS will be charged at 10% on international
                bookings.
              </li>
              <li>
                If PAN validation fails due to non-filing, additional TCS @5%
                will apply, or the booking may be cancelled as per policy.
              </li>
              <li>PAN card is mandatory.</li>
            </ol>
          </Box>

          {/* Country-Specific Tourism Fees */}
          <Box sx={{ mb: 1 }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 500,
                fontFamily: "Roboto",
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                mb: 2,
              }}
            >
              Country-Specific Tourism Fees and Policies
            </Typography>
            <Box
              sx={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                marginTop: "0px",
                "& h4": {
                  m: "8px 0px 4px 0px",
                  fontWeight: 500,
                  fontSize: "12px",
                  lineHeight: "18px",
                },
                "& ul": {
                  marginTop: "0px",
                },
              }}
            >
              <h4>United Arab Emirates (Dubai)</h4>
              <ul>
                <li>
                  Tourism Dirham fee applies per night, based on hotel category.
                </li>
                <li>Fee ranges from AED 7 to AED 20 per room per night.</li>
              </ul>

              <h4>Indonesia (Bali)</h4>
              <ul>
                <li>
                  A tourist levy of IDR 150,000 (~USD 10) is applicable per
                  person.
                </li>
                <li>
                  Payable online via the LoveBali app/website or at the airport.
                </li>
              </ul>

              <h4>Sri Lanka</h4>
              <ul>
                <li>No national tourism tax.</li>
                <li>Standard visa fees apply.</li>
                <li>
                  Some hotels may charge service fees or additional taxes
                  locally.
                </li>
              </ul>

              <h4>Maldives</h4>
              <ul>
                <li>
                  A "Green Tax" of USD 6 per person per night is charged at
                  resorts, hotels, and guesthouses.
                </li>
                <li>
                  Seaplane or speedboat transfers may have additional costs and
                  taxes.
                </li>
              </ul>

              <h4>Nepal</h4>
              <ul>
                <li>No tourism tax at national level.</li>
                <li>
                  Trekking permits and national park entry fees apply in
                  specific regions.
                </li>
              </ul>

              <h4>United States</h4>
              <ul>
                <li>
                  Some cities/states impose "occupancy tax" or "hotel tax".
                </li>
                <li>
                  Hotels may charge daily resort fees (common in Las Vegas,
                  Florida, Hawaii).
                </li>
                <li>
                  Hawaii plans to introduce a 0.75% climate tax on accommodation
                  charges from 2026.
                </li>
              </ul>

              <h4>Thailand</h4>
              <ul>
                <li>
                  Proposed tourism fee of THB 300 (~USD 8) for air arrivals
                  (pending implementation).
                </li>
                <li>No tourism tax currently in place as of 2025.</li>
              </ul>

              <h4>Japan</h4>
              <ul>
                <li>
                  "Sayonara Tax" of JPY 1,000 applies when departing Japan.
                </li>
                <li>
                  Some cities like Tokyo and Osaka charge accommodation taxes
                  ranging from JPY 100 to JPY 300 per person per night.
                </li>
              </ul>

              <h4>Switzerland</h4>
              <ul>
                <li>
                  Most cities apply a tourist tax, usually CHF 2–4 per person
                  per night.
                </li>
                <li>
                  The fee is usually collected by the hotel at check-in or
                  check-out.
                </li>
              </ul>

              <h4>Europe (General)</h4>
              <ul>
                <li>
                  Most European countries/cities charge a tourist or city tax:
                </li>
                <li>Italy (Rome, Venice): €1–€5 per person per night.</li>
                <li>France (Paris): €0.80–€4.00 per night.</li>
                <li>Netherlands (Amsterdam): 7%–12.5% of room cost.</li>
                <li>Spain (Barcelona): €0.75–€3.50 per night.</li>
              </ul>

              <h4>Turkey</h4>
              <ul>
                <li>Accommodation tax of 2% is charged on the room rate.</li>
              </ul>

              <h4>Singapore</h4>
              <ul>
                <li>
                  No tourism tax, but 8% GST (Goods and Services Tax) applies on
                  hotel and service bills.
                </li>
              </ul>

              <h4>Vietnam</h4>
              <ul>
                <li>No specific tourism tax.</li>
                <li>8% VAT is typically added to hotel and service bills.</li>
              </ul>

              <h4>Finland</h4>
              <ul>
                <li>No national tourism tax.</li>
                <li>Some cities may charge a nominal tourist fee.</li>
              </ul>

              <h4>New Zealand</h4>
              <ul>
                <li>
                  International Visitor Conservation and Tourism Levy (IVL) of
                  NZD 100 (~USD 62) is charged at the time of applying for a
                  visa or NZeTA.
                </li>
              </ul>

              <h4>South America (varies by country)</h4>
              <ul>
                <li>
                  Argentina, Peru, Chile, and Brazil may charge hotel taxes.
                </li>
                <li>
                  Foreigners in some countries are exempt if paying in foreign
                  currency and showing a passport.
                </li>
              </ul>

              <h4>Hong Kong</h4>
              <ul>
                <li>No tourism or hotel tax currently imposed.</li>
              </ul>

              <h4>Azerbaijan</h4>
              <ul>
                <li>
                  A tourist tax of AZN 1.30 per person per night is charged at
                  most accommodations.
                </li>
              </ul>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export const MeetYourDestinationTab = ({
  itineraryData = {
    best_time_to_visit: "April to June & September to October",
    destination_safety:
      "Paris is generally safe for tourists, especially in well-known areas. However, beware of pickpockets in crowded places like the metro and near major attractions.",
    cultural_info:
      'Parisians appreciate politeness—always greet with "Bonjour" and say "Merci." French cuisine and café culture are central to daily life. Tipping is appreciated but not mandatory. Many locals speak some English, but basic French phrases are welcomed.',
    what_to_shop:
      "Fashion items, perfumes, luxury brands (Chanel, Louis Vuitton), French wine, artisan cheese, and souvenirs like Eiffel Tower miniatures or macarons.",
    what_to_pack:
      "<h3>Clothing</h3><ul><li>Comfortable walking shoes, layers (a light jacket even in summer), an umbrella, travel adapter (type C/E plug), and stylish outfits for evenings out.</li></ul><h3>Health & Safety</h3><ul><li>Sunscreen (SPF 50+)</li><li>Sunglasses</li><li>Hat</li><li>Travel insurance documents</li></ul><h3>Essentials</h3><ul><li>Power bank</li><li>Reusable water bottle</li><li>Small backpack for daily outings</li></ul>",
  },
}) => {
  const mappedDestinationData = useMemo(() => {
    const data = [];

    if (itineraryData?.best_time_to_visit) {
      data.push({
        type: "best_time_to_visit",
        name: "Best time to visit",
        description: itineraryData?.best_time_to_visit,
      });
    }

    if (itineraryData?.destination_safety) {
      data.push({
        type: "destination_safety",
        name: "Destination Safety",
        description: itineraryData?.destination_safety,
      });
    }

    if (itineraryData?.cultural_info) {
      data.push({
        type: "cultural_info",
        name: "Cultural Information",
        description: itineraryData?.cultural_info,
      });
    }

    if (itineraryData?.what_to_shop) {
      data.push({
        type: "what_to_shop",
        name: "What To Shop",
        description: itineraryData?.what_to_shop,
      });
    }

    if (itineraryData?.what_to_pack) {
      data.push({
        type: "what_to_pack",
        name: "What To Pack",
        description: itineraryData?.what_to_pack,
      });
    }

    return data;
  }, [itineraryData]);
  return (
    <Box
      sx={{
        width: "100%",
      }}
    >
      {mappedDestinationData.map((destination, index) => (
        <Box key={index}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              mb: 1,
            }}
          >
            <Box
              sx={{
                backgroundColor: "#FF3951",
                width: 3,
                height: "20px",
                borderRadius: "2px",
              }}
            />
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 500,
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
              }}
            >
              {destination.name}
            </Typography>
          </Box>
          <Box>
            {destination.type === "what_to_pack" ? (
              <Box
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "12px",
                  lineHeight: "18px",
                  color: "#000000",
                  "& h3, & h4, & h5, & h6": {
                    fontFamily: "Roboto",
                    fontWeight: 500,
                    fontSize: "14px",
                    lineHeight: "18px",
                    color: "#000000",
                    mb: 1,
                  },
                  "& p": {
                    mb: 1,
                  },
                  "& ul, & ol": {
                    mb: 1,
                    pl: 3,
                  },
                  "& li": {
                    mb: 0.5,
                  },
                }}
                dangerouslySetInnerHTML={{ __html: destination.description }}
              />
            ) : (
              <Box>
                <Typography
                  sx={{
                    fontFamily: "Roboto",
                    fontWeight: 400,
                    fontSize: "12px",
                    lineHeight: "18px",
                    whiteSpace: "pre-line",
                  }}
                >
                  {destination.description}
                </Typography>
              </Box>
            )}
          </Box>
          <Separator />
        </Box>
      ))}
    </Box>
  );
};