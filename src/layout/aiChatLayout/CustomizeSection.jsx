import React, { useState } from "react";
import { CloseOutline } from "../../components/common/SvgIcon";

import { Box, Typography } from "@mui/material";
import { CustomizePackage } from "./customize-tabs/CustomizePackage";
import { CustomizeFlight } from "./customize-tabs/CustomizeFlight";
import { CustomizeHotel } from "./customize-tabs/CustomizeHotel";
import { CustomizeTransfer } from "./customize-tabs/CustomizeTransfer";

const CustomizeSection = ({
  packageWidth,
  setIsPackageOpen,
  isMidScreen = false,
}) => {
  const tabs = ["Packages", "Flights", "Hotels", "Transfer"];

  const [activeTab, setActiveTab] = useState(tabs[0]);
  const handleTabClick = (tab) => {
    setActiveTab(tab);
  };

  const renderTabContent = (tab) => {
    switch (tab) {
      case "Packages":
        return <CustomizePackage />;
      case "Flights":
        return <CustomizeFlight />;
      case "Hotels":
        return <CustomizeHotel />;
      case "Transfer":
        return <CustomizeTransfer />;

      default:
        return null;
    }
  };
  return (
    <Box
      sx={{
        width: packageWidth,
        height: "100%",
        ml: !isMidScreen ? "0px" : "243px",
        zIndex: 1001,
        boxShadow: "0px 4px 7px 0px #00000008",
        display: "flex",
        flexDirection: "column",
        background: "#FFF9F9",
      }}
    >
      <Box
        sx={{
          width: "100%",
          background: "#F9F9F9",
          p: "14px 18px",
          boxSizing: "border-box",
          display: "flex",
          justifyContent: "space-between",
          gap: 2,
        }}
      >
        <Typography
          sx={{
            fontSize: 16,
            fontWeight: 500,
            lineHeight: "18px",
            color: "#1E3A8A",
            maxWidth: "calc(100% - 44px)",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          Curate Your Perfect Trip
        </Typography>
        <CloseOutline onClick={() => setIsPackageOpen(false)} />
      </Box>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          width: "100%",
          flex: 1,
          overflow: "hidden",
          maxHeight: "100%",
          // p: "16px",
          boxSizing: "border-box",
        }}
      >
        {/* tabs */}
        <Box
          sx={{
            display: "inline-flex",
            width: "100%",
            background: "#FFFFFF",
            boxSizing: "border-box",
            padding: "12px 22px",
            flexWrap: "nowrap",
            overflowX: "auto",
            scrollbarWidth: "none",
            "&::-webkit-scrollbar": {
              display: "none",
            },
            gap: 1,
            alignItems: "center",
            borderRadius: "0px 0px 12px 12px",
            // my: "16px",
          }}
        >
          {tabs.map((item) => (
            <Box
              key={item}
              sx={{
                display: "inline-flex",
                whiteSpace: "nowrap",
                alignItems: "center",
                width: "max-content",
                padding: "8px 12px",
                borderRadius: "8px",
                background: activeTab === item ? "#FFF9F9" : "transparent",
                fontSize: 14,
                fontWeight: 500,
                lineHeight: "16px",
                fontFamily: "Roboto",
                color: activeTab === item ? "#1E3A8A" : "#706E75",
                cursor: "pointer",
                transition: "all 0.3s ease",
                "&:hover": {
                  background: "#FFF9F9",
                },
              }}
              onClick={() => handleTabClick(item)}
            >
              {item}
            </Box>
          ))}
        </Box>
        {/* content */}
        <Box
          sx={{
            display: "flex",
            width: "100%",
            flex: 1,
            overflowY: "auto",
            p: "24px 22px",
            boxSizing: "border-box",
          }}
        >
          {renderTabContent(activeTab)}
        </Box>
      </Box>
    </Box>
  );
};

export default CustomizeSection;
