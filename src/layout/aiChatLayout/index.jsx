import React from "react";
import { Box, IconButton, useMediaQuery } from "@mui/material";
import AiChatHeader from "../../components/ai-chatbot/AiChatHeader";
import AiChatSidebar from "../../components/ai-chatbot/AiChatSidebar";
import SuggestedPackages from "./SuggestedPackages";
import { Customize } from "../../components/common/SvgIcon";
import CustomizeSection from "./CustomizeSection";

const AiChatLayout = ({
  children,
  isPackageOpen = false,
  setIsPackageOpen = () => {},
  isCustomizeOpen = false,
  setIsCustomizeOpen = () => {},
}) => {
  const isMidScreen = useMediaQuery("(max-width: 1350px)");
  const packageWidth = isPackageOpen
    ? isMidScreen
      ? "calc(100% - 243px)"
      : "477px"
    : "0px";

  const CustomizeWidth = isCustomizeOpen
    ? isMidScreen
      ? "calc(100% - 243px)"
      : "477px"
    : "0px";

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100dvh",
        backgroundColor: "#FFFFFF",
        boxSizing: "border-box",
      }}
    >
      <AiChatHeader />
      <Box
        sx={{
          display: "flex",
          height: "100dvh",
          pt: "56px",
          boxSizing: "border-box",
        }}
      >
        <AiChatSidebar />
        {(isMidScreen && !isPackageOpen) ||
        (isMidScreen && !isCustomizeOpen) ||
        !isMidScreen ? (
          <Box
            component="main"
            sx={{
              display: "flex",
              flexDirection: "column",
              width: `calc(100% - ${
                isPackageOpen
                  ? packageWidth
                  : isCustomizeOpen
                    ? CustomizeWidth
                    : "0px"
              } - 243px)`,
              transition: "width 0.3s ease-in-out",
              height: "100%",
              ml: "243px",
              // mr: isPackageOpen ? "450px" : "0px",
              overflow: "hidden",
              boxSizing: "border-box",
              px: 2,
              py: 3,
            }}
          >
            {children}
            {!isPackageOpen && !isCustomizeOpen && (
              <IconButton
                sx={{
                  position: "absolute",
                  top: "67px",
                  right: 0,
                  display: "flex",
                  p: 0,
                  zIndex: 1000,
                  "&:hover": {
                    backgroundColor: "transparent",
                  },
                }}
                onClick={() => {
                  setIsCustomizeOpen(true);
                }}
              >
                <Customize />
              </IconButton>
            )}
          </Box>
        ) : null}
        {isPackageOpen && (
          <SuggestedPackages
            packageWidth={packageWidth}
            setIsPackageOpen={(value) => {
              setIsPackageOpen(value);
            }}
            isMidScreen={isMidScreen}
          />
        )}
        {isCustomizeOpen && (
          <CustomizeSection
            packageWidth={CustomizeWidth}
            setIsPackageOpen={(value) => {
              setIsCustomizeOpen(value);
            }}
          />
        )}
      </Box>
    </Box>
  );
};

export default AiChatLayout;
