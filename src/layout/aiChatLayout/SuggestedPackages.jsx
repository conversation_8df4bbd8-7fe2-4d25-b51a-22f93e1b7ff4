import React, { useState } from "react";
import { ArrowRight, CloseOutline } from "../../components/common/SvgIcon";
import StarIcon from "@mui/icons-material/Star";
import {
  OverviewTab,
  InclusionsTab,
  ItineraryTab,
  HotelsTab,
  AddOnsTab,
  PolicyTab,
  MeetYourDestinationTab,
  ThreeSixtyViewTab,
} from "./PackagesTabs";
import { Box, Button, Typography } from "@mui/material";

const SuggestedPackages = ({
  packageWidth,
  setIsPackageOpen,
  isMidScreen = false,
}) => {
  const tabs = [
    "Overview",
    "Inclusions",
    "Itinerary",
    "Hotels",
    "Add-Ons",
    "360 view",
    "Policies, T&C",
    "Meet your destination",
  ];

  const [activeTab, setActiveTab] = useState(tabs[0]);
  const handleTabClick = (tab) => {
    setActiveTab(tab);
  };

  const renderTabContent = (tab) => {
    switch (tab) {
      case "Overview":
        return <OverviewTab />;
      case "Inclusions":
        return <InclusionsTab />;
      case "Itinerary":
        return <ItineraryTab />;
      case "Hotels":
        return <HotelsTab />;
      case "Add-Ons":
        return <AddOnsTab />;
      case "360 view":
        return <ThreeSixtyViewTab />;
      case "Policies, T&C":
        return <PolicyTab />;
      case "Meet your destination":
        return <MeetYourDestinationTab />;

      default:
        return null;
    }
  };
  return (
    <Box
      sx={{
        width: packageWidth,
        height: "100%",
        ml: !isMidScreen ? "0px" : "243px",
        zIndex: 1001,
        boxShadow: "0px 4px 7px 0px #00000008",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          width: "100%",
          background: "#F9F9F9",
          p: "10px 16px",
          boxSizing: "border-box",
          display: "flex",
          justifyContent: "space-between",
          gap: 2,
        }}
      >
        <Typography
          sx={{
            fontSize: 14,
            fontWeight: 500,
            lineHeight: "18px",
            color: "#1E3A8A",
            maxWidth: "calc(100% - 44px)",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          Dubai Package
        </Typography>
        <CloseOutline onClick={() => setIsPackageOpen(false)} />
      </Box>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          width: "100%",
          flex: 1,
          overflow: "hidden",
          maxHeight: "100%",
          p: "16px",
          boxSizing: "border-box",
        }}
      >
        {/* header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            gap: 2,
          }}
        >
          <Box>
            <Box
              sx={{
                fontSize: 16,
                fontWeight: 600,
                lineHeight: "20px",
                color: "#333333",
              }}
            >
              Extreme Dubai
            </Box>
            <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
              {["Platinum", "Gold", "Silver", "Bronze"].map((item) => (
                <Box
                  key={item}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    padding: "4px 8px",
                    gap: 1,
                    borderRadius: "4px",
                    background: "#F9F9F9",
                    fontSize: 12,
                    fontWeight: 400,
                    lineHeight: "14px",
                    color: "#333333",
                  }}
                >
                  {item}
                </Box>
              ))}
            </Box>
          </Box>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: "2px",
            }}
          >
            <Typography
              sx={{
                fontSize: 16,
                fontWeight: 500,
                lineHeight: "19px",
                color: "#333333",
              }}
            >
              4
            </Typography>
            <StarIcon sx={{ color: "#00AF31" }} />
          </Box>
        </Box>
        {/* tabs */}
        <Box
          sx={{
            display: "inline-flex",
            width: "100%",
            background: "#FFF9F9",
            boxSizing: "border-box",
            padding: "6px 16px",
            flexWrap: "nowrap",
            overflowX: "auto",
            scrollbarWidth: "none",
            "&::-webkit-scrollbar": {
              display: "none",
            },
            gap: 1,
            alignItems: "center",
            my: "16px",
          }}
        >
          {tabs.map((item) => (
            <Box
              key={item}
              sx={{
                display: "inline-flex",
                whiteSpace: "nowrap",
                alignItems: "center",
                width: "max-content",
                padding: "8px 12px",
                borderRadius: "8px",
                background: activeTab === item ? "#FFFFFF" : "transparent",
                fontSize: 14,
                fontWeight: 500,
                lineHeight: "16px",
                fontFamily: "Roboto",
                color: activeTab === item ? "#1E3A8A" : "#706E75",
                cursor: "pointer",
                transition: "all 0.3s ease",
                "&:hover": {
                  background: "#ffffff5a",
                },
              }}
              onClick={() => handleTabClick(item)}
            >
              {item}
            </Box>
          ))}
        </Box>
        {/* content */}
        <Box
          sx={{
            display: "flex",
            width: "100%",
            flex: 1,
            overflowY: "auto",
          }}
        >
          {renderTabContent(activeTab)}
        </Box>
      </Box>
      {/* footer */}
      <Box
        sx={{
          width: "100%",
          background: "#F9F9F9",
          p: "8px 16px",
          boxSizing: "border-box",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 2,
        }}
      >
        <Typography
          sx={{
            fontSize: 16,
            fontWeight: 600,
            lineHeight: "19px",
            color: "#1E3A8A",
            fontFamily: "Roboto",
          }}
        >
          ₹26,000
          <Box
            component="span"
            sx={{ fontSize: 14, fontWeight: 400, lineHeight: "16px" }}
          >
            /per person
          </Box>
        </Typography>
        <Button
          variant="basic"
          color="primary"
          sx={{
            padding: "8px",
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "12px",
            lineHeight: "14px",
            letterSpacing: "0.02em",
            textTransform: "capitalize",
            color: "#1E3A8A",
            gap: 1,
            "&:hover": {
              background: "#1e3b8a28",
            },
          }}
        >
          Customize
          <ArrowRight />
        </Button>
      </Box>
    </Box>
  );
};

export default SuggestedPackages;
