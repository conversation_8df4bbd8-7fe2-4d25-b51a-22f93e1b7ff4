import React from "react";
import Header from "../../components/Header";
import Footer from "../../components/Footer";
import { Box } from "@mui/material";

const PackageDetailsLayout = ({ children }) => {
  return (
    <Box sx={{ overflow: "visible" }}>
      <Header isSticky={false} />
      <Box
        sx={{
          minHeight: { xs: "calc(100dvh - 57px)", tb: "calc(100dvh - 104px)" },
          marginTop: { xs: "57px", tb: "104px" },
        }}
      >
        {children}
      </Box>
      <Footer />
    </Box>
  );
};

export default PackageDetailsLayout;
