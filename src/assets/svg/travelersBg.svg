<svg width="1472" height="467" viewBox="0 0 1472 467" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.09" filter="url(#filter0_g_716_2242)">
<rect x="16" y="16" width="1440" height="435" fill="#FF3951"/>
</g>
<defs>
<filter id="filter0_g_716_2242" x="0" y="0" width="1472" height="467" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feTurbulence type="fractalNoise" baseFrequency="0.05714285746216774 0.05714285746216774" numOctaves="3" seed="6956" />
<feDisplacementMap in="shape" scale="32" xChannelSelector="R" yChannelSelector="G" result="displacedImage" width="100%" height="100%" />
<feMerge result="effect1_texture_716_2242">
<feMergeNode in="displacedImage"/>
</feMerge>
</filter>
</defs>
</svg>
