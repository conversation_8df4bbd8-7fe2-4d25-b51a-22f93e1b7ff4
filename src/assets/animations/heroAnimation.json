{"v": "5.5.8", "fr": 25, "ip": 0, "op": 150, "w": 1920, "h": 1080, "nm": "Airplane_Final_Comp", "ddd": 0, "assets": [{"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "All_Winds", "parent": 2, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 70, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [956, 540, 0], "to": [-3.859, -2.089, 0], "ti": [3.859, 2.089, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 81, "s": [932.845, 527.467, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100, "s": [932.845, 527.467, 0], "to": [4.192, 2.089, 0], "ti": [-4.192, -2.089, 0]}, {"t": 136, "s": [958, 540, 0]}], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Plane_Body", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [1074, 422, 0], "to": [6.333, -9, 0], "ti": [7.333, 1.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [1112, 368, 0], "to": [-7.333, -1.667, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100, "s": [1030, 412, 0], "to": [0, 0, 0], "ti": [-13.667, 7.333, 0]}, {"t": 149, "s": [1112, 368, 0]}], "ix": 2}, "a": {"a": 0, "k": [1074, 422, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 50, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 91, "s": [95, 95, 100]}, {"t": 136, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 0, "op": 150, "st": 0, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Line_02", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [100]}, {"t": 16, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -43.3, "ix": 10}, "p": {"a": 0, "k": [691.883, 907.128, 0], "ix": 2}, "a": {"a": 0, "k": [228.095, 38.978, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-206.034, 47.761], [285.211, 39.605]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 1, "s": [0]}, {"t": 11, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 6, "s": [0]}, {"t": 16, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 1, "op": 16, "st": -64, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Line_01", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [100]}, {"t": 16, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -47.5, "ix": 10}, "p": {"a": 0, "k": [683.883, 691.128, 0], "ix": 2}, "a": {"a": 0, "k": [228.095, 38.978, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-206.034, 47.761], [285.211, 39.605]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 1, "s": [0]}, {"t": 11, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 6, "s": [0]}, {"t": 16, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 1, "op": 16, "st": -64, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "Wind+-3", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 130, "op": 154, "st": 130, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "Wind+-3", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 109, "op": 133, "st": 109, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "Wind+-3", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 88, "op": 112, "st": 88, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "Wind+-3", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 69, "op": 93, "st": 69, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 0, "nm": "Wind+-3", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 50, "op": 74, "st": 50, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 0, "nm": "Wind+-3", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 31, "op": 55, "st": 31, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 0, "nm": "Wind+-3", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 15, "op": 39, "st": 15, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 0, "nm": "Wind_02", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 134, "op": 163, "st": 134, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 0, "nm": "Wind_02", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 114, "op": 143, "st": 114, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 0, "nm": "Wind_02", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 97, "op": 126, "st": 97, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 0, "nm": "Wind_02", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 76, "op": 105, "st": 76, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 0, "nm": "Wind_02", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 55, "op": 84, "st": 55, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 0, "nm": "Wind_02", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 35, "op": 64, "st": 35, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 0, "nm": "Wind_02", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 15, "op": 44, "st": 15, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 0, "nm": "Wind_01", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 113, "op": 141, "st": 113, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 0, "nm": "Wind_01", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 130, "op": 158, "st": 130, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 0, "nm": "Wind_01", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 89, "op": 117, "st": 89, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 0, "nm": "Wind_01", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 68, "op": 96, "st": 68, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 0, "nm": "Wind_01", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 50, "op": 78, "st": 50, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 0, "nm": "Wind_01", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 31, "op": 59, "st": 31, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 0, "nm": "Wind_01", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 13, "op": 41, "st": 13, "bm": 0}]}, {"id": "comp_2", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "18 Outlines 16", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 15, "s": [100]}, {"t": 23, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -42.5, "ix": 10}, "p": {"a": 0, "k": [533.883, 809.128, 0], "ix": 2}, "a": {"a": 0, "k": [228.095, 38.978, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[19.167, 44.854], [285.211, 39.605]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [0]}, {"t": 18, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0]}, {"t": 23, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 8, "op": 23, "st": -57, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "18 Outlines 15", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 7, "s": [100]}, {"t": 15, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -42.5, "ix": 10}, "p": {"a": 0, "k": [693.883, 889.128, 0], "ix": 2}, "a": {"a": 0, "k": [228.095, 38.978, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[19.167, 44.854], [285.211, 39.605]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 10, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5, "s": [0]}, {"t": 15, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 15, "st": -65, "bm": 0}]}, {"id": "comp_3", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "18 Outlines 18", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [100]}, {"t": 26, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -44.3, "ix": 10}, "p": {"a": 0, "k": [1037.883, 1071.128, 0], "ix": 2}, "a": {"a": 0, "k": [228.095, 38.978, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-109.841, 46.108], [225.784, 41.708]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 11, "s": [0]}, {"t": 21, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [0]}, {"t": 26, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 11, "op": 26, "st": -54, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "18 Outlines 16", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 7, "s": [100]}, {"t": 15, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -44.3, "ix": 10}, "p": {"a": 0, "k": [1037.883, 1071.128, 0], "ix": 2}, "a": {"a": 0, "k": [228.095, 38.978, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-109.841, 46.108], [225.784, 41.708]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 10, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5, "s": [0]}, {"t": 15, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 15, "st": -65, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "18 Outlines 17", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [100]}, {"t": 28, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -44.3, "ix": 10}, "p": {"a": 0, "k": [913.883, 889.128, 0], "ix": 2}, "a": {"a": 0, "k": [228.095, 38.978, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-206.034, 47.761], [285.211, 39.605]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0]}, {"t": 23, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18, "s": [0]}, {"t": 28, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 13, "op": 28, "st": -52, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "18 Outlines 14", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [100]}, {"t": 18, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -44.3, "ix": 10}, "p": {"a": 0, "k": [913.883, 889.128, 0], "ix": 2}, "a": {"a": 0, "k": [228.095, 38.978, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-206.034, 47.761], [285.211, 39.605]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 3, "s": [0]}, {"t": 13, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [0]}, {"t": 18, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 3, "op": 18, "st": -62, "bm": 0}]}, {"id": "comp_4", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "18 Outlines 17", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [100]}, {"t": 27, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -46.4, "ix": 10}, "p": {"a": 0, "k": [733.883, 569.128, 0], "ix": 2}, "a": {"a": 0, "k": [228.095, 38.978, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[19.167, 44.854], [228.729, 43.815]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12, "s": [0]}, {"t": 22, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17, "s": [0]}, {"t": 27, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 12, "op": 27, "st": -53, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "18 Outlines 11", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 7, "s": [100]}, {"t": 15, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -45.8, "ix": 10}, "p": {"a": 0, "k": [467.883, 597.128, 0], "ix": 2}, "a": {"a": 0, "k": [228.095, 38.978, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-206.034, 47.761], [285.211, 39.605]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 10, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5, "s": [0]}, {"t": 15, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 15, "st": -65, "bm": 0}]}, {"id": "comp_5", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Line", "parent": 2, "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [306.483, 410.304, 0], "to": [-0.667, -0.667, 0], "ti": [0.667, 0.667, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 81, "s": [302.483, 406.304, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100, "s": [302.483, 406.304, 0], "to": [0.667, 0.667, 0], "ti": [-0.667, -0.667, 0]}, {"t": 136, "s": [306.483, 410.304, 0]}], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100.024, 100.024, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "01 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [700.904, 802.125, 0], "ix": 2}, "a": {"a": 0, "k": [47.324, 672.493, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [0, 0, 100]}, {"t": 10, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.02;\nfreq = 1;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[34.859, 34.891], [34.797, -34.922], [0, 0], [0, 0], [9.49, -14.881], [-13.896, -13.896], [-33.366, 22.547], [-10.596, 10.591], [0, 0], [0, 0]], "o": [[-34.859, -34.859], [0, 0], [0, 0], [-10.578, 10.602], [-21.77, 34.135], [16.225, 16.203], [14.542, -9.826], [0, 0], [0, 0], [34.859, -34.859]], "v": [[285.487, -286.657], [145.862, -273.329], [-216.094, 136.821], [-253.247, 178.922], [-284.211, 218.381], [-306.45, 305.313], [-218.861, 283.701], [-180.06, 252.078], [-137.979, 214.948], [272.19, -146.97]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Fill_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 5, "x": "var $bm_rt;\nif (comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Show_fill_Airplane')(1) == 1)\n    $bm_rt = 100;\nelse\n    $bm_rt = 0;"}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [359.323, 360.493], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Basis_Mask 4", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [700.904, 802.125, 0], "ix": 2}, "a": {"a": 0, "k": [47.324, 672.493, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [0, 0, 100]}, {"t": 10, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.02;\nfreq = 1;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[34.859, 34.891], [34.797, -34.922], [0, 0], [0, 0], [9.49, -14.881], [-13.896, -13.896], [-33.366, 22.547], [-10.596, 10.591], [0, 0], [0, 0]], "o": [[-34.859, -34.859], [0, 0], [0, 0], [-10.578, 10.602], [-21.77, 34.135], [16.225, 16.203], [14.542, -9.826], [0, 0], [0, 0], [34.859, -34.859]], "v": [[285.487, -286.657], [145.862, -273.329], [-216.094, 136.821], [-253.247, 178.922], [-284.211, 218.381], [-306.45, 305.313], [-218.861, 283.701], [-180.06, 252.078], [-137.979, 214.948], [272.19, -146.97]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Fill_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [359.323, 360.493], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "tail_02", "parent": 2, "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [104.843, 541.674, 0], "to": [-1.667, -1.667, 0], "ti": [1.667, 1.667, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [94.843, 531.674, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 100, "s": [94.843, 531.674, 0], "to": [1.667, 1.667, 0], "ti": [1.667, 1.667, 0]}, {"t": 136, "s": [104.843, 541.674, 0]}], "ix": 2}, "a": {"a": 0, "k": [169.356, 152.128, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 9, "s": [0, 0, 100]}, {"t": 19, "s": [100.024, 100.024, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.02;\nfreq = 1;\ndecay = 4;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-10.578, 10.601]], "o": [[0, 0], [0, 0], [0, 0], [9.491, -14.881], [0, 0]], "v": [[111.383, 10.595], [-34.765, -75.159], [-84.39, -25.487], [41.265, 97.154], [80.228, 58.695]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Fill_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 5, "x": "var $bm_rt;\nif (comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Show_fill_Airplane')(1) == 1)\n    $bm_rt = 100;\nelse\n    $bm_rt = 0;"}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [123.367, 114.137], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 9, "op": 159, "st": 9, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Basis_Mask 3", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [700.904, 802.125, 0], "ix": 2}, "a": {"a": 0, "k": [47.324, 672.493, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [0, 0, 100]}, {"t": 10, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.02;\nfreq = 1;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[34.859, 34.891], [34.797, -34.922], [0, 0], [0, 0], [9.49, -14.881], [-13.896, -13.896], [-33.366, 22.547], [-10.596, 10.591], [0, 0], [0, 0]], "o": [[-34.859, -34.859], [0, 0], [0, 0], [-10.578, 10.602], [-21.77, 34.135], [16.225, 16.203], [14.542, -9.826], [0, 0], [0, 0], [34.859, -34.859]], "v": [[285.487, -286.657], [145.862, -273.329], [-216.094, 136.821], [-253.247, 178.922], [-284.211, 218.381], [-306.45, 305.313], [-218.861, 283.701], [-180.06, 252.078], [-137.979, 214.948], [272.19, -146.97]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Fill_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [359.323, 360.493], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "tail_01", "parent": 2, "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [180.331, 610.816, 0], "to": [-1, -1.667, 0], "ti": [1, 1.667, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [174.331, 600.816, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 100, "s": [174.331, 600.816, 0], "to": [1, 1.667, 0], "ti": [1.333, 1.333, 0]}, {"t": 136, "s": [180.331, 610.816, 0]}], "ix": 2}, "a": {"a": 0, "k": [78.814, 74.369, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 9, "s": [0, 0, 100]}, {"t": 19, "s": [100.024, 100.024, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.02;\nfreq = 1;\ndecay = 4;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[14.542, -9.826], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-10.595, 10.591]], "v": [[-74.838, -15.639], [25.151, 84.393], [74.838, 34.799], [6.044, -84.393], [-36.037, -47.263]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Fill_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 5, "x": "var $bm_rt;\nif (comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Show_fill_Airplane')(1) == 1)\n    $bm_rt = 100;\nelse\n    $bm_rt = 0;"}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [113.815, 123.371], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 9, "op": 159, "st": 9, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Basis_Mask 2", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [700.904, 802.125, 0], "ix": 2}, "a": {"a": 0, "k": [47.324, 672.493, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [0, 0, 100]}, {"t": 10, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.02;\nfreq = 1;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[34.859, 34.891], [34.797, -34.922], [0, 0], [0, 0], [9.49, -14.881], [-13.896, -13.896], [-33.366, 22.547], [-10.596, 10.591], [0, 0], [0, 0]], "o": [[-34.859, -34.859], [0, 0], [0, 0], [-10.578, 10.602], [-21.77, 34.135], [16.225, 16.203], [14.542, -9.826], [0, 0], [0, 0], [34.859, -34.859]], "v": [[285.487, -286.657], [145.862, -273.329], [-216.094, 136.821], [-253.247, 178.922], [-284.211, 218.381], [-306.45, 305.313], [-218.861, 283.701], [-180.06, 252.078], [-137.979, 214.948], [272.19, -146.97]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Fill_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [359.323, 360.493], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 0, "nm": "Right_Wing", "parent": 2, "tt": 2, "refId": "comp_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [461.485, 359.303, 0], "to": [-1, -4.667, 0], "ti": [1, 4.667, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [455.485, 331.303, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 100, "s": [455.485, 331.303, 0], "to": [1, 4.667, 0], "ti": [2.667, 2.667, 0]}, {"t": 136, "s": [461.485, 359.303, 0]}], "ix": 2}, "a": {"a": 0, "k": [1114.961, 489.014, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 5, "s": [0, 0, 100]}, {"t": 15, "s": [100.024, 100.024, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.02;\nfreq = 1;\ndecay = 4;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "w": 1920, "h": 1080, "ip": 5, "op": 155, "st": 5, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Basis_Mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [700.904, 802.125, 0], "ix": 2}, "a": {"a": 0, "k": [47.324, 672.493, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [0, 0, 100]}, {"t": 10, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.02;\nfreq = 1;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[34.859, 34.891], [34.797, -34.922], [0, 0], [0, 0], [9.49, -14.881], [-13.896, -13.896], [-33.366, 22.547], [-10.596, 10.591], [0, 0], [0, 0]], "o": [[-34.859, -34.859], [0, 0], [0, 0], [-10.578, 10.602], [-21.77, 34.135], [16.225, 16.203], [14.542, -9.826], [0, 0], [0, 0], [34.859, -34.859]], "v": [[285.487, -286.657], [145.862, -273.329], [-216.094, 136.821], [-253.247, 178.922], [-284.211, 218.381], [-306.45, 305.313], [-218.861, 283.701], [-180.06, 252.078], [-137.979, 214.948], [272.19, -146.97]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Fill_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [359.323, 360.493], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 0, "nm": "Left_Wing", "parent": 2, "tt": 2, "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [356.484, 258.302, 0], "to": [-2.667, -2.667, 0], "ti": [2.667, 2.667, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 81, "s": [340.484, 242.302, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100, "s": [340.484, 242.302, 0], "to": [2.667, 2.667, 0], "ti": [2.667, 2.667, 0]}, {"t": 136, "s": [356.484, 258.302, 0]}], "ix": 2}, "a": {"a": 0, "k": [1009.986, 388.039, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 5, "s": [0, 0, 100]}, {"t": 15, "s": [100.024, 100.024, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.02;\nfreq = 1;\ndecay = 4;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "w": 1920, "h": 1080, "ip": 5, "op": 155, "st": 5, "bm": 0}]}, {"id": "comp_6", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "09 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1070.977, 482.602, 0], "ix": 2}, "a": {"a": 0, "k": [49.954, 49.376, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[10.977, -10.399], [-10.977, 10.399]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [49.954, 49.376], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "08 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1198.352, 361.336, 0], "ix": 2}, "a": {"a": 0, "k": [122.095, 123.22, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [31.047, -28.797], [0, 0]], "o": [[0, 0], [-31.063, 28.828], [0, 0]], "v": [[83.117, -84.242], [43.211, -26.586], [-83.117, 84.242]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [122.094, 123.22], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}]}, {"id": "comp_7", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Line", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1199.641, 676.578, 0], "ix": 2}, "a": {"a": 0, "k": [121.618, 221.556, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[60.485, 182.578], [82.64, 160.437], [-82.64, -182.578]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [121.618, 221.556], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "<PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1194.195, 666.297, 0], "ix": 2}, "a": {"a": 0, "k": [189.173, 277.275, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.195, -238.297], [150.195, 181.781], [93.71, 238.297], [-150.195, -109.297]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Fill_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 5, "x": "var $bm_rt;\nif (comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Show_fill_Airplane')(1) == 1)\n    $bm_rt = 100;\nelse\n    $bm_rt = 0;"}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [189.173, 277.275], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}]}, {"id": "comp_8", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "line", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [824.422, 302.344, 0], "ix": 2}, "a": {"a": 0, "k": [219.556, 120.634, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-180.578, -59.531], [-158.453, -81.656], [236.578, 113.656]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [219.555, 120.634], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "<PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [836.219, 308.773, 0], "ix": 2}, "a": {"a": 0, "k": [276.759, 189.204, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[273.781, 22.227], [-181.297, -150.227], [-237.781, -93.679], [159.781, 192.227]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.275, 0.328999986836, 0.630999995213, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.591, "ix": 5, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Stroke_width_Airplane')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Fill_color_Airplane')('ADBE Color Control-0001');"}, "o": {"a": 0, "k": 100, "ix": 5, "x": "var $bm_rt;\nif (comp('Airplane_Final_Comp').layer('Airplane_Color_Control').effect('Show_fill_Airplane')(1) == 1)\n    $bm_rt = 100;\nelse\n    $bm_rt = 0;"}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [276.759, 189.204], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 2, "ty": 0, "nm": "Airplane_Animation", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 1080, "ip": 0, "op": 150, "st": 0, "bm": 0}], "markers": []}