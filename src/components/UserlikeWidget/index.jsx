import { useEffect } from "react";

const UserlikeWidget = () => {
  useEffect(() => {
    const script = document.createElement("script");
    script.src =
      "https://userlike-cdn-widgets.s3-eu-west-1.amazonaws.com/2991360f7f034e798a702b2f54201361e8ed8cdd06034b15a549145b7c2354b8.js";
    script.async = true;
    document.body.appendChild(script);

    // Set up a global ready callback
    window.userlikeReady = () => {
      console.log("Userlike is ready ✅");
    };

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return null;
};

export default UserlikeWidget;
