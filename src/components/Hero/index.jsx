/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react";
import { Box, Typography } from "@mui/material";
import StyledHero from "./StyledHero";
import <PERSON><PERSON> from "lottie-react";
import animationData from "../../assets/animations/heroAnimation.json";
import { useNavigate, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import OnboardingModal from "../OnboardingModal";
import SecondaryButton from "../common/Button/SecondaryButton";
import PrimaryButton from "../common/Button/PrimaryButton";
import { setCookie } from "../../utils/cookiesUtils";

const Hero = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { userDetails } = useSelector((state) => state.authentication);

  const [openOnboardingModal, setOpenOnboardingModal] = useState(false);

  // Function to validate UUID
  const isValidUUID = (uuid) => {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  };

  // Check for referral parameter on component mount
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const referralKeyFromUrl = urlParams.get("referral");

    if (
      referralKeyFromUrl &&
      isValidUUID(referralKeyFromUrl) &&
      !userDetails?.user
    ) {
      setCookie("affiliate", referralKeyFromUrl);

      // Open onboarding modal
      setOpenOnboardingModal(true);
      sessionStorage.setItem("hasVisited", "true");

      // remove the referral key from the url
      navigate(`${window.location.pathname}`);
    }
  }, [location.search, userDetails?.user]);

  const handleOpenOnboardingModal = () => {
    setOpenOnboardingModal(true);
  };

  const handleCloseOnboardingModal = () => {
    setOpenOnboardingModal(false);
  };

  const handlePlanWithAI = () => {
    if (userDetails?.user) {
      const visibility = process.env.REACT_APP_ENV === "production";
      if (!visibility) {
        navigate("/ai-chat");
      }
    } else {
      handleOpenOnboardingModal();
    }
  };

  return (
    <StyledHero>
      <Box className="hero-content">
        <Box className="destinations">
          <Typography className="destination-text">MOUNTAINS</Typography>
          <Typography className="destination-text">|</Typography>
          <Typography className="destination-text">PLAINS</Typography>
          <Typography className="destination-text">|</Typography>
          <Typography className="destination-text">BEACHES</Typography>
        </Box>

        <Typography className="main-title">
          Travel Planning Is Broken.
        </Typography>
        <Typography className="gradient-title">
          Zuumm Fixes It - Instantly
        </Typography>

        <Typography className="description-desc">
          82% of travelers spend over 10 hours across multiple websites just to
          figure out what they really want.
        </Typography>

        <Typography className="description-desc-2">
          With ZUUMM AI, craft your ideal escape, compare real time prices,
          <br /> and design your next holiday – in seconds.
        </Typography>

        <Box className="cta-buttons">
          <SecondaryButton onClick={() => navigate("/explore")}>
            Explore
          </SecondaryButton>
          <PrimaryButton onClick={handlePlanWithAI}>Plan With AI</PrimaryButton>
          <Box className="ai-animation">
            <Lottie
              animationData={animationData}
              loop
              autoplay
              style={{ height: 109, width: 79 }}
            />
          </Box>
        </Box>
      </Box>

      {openOnboardingModal && (
        <OnboardingModal
          open={openOnboardingModal}
          onClose={handleCloseOnboardingModal}
          onSuccess={() => {
            navigate("/ai-chat");
          }}
        />
      )}
    </StyledHero>
  );
};

export default Hero;
