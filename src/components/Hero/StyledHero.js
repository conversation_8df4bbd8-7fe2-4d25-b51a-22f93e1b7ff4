import { styled } from "@mui/material/styles";
import heroBG from "../../assets/svg/heroBg.svg";

const StyledHero = styled("div")(
  ({ theme }) => `
  position: relative;
  padding: 150px 120px 80px;
  text-align: center;
  background: url(${heroBG}) no-repeat center center;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;

    @media (max-width: 786px) {
      padding: 100px 54px 80px;
    }

    @media (max-width: 600px) {
    padding: 76px 20px 0px 20px;
    box-sizing: border-box;
    text-align: left;

    br {
      display: none;
    }
  }


  .hero-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
  }

  .destinations {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 25px;
  }
 .destination-text {
      font-family: 'Roboto', sans-serif;
      color: ${theme.palette.secondary.main};
      font-weight: 400;
      font-size: 14px;
      line-height: 100%;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }   

  .hero-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
  }

  .main-title {
    font-family: '<PERSON>ra', serif;
    font-size: 52px;
    font-weight: 600;
    color: ${theme.palette.secondary.main};
    line-height: 100%;
    margin-bottom: 12px;
  }

  .gradient-title {
    font-family: 'Lora', serif;
    font-size: 52px;
    font-weight: 600;
    line-height: 144%;
    background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 16px;
  }

  .description-desc {
    font-family: 'Roboto', sans-serif;
    font-size: 18px;
    font-weight: 400;
    color: ${theme.palette.custom.subText};
    line-height: 160%;
    letter-spacing: 0.05em;
    margin: 12px 0;
    
  }

  .description-desc-2 {
    font-family: 'Roboto', sans-serif;
    font-size: 18px;
    font-weight: 600;
    color: ${theme.palette.custom.subText};
    line-height: 160%;
    letter-spacing: 0.05em;
    margin: 12px 0;
  }

  .cta-buttons {
  box-sizing: border-box;
    margin-top: 40px;
    position: relative;
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-bottom: 40px;
    // height: 44px;
    @media (max-width: 600px) {
      flex-direction: column;
    }

    .deals-btn {
      background: #FFFFFF;
      padding: 6px 30px;
      border: 2px solid #FF3951;
      color: #FF3951;
      font-size: 14px;
      font-weight: 600;
      border-radius: 4px;
      text-transform: none;
    }

.ai-btn {
  position: relative;
  padding: 6px 30px;
  background: #FF3951;
  color: white;
  font-size: 14px;
  font-weight: 600;
  border-radius: 4px;
  text-transform: none;
  overflow: hidden;
  z-index: 1;
  cursor: pointer;
}

.ai-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
  background-size: 100% 200%;
  background-position: left center;
  transition: background-position 4s ease, opacity 0.5s ease;
  z-index: -1;
  opacity: 0;
}

.ai-btn:hover::before {
  background-position: right center;
  opacity: 1;
}
  }

  .ai-animation {
    position: absolute;
    right: 30px;
    bottom: 20px;
    width: 120px;
    height: auto;
    opacity: 1;
    transform: rotate(270deg) scale(2);
  }

  @media (max-width: 600px) {
    .destinations {
      justify-content: left;
      margin-bottom: 12px;
    }
    .destination-text {
      font-size: 10px;
    }
    .main-title {
      font-size: 24px;
      font-weight: 500;
      line-height: 100%;
      margin-bottom: 5px;
    }
    .gradient-title {
      font-size: 23px;
      font-weight: 500;
      line-height: 144%;
    }
    .description-desc {
      font-size: 12px;
      margin: 0;
    }
    .description-desc-2 {
      font-size: 12px;
      margin: 8px 0px;
    }
    .cta-buttons {
      margin-top: 20px;
      justify-content: left;
    }
    .deals-btn {
      padding: 5px 8px !important;
      font-size: 14px !important;
      font-weight: 500 !important;
      line-height: 100% !important;
      height: 44px !important;
    }  
    .ai-btn {
      height: 44px !important;
      padding: 5px 8px !important;
      font-size: 14px !important;
      font-weight: 500 !important;
      line-height: 100% !important;
    }  
    .ai-animation {
      display: none;
    }
    
  }
`
);

export default StyledHero;

