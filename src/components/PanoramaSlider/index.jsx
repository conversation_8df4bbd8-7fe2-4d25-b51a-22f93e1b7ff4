import React, { useEffect, useState } from "react";
import { Box, IconButton, Typography } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/autoplay";
import "./style.css";

import LocationOnIcon from "@mui/icons-material/LocationOn";
import { fetchTrendingDestinations } from "../../store/reducers/Home";
import { useDispatch } from "react-redux";
import { ToastNotifyError } from "../Toast/ToastNotify";
import generateCloudFrontURL from "../../utils";
import ROUTE_PATH from "../../constants/route";
import { useNavigate } from "react-router-dom";
import PanoramaSliderSkeleton from "../Skeleton/PanoramaSliderSkeleton";
import { formatIndianNumber } from "../../utils/helper";
import { ArrowBack, ArrowNext } from "../common/SvgIcon";

const SlickSlider = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [destinations, setDestinations] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getDestinations = async () => {
      try {
        setLoading(true);
        const response = await dispatch(fetchTrendingDestinations());
        const { data, message, status } = response?.payload?.data;
        if (status === "success") {
          const formattedDestinations = data?.destinations?.map(
            (destination) => ({
              external_id: destination.external_id,
              description: destination.description,
              title: destination.title,
              starting_price: destination.starting_price || 0,
              media:
                destination?.media?.length > 0
                  ? generateCloudFrontURL({
                      bucket: process.env.REACT_APP_BUCKET_NAME,
                      key: destination.media[0].media,
                      width: 800,
                      height: 500,
                      fit: "cover",
                      actual: true,
                    })
                  : generateCloudFrontURL({
                      bucket: process.env.REACT_APP_BUCKET_NAME,
                      key: "no-image.jpeg",
                      width: 800,
                      height: 500,
                      fit: "cover",
                      actual: true,
                    }),
            })
          );
          setDestinations(formattedDestinations);
        } else {
          ToastNotifyError(message);
        }
      } catch (error) {
        console.log(error);
      } finally {
        setTimeout(() => {
          setLoading(false);
        }, 2000);
      }
    };
    getDestinations();
  }, [dispatch]);

  // Show skeleton while loading
  if (loading) {
    return <PanoramaSliderSkeleton />;
  }

  return (
    <Box sx={{ width: "100%", maxWidth: "1440px", mx: "auto", height: "100%" }}>
      <Swiper
        loop={true}
        modules={[Navigation, Autoplay]}
        navigation={{
          prevEl: ".custom-swiper-prev",
          nextEl: ".custom-swiper-next",
        }}
        autoplay={{
          delay: 1300,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        centeredSlides={true}
        slidesPerView={3}
        speed={800}
        spaceBetween={30}
        style={{ overflow: "visible" }}
        className="custom-panorama-slider"
        breakpoints={{
          0: {
            slidesPerView: 1,
            centeredSlides: true,
            spaceBetween: 0,
          },
          600: {
            slidesPerView: 3,
            centeredSlides: true,
            spaceBetween: 0,
          },
        }}
      >
        {destinations.map((slide, index) => (
          <SwiperSlide key={`${slide.external_id}-${index}`}>
            {({ isActive, isPrev, isNext }) => (
              <Box
                onClick={() => {
                  navigate(ROUTE_PATH.SELECTED_PACKAGE, {
                    state: {
                      externalid: slide?.external_id,
                      title: slide?.title,
                      type: "destination",
                    },
                  });
                }}
                sx={{
                  position: "relative",
                  width: "100%",
                  height: "400px",
                  borderRadius: "12px",
                  cursor: "pointer",
                  transform: isActive
                    ? {
                        xs: "scaleX(0.9) scaleY(0.9)",
                        sm: "scaleX(1.35) scaleY(1.05)",
                      }
                    : isPrev || isNext
                      ? {
                          xs: "scaleX(1.5) scaleY(0.7)",
                          sm: "scaleX(1.15) scaleY(0.90)",
                        }
                      : "scaleX(1) scaleY(0.8)",
                  transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                  zIndex: isActive ? 10 : isPrev || isNext ? 5 : 1,
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  opacity: isActive ? 1 : isPrev || isNext ? 0.8 : 0.6,
                }}
              >
                <Box
                  sx={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    borderRadius: "12px",
                    background: isActive
                      ? "linear-gradient(180deg, rgba(255,255,255,0) 41.63%, rgba(0,0,0,0.8) 107.72%)"
                      : "rgba(255,255,255,0.6)",
                    zIndex: 2,
                  }}
                >
                  {isActive && (
                    <>
                      <Box
                        sx={{
                          position: "absolute",
                          top: "16px",
                          left: "16px",
                          borderRadius: "32px",
                          background: "white",
                          p: 1,
                          display: "flex",
                          alignItems: "center",
                          gap: 0.5,
                          zIndex: 3,
                          transform: "scaleX(0.74) scaleY(0.98)",
                        }}
                      >
                        <LocationOnIcon
                          sx={{ fontSize: "20px", color: "#FF3951" }}
                        />
                        <Typography
                          sx={{
                            fontSize: "12px",
                            fontWeight: 500,
                            fontFamily: "Roboto",
                            color: "#1E3A8A",
                            textTransform: "capitalize",
                            textAlign: "left",
                          }}
                        >
                          {slide?.title}
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          position: "absolute",
                          bottom: "16px",
                          left: "16px",
                          width: "fit-content",
                          height: "fit-content",
                          boxSizing: "border-box",
                          zIndex: 3,
                          transform: "scaleX(0.74) scaleY(0.98)",
                        }}
                      >
                        <Typography
                          sx={{
                            fontSize: "12px",
                            fontWeight: 500,
                            fontFamily: "Roboto",
                            color: "white",
                            textAlign: "left",
                          }}
                        >
                          Starting at
                        </Typography>
                        <Typography
                          sx={{
                            fontSize: "14px",
                            fontWeight: 500,
                            fontFamily: "Roboto",
                            color: "white",
                            textAlign: "left",
                          }}
                        >
                          ₹
                          {formatIndianNumber(
                            Math.round(slide?.starting_price || 0)
                          )}{" "}
                          per person
                        </Typography>
                      </Box>
                    </>
                  )}
                </Box>
                <img
                  src={slide?.media}
                  alt="destination"
                  style={{
                    objectFit: "cover",
                    borderRadius: "12px",
                    width: "100%",
                    height: "100%",
                    position: "absolute",
                    top: 0,
                    left: 0,
                    zIndex: 1,
                  }}
                  loading="lazy"
                />
              </Box>
            )}
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom Navigation */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          gap: 2,
          mt: 4,
          mb: 4.5,
        }}
        className="custom-panorama-slider-navigation-btns"
      >
        <IconButton
          className="btn-home-page custom-swiper-prev"
          sx={{
            p: 0,
          }}
        >
          <ArrowBack />
        </IconButton>
        <IconButton
          className="btn-home-page custom-swiper-next"
          sx={{
            p: 0,
          }}
        >
          <ArrowNext />
        </IconButton>
      </Box>
    </Box>
  );
};

export default SlickSlider;
