import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import StyledWhatTravelersSay from './StyledWhatTravelersSay';
import StarIcon from '@mui/icons-material/Star';
import testmonialIcon from '../../assets/svg/testmonialIcon.svg';
import testomonial1 from '../../assets/png/testomonial1.png';
import testomonial2 from '../../assets/png/testomonial2.png';
import testomonial3 from '../../assets/png/testomonial3.png';
import testomonial4 from '../../assets/png/testomonial4.png';
import testomonial5 from '../../assets/png/testomonial5.png';

const WhatTravelersSay = () => {
  const [activeIndex, setActiveIndex] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON> Bhansi',
      location: 'Singapore',
      image: testomonial1,
      text: 'I applied for a Singapore visa through ZUUMM and the entire process was handled smoothly. <PERSON><PERSON><PERSON><PERSON> was extremely helpful and got everything done quickly. The team also assisted in planning and executing our end-to-end travel package seamlessly. Would definitely recommend them for both visa services and complete travel planning!',
      rating: 4
    },
    {
      id: 2,
      name: 'Azha Roshan',
      location: 'Dubai',
      image: testomonial2,
      text: 'Had a very smooth and good experience with ZUUMM .. the itinerary of Dubai was tailored and made as per our requirement.... thank you guys 😊',
      rating: 5
    },
    {
      id: 3,
      name: 'Abhishek Pratap',
      location: 'Andaman',
      image: testomonial3,
      text: 'Had a great experience with ZUUMM while booking our Andaman family trip. The itinerary was well-planned, hotels were comfortable, and the support throughout the journey was excellent. Highly recommend them for hassle-free travel!',
      rating: 5
    },
    {
      id: 4,
      name: 'Abhishek Agarwal',
      location: 'Iceland',
      image: testomonial4,
      text: 'Had an amazing 10-day honeymoon in Iceland with everything perfectly arranged by ZUUMM. Every single booking was made well in advance, and the execution was flawless. Highly recommend them and will definitely use their services for all my future travels!',
      rating: 5
    },
    {
      id: 5,
      name: 'Tara Verma',
      location: 'Kashmir',
      image: testomonial5,
      text: 'I had a wonderful trip to Kashmir arranged by ZUUMM. The team was super responsive and ensured everything went smoothly. Great coordination and support throughout!',
      rating: 4
    },
  ];

  const handleTestimonialClick = (index) => {
    setActiveIndex(index);
  };

  return (
    <StyledWhatTravelersSay>
      <Box className="content-wrapper">
        <Typography className="title">
          What our <span className="highlight">Travelers</span> say
        </Typography>
        
        <Box className="rating-wrapper">
          {[...Array(testimonials[activeIndex].rating)].map((_, index) => (
            <StarIcon key={index} className="star-icon" />
          ))}
        </Box>

        <Box className="testimonial-slider">
          <Box className="testimonial-content">
            <Typography className="testimonial-text">
              "{testimonials[activeIndex].text}"
            </Typography>
            <Box className="user-info">
              <Typography className="user-name">{testimonials[activeIndex].name}</Typography>
              <Typography className="user-location">{testimonials[activeIndex].location}</Typography>
            </Box>
          </Box>

          <Box className="avatar-slider">
            {testimonials.map((testimonial, index) => (
              <Box 
                key={testimonial.id}
                className={`avatar-wrapper ${index === activeIndex ? 'active' : ''}`}
                onClick={() => handleTestimonialClick(index)}
              >
                <img 
                  src={testimonial.image} 
                  alt={testimonial.name} 
                  className="avatar-image"
                />
                {index === activeIndex && (
                  <img src={testmonialIcon} alt={testimonial.name} className="testmonial-icon" />
                )}
              </Box>
            ))}
          </Box>
        </Box>
      </Box>
    </StyledWhatTravelersSay>
  );
};

export default WhatTravelersSay; 