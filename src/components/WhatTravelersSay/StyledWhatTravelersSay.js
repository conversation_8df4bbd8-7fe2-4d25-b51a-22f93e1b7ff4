import { styled } from "@mui/material/styles";
import travelersBg from '../../assets/svg/travelersBg.svg';

const StyledWhatTravelersSay = styled("div")(
  ({ theme }) => `
  padding: 50px 0;
  width: 100%;
  background-image: url(${travelersBg});
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  position: relative;
  overflow: hidden;

  .content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    text-align: center;
    position: relative;
    z-index: 1;
  }

  .title {
    font-family: 'Lora', serif;
    font-size: 28px;
    font-weight: 700;
    color: ${theme.palette.secondary.main};
    margin-bottom: 18px;
    line-height: 40px;

    .highlight {
      background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .rating-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
    margin-bottom: 10px;

    .star-icon {
      color: ${theme.palette.custom.lightGreen};
      width: 24px;
      height: 24px;
    }
  }

  .testimonial-slider {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
  }

  .testimonial-content {
    margin-bottom: 20px;

    .testimonial-text {
      font-family: 'Roboto', sans-serif;
      font-size: 14px;
      font-weight: 600;
      color: ${theme.palette.secondary.main};
      line-height: 28px;
      margin-bottom: 20px;
      max-width: 450px;
      margin: 0 auto;
    }

    .user-info {
      margin-top: 20px;
      .user-name {
        font-family: 'Roboto', sans-serif;
        font-weight: 600;
        font-size: 15px;
        color: ${theme.palette.custom.subText};
        line-height: 22px;
        margin-bottom: 4px;
      }

      .user-location {
        font-family: 'Roboto', sans-serif;
        font-weight: 500;
        font-size: 16px;
        color: ${theme.palette.custom.subText};
        line-height: 21px;
      }
    }
  }

  .avatar-slider {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;

    .avatar-wrapper {
      width: 87px;
      height: 88px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      cursor: pointer;
      opacity: 1;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      position: relative;

      .avatar-image {
        width: 60px;
        height: 61px;
        object-fit: cover;
        border-radius: 50%;
        filter: grayscale(100%);
        transition: filter 0.3s ease;
      }

      &.active {
        opacity: 1;
        border-color: #FF3951;
        transform: scale(1.1);

        .avatar-image {
          filter: grayscale(0%);
        }
      }

      .testmonial-icon {
        position: absolute;
        top: -2px;
        left: 17%;
        transform: translateX(-50%);
        width: 24px;
        height: 24px;
        z-index: 10;
        width: 36px;
        height: 36px;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 60px 0;

    .title {
      font-size: 28px;
      line-height: 36px;
    }

    .testimonial-content {
      .testimonial-text {
        font-size: 14px;
      }

      .user-info {
        .user-name {
          font-size: 14px;
        }

        .user-location {
          font-size: 12px;
        }
      }
    }

    .avatar-slider {
      .avatar-wrapper {
        width: 50px;
        height: 50px;

        .avatar-image {
          width: 45px;
          height: 45px;
        }

        .testmonial-icon {
          width: 24px;
          height: 24px;
          top: -8px;
          left: 20%;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .avatar-slider {
      gap: 2px;
      .avatar-wrapper {
        width: 50px;
        height: 50px;

        .avatar-image {
          width: 42px;
          height: 42px;
        }

        .testmonial-icon {
          width: 20px;
          height: 20px;
          top: -6px;
          left: 20%;
        }
      }
    }
  }
`
);

export default StyledWhatTravelersSay; 