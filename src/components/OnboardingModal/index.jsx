// src/components/Footer/FooterAuthModal.jsx
import React, { useMemo, useState, useRef, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Box,
  Modal,
  Typography,
  IconButton,
  Button,
  Tooltip,
} from "@mui/material";
import CustomTextField from "../common/CustomTextField";
import InfoIconOutlined from "@mui/icons-material/InfoOutlined";
import CustomSelect from "../common/CustomSelect";
import { getCountries, getCountryCallingCode } from "libphonenumber-js";
import {
  sendOtp,
  verifyOtp,
  resendOtp,
  signup,
  setOtpSent,
  setOtpVerified,
  clearError,
  fetchSocialLogin,
  updateProfile,
  storeUserDetails,
} from "../../store/reducers/Authentication/index";
import { ToastNotifyError, ToastNotifySuccess } from "../Toast/ToastNotify";
import { CloseOutline } from "../common/SvgIcon";
import { useGoogleSignIn } from "../../utils/googleAuth";
import { getCookie, removeCookie } from "../../utils/cookiesUtils";
// Email validation function
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const OnboardingModal = ({ open, onClose, onSuccess = () => {} }) => {
  const dispatch = useDispatch();
  const { isLoading, userDetails } = useSelector(
    (state) => state.authentication
  );

  const handleClose = () => {
    onClose();
  };

  const countryCodeOptions = useMemo(() => {
    const codes = getCountries()
      .map((country) => {
        const code = getCountryCallingCode(country);
        return {
          value: `+${code}`,
          label: `+${code}`,
        };
      })
      .sort((a, b) => a.value.localeCompare(b.value));

    // Remove duplicates based on value
    const uniqueCodes = codes.filter(
      (code, index, self) =>
        index === self.findIndex((c) => c.value === code.value)
    );

    return uniqueCodes;
  }, []);

  const [step, setStep] = useState(1);
  const [loginVia, setLoginVia] = useState("email"); // email, phone
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const inputRefs = useRef([]);

  const [otp2, setOtp2] = useState(["", "", "", "", "", ""]);
  const inputRefs2 = useRef([]);

  const [resendTimer, setResendTimer] = useState(0);
  const [resendTimer2, setResendTimer2] = useState(0);

  const [isVerifyLoading, setIsVerifyLoading] = useState(false);
  const [isSentLoading, setIsSentLoading] = useState(false);
  const [onboardUser, setOnboardUser] = useState(null);

  useEffect(() => {
    if (resendTimer > 0) {
      setTimeout(() => setResendTimer(resendTimer - 1), 1000);
    }
  }, [resendTimer]);

  useEffect(() => {
    if (userDetails?.user) {
      onClose();
    }
  }, [userDetails?.user, onClose]);

  useEffect(() => {
    if (resendTimer2 > 0) {
      setTimeout(() => setResendTimer2(resendTimer2 - 1), 1000);
    }
  }, [resendTimer2]);

  // Form data state
  const [formData, setFormData] = useState({
    email: "",
    phoneNumber: "",
    countryCode: "+91",
    name: "",
  });

  // Email validation state
  const [isEmailValid, setIsEmailValid] = useState(true);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!open) {
      setStep(1);
      setLoginVia("email");
      setOtp(["", "", "", "", "", ""]);
      setOtp2(["", "", "", "", "", ""]);
      setFormData({
        email: "",
        phoneNumber: "",
        countryCode: "+91",
        name: "",
      });
      setIsEmailValid(true);
      dispatch(clearError());
      dispatch(setOtpSent(false));
      dispatch(setOtpVerified(false));
    }
  }, [open, dispatch]);

  // Handle form data changes
  const handleFormDataChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Validate email when email field changes
    if (field === "email") {
      if (value === "") {
        setIsEmailValid(true); // Don't show error for empty field
      } else {
        setIsEmailValid(isValidEmail(value));
      }
    }
  };

  const handleNextStep = async () => {
    if (step === 1) {
      console.log("formData");
      // Send OTP
      if (isVerifyLoading) {
        return;
      }

      if (isLoading) {
        console.log("isLoading");
      }

      if (loginVia === "phone") {
        if (formData.phoneNumber.length !== 10) {
          ToastNotifyError("Please enter a valid 10 digit phone number");
          return;
        }
      }

      const payload =
        loginVia === "email"
          ? { email: formData.email, source: "LOGIN_SIGNUP" }
          : {
              phone_number: formData.countryCode + formData.phoneNumber,
              source: "LOGIN_SIGNUP",
            };

      try {
        setIsVerifyLoading(true);
        await dispatch(sendOtp(payload)).unwrap();
        ToastNotifySuccess("OTP sent successfully");
        setStep(2);
        setResendTimer(60);
        setOtp(["", "", "", "", "", ""]);
      } catch (error) {
        console.error("Failed to send OTP:", error);
      } finally {
        setIsVerifyLoading(false);
      }
    } else if (step === 2) {
      // Verify OTP
      if (isVerifyLoading) {
        return;
      }

      const otpString = otp.join("");
      const payload = {
        ...(loginVia === "email"
          ? { email: formData.email }
          : { phone_number: formData.countryCode + formData.phoneNumber }),
        otp: otpString,
        source: "LOGIN_SIGNUP",
      };

      try {
        setIsVerifyLoading(true);
        const response = await dispatch(verifyOtp(payload)).unwrap();
        if (response?.status === "success") {
          if (response?.data?.is_valid) {
            if (response?.data?.user_token?.access) {
              setOnboardUser(response?.data);
              if (!response?.data?.user?.is_profile_completed) {
                const { email, phone_number, name } = response?.data?.user;

                setFormData({
                  email,
                  phoneNumber: phone_number?.phone_number || "",
                  countryCode: phone_number?.phone_number_country_code || "+91",
                  name: name,
                });
                setStep(3);
              } else {
                ToastNotifySuccess("Login Successfully");
                onSuccess();
                handleClose();
              }
            } else {
              setStep(3);
              setOnboardUser(null);
            }
          } else {
            throw new Error("Failed to verify OTP");
          }
        }
      } catch (error) {
        console.error("Failed to verify OTP:", error);
      } finally {
        setIsVerifyLoading(false);
      }
    } else if (step === 3) {
      if (isSentLoading) {
        return;
      }

      if (loginVia === "email") {
        if (formData.phoneNumber.length !== 10) {
          ToastNotifyError("Please enter a valid 10 digit phone number");
          return;
        }
      }
      // Send OTP for the second contact method
      const secondContactPayload =
        loginVia === "email"
          ? {
              phone_number: formData.countryCode + formData.phoneNumber,
              source: "LOGIN_SIGNUP",
              step: 2,
            }
          : {
              email: formData.email,
              source: "LOGIN_SIGNUP",
              step: 2,
            };

      try {
        setIsSentLoading(true);
        await dispatch(sendOtp(secondContactPayload)).unwrap();
        ToastNotifySuccess("OTP sent successfully");
        setStep(4);
        setResendTimer2(60);
        setOtp2(["", "", "", "", "", ""]);
      } catch (error) {
        console.error("Failed to send second OTP:", error);
      } finally {
        setIsSentLoading(false);
      }
    } else if (step === 4) {
      if (isVerifyLoading) {
        return;
      }

      // Verify second OTP and then signup
      const otpString = otp2.join("");
      const payload = {
        ...(loginVia === "email"
          ? { phone_number: formData.countryCode + formData.phoneNumber }
          : { email: formData.email }),
        otp: otpString,
        source: "LOGIN_SIGNUP",
      };

      try {
        setIsVerifyLoading(true);
        await dispatch(verifyOtp(payload)).unwrap();

        if (onboardUser?.user && !onboardUser?.user?.is_profile_completed) {
          const updateProfilePayload = {
            full_name: formData.name,
            email: formData.email,
            phone_number_country_code: formData.countryCode,
            phone_number: formData.phoneNumber,
            token: onboardUser?.user_token?.access,
          };

          const updated = await dispatch(
            updateProfile(updateProfilePayload)
          ).unwrap();

          await dispatch(
            storeUserDetails({
              user: updated?.data?.user,
              token: onboardUser?.user_token?.access,
              refreshToken: onboardUser?.user_token?.refresh,
            })
          ).unwrap();

          if (updated?.data?.user?.is_social_user) {
            ToastNotifySuccess("Successfully logged in with Google!");
          } else {
            ToastNotifySuccess("Login successful");
          }
          onSuccess();
          handleClose();
        } else {
          // Call signup API after successful OTP verification
          const affiliateKey = getCookie("affiliate");
          const signupPayload = {
            name: formData.name,
            phone_number: {
              phone_number_country_code: formData.countryCode,
              phone_number: formData.phoneNumber,
              phone_number_otp: parseInt(
                loginVia === "email" ? otp2.join("") : otp.join("")
              ),
            },
            email: {
              email: formData.email,
              email_otp: parseInt(
                loginVia === "email" ? otp.join("") : otp2.join("")
              ),
            },
            ...(affiliateKey ? { affiliate: affiliateKey } : {}),
          };

          await dispatch(signup(signupPayload)).unwrap();
          ToastNotifySuccess("Signup successful");
          onSuccess();
          removeCookie("affiliate");
          handleClose();
        }
      } catch (error) {
        console.error("Failed to verify second OTP or signup:", error);
      } finally {
        setIsVerifyLoading(false);
      }
    }
  };

  const handleResendOtp = async () => {
    if (isSentLoading) {
      return;
    }

    let payload;

    if (step === 2) {
      // Resend OTP for first contact method
      payload =
        loginVia === "email"
          ? { email: formData.email, source: "LOGIN_SIGNUP" }
          : {
              phone_number: formData.countryCode + formData.phoneNumber,
              source: "LOGIN_SIGNUP",
            };
    } else if (step === 4) {
      // Resend OTP for second contact method
      payload =
        loginVia === "email"
          ? {
              phone_number: formData.countryCode + formData.phoneNumber,
              source: "LOGIN_SIGNUP",
            }
          : { email: formData.email, source: "LOGIN_SIGNUP" };
    }

    try {
      setIsSentLoading(true);
      await dispatch(resendOtp(payload)).unwrap();
      if (step === 2) {
        setResendTimer(60);
      } else if (step === 4) {
        setResendTimer2(60);
      }
      ToastNotifySuccess("OTP resent successfully");
    } catch (error) {
      console.error("Failed to resend OTP:", error);
    } finally {
      setIsSentLoading(false);
    }
  };

  // Handle Google Sign-In
  const handleGoogleSignInSuccess = async (response) => {
    try {
      // Call Redux thunk for social login
      const socialResp = await dispatch(
        fetchSocialLogin({
          provider: "GOOGLE",
          id_token: response.idToken,
        })
      ).unwrap();

      if (socialResp?.status === "success") {
        setLoginVia("email");
        if (socialResp?.data?.token?.access) {
          if (!socialResp?.data?.user?.is_profile_completed) {
            setOnboardUser({
              user: socialResp?.data?.user,
              user_token: socialResp?.data?.token,
            });

            const { email, phone_number, name } = socialResp?.data?.user;

            setFormData({
              email,
              phoneNumber: phone_number?.phone_number || "",
              countryCode: phone_number?.phone_number_country_code || "+91",
              name: name,
            });
            setStep(3);
          } else {
            await dispatch(
              storeUserDetails({
                user: socialResp?.data?.user,
                token: socialResp?.data?.token?.access,
                refreshToken: socialResp?.data?.token?.refresh,
              })
            ).unwrap();
            ToastNotifySuccess("Successfully logged in with Google!");
            onSuccess();
            handleClose();
          }
        }
      } else {
        ToastNotifyError("Google sign-in failed. Please try again.");
      }
    } catch (error) {
      console.error("Google Sign-In error:", error);
      ToastNotifyError(
        error.message || "Google sign-in failed. Please try again."
      );
    }
  };

  const handleGoogleSignInError = (error) => {
    console.error("Google Sign-In error:", error);
    ToastNotifyError(
      error.message || "Google sign-in failed. Please try again."
    );
  };

  const handleGoogleLogin = useGoogleSignIn(
    handleGoogleSignInSuccess,
    handleGoogleSignInError
  );

  const stepOneData = () => {
    return (
      <Box
        sx={{
          width: "100%",
          height: "100%",
          boxSizing: "border-box",
          padding: { xs: "0 20px", md: "0 40px" },
          overflowY: "auto",
          margin: "0 auto",
          maxWidth: { xs: "100%", lg: "530px" },
        }}
      >
        <Box
          component="img"
          sx={{
            width: "180px",
            height: "36px",
          }}
          src="/static/common/logo.png"
        />
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: "600",
            fontSize: "32px",
            lineHeight: "40px",
            color: "#333333",
            marginTop: "40px",
            marginBottom: "32px",
          }}
        >
          Login/Sign-up
        </Typography>

        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: "32px",
          }}
        >
          {loginVia === "email" && (
            <CustomTextField
              sx={{
                height: "fit-content !important",
                mb: "0px !important",
              }}
              label="Email"
              value={formData.email}
              required={true}
              onChange={(e) =>
                handleFormDataChange("email", e?.target?.value?.toLowerCase())
              }
              endAdornment={
                <Tooltip
                  title="Your Email will be used to login and sign-up"
                  enterTouchDelay={0}
                >
                  <InfoIconOutlined
                    sx={{
                      color: "#706E75",
                      cursor: "pointer",
                      fontSize: "20px",
                    }}
                  />
                </Tooltip>
              }
            />
          )}
          {loginVia === "phone" && (
            <Box sx={{ display: "flex", gap: "16px", alignItems: "center" }}>
              <Box sx={{ width: "110px" }}>
                <CustomSelect
                  sx={{
                    borderRadius: "10px",
                    width: "80px !important",
                    "& .MuiOutlinedInput-root": {
                      // height: "68px",
                    },
                    "& .MuiSelect-select.MuiSelect-outlined": {
                      paddingRight: "5px !important",
                    },
                  }}
                  label="Code"
                  disableLabel={true}
                  value={formData.countryCode}
                  onChange={(e) =>
                    handleFormDataChange("countryCode", e.target.value)
                  }
                  options={countryCodeOptions}
                  required
                  customHeight="auto"
                  customMarginBottom="0px"
                />
              </Box>
              <Box sx={{ width: "100%" }}>
                <CustomTextField
                  sx={{
                    height: "auto !important",
                    mb: "0px !important",
                  }}
                  label="Phone Number"
                  value={formData.phoneNumber}
                  required={true}
                  onChange={(e) => {
                    const value = e.target.value;
                    // Only allow numbers 0-9
                    if (/^[0-9]*$/.test(value)) {
                      handleFormDataChange("phoneNumber", value);
                    }
                  }}
                  type="tel"
                  inputProps={{
                    maxLength: 10,
                    pattern: "[0-9]*",
                  }}
                />
              </Box>
            </Box>
          )}
          <Button
            variant="contained"
            sx={{
              background: "linear-gradient(90deg, #FF3951 0%, #FF3951 100%)",
              color: "#FFFFFF",
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "16px",
              px: 5,
              py: "8px",
              borderRadius: "4px",
              transition: "all 0.3s ease",
              textTransform: "none",
              boxShadow: "none",
              "&:hover": {
                background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                boxShadow: "none",
              },
              "&:disabled": {
                background: "linear-gradient(90deg, #F6F6F6 0%, #F6F6F6 100%)",
                opacity: 1,
                color: "#333333",
                cursor: "not-allowed",
              },
            }}
            fullWidth
            onClick={handleNextStep}
            disabled={
              isVerifyLoading ||
              (loginVia === "email"
                ? !formData.email || !isEmailValid
                : !formData.phoneNumber)
            }
          >
            {isVerifyLoading ? "Sending..." : "Continue"}
          </Button>
        </Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "16px",
            lineHeight: "100%",
            letterSpacing: "0.05em",
            textAlign: "center",
            color: "#333333",
            mt: 2,
          }}
        >
          or
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            mt: 2,
          }}
        >
          <Button
            variant="text"
            sx={{
              width: "fit-content",
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "16px",
              lineHeight: "100%",
              letterSpacing: "0.05em",
              textTransform: "none",
              color: "#0083E7",
              "&:hover": {
                textDecoration: "underline",
                background: "transparent",
              },
            }}
            onClick={() => {
              setLoginVia(loginVia === "email" ? "phone" : "email");
              setFormData({
                email: "",
                phoneNumber: "",
                countryCode: "+91",
              });
            }}
          >
            {loginVia === "email"
              ? "Continue with Phone Number"
              : "Continue with Email"}
          </Button>
        </Box>
        <Box>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              mt: "24px",
            }}
          >
            <Box
              sx={{
                width: "100%",
                height: "1px",
                background: "#706E75",
              }}
            />
            <Box
              sx={{
                display: "flex",
                width: "max-content",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "12px",
                  lineHeight: "14px",
                  color: "#706E75",
                  px: "8px",
                  width: "max-content",
                }}
              >
                or Login/Signup With
              </Typography>
            </Box>
            <Box
              sx={{
                width: "100%",
                height: "1px",
                background: "#706E75",
              }}
            />
          </Box>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            mt: "20px",
          }}
        >
          <IconButton
            sx={{
              p: 0,
            }}
            onClick={handleGoogleLogin}
          >
            <Box
              component="img"
              src="/static/common/google.png"
              sx={{
                width: "44px",
                height: "44px",
              }}
            />
          </IconButton>
        </Box>
        <Box>
          <Box
            sx={{
              fontFamily: "Roboto",
              fontWeight: 400,
              fontSize: "12px",
              lineHeight: "14px",
              color: "#333333",
              textAlign: "center",
              mt: "18px",
            }}
          >
            By proceeding, you agree to Zuumm’s{" "}
            <Box
              component="a"
              href="/privacy"
              target="_blank"
              sx={{
                color: "#0083E7",
                textDecoration: "none",
                fontWeight: 500,
                "&:hover": {
                  textDecoration: "underline",
                },
              }}
            >
              Privacy Policy
            </Box>{" "}
            and{" "}
            <Box
              component="a"
              href="/terms"
              target="_blank"
              sx={{
                color: "#0083E7",
                textDecoration: "none",
                fontWeight: 500,
                "&:hover": {
                  textDecoration: "underline",
                },
              }}
            >
              T&Cs.
            </Box>
          </Box>
        </Box>
      </Box>
    );
  };

  const stepTwoData = () => {
    const handleChange = (index, value) => {
      if (value.length <= 1 && /^[0-9]*$/.test(value)) {
        const newOtp = [...otp];
        newOtp[index] = value;
        setOtp(newOtp);

        // Move to next input if value is entered
        if (value && index < 5) {
          inputRefs.current[index + 1].focus();
        }
      }
    };

    const handleKeyDown = (index, e) => {
      // Move to previous input on backspace if current input is empty
      if (e.key === "Backspace" && !otp[index] && index > 0) {
        inputRefs.current[index - 1].focus();
      }
    };

    const maskedContact =
      loginVia === "email"
        ? formData.email.replace(/(.{2}).*(@.*)/, "$1***$2")
        : formData.countryCode +
          formData.phoneNumber.replace(/(\d{2})\d{6}(\d{2})/, "$1******$2");

    return (
      <Box
        sx={{
          width: "100%",
          height: "100%",
          boxSizing: "border-box",
          display: "flex",
          flexDirection: "column",
          padding: { xs: "20px", lg: "40px" },
          mt: "53px",
          margin: "0 auto",
          maxWidth: { xs: "100%", sm: "490px", lg: "530px" },
        }}
      >
        <Box sx={{ mb: "24px" }}>
          <Box
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "16px",
              display: "flex",
              cursor: "pointer",
              color: "#0083E7",
              "&:hover": {
                textDecoration: "underline",
              },
            }}
            onClick={() => {
              setStep(1);
            }}
          >
            Back
          </Box>
        </Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "32px",
            lineHeight: "40px",
            color: "#333333",
          }}
        >
          Enter OTP
        </Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 400,
            fontSize: "16px",
            lineHeight: "24px",
            color: "#333333",
            mb: "32px",
          }}
        >
          OTP has been sent to {maskedContact}
        </Box>
        <Box sx={{ display: "flex", flexDirection: "column", gap: "12px" }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              gap: "12px",
            }}
          >
            <Box
              sx={{
                display: "flex",
                width: "100%",
                gap: "8px",
                justifyContent: "space-between",
                "& .otp-input": {
                  width: { xs: "32px", sm: "50px" },
                  height: { xs: "32px", sm: "48px" },
                  border: "1px solid rgba(255, 57, 81, 0.05)",
                  borderRadius: "10px",
                  textAlign: "center",
                  outline: "none",
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  lineHeight: "100%",
                  fontSize: { xs: "14px", sm: "16px" },
                  color: "#706E75",
                  position: "relative",
                  background: "#FFFFFF",

                  "&::placeholder": {
                    color: "#706E75",
                    opacity: 1,
                  },
                  "&:focus": {
                    borderColor: "#DB0000",
                  },
                  "&::-webkit-inner-spin-button": {
                    WebkitAppearance: "none",
                    margin: 0,
                  },
                  "&::-webkit-outer-spin-button": {
                    WebkitAppearance: "none",
                    margin: 0,
                  },
                },
                "& .otp-input-container": {
                  display: "flex",
                  gap: "8px",
                  marginBottom: "16px",
                  justifyContent: "flex-end",
                },
              }}
            >
              {otp.map((digit, index) => (
                <input
                  key={index}
                  type="tel"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  maxLength={1}
                  value={digit}
                  placeholder="|"
                  ref={(el) => (inputRefs.current[index] = el)}
                  onChange={(e) => handleChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  className="otp-input"
                  autoFocus={index === 0}
                  autoComplete="one-time-code"
                />
              ))}
            </Box>
            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              {resendTimer > 0 ? (
                <Box
                  sx={{
                    fontFamily: "Roboto",
                    fontWeight: 500,
                    fontSize: "12px",
                    lineHeight: "14px",
                    letterSpacing: "0.05em",
                  }}
                >
                  Resend OTP in 00:
                  {resendTimer?.toString()?.padStart(2, "0") || "00"}
                </Box>
              ) : (
                <Button
                  variant="text"
                  sx={{
                    width: "fit-content",
                    fontFamily: "Roboto",
                    fontWeight: 500,
                    fontSize: "12px",
                    lineHeight: "14px",
                    letterSpacing: "0.05em",
                    textTransform: "none",
                    color: "#0083E7",
                    p: 0,
                    "&:hover": {
                      textDecoration: "underline",
                      background: "transparent",
                    },
                  }}
                  onClick={handleResendOtp}
                  disabled={isSentLoading}
                >
                  {isSentLoading ? "Sending..." : "Resend OTP"}
                </Button>
              )}
            </Box>
          </Box>
        </Box>
        <Box sx={{ mt: "36px" }}>
          <Button
            variant="contained"
            sx={{
              background: "linear-gradient(90deg, #FF3951 0%, #FF3951 100%)",
              color: "#FFFFFF",
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "16px",
              px: 5,
              py: "8px",
              borderRadius: "4px",
              transition: "all 0.3s ease",
              textTransform: "none",
              boxShadow: "none",
              "&:hover": {
                background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                boxShadow: "none",
              },
              "&:disabled": {
                background: "linear-gradient(90deg, #F6F6F6 0%, #F6F6F6 100%)",
                opacity: 1,
                color: "#333333",
                cursor: "not-allowed",
              },
            }}
            fullWidth
            onClick={handleNextStep}
            disabled={isVerifyLoading || otp.join("").length !== 6}
          >
            {isVerifyLoading ? "Verifying..." : "Verify"}
          </Button>
        </Box>
      </Box>
    );
  };

  const stepFourData = () => {
    const handleChange = (index, value) => {
      if (value.length <= 1 && /^[0-9]*$/.test(value)) {
        const newOtp = [...otp2];
        newOtp[index] = value;
        setOtp2(newOtp);

        // Move to next input if value is entered
        if (value && index < 5) {
          inputRefs2.current[index + 1].focus();
        }
      }
    };

    const handleKeyDown = (index, e) => {
      // Move to previous input on backspace if current input is empty
      if (e.key === "Backspace" && !otp2[index] && index > 0) {
        inputRefs2.current[index - 1].focus();
      }
    };

    const maskedContact =
      loginVia === "email"
        ? formData.countryCode +
          formData.phoneNumber.replace(/(\d{2})\d{6}(\d{2})/, "$1******$2")
        : formData.email.replace(/(.{2}).*(@.*)/, "$1***$2");

    return (
      <Box
        sx={{
          width: "100%",
          height: "100%",
          boxSizing: "border-box",
          display: "flex",
          flexDirection: "column",
          padding: { xs: "20px", lg: "40px" },
          mt: "53px",
          margin: "0 auto",
          maxWidth: { xs: "100%", sm: "490px", lg: "530px" },
        }}
      >
        <Box sx={{ mb: "24px" }}>
          <Box
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "16px",
              display: "flex",
              cursor: "pointer",
              color: "#0083E7",
              "&:hover": {
                textDecoration: "underline",
              },
            }}
            onClick={() => {
              setStep(3);
            }}
          >
            Back
          </Box>
        </Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "32px",
            lineHeight: "40px",
            color: "#333333",
          }}
        >
          Enter OTP
        </Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 400,
            fontSize: "16px",
            lineHeight: "24px",
            color: "#333333",
            mb: "32px",
          }}
        >
          OTP has been sent to {maskedContact}
        </Box>
        <Box sx={{ display: "flex", flexDirection: "column", gap: "12px" }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              gap: "12px",
            }}
          >
            <Box
              sx={{
                display: "flex",
                width: "100%",
                gap: "8px",
                justifyContent: { xs: "center", sm: "space-between" },
                "& .otp-input": {
                  width: { xs: "32px", sm: "50px" },
                  height: { xs: "32px", sm: "48px" },
                  border: "1px solid rgba(255, 57, 81, 0.05)",
                  borderRadius: "10px",
                  textAlign: "center",
                  outline: "none",
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  lineHeight: "100%",
                  fontSize: { xs: "14px", sm: "16px" },
                  color: "#706E75",
                  position: "relative",
                  background: "#FFFFFF",

                  "&::placeholder": {
                    color: "#706E75",
                    opacity: 1,
                  },
                  "&:focus": {
                    borderColor: "#DB0000",
                  },
                  "&::-webkit-inner-spin-button": {
                    WebkitAppearance: "none",
                    margin: 0,
                  },
                  "&::-webkit-outer-spin-button": {
                    WebkitAppearance: "none",
                    margin: 0,
                  },
                },
                "& .otp-input-container": {
                  display: "flex",
                  gap: "8px",
                  marginBottom: "16px",
                  justifyContent: "flex-end",
                },
              }}
            >
              {otp2.map((digit, index) => (
                <input
                  key={index}
                  type="tel"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  maxLength={1}
                  value={digit}
                  placeholder="|"
                  ref={(el) => (inputRefs2.current[index] = el)}
                  onChange={(e) => handleChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  className="otp-input"
                  autoFocus={index === 0}
                  autoComplete="one-time-code"
                />
              ))}
            </Box>
            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              {resendTimer2 > 0 ? (
                <Box
                  sx={{
                    fontFamily: "Roboto",
                    fontWeight: 500,
                    fontSize: "12px",
                    lineHeight: "14px",
                    letterSpacing: "0.05em",
                  }}
                >
                  Resend OTP in 00:
                  {resendTimer2?.toString()?.padStart(2, "0") || "00"}
                </Box>
              ) : (
                <Button
                  variant="text"
                  sx={{
                    width: "fit-content",
                    fontFamily: "Roboto",
                    fontWeight: 500,
                    fontSize: "12px",
                    lineHeight: "14px",
                    letterSpacing: "0.05em",
                    textTransform: "none",
                    color: "#0083E7",
                    p: 0,
                    "&:hover": {
                      textDecoration: "underline",
                      background: "transparent",
                    },
                  }}
                  onClick={handleResendOtp}
                  disabled={isSentLoading}
                >
                  {isSentLoading ? "Sending..." : "Resend OTP"}
                </Button>
              )}
            </Box>
          </Box>
        </Box>
        <Box sx={{ mt: "36px" }}>
          <Button
            variant="contained"
            sx={{
              background: "linear-gradient(90deg, #FF3951 0%, #FF3951 100%)",
              color: "#FFFFFF",
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "16px",
              px: 5,
              py: "8px",
              borderRadius: "4px",
              transition: "all 0.3s ease",
              textTransform: "none",
              boxShadow: "none",
              "&:hover": {
                background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                boxShadow: "none",
              },
              "&:disabled": {
                background: "linear-gradient(90deg, #F6F6F6 0%, #F6F6F6 100%)",
                opacity: 1,
                color: "#333333",
                cursor: "not-allowed",
              },
            }}
            fullWidth
            onClick={handleNextStep}
            disabled={isVerifyLoading || otp2.join("").length !== 6}
          >
            {isVerifyLoading ? "Verifying..." : "Verify"}
          </Button>
        </Box>
      </Box>
    );
  };

  const stepThreeData = () => {
    return (
      <Box
        sx={{
          width: "100%",
          height: "100%",
          boxSizing: "border-box",
          display: "flex",
          flexDirection: "column",
          padding: { xs: "20px", lg: "40px" },
          mt: "53px",
          margin: "0 auto",
          maxWidth: { xs: "100%", sm: "490px", lg: "530px" },
        }}
      >
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "32px",
            lineHeight: "40px",
            color: "#333333",
          }}
        >
          Enter Details
        </Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 400,
            fontSize: "16px",
            lineHeight: "24px",
            color: "#333333",
            mb: "32px",
          }}
        >
          Please enter your details
        </Box>
        <Box sx={{ display: "flex", flexDirection: "column", gap: "20px" }}>
          <CustomTextField
            sx={{
              height: "fit-content !important",
              mb: "0px !important",
            }}
            label="Name"
            value={formData.name}
            required={true}
            onChange={(e) => handleFormDataChange("name", e.target.value)}
          />

          {loginVia !== "phone" ? (
            <Box sx={{ display: "flex", gap: "16px", alignItems: "center" }}>
              <Box sx={{ width: "110px" }}>
                <CustomSelect
                  sx={{
                    // height: "68px !important",
                    // mb: "0px !important",
                    borderRadius: "10px",
                    "& .MuiOutlinedInput-root": {
                      // height: "68px",
                    },
                  }}
                  disableLabel={true}
                  label="Code"
                  value={formData.countryCode}
                  onChange={(e) =>
                    handleFormDataChange("countryCode", e.target.value)
                  }
                  options={countryCodeOptions}
                  required
                  customHeight="auto"
                  customMarginBottom="0px"
                />
              </Box>
              <Box sx={{ width: "100%" }}>
                <CustomTextField
                  sx={{
                    height: "auto !important",
                    mb: "0px !important",
                  }}
                  label="Phone Number"
                  value={formData.phoneNumber}
                  required={true}
                  onChange={(e) => {
                    const value = e.target.value;
                    // Only allow numbers 0-9
                    if (/^[0-9]*$/.test(value)) {
                      handleFormDataChange("phoneNumber", value);
                    }
                  }}
                  type="tel"
                  inputProps={{
                    maxLength: 10,
                    pattern: "[0-9]*",
                  }}
                />
              </Box>
            </Box>
          ) : (
            <CustomTextField
              sx={{
                height: "fit-content !important",
                mb: "0px !important",
              }}
              label="Email"
              value={formData.email}
              required={true}
              onChange={(e) =>
                handleFormDataChange("email", e?.target?.value?.toLowerCase())
              }
              endAdornment={
                <Tooltip
                  title="Your Email will be used to login and sign-up"
                  enterTouchDelay={0}
                >
                  <InfoIconOutlined
                    sx={{
                      color: "#706E75",
                      cursor: "pointer",
                      fontSize: "20px",
                    }}
                  />
                </Tooltip>
              }
            />
          )}
        </Box>
        <Box sx={{ mt: "36px" }}>
          <Button
            variant="contained"
            sx={{
              background: "linear-gradient(90deg, #FF3951 0%, #FF3951 100%)",
              color: "#FFFFFF",
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "16px",
              px: 5,
              py: "8px",
              borderRadius: "4px",
              transition: "all 0.3s ease",
              textTransform: "none",
              boxShadow: "none",
              "&:hover": {
                background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
                boxShadow: "none",
              },
              "&:disabled": {
                background: "linear-gradient(90deg, #F6F6F6 0%, #F6F6F6 100%)",
                opacity: 1,
                color: "#333333",
                cursor: "not-allowed",
              },
            }}
            fullWidth
            onClick={handleNextStep}
            disabled={
              isVerifyLoading ||
              !formData.name ||
              (loginVia !== "phone"
                ? !formData.phoneNumber
                : !formData.email || !isEmailValid)
            }
          >
            {isVerifyLoading ? "Processing..." : "Continue"}
          </Button>
        </Box>
      </Box>
    );
  };

  return (
    <Modal
      open={open}
      // onClose={handleClose}
      BackdropProps={{
        sx: {
          backdropFilter: "blur(16px)",
          background: "rgba(51, 51, 51, 0.4)",
        },
      }}
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "white",
          borderRadius: "20px",
          maxWidth: "1234px",
          width: "calc(100vw - 160px)", // 100vw - 80px - 80px
          height: "calc(100dvh - 80px)",
          maxHeight: "613px",
          outline: "none",
          boxShadow: 24,
          background: "linear-gradient(90deg, #FEEBEE 0%, #F2EBFA 125.89%)",
          p: 0,
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-end",
          overflow: "visible", // Make it scrollable
          // Responsive adjustments
          "@media (max-width: 1200px)": {
            width: "calc(100vw - 80px)",
            maxWidth: "550px",
          },
          "@media (max-width: 900px)": {
            width: "calc(100vw - 40px)",
          },
          "@media (max-width: 600px)": {
            width: "calc(100dvw)", // Even smaller margins on very small screens
            borderRadius: "0px",
            height: "100dvh",
            maxHeight: "100%",
          },
        }}
      >
        <Box
          sx={{
            display: { xs: "none", lg: "block" },
            position: "absolute",
            top: "-40px",
            left: { xs: "40px", lg: "60px" },
            zIndex: 1,
            width: { xs: "300px", lg: "460px" },
            height: { xs: "400px", lg: "695px" },
          }}
        >
          <Box
            component="svg"
            sx={{
              width: "100%",
              height: "100%",
            }}
            viewBox="0 0 460 695"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.08016e-07 23C4.06533e-07 10.2975 10.2975 0 23 0H349.105C361.808 0 372.105 10.2975 372.105 23V76.1268C372.105 88.8293 382.403 99.1268 395.105 99.1268H437C449.703 99.1268 460 109.424 460 122.127V671.5C460 684.203 449.703 694.5 437 694.5H23C10.2975 694.5 2.70116e-05 684.203 2.65101e-05 671.5L9.08016e-07 23Z"
              fill="white"
            />
          </Box>
          <Box
            sx={{
              zIndex: 2,
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              width: "100%",
              height: "100%",
              display: "flex",
              overflow: "visible",
            }}
          >
            <Box
              sx={{
                display: "flex",
                px: "28px",
                pt: "132px",
                pb: "34px",
                boxSizing: "border-box",
                flexDirection: "column",
                justifyContent: "space-between",
                gap: "24px",
              }}
            >
              <Box>
                <Box
                  sx={{
                    fontFamily: "Lora",
                    fontWeight: 500,
                    fontSize: "32px",
                    lineHeight: "40px",
                    color: "#FF3951",
                  }}
                >
                  Jet-Set Without the Stress.
                </Box>
                <Box
                  sx={{
                    fontFamily: "Roboto",
                    fontWeight: 400,
                    fontSize: "24px",
                    lineHeight: "32px",
                    color: "#000000",
                    mt: "16px",
                  }}
                >
                  Your AI travel assistant is on standby
                </Box>
                <Box
                  sx={{
                    fontFamily: "Roboto",
                    fontWeight: 400,
                    fontSize: "16px",
                    lineHeight: "1.6",
                    letterSpacing: "0.05em",
                    color: "#333333",
                    mt: "12px",
                  }}
                >
                  Share your mobile number or email to receive personalized
                  updates, seamless support, and a better overall experience
                  tailored just for you.
                </Box>
              </Box>
              <Box
                sx={{
                  mt: "auto",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  position: "relative",
                }}
              >
                <Box
                  component="img"
                  src="/static/onboarding/aiSticker.png"
                  sx={{
                    width: "325px",
                    height: "133.5px",
                  }}
                />
                <Box
                  component="img"
                  src="/static/onboarding/aiRobot.png"
                  sx={{
                    width: "139px",
                    height: "150px",
                    position: "absolute",
                    top: "-106px",
                    left: "-39px",
                  }}
                />
              </Box>
            </Box>
          </Box>
        </Box>
        <Box
          sx={{
            position: "relative",
            display: "flex",
            width: "100%",
            height: "100%",
            flexDirection: "column",
          }}
        >
          {/* Elliptical element */}
          <Box
            sx={{
              position: "absolute",
              width: "356px",
              height: "356px",
              left: "50%",
              top: "50%",
              transform: "translate(-50%, -50%)",
              background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
              opacity: 0.5,
              filter: "blur(174px)",
              borderRadius: "50%",
              zIndex: 0,
            }}
          />

          <IconButton
            onClick={handleClose}
            sx={{
              alignSelf: "flex-end",
              zIndex: 10,
              position: "absolute",
              right: "16px",
              top: "16px",
            }}
          >
            <CloseOutline />
          </IconButton>
          <Box>
            <Box
              sx={{
                width: "100%",
                flex: 1,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                position: "relative",
                zIndex: 1, // Ensure content is above the ellipse
                pl: { xs: "0px", lg: "580px" },
                pt: "60px",
                boxSizing: "border-box",
              }}
            >
              {step === 1 && stepOneData()}
              {step === 2 && stepTwoData()}
              {step === 3 && stepThreeData()}
              {step === 4 && stepFourData()}
            </Box>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default OnboardingModal;
