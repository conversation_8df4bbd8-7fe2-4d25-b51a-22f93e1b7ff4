import React, { useState } from "react";
import { Box, Button, Modal, Typography } from "@mui/material";
import StyledLaunchAITravel from "./StyledLaunchAITravel";
import BrandOnboarding from "../../pages/BrandOnboarding";
import successTickIcon from "../../assets/svg/successTickIcon.svg";

const LaunchAITravel = ({ handleScheduleDemo = () => {} }) => {
  const [open, setOpen] = useState(false);
  const [openSuccess, setOpenSuccess] = useState(false);

  const handleSuccess = () => {
    setOpen(false);
    setOpenSuccess(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      <StyledLaunchAITravel>
        <Box className="hero-content">
          <Typography className="main-title">
            Launch Your <span className="gradient-title">AI Travel</span>
            <br /> Business in Minutes -
          </Typography>

          <Typography className="sub-title">No Experience Needed</Typography>

          <Typography className="description-desc-2">
            Most travel partners face slow, complex integrations and lack the
            infrastructure to deliver intelligent, real-time travel planning and
            fulfillment at scale.
          </Typography>

          <Box className="cta-buttons">
            <Button className="deals-btn" onClick={handleScheduleDemo}>
              Schedule a demo
            </Button>
            <Button className="ai-btn" onClick={() => setOpen(true)}>
              Get Started
            </Button>
          </Box>
        </Box>
      </StyledLaunchAITravel>
      <Modal
        open={open}
        onClose={null}
        disableEscapeKeyDown
        BackdropProps={{
          sx: {
            backdropFilter: "blur(3px)",
          },
          onClick: () => null,
        }}
      >
        <Box className="onboarding-modal-container">
          <BrandOnboarding
            handleSuccess={handleSuccess}
            setClose={handleClose}
          />
        </Box>
      </Modal>
      <Modal
        open={openSuccess}
        onClose={null}
        disableEscapeKeyDown
        BackdropProps={{
          sx: {
            backdropFilter: "blur(3px)",
          },
          onClick: () => null,
        }}
      >
        <Box className="onboarding-success-modal" sx={{ outline: "none" }}>
          <Box className="success-icon">
            <img src={successTickIcon} alt="success-tick" />
          </Box>
          <Box className="success-content">
            <Typography className="success-title">Congratulations</Typography>
            <Typography className="success-description">
              You're on the waitlist! Keep an eye on your inbox — we'll be in
              touch soon
            </Typography>
          </Box>
          <Box className="success-btn">
            <Button className="close-btn" onClick={() => setOpenSuccess(false)}>
              Continue Browsing
            </Button>
          </Box>
        </Box>
      </Modal>
    </>
  );
};

export default LaunchAITravel;
