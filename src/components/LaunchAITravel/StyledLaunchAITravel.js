import { styled } from "@mui/material/styles";

const StyledLaunchAITravel = styled("div")(
  ({ theme }) => `
  position: relative;
  padding: 150px 120px 0px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: 600px) {
    padding: 100px 20px 0px;
    justify-content: flex-start;
    text-align: left;
  } 

  .onboarding-modal-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 1200px;
    max-height: 90vh;
    overflow-y: auto;
    background-color: #FFF9F9 !important;
    border-radius: 8px;
    outline: none;
    padding: 20px;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }

  .hero-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
  }

  .main-title {
    font-family: 'Lora', serif;
    font-size: 52px;
    font-weight: 500;
    color: ${theme.palette.secondary.main};
    line-height: 100%;
    margin-bottom: 16px;
  }

  .gradient-title {
    font-family: 'Lora', serif;
    font-size: 52px;
    font-weight: 500;
    line-height: 100%;
    background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 16px;
  }

  .sub-title {
    font-family: 'Lora', serif;
    font-size: 36px;
    font-weight: 500;
    line-height: 100%;
    color: ${theme.palette.secondary.main};
    margin: 30px 0px 20px;
  }

  .description-desc-2 {
    font-family: 'Roboto', sans-serif;
    font-size: 18px;
    font-weight: 400;
    color: ${theme.palette.custom.subText};
    line-height: 160%;
    letter-spacing: 0.05em;
    margin: 16px 0;
    
  }

  .cta-buttons {
    position: relative;
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-top: 40px;
    margin-bottom: 40px;
    height: 44px;

    .deals-btn {
      padding: 6px 30px;
      border: 1px solid #FF3951;
      color: #FF3951;
      font-size: 14px;
      font-weight: 600;
      border-radius: 4px;
      text-transform: none;
      background: white;
    }

.ai-btn {
  position: relative;
  padding: 6px 30px;
  background: #FF3951;
  color: white;
  font-size: 14px;
  font-weight: 600;
  border-radius: 4px;
  text-transform: none;
  overflow: hidden;
  z-index: 1;
  cursor: pointer;
}

.ai-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
  background-size: 100% 200%;
  background-position: left center;
  transition: background-position 4s ease, opacity 0.5s ease;
  z-index: -1;
  opacity: 0;
}

.ai-btn:hover::before {
  background-position: right center;
  opacity: 1;
}
  
  }

@media (max-width: 768px) {
    padding: 95px 100px 0px;
}

@media (max-width: 600px) {
  padding: 78px 20px 0px 20px;
  .main-title {
    font-size: 24px;
  }
  .gradient-title {
    font-size: 24px;
  }
  .sub-title {
    font-size: 18px;
    margin: 10px 0px 0px;
  }
  .description-desc-2 {
    font-size: 12px;
  } 
  .cta-buttons {
    margin-top: 20px;
    margin-bottom: 20px;
    height: 40px;
    justify-content: flex-start;
  }
  .deals-btn {
    padding: 8px 12px !important;
    font-size: 12px !important;
  }  
  .ai-btn {
    padding: 8px 12px !important;
    font-size: 12px !important;
  }  
}
`
);

export default StyledLaunchAITravel;

