import { styled } from "@mui/material/styles";
import howitWorksBg from "../../assets/svg/howItWorksBg.svg";

const StyledHowZuummWorks = styled("div")(
  ({ theme }) => `
  margin-top: 30px;
  padding: 60px 0;
  background: url(${howitWorksBg}) no-repeat center center;
  background-size: cover;

  @media (max-width: 600px) {
    padding: 25px 0px !important;
  }
  
  @keyframes gradientMove {
    0% {
      background-position: 100% 0;
    }
    100% {
      background-position: 0% 0;
    }
  }

  .content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    text-align: center;
  }

  .main-title {
    font-family: '<PERSON>ra', serif;
    font-size: 32px;
    line-height: 45px;
    font-weight: 700;
    color: ${theme.palette.secondary.main};
  }

  .subtitle {
    font-family: 'Lora', serif;
    font-size: 32px;
    line-height: 45px;
    font-weight: 700;
     background: linear-gradient(90deg, #FF3951 25%, #5530F9 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
  }

  .description {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    color: ${theme.palette.custom.subText};
    font-weight: 400;
    margin: 15px 0 40px;
    line-height: 150%;
    letter-spacing: 0.05em;
  }

  .steps-container {
    display: flex;
    flex-direction: row;
    gap: 20px;
    margin-bottom: 50px;
    align-items: center;
    padding: 0px 20px;
  }

  .step-card {
    width: 30%;
    height: 220px;
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    align-items: left;
    text-align: left;

    .icon-wrapper {
      width: 44px;
      height: 44px;
      margin-bottom: 18px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .icons {
      width: 38px !important;
      height: 38px !important;
    }

    .step-title {
      font-family: 'Lora', serif;
      font-size: 18px;
      font-weight: 600;
      color: ${theme.palette.custom.darkBlue2};
      margin-bottom: 8px;
      line-height: 100%;
    }

    .step-description {
      font-family: 'Roboto', sans-serif;
      font-size: 12px;
      font-weight: 500;
      color: ${theme.palette.custom.subText};
      margin-bottom: 16px;
      line-height: 18px;
    }

    .step-subtext {
      font-family: 'Roboto', sans-serif;
      font-size: 14px;
      color: ${theme.palette.custom.subText};
      font-weight: 400;
      line-height: 24px;
      letter-spacing: 0.05em;
    }
  }

  .learn-more-btn {
    position: relative;
  padding: 6px 30px;
  background: #FF3951;
  color: white;
  font-size: 14px;
  font-weight: 600;
  border-radius: 4px;
  text-transform: none;
  overflow: hidden;
  z-index: 1;
  cursor: pointer;
  box-shadow: none;
  height: 44px;
  }
  .learn-more-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
  background-size: 100% 200%;
  background-position: left center;
  transition: background-position 4s ease, opacity 0.5s ease;
  z-index: -1;
  opacity: 0;
}

.learn-more-btn:hover::before {
  background-position: right center;
  opacity: 1;
}

  @media (max-width: 1024px) {
    .steps-container {
      
    }
  }
    @media (max-width: 786px) {
      .step-card {
        height: 255px;
      }
    }

    @media (max-width: 600px) {
    .content-wrapper {
      text-align: left;
    }
    .main-title {
      font-size: 24px;
      line-height: 25px;
    }
    .subtitle {
      font-size: 24px;
    }
    .description {
      font-size: 16px;
      margin: 0px;
      margin-bottom: 20px;
    }  
    .steps-container {
      gap: 24px;
      flex-direction: column;
      margin-bottom: 30px;
    }
    .step-card {
      width: 90%;
    }
    .learn-more-btn-wrapper {
      display: flex;
      justify-content: center;
    }
  }

`
);

export default StyledHowZuummWorks;
