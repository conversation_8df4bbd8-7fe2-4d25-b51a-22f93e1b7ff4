import React from 'react';
import { Box, Typography } from "@mui/material";
import StyledHowZuummWorks from './StyledHowZuummWorks';
import destinationIcon from '../../assets/svg/destinationIcon.svg';
import optionsIcon from '../../assets/svg/optionsIcon.svg';
import bookAndGoIcon from '../../assets/svg/bookAndGoIcon.svg';


const HowZuummWorks = () => {
  const steps = [
    {
      icon: destinationIcon,
      title: 'Tell Us What You Want',
      description: 'Start with a thought — or let our AI inspire you based on your vibe, budget, and past travel style.',
      subtext: 'Got questions? Our AI agents & Concierge answer them instantly - no waiting'
    },
    {
      icon: optionsIcon,
      title: 'Tailor your options',
      description: "ZUUMM'S AI serves up multiple curated combinations — flights, hotels, and experiences.",
      subtext: 'Compare, customize, and fine-tune your perfect getaway.'
    },
    {
      icon: bookAndGoIcon,
      title: 'Book & Go',
      description: 'Lock in your favorite trip with secure payment.',
      subtext: 'Get a ready-to-go itinerary — plus visa, insurance, and local support, all handled.'
    }
  ];

  return (
    <StyledHowZuummWorks>
      <Box className="content-wrapper">
        <Typography className="main-title">How Zuumm Works</Typography>
        <Typography className="subtitle">It's as Easy as 1, 2, 3</Typography>

        <Typography variant="body1" className="description">
          Watch ZUUMM spin up the perfect trip like magic. No stress, just wow
        </Typography>

        <Box className="steps-container">
          {steps.map((step, index) => (
            <Box key={index} className="step-card">
              <Box className="icon-wrapper">
                <img src={step.icon} alt={step.title} className="icons" />
              </Box>
              <Typography className="step-title">{step.title}</Typography>
              <Typography className="step-description">
                {step.description}
              </Typography>
              <Typography className="step-subtext">{step.subtext}</Typography>
            </Box>
          ))}
        </Box>
        {/* <Box className="learn-more-btn-wrapper">
          <Button variant="contained" className="learn-more-btn">
            Learn More
          </Button>
        </Box> */}
      </Box>
    </StyledHowZuummWorks>
  );
};

export default HowZuummWorks; 