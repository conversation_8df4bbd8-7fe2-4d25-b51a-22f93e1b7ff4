import React, { useState } from "react";
import {
  Box,
  IconButton,
  Fade,
  Typography,
  List,
  ListItem,
  Skeleton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
} from "@mui/material";
import MenuOpenIcon from "@mui/icons-material/MenuOpen";
import MenuIcon from "@mui/icons-material/Menu";
import { EditIcon, Forward } from "../../common/SvgIcon";
import ChatMenu from "./ChatMenu";
import ConversationsSkeleton from "../skeleton/ConversationsSkeleton";
import ConfirmationModal from "../../common/ConfirmationModal";

const EXPANDED_WIDTH = 250;
const COLLAPSED_WIDTH = 60;

const AiChatLeftSidebar = ({
  conversations = [],
  isLoading = false,
  newChat = true,
  handleNewChat = () => {},
  handleSelectConversation = () => {},
  selectedConversation = null,
  handleDeleteConversation = () => {},
  handleRenameConversation = () => {},
  expanded = true,
  setExpanded = () => {},
}) => {
  const [renameModalOpen, setRenameModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [renameConversationId, setRenameConversationId] = useState(null);
  const [deleteConversationId, setDeleteConversationId] = useState(null);
  const [newTitle, setNewTitle] = useState("");

  const [preferences] = useState([
    { id: "preference-1", title: "Language" },
    { id: "preference-2", title: "Theme" },
    { id: "preference-3", title: "Notifications" },
    { id: "preference-4", title: "Privacy" },
    { id: "preference-5", title: "Help" },
  ]);

  const [preferencesLoading] = useState(false);

  const handleNewChatClick = () => {
    handleNewChat();
  };

  const handleChatOptions = (chatId, action) => {
    if (action === "delete") {
      setDeleteConversationId(chatId);
      setDeleteModalOpen(true);
    } else if (action === "edit") {
      // Find the current conversation title
      const conversation = conversations.find(
        (conv) => conv.conversation_id === chatId
      );
      setNewTitle(
        conversation?.title || conversation?.last_message_preview || ""
      );
      setRenameConversationId(chatId);
      setRenameModalOpen(true);
    }
  };

  const handleRenameSubmit = () => {
    if (newTitle.trim() && renameConversationId) {
      handleRenameConversation(renameConversationId, newTitle.trim());
      setRenameModalOpen(false);
      setNewTitle("");
      setRenameConversationId(null);
    }
  };

  const handleRenameCancel = () => {
    setRenameModalOpen(false);
    setNewTitle("");
    setRenameConversationId(null);
  };

  const handleDeleteConfirm = () => {
    if (deleteConversationId) {
      handleDeleteConversation(deleteConversationId);
      setDeleteModalOpen(false);
      setDeleteConversationId(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModalOpen(false);
    setDeleteConversationId(null);
  };

  return (
    <Box
      sx={{
        width: expanded ? EXPANDED_WIDTH : COLLAPSED_WIDTH,
        height: "100%",
        position: "relative",
        transition: "width 0.6s cubic-bezier(0.77, 0, 0.175, 1)",
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        zIndex: 3,
      }}
    >
      <IconButton
        onClick={() => setExpanded((prev) => !prev)}
        sx={{
          position: "absolute",
          top: 18,
          right: expanded ? 10 : 8,
          zIndex: 10,
          background: "#fff",
          boxShadow: "0px 0px 10px 0px rgba(255, 57, 80, 0.1)", // use this color for shadow FF3951
          transition:
            "right 0.6s cubic-bezier(0.77, 0, 0.175, 1), transform 0.6s cubic-bezier(0.77, 0, 0.175, 1)",
          transform: expanded ? "none" : "rotate(180deg)",
          "&:hover": {
            background: "#ff395032",
          },
        }}
      >
        {expanded ? (
          <MenuOpenIcon sx={{ color: "#FF3951" }} />
        ) : (
          <MenuIcon sx={{ color: "#FF3951" }} />
        )}
      </IconButton>
      <Box
        sx={{
          width: "100%",
          height: "100%",
          display: "flex",
          flexDirection: "column",
          mt: 8,
          boxSizing: "border-box",
        }}
      >
        <Box
          onClick={handleNewChatClick}
          sx={{
            display: "flex",
            alignItems: "center",
            boxSizing: "border-box",
            width: "max-content",
            minWidth: "100%",
            gap: "12px",
            padding: expanded ? "17px 24px" : "17px 20px",
            color: "#FF3951",
            cursor: "pointer",
            transition: "transform 0.3s ease",
            justifyContent: !expanded ? "center" : "flex-start",
            mb: 2,
            "&:hover": {
              "& .new-chat-text": {
                textDecoration: "underline",
              },
            },
          }}
        >
          <EditIcon width={20} height={20} color="#FF3951" />

          <Fade in={expanded} timeout={700}>
            <Box>
              <Typography
                className="new-chat-text"
                sx={{
                  fontFamily: "Roboto",
                  fontSize: "14px",
                  lineHeight: "16px",
                  fontWeight: 500,
                  letterSpacing: "0.05em",
                }}
              >
                Start New Chat
              </Typography>
            </Box>
          </Fade>
        </Box>
        <Fade in={expanded} timeout={700}>
          <Box
            sx={{
              width: "100%",
              height: "100%",
              display: "flex",
              flexDirection: "column",
              boxSizing: "border-box",
            }}
          >
            {/* Recent Chats Section */}
            <Box>
              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontSize: "12px",
                  lineHeight: "14px",
                  fontWeight: 500,
                  letterSpacing: "0.05em",
                  color: "#1E3A8A",
                  mb: 1,
                  pl: 3,
                }}
              >
                Chats
              </Typography>
              <List
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  // gap: "4px",
                  padding: 0,
                  maxHeight: "150px",
                  overflowY: "auto",
                  "&::-webkit-scrollbar": {
                    background: "transparent",
                    width: "3px",
                    borderRadius: "3px",
                  },
                  "&::-webkit-scrollbar-thumb": {
                    background: "#ff39504b",
                  },
                  "&::-webkit-scrollbar-track": {
                    background: "transparent",
                  },
                }}
              >
                {isLoading ? (
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      gap: "4px",
                      padding: "0px 12px 0px 24px",
                    }}
                  >
                    {[1, 2, 3].map((index) => (
                      <ConversationsSkeleton key={index} />
                    ))}
                  </Box>
                ) : conversations.length > 0 ? (
                  conversations.map((chat) => (
                    <ListItem
                      key={chat.conversation_id}
                      sx={{
                        padding: "0px 12px 0px 24px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        borderBottom: "1px solid #F6F6F6",
                        cursor: "pointer",
                        backgroundColor:
                          chat.conversation_id === selectedConversation
                            ? "#FFFFFF"
                            : "transparent",
                        "&:hover": {
                          backgroundColor: "#FFFFFF",
                          "& .chat-options": {
                            display: "block",
                          },
                        },
                        "&.chat-options": {
                          display: "none",
                          transition: "all 0.3s ease",
                          "&.open-menu": {
                            display: "block",
                          },
                          "&:hover": {
                            transform: "scale(1.1)",
                            ".open-menu": {
                              display: "block",
                            },
                          },
                        },
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSelectConversation(chat.conversation_id);
                      }}
                    >
                      <Typography
                        sx={{
                          padding: "11px 0px",
                          fontFamily: "Roboto",
                          fontSize: "12px",
                          lineHeight: "14px",
                          fontWeight: 500,
                          color:
                            chat.conversation_id === selectedConversation
                              ? "#333333"
                              : "#706E75",
                          maxWidth: "170px",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {chat?.title || chat?.last_message_preview || ""}
                      </Typography>
                      <ChatMenu
                        chatId={chat.conversation_id}
                        handleChatOptions={handleChatOptions}
                      />
                    </ListItem>
                  ))
                ) : (
                  <Box
                    sx={{
                      display: "flex",
                      minHeight: "114px",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Typography
                      sx={{
                        fontFamily: "Roboto",
                        fontSize: "12px",
                        lineHeight: "14px",
                        color: "#706E75",
                        textAlign: "center",
                      }}
                    >
                      No conversations yet
                    </Typography>
                  </Box>
                )}
              </List>
            </Box>
            {/* Horizontal Line */}
            <Box
              sx={{
                width: "100%",
                height: "1px",
                backgroundColor: "#FF39510D",
                margin: "20px 0px",
              }}
            />
            {/* Preferences Section */}
            <Box sx={{ mb: 2 }}>
              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontSize: "12px",
                  lineHeight: "14px",
                  fontWeight: 500,
                  letterSpacing: "0.05em",
                  color: "#1E3A8A",
                  mb: 1,
                  pl: 3,
                }}
              >
                Preferences
              </Typography>
              <List
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "4px",
                  padding: 0,
                  maxHeight: "150px",
                  overflowY: "auto",
                  "&::-webkit-scrollbar": {
                    background: "transparent",
                    width: "3px",
                    borderRadius: "3px",
                  },
                  "&::-webkit-scrollbar-thumb": {
                    background: "#ff39504b",
                  },
                  "&::-webkit-scrollbar-track": {
                    background: "transparent",
                  },
                }}
              >
                {preferencesLoading ? (
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      gap: "4px",
                      padding: "0px 12px 0px 24px",
                    }}
                  >
                    {[1, 2, 3].map((index) => (
                      <Box
                        key={index}
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                          padding: "11px 0px",
                        }}
                      >
                        <Skeleton
                          variant="text"
                          width={Math.random() * 100 + 80}
                          height={14}
                          sx={{
                            backgroundColor: "#f0f0f0",
                            "&::after": {
                              background:
                                "linear-gradient(90deg, transparent, #e0e0e0, transparent)",
                            },
                          }}
                        />
                        <Skeleton
                          variant="circular"
                          width={16}
                          height={16}
                          sx={{
                            backgroundColor: "#f0f0f0",
                            "&::after": {
                              background:
                                "linear-gradient(90deg, transparent, #e0e0e0, transparent)",
                            },
                          }}
                        />
                      </Box>
                    ))}
                  </Box>
                ) : (
                  preferences.map((chat) => (
                    <ListItem
                      key={chat.id}
                      sx={{
                        padding: "11px 12px 11px 24px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        cursor: "pointer",
                        backgroundColor:
                          chat.conversation_id === selectedConversation
                            ? "#FFFFFF"
                            : "transparent",
                        "&:hover": {
                          backgroundColor: "#FFFFFF",
                        },
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        // handleSelectConversation(chat.external_id);
                      }}
                    >
                      <Typography
                        sx={{
                          fontFamily: "Roboto",
                          fontSize: "12px",
                          lineHeight: "14px",
                          fontWeight: 500,
                          color:
                            chat.conversation_id === selectedConversation
                              ? "#333333"
                              : "#706E75",
                          maxWidth: "170px",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {chat.title}
                      </Typography>
                      <Forward />
                    </ListItem>
                  ))
                )}
              </List>
            </Box>
            {/* Support Section */}
            <Box
              sx={{
                marginTop: "auto",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                padding: "15px 12px 15px 24px",
                cursor: "pointer",
                color: "#706E75",
                transition: "all 0.3s ease",
                "&:hover": {
                  backgroundColor: "#FFFFFF",
                  color: "#333333",
                },
              }}
            >
              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontSize: "16px",
                  lineHeight: "19px",
                  fontWeight: 500,
                }}
              >
                Support
              </Typography>
              <Forward />
            </Box>
          </Box>
        </Fade>
      </Box>
      <Dialog
        open={renameModalOpen}
        onClose={handleRenameCancel}
        PaperProps={{
          sx: {
            borderRadius: "12px",
            minWidth: "400px",
          },
        }}
      >
        <DialogTitle
          sx={{
            fontFamily: "Roboto",
            fontSize: "18px",
            fontWeight: 500,
            color: "#333333",
            pb: 1,
          }}
        >
          Rename Chat
        </DialogTitle>
        <DialogContent sx={{ pt: 0 }}>
          <TextField
            autoFocus
            margin="dense"
            label="New Title"
            type="text"
            fullWidth
            variant="outlined"
            value={newTitle}
            onChange={(e) => setNewTitle(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === "Enter") {
                handleRenameSubmit();
              }
            }}
            sx={{
              mt: 1,
              "& .MuiOutlinedInput-root": {
                borderRadius: "8px",
              },
            }}
          />
        </DialogContent>
        <DialogActions sx={{ p: 2, pt: 0 }}>
          <Button
            onClick={handleRenameCancel}
            sx={{
              color: "#706E75",
              textTransform: "none",
              fontWeight: 500,
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleRenameSubmit}
            disabled={!newTitle.trim()}
            sx={{
              backgroundColor: "#FF3951",
              color: "white",
              textTransform: "none",
              fontWeight: 500,
              "&:hover": {
                backgroundColor: "#E62E47",
              },
              "&:disabled": {
                backgroundColor: "#E0E0E0",
                color: "#9E9E9E",
              },
            }}
          >
            Rename
          </Button>
        </DialogActions>
      </Dialog>
      <ConfirmationModal
        open={deleteModalOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Conversation"
        description="Are you sure you want to delete this conversation? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
      />
    </Box>
  );
};

export default AiChatLeftSidebar;
