import React from "react";
import { Box } from "@mui/material";
import {
  PackageSidebar,
  CustomizeSidebar,
  HotelSidebar,
} from "./sidebar-types";
import PreferencePackage from "./sidebar-types/PreferencePackage";

// Right sidebar version
const AiChatRightSidebar = ({
  sidebarType = "customize",
  open = false,
  onClose,
  packageData = null,
  hotelData = null,
  preferencePackageData = null,
  handleUpdatePreferencePackage = null,
}) => {
  const SIDEBAR_WIDTHS = {
    "preference-package": 363,
    customize: 363,
    package: 577,
    hotel: 577,
  };
  const expandedWidth = SIDEBAR_WIDTHS[sidebarType] || SIDEBAR_WIDTHS.customize;

  const renderSidebarContent = () => {
    switch (sidebarType) {
      case "package":
        return (
          <PackageSidebar
            packageWidth={expandedWidth}
            onClose={onClose}
            packageData={packageData}
          />
        );
      case "customize":
        return (
          <CustomizeSidebar packageWidth={expandedWidth} onClose={onClose} />
        );
      case "preference-package":
        return (
          <PreferencePackage
            packageWidth={expandedWidth}
            onClose={onClose}
            preferencePackageData={preferencePackageData}
            handleUpdatePreferencePackage={handleUpdatePreferencePackage}
          />
        );
      case "hotel":
        return (
          <HotelSidebar
            hotelWidth={expandedWidth}
            onClose={onClose}
            hotelData={hotelData}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Box
      sx={{
        width: open ? expandedWidth : 0,
        height: "100%",
        position: "relative",
        transition: "width 0.6s cubic-bezier(0.77, 0, 0.175, 1)",
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        zIndex: 3,
      }}
    >
      {renderSidebarContent()}
    </Box>
  );
};

export default AiChatRightSidebar;
