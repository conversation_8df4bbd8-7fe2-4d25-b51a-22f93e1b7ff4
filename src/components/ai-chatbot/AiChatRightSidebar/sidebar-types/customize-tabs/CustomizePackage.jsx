/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react";
import { Box, Typography, IconButton } from "@mui/material";
import CustomSelect from "../../../../common/CustomSelect";
import CustomTextField from "../../../../common/CustomTextField";
import PrimaryButton from "../../../../common/Button/PrimaryButton";
import { Add, Sub } from "../../../../common/SvgIcon";
import { ToastNotifyError } from "../../../../Toast/ToastNotify";

// Custom Counter Component
const CustomCounter = ({
  label,
  value,
  onChange,
  min = 0,
  max = 10,
  disabled = false,
}) => {
  const handleIncrease = () => {
    if (value < max && !disabled) {
      onChange(value + 1);
    }
  };

  const handleDecrease = () => {
    if (value > min && !disabled) {
      onChange(value - 1);
    }
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
      <Typography
        sx={{
          fontFamily: "Roboto",
          fontWeight: 400,
          fontSize: "12px",
          lineHeight: "14px",
          color: "#333333",
        }}
      >
        {label}
      </Typography>
      <Box
        sx={{
          border: "1px solid #E5E7EB",
          background: "#FFF",
          borderRadius: "8px",
          padding: "8px 12px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          gap: "8px",
          minWidth: "120px",
        }}
      >
        <IconButton
          variant="text"
          color="default"
          sx={{ p: "0px" }}
          disabled={value <= min || disabled}
          onClick={handleDecrease}
        >
          <Sub
            sx={{ width: "16px", height: "16px" }}
            active
            disabled={value <= min || disabled}
          />
        </IconButton>
        <Box
          sx={{
            color: "#333333",
            fontFamily: "Roboto",
            fontSize: "14px",
            fontWeight: 400,
            lineHeight: "16px",
            minWidth: "20px",
            textAlign: "center",
          }}
        >
          {value}
        </Box>
        <IconButton
          variant="text"
          color="default"
          sx={{ p: "0px" }}
          disabled={value >= max || disabled}
          onClick={handleIncrease}
        >
          <Add type="secondary" disabled={value >= max || disabled} />
        </IconButton>
      </Box>
    </Box>
  );
};

export const CustomizePackage = ({
  initialData = null,
  handleUpdatePreferencePackage = () => {},
}) => {
  // Initialize form data from initialData.options
  const initializeFormData = () => {
    if (!initialData?.options) {
      return {
        destination: "",
        travelType: "",
        adults: 1,
        children: 0,
        infants: 0,
        budget: "",
        days: 3,
        nights: 2,
      };
    }

    const formData = {};
    initialData.options.forEach((option) => {
      switch (option.option_type) {
        case "destination_name":
          formData.destination = option.value || "";
          break;
        case "travel_type":
          formData.travelType = option.value || "";
          break;
        case "num_adults":
          formData.adults = option.value || 1;
          break;
        case "num_children":
          formData.children = option.value || 0;
          break;
        case "num_infants":
          formData.infants = option.value || 0;
          break;
        case "budget":
          formData.budget = option.value || "";
          break;
        case "days":
          formData.days = option.value || 3;
          break;
        case "nights":
          formData.nights = option.value || 2;
          break;
        default:
          break;
      }
    });
    return formData;
  };

  const [formData, setFormData] = useState(initializeFormData);

  // Update form data when initialData changes
  useEffect(() => {
    setFormData(initializeFormData());
  }, [initialData]);

  const handleFormChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Generate options from initialData
  const getTravelTypeOptions = () => {
    if (!initialData?.options) return [];

    const travelTypeOption = initialData.options.find(
      (opt) => opt.option_type === "travel_type"
    );
    if (!travelTypeOption?.options) return [];

    return travelTypeOption.options.map((option) => ({
      value: option,
      label: option.charAt(0).toUpperCase() + option.slice(1),
    }));
  };

  const getBudgetOptions = () => {
    if (!initialData?.options) return [];

    const budgetOption = initialData.options.find(
      (opt) => opt.option_type === "budget"
    );
    if (!budgetOption?.options) return [];

    return budgetOption.options.map((option) => ({
      value: option,
      label: option.charAt(0).toUpperCase() + option.slice(1),
    }));
  };

  // Get min/max values from initialData
  const getCounterConfig = (optionType) => {
    if (!initialData?.options) return { min: 0, max: 10 };

    const option = initialData.options.find(
      (opt) => opt.option_type === optionType
    );
    if (!option) return { min: 0, max: 10 };

    return {
      min: option.min || 0,
      max: option.max || 10,
    };
  };

  const travelTypeOptions = getTravelTypeOptions();
  const budgetOptions = getBudgetOptions();
  const adultsConfig = getCounterConfig("num_adults");
  const childrenConfig = getCounterConfig("num_children");
  const infantsConfig = getCounterConfig("num_infants");
  const daysConfig = getCounterConfig("days");
  const nightsConfig = getCounterConfig("nights");

  const handleUpdate = (payload) => {
    const difference = payload?.days - payload?.nights;

    if (!payload?.destination?.trim()) {
      ToastNotifyError("Destination is required");
      return;
    } else if (!payload?.travelType) {
      ToastNotifyError("Travel type is required");
      return;
    } else if (
      payload.travelType === "family" ||
      payload.travelType === "friends"
    ) {
      const totalPersons = payload.adults + payload.children + payload.infants;
      if (totalPersons < 1) {
        ToastNotifyError("At least 1 person is required");
        return;
      }
    } else if (!payload?.budget) {
      ToastNotifyError("Comfort zone is required");
      return;
    } else if (!payload?.days) {
      ToastNotifyError("No of Days is required");
      return;
    } else if (!payload?.nights) {
      ToastNotifyError("No of Nights is required");
      return;
    } else if (difference > 1 || difference < -1) {
      ToastNotifyError(
        "Days and nights must be equal or difference must be 1."
      );
      return;
    }

    const finalPayload = `Update my trip preferences: destination ${payload.destination}, ${payload.travelType} travel${payload?.travelType === "family" || payload?.travelType === "friends" ? ` with ${payload.adults} adults, ${payload.children} children, ${payload.infants} infants` : ""}, ${payload.budget} budget, ${payload.days} days and ${payload.nights} nights.`;

    handleUpdatePreferencePackage(finalPayload);
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        position: "relative",
      }}
    >
      {/* Header Section */}
      <Box sx={{ p: "24px 22px", background: "#FFFFFF" }}>
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontStyle: "normal",
            fontSize: "16px",
            lineHeight: "20px",
            letterSpacing: "0.02em",
            textTransform: "capitalize",
            color: "#1E3A8A",
            mb: 1,
          }}
        >
          Start With Your Preferences – You're in Control
        </Typography>

        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 400,
            fontStyle: "normal",
            fontSize: "12px",
            lineHeight: "18px",
            letterSpacing: "0.02em",
            textTransform: "capitalize",
            color: "#333333",
          }}
        >
          From beach type to travel dates, set your preferences now and update
          them anytime.
        </Typography>
      </Box>

      {/* Form Section */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          flex: 1,
          paddingBottom: "20px",
          p: "24px 22px",
        }}
      >
        {/* Destination Selection */}
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              lineHeight: "16px",
              color: "#333333",
            }}
          >
            Destination
          </Typography>
          <CustomTextField
            sx={{
              height: "fit-content !important",
              mb: "0px !important",
              "& .MuiInputBase-root": {
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "14px",
                lineHeight: "16px",
                color: "#333333",
              },
            }}
            placeholder="Enter destination"
            value={formData.destination}
            onChange={(e) => handleFormChange("destination", e.target.value)}
          />
        </Box>

        {/* Travel Type */}
        {travelTypeOptions.length > 0 && (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 500,
                fontSize: "14px",
                lineHeight: "16px",
                color: "#333333",
              }}
            >
              Travel Type
            </Typography>
            <CustomSelect
              hidePlaceholderAsValue={true}
              label=""
              value={formData.travelType}
              onChange={(e) => handleFormChange("travelType", e.target.value)}
              options={travelTypeOptions}
              placeholder="Select travel type"
              customMarginBottom="0px"
              sx={{
                "& .MuiSelect-select": {
                  fontSize: "14px !important",
                  lineHeight: "16px !important",
                },
              }}
            />
          </Box>
        )}

        {/* Number of Persons Travelling */}
        {formData?.travelType === "family" ||
        formData?.travelType === "friends" ? (
          <Box>
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 500,
                fontSize: "14px",
                color: "#333333",
                mb: 1,
              }}
            >
              Number of Persons Traveling
            </Typography>
            <Box
              sx={{
                display: "grid",
                width: "100%",
                gridTemplateColumns: "repeat(2, 1fr)",
                flexWrap: "wrap",
                gap: 2,
                alignItems: "center",
              }}
            >
              <CustomCounter
                label="Adults"
                value={formData.adults}
                onChange={(value) => handleFormChange("adults", value)}
                min={adultsConfig.min}
                max={adultsConfig.max}
              />
              <CustomCounter
                label="Children"
                value={formData.children}
                onChange={(value) => handleFormChange("children", value)}
                min={childrenConfig.min}
                max={childrenConfig.max}
              />
              <CustomCounter
                label="Infants"
                value={formData.infants}
                onChange={(value) => handleFormChange("infants", value)}
                min={infantsConfig.min}
                max={infantsConfig.max}
              />
            </Box>
          </Box>
        ) : null}

        {/* Budget */}
        {budgetOptions.length > 0 && (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 500,
                fontSize: "14px",
                color: "#333333",
              }}
            >
              Comfort zone
            </Typography>
            <CustomSelect
              label=""
              value={formData.budget}
              onChange={(e) => handleFormChange("budget", e.target.value)}
              options={budgetOptions}
              hidePlaceholderAsValue={true}
              placeholder="Select budget"
              customMarginBottom="0px"
              sx={{
                "& .MuiSelect-select": {
                  fontSize: "14px !important",
                  lineHeight: "16px !important",
                },
              }}
            />
          </Box>
        )}

        {/* Days and Nights */}
        <Box>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              color: "#333333",
              mb: 1,
            }}
          >
            Duration
          </Typography>
          <Box
            sx={{
              display: "flex",
              gap: 2,
              alignItems: "flex-start",
            }}
          >
            <CustomCounter
              label="Nights"
              value={formData.nights}
              onChange={(value) => handleFormChange("nights", value)}
              min={nightsConfig.min}
              max={nightsConfig.max}
            />
            <CustomCounter
              label="Days"
              value={formData.days}
              onChange={(value) => handleFormChange("days", value)}
              min={daysConfig.min}
              max={daysConfig.max}
            />
          </Box>
        </Box>
      </Box>

      {/* footer */}
      <Box
        sx={{
          position: "sticky",
          bottom: "-20px",
          background: "#FFF9F9",
          width: "100%",
          p: "20px 22px",
          boxSizing: "border-box",
        }}
      >
        <PrimaryButton width="100%" onClick={() => handleUpdate(formData)}>
          Update Preferences
        </PrimaryButton>
      </Box>
    </Box>
  );
};
