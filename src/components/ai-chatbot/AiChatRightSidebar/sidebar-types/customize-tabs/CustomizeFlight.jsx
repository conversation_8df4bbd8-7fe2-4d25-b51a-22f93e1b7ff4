import React, { useState } from "react";
import { Box, Typography, RadioGroup, FormControl } from "@mui/material";
import CustomSelect from "../../../../common/CustomSelect";
import PrimaryButton from "../../../../common/Button/PrimaryButton";
import PrimaryRadio from "../../../../common/RadioButton/PrimaryRadio";

export const CustomizeFlight = () => {
  const [formData, setFormData] = useState({
    travelType: "",
    tripType: "oneway",
    from: "",
    to: "",
    departureDate: "",
    returnDate: "",
    adults: "",
    children: "",
    infants: "",
    comfortZone: "",
    numberOfDays: "",
    category: "",
    specialFare: "",
    stops: "",
    timeOfDay: "",
  });

  const handleFormChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const cityOptions = [
    { value: "mumbai", label: "Mumbai" },
    { value: "delhi", label: "Delhi" },
    { value: "bangalore", label: "Bangalore" },
    { value: "chennai", label: "Chennai" },
    { value: "kolkata", label: "Kolkata" },
    { value: "hyderabad", label: "Hyderabad" },
    { value: "pune", label: "Pune" },
    { value: "ahmedabad", label: "Ahmedabad" },
    { value: "jaipur", label: "Jaipur" },
    { value: "goa", label: "Goa" },
  ];

  const personOptions = [
    { value: "1", label: "1" },
    { value: "2", label: "2" },
    { value: "3", label: "3" },
    { value: "4", label: "4" },
    { value: "5", label: "5" },
    { value: "6", label: "6" },
    { value: "7", label: "7" },
    { value: "8", label: "8" },
    { value: "9", label: "9" },
    { value: "10", label: "10" },
    { value: "11", label: "11" },
    { value: "12", label: "12" },
    { value: "13", label: "13" },
    { value: "14", label: "14" },
    { value: "15", label: "15" },
  ];

  const specialFareOptions = [
    { value: "student", label: "Student Fare" },
    { value: "senior", label: "Senior Citizen" },
    { value: "military", label: "Military" },
    { value: "corporate", label: "Corporate" },
    { value: "group", label: "Group Booking" },
  ];

  const stopsOptions = [
    { value: "direct", label: "Direct" },
    { value: "1stop", label: "1 Stop" },
    { value: "2stop", label: "2 Stop" },
  ];

  const timeOfDayOptions = [
    { value: "morning", label: "Morning" },
    { value: "afternoon", label: "Afternoon" },
    { value: "evening", label: "Evening" },
    { value: "night", label: "Night" },
  ];

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        position: "relative",
        // p: "12px 23px",
      }}
    >
      {/* Header Section */}
      <Box
        sx={{
          mb: 3,
          // background: "#FFFFFF",
        }}
      >
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontStyle: "normal",
            fontSize: "16px",
            lineHeight: "20px",
            letterSpacing: "0.02em",
            textTransform: "capitalize",
            color: "#1E3A8A",
            mb: 1,
          }}
        >
          Trip Planner
        </Typography>

        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 400,
            fontStyle: "normal",
            fontSize: "12px",
            lineHeight: "18px",
            letterSpacing: "0.02em",
            textTransform: "capitalize",
            color: "#333333",
          }}
        >
          Modify where you're flying.....
        </Typography>
      </Box>

      {/* Form Section */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          flex: 1,
          paddingBottom: "20px",
        }}
      >
        {/* Trip Type Radio Buttons */}
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <FormControl>
            <RadioGroup
              row
              sx={{
                gap: "9px",
              }}
              value={formData.tripType}
              onChange={(e) => handleFormChange("tripType", e.target.value)}
            >
              <PrimaryRadio value="oneway" label="One Way" />
              <PrimaryRadio value="roundtrip" label="Round Trip" />
            </RadioGroup>
          </FormControl>
        </Box>

        {/* From and To */}

        <Box
          sx={{
            width: "100%",
            display: "flex",
            gap: 2,
            alignItems: "center",
          }}
        >
          <Box sx={{ flex: 1 }}>
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 500,
                fontSize: "14px",
                color: "#1E3A8A",
                mb: 1,
              }}
            >
              From
            </Typography>
            <CustomSelect
              label=""
              value={formData.from}
              onChange={(e) => handleFormChange("from", e.target.value)}
              options={cityOptions}
              placeholder="Select departure city"
              customMarginBottom="0px"
            />
          </Box>
          <Box sx={{ flex: 1 }}>
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 500,
                fontSize: "14px",
                color: "#1E3A8A",
                mb: 1,
              }}
            >
              To
            </Typography>
            <CustomSelect
              label=""
              value={formData.to}
              onChange={(e) => handleFormChange("to", e.target.value)}
              options={cityOptions}
              placeholder="Select destination city"
              customMarginBottom="0px"
            />
          </Box>
        </Box>

        {/* Departure and Return Date */}
        <Box>
          <Box
            sx={{
              display: "flex",
              gap: 2,
              alignItems: "center",
            }}
          >
            <Box sx={{ flex: 1 }}>
              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 500,
                  fontSize: "14px",
                  color: "#1E3A8A",
                  mb: 1,
                }}
              >
                Departure
              </Typography>
              <input
                type="date"
                value={formData.departureDate}
                onChange={(e) =>
                  handleFormChange("departureDate", e.target.value)
                }
                style={{
                  width: "100%",
                  padding: "12px",
                  border: "1px solid #ddd",
                  borderRadius: "4px",
                  fontFamily: "Roboto",
                  fontSize: "14px",
                  boxSizing: "border-box",
                }}
              />
            </Box>
            {formData.tripType === "roundtrip" && (
              <Box sx={{ flex: 1 }}>
                <Typography
                  sx={{
                    fontFamily: "Roboto",
                    fontWeight: 500,
                    fontSize: "14px",
                    color: "#1E3A8A",
                    mb: 1,
                  }}
                >
                  Return
                </Typography>
                <input
                  type="date"
                  value={formData.returnDate}
                  onChange={(e) =>
                    handleFormChange("returnDate", e.target.value)
                  }
                  style={{
                    width: "100%",
                    padding: "12px",
                    border: "1px solid #ddd",
                    borderRadius: "4px",
                    fontFamily: "Roboto",
                    fontSize: "14px",
                    boxSizing: "border-box",
                  }}
                />
              </Box>
            )}
          </Box>
        </Box>

        {/* Number of Persons Travelling */}
        <Box>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              color: "#1E3A8A",
              mb: 1,
            }}
          >
            No of Person Traveling
          </Typography>
          <Box
            sx={{
              display: "flex",
              gap: 2,
              alignItems: "center",
            }}
          >
            <Box sx={{ flex: 1 }}>
              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "12px",
                  lineHeight: "14px",
                  color: "#1E3A8A",
                  mb: 1,
                }}
              >
                Adults
              </Typography>
              <CustomSelect
                label=""
                value={formData.adults}
                onChange={(e) => handleFormChange("adults", e.target.value)}
                options={personOptions}
                placeholder="0"
                customMarginBottom="0px"
              />
            </Box>
            <Box sx={{ flex: 1 }}>
              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "12px",
                  lineHeight: "14px",
                  color: "#1E3A8A",
                  mb: 1,
                }}
              >
                Children
              </Typography>
              <CustomSelect
                label=""
                value={formData.children}
                onChange={(e) => handleFormChange("children", e.target.value)}
                options={personOptions}
                placeholder="0"
                customMarginBottom="0px"
              />
            </Box>
            <Box sx={{ flex: 1 }}>
              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "12px",
                  lineHeight: "14px",
                  color: "#1E3A8A",
                  mb: 1,
                }}
              >
                Infants
              </Typography>
              <CustomSelect
                label=""
                value={formData.infants}
                onChange={(e) => handleFormChange("infants", e.target.value)}
                options={personOptions}
                placeholder="0"
                customMarginBottom="0px"
              />
            </Box>
          </Box>
        </Box>

        {/* Select a special fare */}
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              color: "#1E3A8A",
            }}
          >
            Select a special fare
          </Typography>
          <FormControl>
            <RadioGroup
              value={formData.specialFare}
              sx={{
                gap: "9px",
                flexDirection: "row",
                flexWrap: "wrap",
                justifyContent: "flex-start",
                display: "flex",
              }}
              onChange={(e) => handleFormChange("specialFare", e.target.value)}
            >
              {specialFareOptions.map((option) => (
                <PrimaryRadio
                  key={option.value}
                  value={option.value}
                  label={option.label}
                />
              ))}
            </RadioGroup>
          </FormControl>
        </Box>

        {/* Stops */}
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              color: "#1E3A8A",
            }}
          >
            Select stops
          </Typography>
          <FormControl>
            <RadioGroup
              value={formData.stops}
              sx={{
                gap: "9px",
                flexDirection: "row",
                flexWrap: "wrap",
                justifyContent: "flex-start",
                display: "flex",
              }}
              onChange={(e) => handleFormChange("stops", e.target.value)}
            >
              {stopsOptions.map((option) => (
                <PrimaryRadio
                  key={option.value}
                  value={option.value}
                  label={option.label}
                />
              ))}
            </RadioGroup>
          </FormControl>
        </Box>

        {/* Time of Day */}
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              color: "#1E3A8A",
            }}
          >
            Select time of day
          </Typography>
          <FormControl>
            <RadioGroup
              value={formData.timeOfDay}
              sx={{
                gap: "9px",
                flexDirection: "row",
                flexWrap: "wrap",
                justifyContent: "flex-start",
                display: "flex",
              }}
              onChange={(e) => handleFormChange("timeOfDay", e.target.value)}
            >
              {timeOfDayOptions.map((option) => (
                <PrimaryRadio
                  key={option.value}
                  value={option.value}
                  label={option.label}
                />
              ))}
            </RadioGroup>
          </FormControl>
        </Box>
      </Box>

      {/* footer */}
      <Box
        sx={{
          position: "sticky",
          bottom: "-20px",
          background: "#FFF9F9",
          width: "100%",
          p: "20px 0px",
          boxSizing: "border-box",
        }}
      >
        <PrimaryButton width="100%">Update</PrimaryButton>
      </Box>
    </Box>
  );
};
