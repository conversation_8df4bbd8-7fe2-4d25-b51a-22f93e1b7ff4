import { Box, Stack, Typography } from "@mui/material";
import { useMemo } from "react";
import styled from "styled-components";
import generateCloudFrontURL from "../../../../../utils";
import StarIcon from "@mui/icons-material/Star";
import StarHalfIcon from "@mui/icons-material/StarHalf";
import StarBorderIcon from "@mui/icons-material/StarBorder";
import PrimaryButton from "../../../../common/Button/PrimaryButton";

const highlightsIconUrl = "/static/package/highlight";

const highlightsIcons = [
  "island",
  "beach",
  "ship",
  "passenger",
  "breakfast",
  "taxi",
  "bus",
  "hotel",
  "flight",
  "visa",
  "forex_card",
  "insurance",
  "common",
];

const getHighlightIconUrl = (icon) => {
  let url = "";
  if (highlightsIcons.includes(icon)) {
    url = `${highlightsIconUrl}/${icon}.png`;
  } else {
    url = `${highlightsIconUrl}/common.png`;
  }
  return url;
};

const Separator = styled(Box)(({ theme }) => ({
  width: "100%",
  height: "2px",
  background: "#333333",
  opacity: 0.05,
  marginTop: "16px",
  marginBottom: "16px",
}));

export const OverviewTab = ({ packageData = null }) => {
  const image = generateCloudFrontURL({
    bucket: process.env.REACT_APP_BUCKET_NAME,
    key:
      packageData?.package_media?.length > 0
        ? packageData?.package_media[0]?.file
        : "no-image.jpg",
    width: 300,
    height: 200,
    fit: "cover",
    actual: true,
  });
  return (
    <Box>
      <Box mb={1.5}>
        <Box
          component="img"
          src={image}
          sx={{
            width: "100%",
            height: "246px",
            objectFit: "cover",
            borderRadius: "12px",
          }}
        />
      </Box>
      <Box
        mb={1.5}
        sx={{
          fontFamily: "Roboto",
          fontSize: 14,
          fontWeight: 400,
          lineHeight: "18px",
          color: "#333333",
        }}
      >
        {packageData?.about_this_tour || ""}
      </Box>
      <Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontSize: 14,
            fontWeight: 500,
            lineHeight: "16px",
            color: "#333333",
            mb: 1,
          }}
        >
          Tour Highlights
        </Box>
        <Stack direction="column" spacing={"6px"}>
          {packageData?.highlights?.map((item, index) => (
            <Box key={index + "highlights"} sx={{ display: "flex", gap: 0.5 }}>
              <img
                src={getHighlightIconUrl(item?.icon_class)}
                alt="A"
                style={{ width: "16px", height: "16px" }}
              />
              <Box
                sx={{
                  fontSize: 14,
                  fontWeight: 400,
                  lineHeight: "16px",
                  color: "#333333",
                }}
              >
                {typeof item === "string" ? item : item?.value || ""}
              </Box>
            </Box>
          ))}
        </Stack>
      </Box>
      <Box mt={1.5}>
        <Box>
          <Box
            sx={{
              fontFamily: "Roboto",
              fontSize: 14,
              fontWeight: 500,
              lineHeight: "16px",
              color: "#333333",
              mb: 1,
            }}
          >
            Inclusions
          </Box>
          <Stack direction="column" spacing={"6px"}>
            {packageData?.inclusions?.map((item, index) => (
              <Box
                key={index + "highlights"}
                sx={{ display: "flex", gap: 0.5 }}
              >
                <img
                  src={getHighlightIconUrl(item.icon_class)}
                  alt="A"
                  style={{ width: "16px", height: "16px" }}
                />
                <Box
                  sx={{
                    fontSize: 14,
                    fontWeight: 400,
                    lineHeight: "16px",
                    color: "#333333",
                  }}
                >
                  {typeof item === "string" ? item : item?.value || ""}
                </Box>
              </Box>
            ))}
          </Stack>
        </Box>
        {packageData?.exclusions?.length > 0 && (
          <Box mt={1.5}>
            <Box
              sx={{
                fontFamily: "Roboto",
                fontSize: 14,
                fontWeight: 500,
                lineHeight: "16px",
                color: "#333333",
                mb: 1,
              }}
            >
              Exclusions
            </Box>
            <Stack direction="column" spacing={"6px"}>
              {packageData?.exclusions?.map((item, index) => (
                <Box
                  key={index + "highlights"}
                  sx={{ display: "flex", gap: 0.5 }}
                >
                  <img
                    src={"/static/common/close.png"}
                    alt="exclusions"
                    style={{ width: "14px", height: "14px" }}
                  />
                  <Box
                    sx={{
                      fontSize: 14,
                      fontWeight: 400,
                      lineHeight: "16px",
                      color: "#333333",
                    }}
                  >
                    {typeof item === "string" ? item : item?.value || ""}
                  </Box>
                </Box>
              ))}
            </Stack>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export const InclusionsTab = ({ packageData = null }) => {
  return (
    <Box>
      <Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontSize: 12,
            fontWeight: 500,
            lineHeight: "14px",
            color: "#333333",
            mb: 1,
          }}
        >
          Inclusions
        </Box>
        <Stack direction="column" spacing={"6px"}>
          {packageData?.inclusions?.map((item, index) => (
            <Box key={index + "highlights"} sx={{ display: "flex", gap: 0.5 }}>
              <img
                src={getHighlightIconUrl(item.icon_class)}
                alt="A"
                style={{ width: "16px", height: "16px" }}
              />
              <Box
                sx={{
                  fontSize: 12,
                  fontWeight: 400,
                  lineHeight: "14px",
                  color: "#333333",
                }}
              >
                {typeof item === "string" ? item : item?.value || ""}
              </Box>
            </Box>
          ))}
        </Stack>
      </Box>
      {packageData?.exclusions?.length > 0 && (
        <Box mt={1.5}>
          <Box
            sx={{
              fontFamily: "Roboto",
              fontSize: 12,
              fontWeight: 500,
              lineHeight: "14px",
              color: "#333333",
              mb: 1,
            }}
          >
            Exclusions
          </Box>
          <Stack direction="column" spacing={"6px"}>
            {packageData?.exclusions?.map((item, index) => (
              <Box
                key={index + "highlights"}
                sx={{ display: "flex", gap: 0.5 }}
              >
                <img
                  src={"/static/common/close.png"}
                  alt="exclusions"
                  style={{ width: "14px", height: "14px" }}
                />
                <Box
                  sx={{
                    fontSize: 12,
                    fontWeight: 400,
                    lineHeight: "14px",
                    color: "#333333",
                  }}
                >
                  {typeof item === "string" ? item : item?.value || ""}
                </Box>
              </Box>
            ))}
          </Stack>
        </Box>
      )}
    </Box>
  );
};

export const ItineraryTab = ({ packageData = null }) => {
  const isCustom =
    packageData?.type === "Custom AI" || packageData?.type === "Custom Admin";
  const customItinerary = isCustom ? packageData?.itineraries : [];
  const fixedItinerary = packageData?.itineraries;

  return (
    <Box>
      {isCustom ? (
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            "& h3": {
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "12px",
              lineHeight: "18px",
              color: "#000000",
              mb: 2,
            },
            "& ul": {
              pl: "16px",
            },
            "& li": {
              pl: 0,
              fontFamily: "Roboto",
              fontWeight: 400,
              fontSize: "12px",
              lineHeight: "18px",
              color: "#000",
              mb: 1,
            },
          }}
        >
          {customItinerary?.map((item, index) => (
            <>
              <Box key={index}>
                <Typography variant="h3">
                  Day {item?.day_number} - {item?.day_title}
                </Typography>
                <ul>
                  {item?.day_items?.map((desc, index) => (
                    <li key={index}>
                      <strong>{desc?.title}</strong>:{" "}
                      <span>{desc?.description}</span>
                    </li>
                  ))}
                </ul>
                <ul>
                  {item?.day_key_points?.map((keyPoint, index) => (
                    <li key={index}>{keyPoint}</li>
                  ))}
                </ul>
              </Box>
              {index < customItinerary?.length - 1 && <Separator />}
            </>
          ))}
        </Box>
      ) : (
        <Box
          sx={{
            fontFamily: "Roboto",
            fontSize: 12,
            fontWeight: 400,
            lineHeight: "18px",
            color: "#333333",
            "& h3, & h4, & h5, & h6": {
              fontSize: 12,
              fontFamily: "Roboto",
              fontWeight: 500,
              color: "#000000",
              mb: 1,
              mt: 1,
            },
            "& ul, & ol": {
              fontSize: 12,
              fontFamily: "Roboto",
              fontWeight: 400,
              color: "#000000",
              mb: 1,
              mt: 1,
            },
          }}
          dangerouslySetInnerHTML={{ __html: fixedItinerary }}
        />
      )}
    </Box>
  );
};

export const HotelsTab = ({ packageData = null }) => {
  const hotelsData = packageData?.hotels || [];

  const filteredHotels = hotelsData?.map((hotel) => {
    return {
      ...hotel,
      image: generateCloudFrontURL({
        bucket: process.env.REACT_APP_BUCKET_NAME,
        key: hotel?.image || "no-image.jpeg",
        width: 100,
        height: 70,
        fit: "cover",
      }),
    };
  });

  return (
    <Box
      sx={{
        width: "100%",
      }}
    >
      {filteredHotels.map((hotel, index) => (
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column" },
            overflow: "hidden",
          }}
          key={index + "hotel"}
        >
          {hotel?.city && (
            <Box
              sx={{
                fontFamily: "Lora",
                fontWeight: 600,
                fontSize: "16px",
                lineHeight: "20px",
                color: "#1E3A8A",
                textTransform: "capitalize",
                mb: 1.5,
              }}
            >
              {hotel?.city || ""}
            </Box>
          )}
          <Box sx={{ display: "flex", gap: 1, mb: 1 }}>
            <Box
              sx={{
                width: "94px",
                height: "68px",
                borderRadius: "8px",
                objectFit: "cover",
              }}
              component="img"
              src={hotel?.image}
            />
            <Box sx={{ flex: 1 }}>
              <Box sx={{ display: "inline-flex", gap: 1 }}>
                <Typography
                  sx={{
                    fontFamily: "Roboto",
                    fontWeight: 500,
                    fontSize: "14px",
                    lineHeight: "22px",
                    color: "#333333",
                  }}
                >
                  {hotel?.name || ""}
                </Typography>
                {hotel?.rating && (
                  <Box component="span" sx={{ display: "inline-flex" }}>
                    {Array.from({ length: 5 }).map((_, index) => {
                      if (index < Math.floor(hotel?.rating || 0)) {
                        return (
                          <StarIcon
                            sx={{ color: "#00AF31", fontSize: "16px" }}
                            key={index}
                          />
                        );
                      } else if (
                        index === Math.floor(hotel?.rating || 0) &&
                        hotel?.rating % 1 !== 0
                      ) {
                        return (
                          <StarHalfIcon
                            sx={{ color: "#00AF31", fontSize: "16px" }}
                            key={index}
                          />
                        );
                      } else {
                        return (
                          <StarBorderIcon
                            sx={{ color: "#00AF31", fontSize: "16px" }}
                            key={index}
                          />
                        );
                      }
                    })}
                  </Box>
                )}
              </Box>

              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "14px",
                  lineHeight: "22px",
                  color: "#333333",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  display: "-webkit-box",
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: "vertical",
                }}
              >
                {hotel?.address || ""}
              </Typography>
            </Box>
          </Box>

          {hotel?.description && (
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "12px",
                lineHeight: "18px",
                color: "#333333",
                overflow: "hidden",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                mb: 1,
              }}
            >
              {hotel?.description || ""}
            </Typography>
          )}

          {hotel?.amenities?.length > 0 && (
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 500,
                fontSize: "12px",
                lineHeight: "18px",
                color: "#333333",
                overflow: "hidden",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                mb: 1,
              }}
            >
              {hotel?.amenities?.join(", ")}
            </Typography>
          )}

          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 400,
              fontSize: "12px",
              lineHeight: "18px",
              color: "#333333",
              overflow: "hidden",
              textOverflow: "ellipsis",
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
            }}
          >
            <strong>Note:</strong>{" "}
            {
              "Exact hotels may vary based on availability; upgrades to 4-star/5-star options (e.g., Holiday Inn Resort Phuket) available at additional cost."
            }
          </Typography>
          <Separator />
        </Box>
      ))}
    </Box>
  );
};

export const AddOnsTab = ({ packageData = null }) => {
  const handleAddOnClick = (value) => {
    if (window?.userlike) {
      // const getPath = window.location.pathname;
      // const text = `Hi there! 👋 I'm on the package page — ${getPath}. \n I want to add this add-on: "${value}". \n Please check the add-on.`;

      // Open the chat widget
      window.userlike.userlikeStartChat();
    }
  };
  return (
    <>
      <Box
        sx={{
          fontFamily: "Lora",
          fontWeight: 600,
          fontSize: "16px",
          lineHeight: "19px",
          letterSpacing: "0%",
          color: "#1E3A8A",
          mb: 2,
        }}
      >
        Enhance Your Stay
      </Box>
      <Box
        sx={{
          width: "100%",
          height: "fit-content",
          display: "grid",
          gridTemplateColumns: "repeat(2, 1fr)",
          gap: 2,
          flexWrap: "wrap",
          boxSizing: "border-box",
        }}
      >
        {packageData?.addons?.map((item, index) => (
          <Box
            key={index + "add-on"}
            sx={{
              display: "flex",
              // aspectRatio: "1/1",
              flexDirection: "column",
              gap: "6px",
              p: 2.5,
              alignItems: "center",
              borderRadius: "8px",
              border: "1px solid #FF3951",
              background: "#FFF9F9",
            }}
          >
            <img
              src={getHighlightIconUrl(item?.icon_class)}
              alt="add-on"
              style={{ width: "60px", height: "60px" }}
            />
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 500,
                fontSize: "16px",
                lineHeight: "22px",
                textAlign: "center",
                textTransform: "capitalize",
                color: "#333333",
              }}
            >
              {typeof item === "string" ? item : item?.value || ""}
            </Typography>
          </Box>
        ))}
      </Box>
      <Box
        sx={{
          width: "100%",
          height: "1px",
          background: "rgba(51, 51, 51, 0.05)",
          my: 2,
        }}
      />
      <Box>
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "16px",
            lineHeight: "19px",
            color: "#333333",
          }}
        >
          One package, endless possibilities—chat with an expert to tailor it
          your way!
        </Typography>
        <Box sx={{ display: "flex", justifyContent: "center", mt: 3.5 }}>
          <PrimaryButton onClick={handleAddOnClick}>
            Talk to Expert
          </PrimaryButton>
        </Box>
      </Box>
    </>
  );
};

export const ThreeSixtyViewTab = () => {
  return (
    <Box>
      <Box
        display="none"
        component="video"
        src="https://videos.pexels.com/video-files/8996454/8996454-uhd_2560_1440_24fps.mp4"
        autoPlay
        controls
        loop
        muted
        playsInline
        sx={{
          width: "100%",
          height: "267px",
          objectFit: "cover",
          borderRadius: "8px",
        }}
      />
    </Box>
  );
};
export const PolicyTab = () => {
  return (
    <Box width="100%">
      <Box>
        <Box>
          {/* Terms & Conditions */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 500,
                fontFamily: "Roboto",
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                mb: 2,
              }}
            >
              Zuumm – Terms & Conditions & Important Information
            </Typography>
            <ol
              style={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                paddingLeft: "20px",
                marginTop: "0px",
              }}
            >
              <li>
                The package price is calculated per person, based on a minimum
                of two travelers.
              </li>
              <li>
                Visa Assistance: Zuumm supports with visa applications, but
                final approval is at the sole discretion of the
                embassy/consulate.
              </li>
              <li>
                Passport Validity: Ensure your passport is valid for at least 6
                months from your return date.
              </li>
              <li>
                Flight Schedule: Always check flight status 24 hours before
                departure as schedules can change without notice.
              </li>
              <li>
                Correct Names: Names on bookings must exactly match the travel
                documents (passport, etc.).
              </li>
              <li>
                Airport Transfers: After baggage claim, head to the assigned
                exit and meet our representative within 60 minutes of landing.
              </li>
              <li>
                Hotel Check-In: Standard check-in is 2 PM, check-out is 11 AM.
                Early/late access is subject to hotel availability and may be
                chargeable.
              </li>
              <li>
                Hotel Deposits: Some hotels may ask for a refundable security
                deposit at check-in.
              </li>
              <li>
                Package Exclusions:
                <ul style={{ paddingLeft: "20px", marginTop: "8px" }}>
                  <li>Visa fee is not included.</li>
                  <li>
                    Gala dinner charges (e.g., Christmas/New Year) are not
                    included.
                  </li>
                  <li>
                    Personal expenses like room service, laundry, or mini-bar
                    are excluded.
                  </li>
                </ul>
              </li>
              <li>
                Customizations & Vouchers: Packages can be customized. Final
                itinerary and vouchers will be shared 72 hours prior to
                departure after full payment.
              </li>
              <li>
                Natural Disruptions: In case of delays or cancellations due to
                weather or other unavoidable issues, refunds will be processed
                as per vendor policies.
              </li>
              <li>
                Low-Cost Flights: For low-cost airlines, baggage is not included
                unless added separately during booking. So ensure baggage policy
                before confirming the flights.
              </li>
              <li>
                Hotel Substitution: If the selected hotel is unavailable, a
                similar category hotel will be provided.
              </li>
              <li>
                Punctuality: Be on time for activities and transfers. Delays may
                result in missed services without refund.
              </li>
              <li>
                Special Event Charges: Additional charges may apply at hotels
                during festivals/peak dates. Zuumm will inform you wherever
                possible.
              </li>
              <li>
                Activity Refunds: Paid activities that are cancelled will be
                refunded within 30 days. Complimentary activities are
                non-refundable.
              </li>
              <li>
                Itinerary Changes: Zuumm may adjust the itinerary due to flight
                delays, closures, weather, or other disruptions.
              </li>
            </ol>
          </Box>

          {/* Cancellation Policy */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 500,
                fontFamily: "Roboto",
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                mb: 2,
              }}
            >
              Zuumm Holiday Packages – Cancellation Policy
            </Typography>
            <ol
              style={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                paddingLeft: "20px",
                marginTop: "0px",
              }}
            >
              <li>
                Without Flights: Cancellations allowed only if made 30+ days
                before the travel date.
              </li>
              <li>
                Less than 30 Days: Bookings made less than 30 days before travel
                are non-refundable.
              </li>
              <li>
                With Flights: Cancellations/reschedules follow the airline's
                policy.
              </li>
              <li>
                Land Services: Hotels, transfers, and activities follow Zuumm's
                standard cancellation policy.
              </li>
              <li>
                TCS (Tax Collected at Source) is non-refundable, but can be
                claimed when filing your income tax return.
              </li>
              <li>
                A 5% GST will be charged on the land package value, excluding
                any flight costs.
              </li>
            </ol>
          </Box>

          {/* TCS Conditions */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 500,
                fontFamily: "Roboto",
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                mb: 2,
              }}
            >
              TCS Conditions
            </Typography>
            <ol
              style={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                paddingLeft: "20px",
                marginTop: "0px",
              }}
            >
              <li>
                If you haven't filed ITRs for the past 2 years and your TDS/TCS
                exceeds ₹50,000, TCS will be charged at 10% on international
                bookings.
              </li>
              <li>
                If PAN validation fails due to non-filing, additional TCS @5%
                will apply, or the booking may be cancelled as per policy.
              </li>
              <li>PAN card is mandatory.</li>
            </ol>
          </Box>

          {/* Country-Specific Tourism Fees */}
          <Box sx={{ mb: 1 }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 500,
                fontFamily: "Roboto",
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                mb: 2,
              }}
            >
              Country-Specific Tourism Fees and Policies
            </Typography>
            <Box
              sx={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
                marginTop: "0px",
                "& h4": {
                  m: "8px 0px 4px 0px",
                  fontWeight: 500,
                  fontSize: "12px",
                  lineHeight: "18px",
                },
                "& ul": {
                  marginTop: "0px",
                },
              }}
            >
              <h4>United Arab Emirates (Dubai)</h4>
              <ul>
                <li>
                  Tourism Dirham fee applies per night, based on hotel category.
                </li>
                <li>Fee ranges from AED 7 to AED 20 per room per night.</li>
              </ul>

              <h4>Indonesia (Bali)</h4>
              <ul>
                <li>
                  A tourist levy of IDR 150,000 (~USD 10) is applicable per
                  person.
                </li>
                <li>
                  Payable online via the LoveBali app/website or at the airport.
                </li>
              </ul>

              <h4>Sri Lanka</h4>
              <ul>
                <li>No national tourism tax.</li>
                <li>Standard visa fees apply.</li>
                <li>
                  Some hotels may charge service fees or additional taxes
                  locally.
                </li>
              </ul>

              <h4>Maldives</h4>
              <ul>
                <li>
                  A "Green Tax" of USD 6 per person per night is charged at
                  resorts, hotels, and guesthouses.
                </li>
                <li>
                  Seaplane or speedboat transfers may have additional costs and
                  taxes.
                </li>
              </ul>

              <h4>Nepal</h4>
              <ul>
                <li>No tourism tax at national level.</li>
                <li>
                  Trekking permits and national park entry fees apply in
                  specific regions.
                </li>
              </ul>

              <h4>United States</h4>
              <ul>
                <li>
                  Some cities/states impose "occupancy tax" or "hotel tax".
                </li>
                <li>
                  Hotels may charge daily resort fees (common in Las Vegas,
                  Florida, Hawaii).
                </li>
                <li>
                  Hawaii plans to introduce a 0.75% climate tax on accommodation
                  charges from 2026.
                </li>
              </ul>

              <h4>Thailand</h4>
              <ul>
                <li>
                  Proposed tourism fee of THB 300 (~USD 8) for air arrivals
                  (pending implementation).
                </li>
                <li>No tourism tax currently in place as of 2025.</li>
              </ul>

              <h4>Japan</h4>
              <ul>
                <li>
                  "Sayonara Tax" of JPY 1,000 applies when departing Japan.
                </li>
                <li>
                  Some cities like Tokyo and Osaka charge accommodation taxes
                  ranging from JPY 100 to JPY 300 per person per night.
                </li>
              </ul>

              <h4>Switzerland</h4>
              <ul>
                <li>
                  Most cities apply a tourist tax, usually CHF 2–4 per person
                  per night.
                </li>
                <li>
                  The fee is usually collected by the hotel at check-in or
                  check-out.
                </li>
              </ul>

              <h4>Europe (General)</h4>
              <ul>
                <li>
                  Most European countries/cities charge a tourist or city tax:
                </li>
                <li>Italy (Rome, Venice): €1–€5 per person per night.</li>
                <li>France (Paris): €0.80–€4.00 per night.</li>
                <li>Netherlands (Amsterdam): 7%–12.5% of room cost.</li>
                <li>Spain (Barcelona): €0.75–€3.50 per night.</li>
              </ul>

              <h4>Turkey</h4>
              <ul>
                <li>Accommodation tax of 2% is charged on the room rate.</li>
              </ul>

              <h4>Singapore</h4>
              <ul>
                <li>
                  No tourism tax, but 8% GST (Goods and Services Tax) applies on
                  hotel and service bills.
                </li>
              </ul>

              <h4>Vietnam</h4>
              <ul>
                <li>No specific tourism tax.</li>
                <li>8% VAT is typically added to hotel and service bills.</li>
              </ul>

              <h4>Finland</h4>
              <ul>
                <li>No national tourism tax.</li>
                <li>Some cities may charge a nominal tourist fee.</li>
              </ul>

              <h4>New Zealand</h4>
              <ul>
                <li>
                  International Visitor Conservation and Tourism Levy (IVL) of
                  NZD 100 (~USD 62) is charged at the time of applying for a
                  visa or NZeTA.
                </li>
              </ul>

              <h4>South America (varies by country)</h4>
              <ul>
                <li>
                  Argentina, Peru, Chile, and Brazil may charge hotel taxes.
                </li>
                <li>
                  Foreigners in some countries are exempt if paying in foreign
                  currency and showing a passport.
                </li>
              </ul>

              <h4>Hong Kong</h4>
              <ul>
                <li>No tourism or hotel tax currently imposed.</li>
              </ul>

              <h4>Azerbaijan</h4>
              <ul>
                <li>
                  A tourist tax of AZN 1.30 per person per night is charged at
                  most accommodations.
                </li>
              </ul>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export const MeetYourDestinationTab = ({ packageData = null }) => {
  const mappedDestinationData = useMemo(() => {
    const data = [];

    if (packageData?.best_time_to_visit) {
      data.push({
        type: "best_time_to_visit",
        name: "Best time to visit",
        description: packageData?.best_time_to_visit,
      });
    }

    if (packageData?.destination_safety) {
      data.push({
        type: "destination_safety",
        name: "Destination Safety",
        description: packageData?.destination_safety,
      });
    }

    if (packageData?.cultural_info) {
      data.push({
        type: "cultural_info",
        name: "Cultural Information",
        description: packageData?.cultural_info,
      });
    }

    if (packageData?.what_to_shop) {
      data.push({
        type: "what_to_shop",
        name: "What To Shop",
        description: packageData?.what_to_shop,
      });
    }

    if (packageData?.what_to_pack) {
      data.push({
        type: "what_to_pack",
        name: "What To Pack",
        description: packageData?.what_to_pack,
      });
    }

    return data;
  }, [packageData]);
  return (
    <Box
      sx={{
        width: "100%",
      }}
    >
      {mappedDestinationData.map((destination, index) => (
        <Box key={index}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              mb: 1,
            }}
          >
            <Box
              sx={{
                backgroundColor: "#FF3951",
                width: 3,
                height: "20px",
                borderRadius: "2px",
              }}
            />
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 500,
                fontSize: "12px",
                lineHeight: "18px",
                color: "#000000",
              }}
            >
              {destination.name}
            </Typography>
          </Box>
          <Box>
            {destination.type === "what_to_pack" ? (
              <Box
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "12px",
                  lineHeight: "18px",
                  color: "#000000",
                  "& h3, & h4, & h5, & h6": {
                    fontFamily: "Roboto",
                    fontWeight: 500,
                    fontSize: "14px",
                    lineHeight: "18px",
                    color: "#000000",
                    mb: 1,
                  },
                  "& p": {
                    mb: 1,
                  },
                  "& ul, & ol": {
                    mb: 1,
                    pl: 3,
                  },
                  "& li": {
                    mb: 0.5,
                  },
                }}
                dangerouslySetInnerHTML={{ __html: destination.description }}
              />
            ) : (
              <Box>
                <Typography
                  sx={{
                    fontFamily: "Roboto",
                    fontWeight: 400,
                    fontSize: "12px",
                    lineHeight: "18px",
                    whiteSpace: "pre-line",
                  }}
                >
                  {destination.description}
                </Typography>
              </Box>
            )}
          </Box>
          <Separator />
        </Box>
      ))}
    </Box>
  );
};