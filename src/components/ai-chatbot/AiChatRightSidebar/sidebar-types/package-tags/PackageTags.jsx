import { Box } from "@mui/material";
import React from "react";

const PackageTags = ({ tag = "luxury", text = "LUXURY" }) => {
  const tagStyles = {
    width: "fit-content",
    height: "fit-content",
    padding: "5px 9px 3px 9px",
    borderRadius: "4px",
    fontFamily: "Roboto",
    fontWeight: 500,
    fontStyle: "normal",
    fontSize: "10px",
    lineHeight: "10px",
  };

  const luxuryStyle = {
    ...tagStyles,
    background: "linear-gradient(90deg, #FF4B4E 0%, #800CB0 100%)",
    border: "1px solid #FFFFFF",
    color: "#FFFFFF",
  };

  const standardStyle = {
    ...tagStyles,
    background: "linear-gradient(90deg, #89D2FF 0%, #00588F 100%)",
    border: "1px solid #FFFFFF",
    color: "#FFFFFF",
  };

  const premiumStyle = {
    ...tagStyles,
    background: "linear-gradient(90deg, #FFD86E 0%, #B78700 100%)",
    border: "1px solid #FFFFFF",
    color: "#FFFFFF",
  };

  const customizeStyle = {
    ...tagStyles,
    background: "#FFFFFF",
    border: "1px solid #1E3A8A",
    color: "#1E3A8A",
    fontWeight: 400,
    padding: "4px 9px 4px 9px",
  };

  if (tag === "luxury") {
    return <Box style={luxuryStyle}>{text || "LUXURY"}</Box>;
  } else if (tag === "standard") {
    return <Box style={standardStyle}>{text || "STANDARD"}</Box>;
  } else if (tag === "premium") {
    return <Box style={premiumStyle}>{text || "PREMIUM"}</Box>;
  } else if (tag === "customize") {
    return <Box style={customizeStyle}>{text || "CUSTOMIZE"}</Box>;
  }
};

export default PackageTags;
