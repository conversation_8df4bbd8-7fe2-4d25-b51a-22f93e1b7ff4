import React from "react";
import { CloseOutline } from "../../../common/SvgIcon";

import { Box, Typography } from "@mui/material";
import { CustomizePackage } from "./customize-tabs/CustomizePackage";

const PreferencePackage = ({
  packageWidth,
  onClose = () => {},
  preferencePackageData = null,
  handleUpdatePreferencePackage = null,
}) => {
  return (
    <Box
      sx={{
        width: packageWidth,
        height: "100%",
        zIndex: 1001,
        boxShadow: "0px 4px 7px 0px #00000008",
        display: "flex",
        flexDirection: "column",
        background: "#FFF9F9",
      }}
    >
      <Box
        sx={{
          width: "100%",
          background: "#F9F9F9",
          p: "14px 18px",
          boxSizing: "border-box",
          display: "flex",
          justifyContent: "space-between",
          gap: 2,
        }}
      >
        <Typography
          sx={{
            fontSize: 16,
            fontWeight: 500,
            lineHeight: "18px",
            color: "#1E3A8A",
            maxWidth: "calc(100% - 44px)",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          Curate Your Perfect Trip
        </Typography>
        <CloseOutline onClick={onClose} />
      </Box>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          width: "100%",
          flex: 1,
          overflow: "hidden",
          maxHeight: "100%",
          // p: "16px",
          boxSizing: "border-box",
        }}
      >
        {/* content */}
        <Box
          sx={{
            display: "flex",
            width: "100%",
            flex: 1,
            overflowY: "auto",
            boxSizing: "border-box",
          }}
        >
          <Box sx={{ width: "100%" }}>
            <CustomizePackage
              initialData={preferencePackageData}
              handleUpdatePreferencePackage={handleUpdatePreferencePackage}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default PreferencePackage;
