import React from "react";
import { Box, Typography } from "@mui/material";
import BasicButton from "../../../../common/Button/BasicButton";
import generateCloudFrontURL from "../../../../../utils";
import { formatIndianNumber } from "../../../../../utils/helper";
import SecondaryButton from "../../../../common/Button/SecondaryButton";
import { TickIcon } from "../../../../common/SvgIcon";

const noImage = generateCloudFrontURL({
  bucket: process.env.REACT_APP_BUCKET_NAME,
  key: "no-image.jpg",
  width: 144,
  height: 144,
  fit: "cover",
});

const Rooms = ({ hotelData = null }) => {
  console.log("hotelData: ", hotelData);
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        width: "100%",
        height: "100%",
        boxSizing: "border-box",
      }}
    >
      <Box
        sx={{
          display: "flex",
          width: "100%",
          boxSizing: "border-box",
          padding: "20px",
          gap: 2,
          justifyContent: "space-between",
          alignItems: "center",
          background: "#FFF9F9",
          borderBottom: "1px solid #FF39510D",
        }}
      >
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 600,
            fontStyle: "Regular",
            fontSize: "16px",
            lineHeight: "19px",
            color: "#333333",
          }}
        >
          01Aug - 02 Aug, 2 Adults
        </Box>
        <BasicButton>Edit</BasicButton>
      </Box>
      <Box
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          gap: 1.5,
          width: "100%",
          boxSizing: "border-box",
          padding: "16px",
          overflowY: "auto",
        }}
      >
        {Array.from({ length: 10 }).map((_, index) => (
          <Box
            key={index}
            sx={{
              display: "flex",
              flexDirection: "column",
              background: "#FBFBFB",
              borderRadius: "8px",
              boxSizing: "border-box",
            }}
          >
            <Box
              sx={{
                padding: "18px 12px 0px 12px",
              }}
            >
              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 600,
                  fontStyle: "SemiBold",
                  fontSize: "16px",
                  lineHeight: "19px",
                  color: "#333333",
                  mb: 2,
                }}
              >
                Executive Suite with Breakfast
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  gap: 1.5,
                  borderBottom: "1px dashed #A6A6A680",
                  pb: 1.5,
                }}
              >
                <Box
                  component="img"
                  src={noImage}
                  sx={{ width: "135px", height: "135px" }}
                  alt="room"
                />
                <Box
                  sx={{
                    flex: 1,
                    display: "flex",
                    flexDirection: "column",
                    gap: 1.5,
                    fontFamily: "Roboto",
                    fontWeight: 400,
                    fontStyle: "Regular",
                    fontSize: "14px",
                    lineHeight: "16px",
                    color: "#333333",
                  }}
                >
                  <Typography>323 sq.ft (30 sq.mt)</Typography>
                  <Typography>1 King Bed</Typography>
                </Box>
              </Box>
              <Box
                my={2}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  flexWrap: "wrap",
                  width: "100%",
                  boxSizing: "border-box",
                  padding: "4px",
                }}
              >
                {hotelData?.amenities?.map((amenity, index) => (
                  <Box
                    key={amenity + index}
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 0.5,
                      width: "max-content",
                      flexShrink: 0,
                    }}
                  >
                    <TickIcon width={"20px"} height={"20px"} />
                    <Box
                      sx={{
                        fontFamily: "Roboto",
                        fontWeight: 400,
                        fontSize: "14px",
                        lineHeight: "16px",
                        color: "#333333",
                      }}
                    >
                      {amenity}
                    </Box>
                  </Box>
                ))}
              </Box>
            </Box>
            <Box
              sx={{
                width: "100%",
                background: "#F7F7F7",
                p: "8px 16px",
                boxSizing: "border-box",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                gap: 2,
              }}
            >
              <Box>
                <Typography
                  sx={{
                    fontSize: 16,
                    fontWeight: 600,
                    lineHeight: "19px",
                    color: "#1E3A8A",
                    fontFamily: "Roboto",
                  }}
                >
                  {`₹${formatIndianNumber(Math.round(hotelData?.price_per_person || 0))}`}{" "}
                </Typography>
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: 400,
                    lineHeight: "24px",
                    color: "#706E75",
                    fontFamily: "Roboto",
                  }}
                >
                  +{" "}
                  {`₹${formatIndianNumber(Math.round(hotelData?.price_per_person || 0))}`}{" "}
                  taxes & fees Per Night
                </Typography>
              </Box>
              <SecondaryButton py="5.5px" px="24px">
                Select
              </SecondaryButton>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default Rooms;
