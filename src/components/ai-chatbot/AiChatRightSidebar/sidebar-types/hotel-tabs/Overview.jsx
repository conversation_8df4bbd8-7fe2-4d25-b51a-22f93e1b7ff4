import React, { useMemo } from "react";
import { Box, Typography } from "@mui/material";
import StarIcon from "@mui/icons-material/Star";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import EmailIcon from "@mui/icons-material/Email";
import PhoneIcon from "@mui/icons-material/Phone";
import generateCloudFrontURL from "../../../../../utils";

const Overview = ({ hotelData }) => {
  console.log("hotelData: ", hotelData);
  const noImage = generateCloudFrontURL({
    bucket: process.env.REACT_APP_BUCKET_NAME,
    key: "no-image.jpg",
    width: 144,
    height: 144,
    fit: "cover",
  });
  const totalSlots = 5;
  const hotelImages =
    hotelData?.media?.length > 0
      ? hotelData.media?.slice(0, totalSlots)?.map((image) =>
          generateCloudFrontURL({
            bucket: process.env.REACT_APP_BUCKET_NAME,
            key: image || "no-image.jpg",
            width: 144,
            height: 144,
            fit: "cover",
          })
        )
      : [];

  const images = [
    ...hotelImages,
    ...Array(totalSlots - hotelImages?.length || 0).fill(noImage),
  ];

  const contactDetails = useMemo(() => {
    if (!hotelData?.contacts?.length) return { email: null, phoneNumbers: [] };

    const firstContact = hotelData?.contacts[0];

    return {
      email: firstContact.email?.[0] || null,
      phoneNumbers: firstContact.phone
        ? firstContact.phone.split(",").map((num) => num.trim())
        : [],
    };
  }, [hotelData?.contacts]);
  return (
    <Box sx={{ padding: "0px 16px 16px 16px", boxSizing: "border-box" }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          gap: 2,
        }}
      >
        <Box>
          <Box
            sx={{
              fontSize: 16,
              fontWeight: 600,
              lineHeight: "20px",
              color: "#333333",
            }}
          >
            {hotelData?.name || "N/A"}
          </Box>
        </Box>
        {hotelData?.rating && (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: "2px",
            }}
          >
            <Typography
              sx={{
                fontSize: 16,
                fontWeight: 500,
                lineHeight: "19px",
                color: "#333333",
              }}
            >
              {hotelData?.rating || "N/A"}
            </Typography>
            <StarIcon sx={{ color: "#00AF31" }} />
          </Box>
        )}
      </Box>
      <Box
        sx={{
          color: "#0083E7",
          display: "flex",
          alignItems: "center",
          gap: "2px",
          mt: 1.5,
        }}
      >
        {/* add mui location icon */}
        <LocationOnIcon sx={{ fontSize: "16px" }} />
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 400,
            fontStyle: "Regular",
            fontSize: "14px",
            lineHeight: "16px",
          }}
        >
          {hotelData?.hotel_address}
        </Typography>
      </Box>

      {contactDetails?.email && (
        <Box
          sx={{
            color: "#0083E7",
            display: "flex",
            alignItems: "center",
            gap: "4px",
            mt: 1.5,
          }}
        >
          {/* add mui location icon */}
          <EmailIcon sx={{ fontSize: "16px" }} />
          <Typography
            component="a"
            href={`mailto:${contactDetails?.email}`}
            sx={{
              fontFamily: "Roboto",
              fontWeight: 400,
              fontStyle: "Regular",
              fontSize: "14px",
              lineHeight: "16px",
              textDecoration: "none",
              textTransform: "lowercase",
              color: "inherit",
              "&:hover": {
                textDecoration: "underline",
              },
            }}
          >
            {contactDetails?.email}
          </Typography>
        </Box>
      )}

      {contactDetails?.phoneNumbers?.length > 0 && (
        <Box
          sx={{
            color: "#0083E7",
            display: "flex",
            alignItems: "center",
            gap: "4px",
            mt: 1.5,
          }}
        >
          {/* add mui location icon */}
          <PhoneIcon sx={{ fontSize: "16px" }} />
          {contactDetails?.phoneNumbers?.map((phone, index) => (
            <Typography
              key={index}
              component="a"
              href={`tel:${phone}`}
              sx={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontStyle: "Regular",
                fontSize: "14px",
                lineHeight: "16px",
                textDecoration: "none",
                color: "inherit",
                "&:hover": {
                  textDecoration: "underline",
                },
              }}
            >
              {phone}
              {index < contactDetails?.phoneNumbers.length - 1 ? ", " : ""}
            </Typography>
          ))}
        </Box>
      )}

      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: "repeat(3, 1fr)",
          gridTemplateRows: "repeat(2, 72px)",
          gridTemplateAreas: `
            "item1 item2 item3"
            "item1 item4 item5"
          `,
          gap: "4px",
          mt: 1.5,
          width: "100%",
          boxSizing: "border-box",
        }}
      >
        {images?.map((image, index) => (
          <Box
            key={index}
            sx={{
              gridArea: `item${index + 1}`,
              width: "100%",
              height: "100%",
              borderRadius: "4px",
              overflow: "hidden",
            }}
          >
            <img
              src={image}
              alt="hotel"
              width="100%"
              height="100%"
              style={{ objectFit: "cover" }}
            />
          </Box>
        ))}
      </Box>
      <Box mt={1.5}>
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 400,
            fontStyle: "Regular",
            fontSize: "14px",
            lineHeight: "18px",
            color: "#333333",
          }}
        >
          {hotelData?.description || ""}
        </Typography>
      </Box>
      <Box
        sx={{
          mt: 2.5,
        }}
      >
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 600,
            fontStyle: "SemiBold",
            fontSize: "14px",
            lineHeight: "16px",
            color: "#333333",
          }}
        >
          Amenities
        </Typography>
        <Box
          sx={{
            width: "30px",
            height: "2px",
            backgroundColor: "#FF3951",
          }}
        />
        <Box
          mt={0.5}
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 1,
            flexWrap: "wrap",
            width: "100%",
            boxSizing: "border-box",
            padding: "4px",
          }}
        >
          {hotelData?.amenities?.map((amenity, index) => (
            <Box
              key={amenity + index}
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 0.5,
                width: "max-content",
                flexShrink: 0,
              }}
            >
              <Box
                sx={{
                  minWidth: "4px",
                  minHeight: "4px",
                  width: "4px",
                  height: "4px",
                  backgroundColor: "#FF3951",
                  borderRadius: "2px",
                  flexShrink: 0,
                }}
              />
              <Box
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "14px",
                  lineHeight: "16px",
                  color: "#333333",
                }}
              >
                {amenity}
              </Box>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default Overview;
