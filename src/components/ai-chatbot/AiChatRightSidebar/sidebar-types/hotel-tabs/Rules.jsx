import React from "react";
import { Box, Typography } from "@mui/material";
import {
  PropertyRules,
  UserRead,
  IdProof,
  ListIcon,
} from "../../../../common/SvgIcon";

const Rules = () => {
  return (
    <Box
      sx={{
        width: "100%",
        height: "100%",
        p: "0px 16px 16px 16px",
        boxSizing: "border-box",
      }}
    >
      <Box>
        <Box
          sx={{
            width: "100%",
            display: "flex",
            gap: "4px",
            alignItems: "center",
          }}
        >
          <PropertyRules />
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 600,
              fontStyle: "SemiBold",
              fontSize: "16px",
              lineHeight: "19px",
              color: "#000000",
            }}
          >
            Property Rules
          </Typography>
        </Box>
        <Box
          sx={{
            mt: "12px",
            width: "100%",
            display: "flex",
            justifyContent: "space-evenly",
            alignItems: "center",
            gap: "4px",
            background: "#FFF9F9",
            p: "6px 12px",
            borderRadius: "4px",
            boxSizing: "border-box",
            border: "1px solid #FF39510D",
          }}
        >
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "16px",
              lineHeight: "19px",
              color: "#000000",
            }}
          >
            Check-in: 14:00
          </Typography>
          <Box sx={{ width: "1px", height: "28px", background: "#000000" }} />
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "16px",
              lineHeight: "19px",
              color: "#000000",
            }}
          >
            Check-out: 12:00
          </Typography>
        </Box>
        <Box
          sx={{
            my: "16px",
            width: "100%",
            height: "1px",
            borderBottom: "1px dashed #A6A6A680",
          }}
        />
      </Box>
      <Box>
        <Box
          sx={{
            width: "100%",
            display: "flex",
            gap: "4px",
            alignItems: "center",
          }}
        >
          <UserRead />
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 600,
              fontStyle: "SemiBold",
              fontSize: "16px",
              lineHeight: "19px",
              color: "#000000",
            }}
          >
            Must Read Rules
          </Typography>
        </Box>
        <Box
          sx={{
            mt: "12px",
            width: "100%",
            display: "flex",
            flexDirection: "column",
            gap: "12px",
          }}
        >
          <Box sx={{ display: "flex", gap: "2px" }}>
            <ListIcon />
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "14px",
                lineHeight: "16px",
                color: "#000000",
              }}
            >
              Outside food is not allowed.
            </Typography>
          </Box>
          <Box sx={{ display: "flex", gap: "2px" }}>
            <ListIcon />
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "14px",
                lineHeight: "16px",
                color: "#000000",
              }}
            >
              Primary Guest should be at least 18 years of age.
            </Typography>
          </Box>
        </Box>
        <Box
          sx={{
            my: "16px",
            width: "100%",
            height: "1px",
            borderBottom: "1px dashed #A6A6A680",
          }}
        />
      </Box>
      <Box>
        <Box
          sx={{
            width: "100%",
            display: "flex",
            gap: "4px",
            alignItems: "center",
          }}
        >
          <IdProof />
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 600,
              fontStyle: "SemiBold",
              fontSize: "16px",
              lineHeight: "19px",
              color: "#000000",
            }}
          >
            ID Proof Related
          </Typography>
        </Box>
        <Box
          sx={{
            mt: "12px",
            width: "100%",
            display: "flex",
            flexDirection: "column",
            gap: "12px",
          }}
        >
          <Box sx={{ display: "flex", gap: "2px" }}>
            <ListIcon />
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "14px",
                lineHeight: "16px",
                color: "#000000",
              }}
            >
              Passport, Aadhaar and Driving License are accepted as ID proof(s).
            </Typography>
          </Box>
          <Box sx={{ display: "flex", gap: "2px" }}>
            <ListIcon />
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "14px",
                lineHeight: "16px",
                color: "#000000",
              }}
            >
              Local ids are allowed.
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Rules;
