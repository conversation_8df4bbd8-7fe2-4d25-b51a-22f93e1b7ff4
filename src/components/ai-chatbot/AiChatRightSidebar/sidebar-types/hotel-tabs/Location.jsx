import React, { useMemo } from "react";
import { Box } from "@mui/material";
import { Typography } from "@mui/material";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import EmailIcon from "@mui/icons-material/Email";
import PhoneIcon from "@mui/icons-material/Phone";
import GoogleMap from "../../../../GoogleMap";

const Location = ({ hotelData = null }) => {
  const contactDetails = useMemo(() => {
    if (!hotelData?.contacts?.length) return { email: null, phoneNumbers: [] };

    const firstContact = hotelData?.contacts[0];

    return {
      email: firstContact.email?.[0] || null,
      phoneNumbers: firstContact.phone
        ? firstContact.phone.split(",").map((num) => num.trim())
        : [],
    };
  }, [hotelData?.contacts]);
  return (
    <Box sx={{ padding: "0px 16px", boxSizing: "border-box" }}>
      <Box>
        <Typography>Address</Typography>
        <Box
          sx={{
            color: "#0083E7",
            display: "flex",
            alignItems: "center",
            gap: "2px",
            mt: 1.5,
          }}
        >
          {/* add mui location icon */}
          <LocationOnIcon sx={{ fontSize: "16px" }} />
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 400,
              fontStyle: "Regular",
              fontSize: "14px",
              lineHeight: "16px",
            }}
          >
            {hotelData?.hotel_address}
          </Typography>
        </Box>

        {contactDetails?.email && (
          <Box
            sx={{
              color: "#0083E7",
              display: "flex",
              alignItems: "center",
              gap: "4px",
              mt: 1.5,
            }}
          >
            {/* add mui location icon */}
            <EmailIcon sx={{ fontSize: "16px" }} />
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontStyle: "Regular",
                fontSize: "14px",
                lineHeight: "16px",
              }}
            >
              {contactDetails?.email}
            </Typography>
          </Box>
        )}

        {contactDetails?.phoneNumbers?.length > 0 && (
          <Box
            sx={{
              color: "#0083E7",
              display: "flex",
              alignItems: "center",
              gap: "4px",
              mt: 1.5,
            }}
          >
            {/* add mui location icon */}
            <PhoneIcon sx={{ fontSize: "16px" }} />
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontStyle: "Regular",
                fontSize: "14px",
                lineHeight: "16px",
              }}
            >
              {contactDetails?.phoneNumbers?.map((phone) => phone).join(", ")}
            </Typography>
          </Box>
        )}
      </Box>
      {hotelData?.address?.location?.coordinates?.length > 0 && (
        <Box sx={{ mt: 3 }}>
          <GoogleMap
            address={hotelData?.hotel_address}
            lat={hotelData?.address?.location?.coordinates[1]}
            lng={hotelData?.address?.location?.coordinates[0]}
            height="236px"
            zoom={14}
          />
        </Box>
      )}
    </Box>
  );
};

export default Location;
