/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import { ArrowRight, CloseOutline } from "../../../common/SvgIcon";
import StarIcon from "@mui/icons-material/Star";
import {
  OverviewTab,
  InclusionsTab,
  ItineraryTab,
  HotelsTab,
  AddOnsTab,
  PolicyTab,
  MeetYourDestinationTab,
  ThreeSixtyViewTab,
} from "./package-tabs/PackagesTabs";
import { Box, Button, Typography, Fade } from "@mui/material";
import { formatIndianNumber } from "../../../../utils/helper";
import PackageTags from "./package-tags/PackageTags";

const PackageSidebar = ({
  packageWidth,
  onClose = () => {},
  packageData = null,
}) => {
  const tabs = [
    "Overview",
    "Itinerary",
    "Add-Ons",
    "360 view",
    "Policies, T&C",
    "Hotels",
    "Meet your destination",
  ];

  const [activeTab, setActiveTab] = useState(tabs[0]);
  const [fadeKey, setFadeKey] = useState(0);

  const keyFeature = [
    {
      title: "Owner",
      value: packageData?.owner || "ZUUMM",
    },
    {
      title: "Price",
      value: `INR ${formatIndianNumber(Math.round(packageData?.price_per_person || 0))}/per person`,
    },
    {
      title: "Duration",
      value: packageData?.duration || "Duration",
    },
    ...(packageData?.visa_type?.length > 0
      ? [
          {
            title: "Visa",
            value: packageData?.visa_type[0],
          },
        ]
      : []),
  ];

  useEffect(() => {
    setActiveTab(tabs[0]);
  }, [packageData?.package_no]);

  const handleTabClick = (tab) => {
    setActiveTab(tab);
    setFadeKey((prev) => prev + 1); // Force re-render for fade animation
  };

  const renderTabContent = (tab) => {
    switch (tab) {
      case "Overview":
        return <OverviewTab packageData={packageData} />;
      case "Inclusions":
        return <InclusionsTab packageData={packageData} />;
      case "Itinerary":
        return <ItineraryTab packageData={packageData} />;
      case "Hotels":
        return <HotelsTab packageData={packageData} />;
      case "Add-Ons":
        return <AddOnsTab packageData={packageData} />;
      case "360 view":
        return <ThreeSixtyViewTab packageData={packageData} />;
      case "Policies, T&C":
        return <PolicyTab packageData={packageData} />;
      case "Meet your destination":
        return <MeetYourDestinationTab packageData={packageData} />;

      default:
        return null;
    }
  };
  return (
    <Box
      sx={{
        width: packageWidth,
        height: "100%",
        zIndex: 1001,
        boxShadow: "0px 4px 7px 0px #00000008",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          width: "100%",
          background: "#F9F9F9",
          p: "10px 16px",
          boxSizing: "border-box",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 2,
        }}
      >
        <Typography
          sx={{
            fontSize: 14,
            fontWeight: 500,
            lineHeight: "18px",
            color: "#1E3A8A",
            maxWidth: "calc(100% - 44px)",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          {packageData?.package_no || "N/A"}
        </Typography>
        <CloseOutline onClick={onClose} />
      </Box>

      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          width: "100%",
          flex: 1,
          overflow: "hidden",
          maxHeight: "100%",
          p: "16px",
          boxSizing: "border-box",
        }}
      >
        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "space-between",
            mb: 1,
          }}
        >
          <Box sx={{ display: "flex", gap: 1, flex: 1, flexWrap: "wrap" }}>
            <PackageTags tag="luxury" />
            {/* <PackageTags tag="standard" /> */}
            {/* <PackageTags tag="premium" /> */}
            <PackageTags
              tag="customize"
              text={
                packageData?.type === "Custom AI" ||
                packageData?.type === "Custom Admin"
                  ? "Customizable"
                  : "Fixed"
              }
            />
          </Box>
          <Box>
            <Box display="flex" alignItems="center" gap={0}>
              <Typography
                sx={{
                  lineHeight: "16px",
                  color: "#333333",
                  fontFamily: "Roboto",
                  fontWeight: 500,
                  fontSize: 14,
                }}
              >
                {packageData?.rating || "N/A"}
              </Typography>
              {packageData?.rating > 0 && (
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: "2px",
                  }}
                >
                  {Array.from({
                    length: Math.floor(1 || 0),
                  }).map((_, index) => (
                    <StarIcon
                      key={index}
                      sx={{ color: "#00AF31", fontSize: 20 }}
                    />
                  ))}
                </Box>
              )}
            </Box>
          </Box>
        </Box>
        {/* header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            gap: 2,
          }}
        >
          <Box>
            <Box
              sx={{
                fontSize: 16,
                fontWeight: 600,
                lineHeight: "20px",
                color: "#333333",
              }}
            >
              {packageData?.title || "N/A"}
            </Box>
            <Box
              sx={{ display: "inline-flex", flexWrap: "wrap", gap: 1, mt: 1 }}
            >
              {keyFeature?.map((item, index) => (
                <Box
                  sx={{
                    display: "inline-flex",
                    gap: 1,
                    alignItems: "center",
                  }}
                  key={index + item?.title}
                >
                  <Box
                    sx={{
                      width: "3px",
                      height: "3px",
                      borderRadius: "50%",
                      background: "#1E3A8A",
                    }}
                  />
                  <Box
                    key={index + item?.title}
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      fontSize: 12,
                      fontWeight: 500,
                      lineHeight: "16px",
                      color: "#1E3A8A",
                    }}
                  >
                    {item?.value || item?.title}
                  </Box>
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
        {/* tabs */}
        <Box
          sx={{
            display: "inline-flex",
            width: "100%",
            background: "#FFF9F9",
            boxSizing: "border-box",
            padding: "6px 16px",
            borderRadius: "8px",
            flexWrap: "nowrap",
            overflowX: "auto",
            scrollbarWidth: "none",
            "&::-webkit-scrollbar": {
              display: "none",
            },
            gap: 1,
            alignItems: "center",
            my: "16px",
          }}
        >
          {tabs?.map((item) => (
            <Box
              key={item}
              sx={{
                display: "inline-flex",
                whiteSpace: "nowrap",
                alignItems: "center",
                width: "max-content",
                padding: "8px 12px",
                borderRadius: "8px",
                background: activeTab === item ? "#FFFFFF" : "transparent",
                fontSize: 14,
                fontWeight: 500,
                lineHeight: "16px",
                fontFamily: "Roboto",
                color: activeTab === item ? "#1E3A8A" : "#706E75",
                cursor: "pointer",
                transition: "all 0.3s ease",
                "&:hover": {
                  background: "#fbe8e8",
                },
              }}
              onClick={() => handleTabClick(item)}
            >
              {item}
            </Box>
          ))}
        </Box>
        {/* content */}
        <Box
          sx={{
            display: "flex",
            width: "100%",
            flex: 1,
            overflowY: "auto",
          }}
        >
          <Fade in={true} timeout={500} key={fadeKey}>
            <Box sx={{ width: "100%" }}>{renderTabContent(activeTab)}</Box>
          </Fade>
        </Box>
      </Box>
      {/* footer */}
      <Box
        sx={{
          width: "100%",
          background: "#F9F9F9",
          p: "8px 16px",
          boxSizing: "border-box",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 2,
        }}
      >
        <Typography
          sx={{
            fontSize: 16,
            fontWeight: 600,
            lineHeight: "19px",
            color: "#1E3A8A",
            fontFamily: "Roboto",
          }}
        >
          {`INR ${formatIndianNumber(Math.round(packageData?.price_per_person || 0))}`}
          <Box
            component="span"
            sx={{ fontSize: 14, fontWeight: 400, lineHeight: "16px" }}
          >
            /per person
          </Box>
        </Typography>
        <Button
          variant="basic"
          color="primary"
          sx={{
            padding: "8px",
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "12px",
            lineHeight: "14px",
            letterSpacing: "0.02em",
            textTransform: "capitalize",
            color: "#1E3A8A",
            gap: 1,
            "&:hover": {
              background: "#1e3b8a28",
            },
          }}
        >
          Customize
          <ArrowRight />
        </Button>
      </Box>
    </Box>
  );
};

export default PackageSidebar;
