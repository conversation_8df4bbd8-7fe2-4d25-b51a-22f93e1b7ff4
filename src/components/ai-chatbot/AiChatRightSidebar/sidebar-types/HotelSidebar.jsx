/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import { CloseOutline } from "../../../common/SvgIcon";
import { Box, Typography, Fade } from "@mui/material";
import { formatIndianNumber } from "../../../../utils/helper";
import { Location, Overview, Rooms, Rules } from "./hotel-tabs";
import PrimaryButton from "../../../common/Button/PrimaryButton";

const HotelSidebar = ({ hotelWidth, onClose = () => {}, hotelData = null }) => {
  console.log("hotelData: ", hotelData);
  const tabs = ["Overview", "Rooms", "Location", "Property Rules"];

  const [activeTab, setActiveTab] = useState(tabs[0]);
  const [fadeKey, setFadeKey] = useState(0);

  useEffect(() => {
    setActiveTab(tabs[0]);
  }, [hotelData?.id]);

  const handleTabClick = (tab) => {
    setActiveTab(tab);
    setFadeKey((prev) => prev + 1); // Force re-render for fade animation
  };

  const renderTabContent = (tab) => {
    switch (tab) {
      case "Overview":
        return <Overview hotelData={hotelData} />;
      case "Rooms":
        return <Rooms hotelData={hotelData} />;
      case "Location":
        return <Location hotelData={hotelData} />;
      case "Property Rules":
        return <Rules hotelData={hotelData} />;

      default:
        return null;
    }
  };
  return (
    <Box
      sx={{
        width: hotelWidth,
        height: "100%",
        zIndex: 1001,
        boxShadow: "0px 4px 7px 0px #00000008",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          width: "100%",
          background: "#F9F9F9",
          p: "10px 16px",
          boxSizing: "border-box",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 2,
        }}
      >
        <Typography
          sx={{
            fontSize: 14,
            fontWeight: 500,
            lineHeight: "18px",
            color: "#1E3A8A",
            maxWidth: "calc(100% - 44px)",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          Hotel Details
        </Typography>
        <CloseOutline onClick={onClose} />
      </Box>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          width: "100%",
          flex: 1,
          overflow: "hidden",
          maxHeight: "100%",
          p: "0px",
          boxSizing: "border-box",
        }}
      >
        {/* tabs */}
        <Box sx={{ padding: "16px 16px 20px 16px", boxSizing: "border-box" }}>
          <Box
            sx={{
              display: "inline-flex",
              width: "100%",
              background: "#FFF9F9",
              boxSizing: "border-box",
              padding: "0px 16px",
              borderRadius: "8px",
              flexWrap: "nowrap",
              overflowX: "auto",
              scrollbarWidth: "none",
              "&::-webkit-scrollbar": {
                display: "none",
              },
              gap: 1,
              alignItems: "center",
            }}
          >
            {tabs?.map((item) => (
              <Box
                key={item}
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  gap: 0,
                }}
              >
                <Box
                  key={item}
                  sx={{
                    display: "inline-flex",
                    whiteSpace: "nowrap",
                    alignItems: "center",
                    width: "max-content",
                    padding: "10px 12px 8px 12px",
                    fontWeight: 500,
                    fontSize: "14px",
                    lineHeight: "16px",
                    fontFamily: "Roboto",
                    color: activeTab === item ? "#1E3A8A" : "#706E75",
                    cursor: "pointer",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      color: "#1E3A8A",
                    },
                  }}
                  onClick={() => handleTabClick(item)}
                >
                  {item}
                </Box>
                <Box
                  sx={{
                    width: "100%",
                    height: "2px",
                    backgroundColor: activeTab === item ? "#FF3951" : "#FFF9F9",
                  }}
                />
              </Box>
            ))}
          </Box>
        </Box>
        {/* content */}
        <Box
          sx={{
            display: "flex",
            width: "100%",
            flex: 1,
            overflowY: "auto",
          }}
        >
          <Fade in={true} timeout={500} key={fadeKey}>
            <Box sx={{ width: "100%" }}>{renderTabContent(activeTab)}</Box>
          </Fade>
        </Box>
      </Box>
      {/* footer */}
      <Box
        sx={{
          width: "100%",
          background: "##FFFFFF",
          p: "8px 16px",
          boxSizing: "border-box",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 2,
          boxShadow: "0px -4px 8px 0px #0000000D",
        }}
      >
        <Box>
          <Typography
            sx={{
              fontSize: 16,
              fontWeight: 600,
              lineHeight: "19px",
              color: "#1E3A8A",
              fontFamily: "Roboto",
            }}
          >
            {`₹${formatIndianNumber(Math.round(hotelData?.base_price || 0))}`}{" "}
            <Box
              component="span"
              sx={{
                fontSize: 14,
                fontWeight: 400,
                lineHeight: "16px",
                color: "#333333",
              }}
            >
              per night
            </Box>
          </Typography>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: 400,
              lineHeight: "24px",
              color: "#706E75",
              fontFamily: "Roboto",
            }}
          >
            + {`₹${formatIndianNumber(Math.round(hotelData?.base_price || 0))}`}{" "}
            taxes & fees
          </Typography>
        </Box>
        <PrimaryButton>Book</PrimaryButton>
      </Box>
    </Box>
  );
};

export default HotelSidebar;
