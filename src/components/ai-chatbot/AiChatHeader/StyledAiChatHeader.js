import { styled } from "@mui/material/styles";
import { Box } from "@mui/material";

const StyledAiChatHeader = styled(Box)(({ theme }) => ({
  position: "fixed",
  top: 0,
  left: 0,
  right: 0,
  zIndex: 1000,
  backgroundColor: "#FFFFFF",
  boxShadow: "0px 4px 12px 0px #FF395112",

  ".header-content": {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: "7px 24px",
    maxWidth: "100%",
    boxSizing: "border-box",
    minHeight: "55px",
  },

  ".logo-container": {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    flex: 1,

    ".logo": {
      height: "18.5px",
      width: "92px",
    },
  },

  ".profile-section": {
    display: "flex",
    alignItems: "center",
  },
}));

export default StyledAiChatHeader;
