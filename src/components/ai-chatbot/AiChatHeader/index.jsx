import React from "react";
import { Box, Typography, Stack } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useNavigate } from "react-router-dom";
import logo from "../../../assets/svg/logo.svg";
import StyledAiChatHeader from "./StyledAiChatHeader";
import UserMenu from "../../Header/UserMenu";
import { useSelector } from "react-redux";

const AiChatHeader = () => {
  const navigate = useNavigate();
  const { userDetails } = useSelector((state) => state.authentication);

  const handleBackClick = () => {
    navigate(-1);
  };

  return (
    <StyledAiChatHeader>
      <Box className="header-content">
        <Stack
          direction={"row"}
          alignItems={"center"}
          gap={1}
          onClick={handleBackClick}
          sx={{
            color: "#0083E7",
            cursor: "pointer",
            transition: "transform 0.3s ease",
            "&:hover": {
              transform: "scale(0.9)",
              textDecoration: "underline",
            },
          }}
        >
          <ArrowBackIcon />
          <Typography
            variant="body1"
            sx={{
              fontSize: "16px",
              fontWeight: 500,
              lineHeight: "19px",
              fontFamily: "Roboto",
            }}
          >
            Back
          </Typography>
        </Stack>

        <Box className="logo-container">
          <Box component="img" src={logo} alt="Zuumm" className="logo" />
        </Box>

        <Box className="profile-section">
          <UserMenu name={userDetails?.user?.name || ""} />
        </Box>
      </Box>
    </StyledAiChatHeader>
  );
};

export default AiChatHeader;
