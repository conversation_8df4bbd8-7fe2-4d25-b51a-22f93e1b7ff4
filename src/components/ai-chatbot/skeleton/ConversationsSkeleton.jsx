import { Skeleton } from "@mui/material";
import { Box } from "@mui/material";
import React from "react";

const ConversationsSkeleton = () => {
  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        padding: "11px 0px",
      }}
    >
      <Skeleton
        variant="text"
        width={"80%"}
        height={16}
        sx={{
          backgroundColor: "#f0f0f0",
          "&::after": {
            background:
              "linear-gradient(90deg, transparent, #e0e0e0, transparent)",
          },
        }}
      />
      <Skeleton
        variant="circular"
        width={16}
        height={16}
        sx={{
          backgroundColor: "#f0f0f0",
          "&::after": {
            background:
              "linear-gradient(90deg, transparent, #e0e0e0, transparent)",
          },
        }}
      />
    </Box>
  );
};

export default ConversationsSkeleton;
