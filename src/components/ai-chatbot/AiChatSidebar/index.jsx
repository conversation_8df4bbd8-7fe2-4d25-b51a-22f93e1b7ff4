import React, { useState } from "react";
import { Box, Typography, List, ListItem } from "@mui/material";

import StyledAiChatSidebar from "./StyledAiChatSidebar";
import { RobotSmall, NewChat, Forward } from "../../common/SvgIcon";
import ChatMenuOption from "./ChatMenuOption";

const AiChatSidebar = () => {
  const [recentChats] = useState([
    {
      id: "recent-chat-1",
      title: "Travel planning for Bali and Bali ",
    },
    { id: "recent-chat-2", title: "Best restaurants in Paris" },
    { id: "recent-chat-3", title: "Budget travel tips" },
    { id: "recent-chat-4", title: "Solo travel safety" },
    { id: "recent-chat-5", title: "Packing list for Europe" },
  ]);

  const [preferences] = useState([
    { id: "preference-1", title: "Language" },
    { id: "preference-2", title: "Theme" },
    { id: "preference-3", title: "Notifications" },
    { id: "preference-4", title: "Privacy" },
    { id: "preference-5", title: "Help" },
  ]);

  const [activeChatId, setActiveChatId] = useState(null);

  const handleNewChat = () => {
    // Handle new chat creation
    console.log("New chat created");
  };

  const handleChatClick = (chatId) => {
    setActiveChatId(chatId);
  };

  const handleChatOptions = (chatId, action) => {
    console.log("Chat options for:", chatId, action);
  };

  return (
    <StyledAiChatSidebar>
      <Box className="sidebar-robot-section">
        <RobotSmall />
      </Box>
      <Box className="sidebar-new-chat-section" onClick={handleNewChat}>
        <NewChat />
        <Typography className="sidebar-new-chat-text">
          Start New Chat
        </Typography>
      </Box>

      <Box className="sidebar-recent-chats-section">
        <Typography className="sidebar-recent-chats-title">Chats</Typography>
        <List className="chat-list">
          {recentChats.map((chat) => (
            <ListItem
              key={chat.id}
              className={`chat-item ${chat.id === activeChatId ? "active" : ""}`}
              button
              onClick={(e) => {
                e.stopPropagation();
                console.log("Chat clicked:", chat.id);
                handleChatClick(chat.id);
              }}
            >
              <Typography className="chat-text">{chat.title}</Typography>
              <ChatMenuOption
                chatId={chat.id}
                handleChatOptions={handleChatOptions}
              />
            </ListItem>
          ))}
          <Box className="scroll-shadow" />
        </List>
      </Box>
      <Box className="sidebar-horizontal-line" />

      <Box className="sidebar-recent-chats-section">
        <Typography className="sidebar-recent-chats-title">
          Preferences
        </Typography>
        <List className="chat-list">
          {preferences.map((chat) => (
            <ListItem
              key={chat.id}
              className={`chat-item ${chat.id === activeChatId ? "active" : ""}`}
              button
              onClick={(e) => {
                e.stopPropagation();
                console.log("Chat clicked:", chat.id);
                handleChatClick(chat.id);
              }}
            >
              <Typography className="chat-text">{chat.title}</Typography>
              <ChatMenuOption
                chatId={chat.id}
                handleChatOptions={handleChatOptions}
              />
            </ListItem>
          ))}
          <Box className="scroll-shadow" />
        </List>
      </Box>
      <Box className="sidebar-bottom-section">
        <Typography className="sidebar-support-title">Support</Typography>
        <Forward />
      </Box>
    </StyledAiChatSidebar>
  );
};

export default AiChatSidebar;
