import React, { useState } from "react";
import { Typography, Menu, MenuItem, Box } from "@mui/material";
import { VerticalOption } from "../../common/SvgIcon";

const ChatMenuOption = ({ chatId, handleChatOptions }) => {
  const [userMenuAnchor, setUserMenuAnchor] = useState(null);

  const menuItems = [
    {
      label: "Rename",
      icon: "/static/menu-drop/avatar.png",
      onClick: () => {
        handleChatOptions(chatId, "edit");
        handleUserMenuClose();
      },
    },

    {
      label: "Favorite",
      icon: "/static/menu-drop/saved.png",
      onClick: () => {
        handleChatOptions(chatId, "save");
        handleUserMenuClose();
      },
    },
    {
      label: "Delete",
      icon: "/static/menu-drop/saved.png",
      onClick: () => {
        handleChatOptions(chatId, "delete");
        handleUserMenuClose();
      },
    },
  ];

  const handleUserMenuOpen = (event) => {
    event.stopPropagation();
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  return (
    <Box onClick={(e) => e.stopPropagation()}>
      <VerticalOption
        className={`chat-options ${userMenuAnchor ? "open-menu" : ""}`}
        onClick={handleUserMenuOpen}
      />
      <Menu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={handleUserMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 170,
            boxShadow: "0px 0px 8px 0px #0000000D",
            borderRadius: "10px",
            border: "1px solid #FF39510D",
          },
        }}
        MenuListProps={{
          sx: {
            padding: 0,
          },
        }}
      >
        {menuItems.map((item, index) => (
          <MenuItem
            sx={{
              p: 1.5,
              borderBottom:
                index !== menuItems.length - 1 &&
                "1px solid rgba(51, 51, 51, 0.05)",
              display: "flex",
              alignItems: "center",
              gap: 0.5,
              "&:hover": {
                backgroundColor: "#FF39510D",
              },
            }}
            onClick={item?.onClick}
            key={item.label}
          >
            <Box
              component={"img"}
              src={item.icon}
              alt={item.label}
              sx={{ width: "20px", height: "20px" }}
            />
            <Typography
              variant="body2"
              sx={{
                fontSize: "14px",
                lineHeight: "16px",
                fontWeight: 400,
                fontFamily: "Roboto",
                color: "#333333",
              }}
            >
              {item.label}
            </Typography>
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};

export default ChatMenuOption;
