import { styled } from "@mui/material/styles";
import { Box } from "@mui/material";

const StyledAiChatSidebar = styled(Box)(({ theme }) => ({
  position: "fixed",
  top: "80px", // Below the header
  left: 0,
  height: "calc(100dvh - 80px)",
  zIndex: 999,
  transition: "width 0.3s ease",
  width: "243px",
  overflow: "hidden",
  backgroundColor: "#FFF9F9",
  display: "flex",
  flexDirection: "column",

  ".sidebar-robot-section": {
    background: "linear-gradient(90deg, #FEEBEE 0%, #F2EBFA 125.89%)",
    boxSizing: "border-box",
    padding: "10px 24px",
  },

  ".sidebar-new-chat-section": {
    marginTop: "8px",
    marginBottom: "20px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    gap: "12px",
    padding: "13px",
    color: "#FF3951",
    cursor: "pointer",
    transition: "transform 0.3s ease",
    "&:hover": {
      transform: "scale(1.05)",
      ".sidebar-new-chat-text": {
        textDecoration: "underline",
      },
    },

    ".sidebar-new-chat-text": {
      fontFamily: "Roboto",
      fontSize: "16px",
      lineHeight: "19px",
      fontWeight: 500,
      letterSpacing: "0.05em",
    },
  },

  ".sidebar-horizontal-line": {
    width: "100%",
    height: "1px",
    backgroundColor: "#FF39510D",
    margin: "20px 0px",
  },

  ".sidebar-recent-chats-section": {
    ".sidebar-recent-chats-title": {
      fontFamily: "Roboto",
      fontSize: "12px",
      lineHeight: "14px",
      fontWeight: 500,
      letterSpacing: "0.05em",
      color: "#1E3A8A",
      padding: "0px 24px",
      marginBottom: "12px",
    },
    ".chat-list": {
      display: "flex",
      flexDirection: "column",
      gap: "4px",
      padding: "0px",
      maxHeight: "calc(150px)",
      overflowY: "auto",
      position: "relative",

      "&::-webkit-scrollbar": {
        background: "transparent",
        width: "3px",
        borderRadius: "3px",
      },
      "&::-webkit-scrollbar-thumb": {
        background: "#ff39504b",
      },
      "&::-webkit-scrollbar-track": {
        background: "transparent",
      },

      ".chat-item": {
        padding: "11px 12px 11px 24px",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        cursor: "pointer",
        "&:hover": {
          backgroundColor: "#FFFFFF",
          ".chat-options": {
            display: "block",
          },
        },

        "&.active": {
          backgroundColor: "#FFFFFF",
          ".chat-options": {
            display: "block",
          },
          ".chat-text": {
            color: "#333333",
          },
        },

        ".chat-options": {
          display: "none",
          transition: "all 0.3s ease",
          "&.open-menu": {
            display: "block",
          },
          "&:hover": {
            transform: "scale(1.1)",
            ".open-menu": {
              display: "block",
            },
          },
        },

        ".chat-text": {
          fontFamily: "Roboto",
          fontSize: "12px",
          lineHeight: "14px",
          fontWeight: 500,
          color: "#706E75",
          maxWidth: "170px",
          overflow: "hidden",
          textOverflow: "ellipsis",
          whiteSpace: "nowrap",
        },
      },
    },
  },

  ".sidebar-bottom-section": {
    marginTop: "auto",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: "15px 12px 15px 24px",
    cursor: "pointer",
    color: "#706E75",
    transition: "all 0.3s ease",
    "&:hover": {
      backgroundColor: "#FFFFFF",
      color: "#333333",
    },

    ".sidebar-support-title": {
      fontFamily: "Roboto",
      fontSize: "16px",
      lineHeight: "19px",
      fontWeight: 500,
    },
  },
}));

export default StyledAiChatSidebar;
