/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useRef, useEffect } from "react";
import {
  Box,
  IconButton,
  TextField,
  CircularProgress,
  Typography,
} from "@mui/material";
import SpeechRecognition, {
  useSpeechRecognition,
} from "react-speech-recognition";
import { Add, Mic, Send } from "../../common/SvgIcon";
import ChatSection from "./ChatSection";
import { fixedKeyWords } from "./chatData";
import FixedKeyWords from "./suggestions/FixedKeyWords";

const ChatInterface = ({
  openRightSidebar = () => {},
  newChat = true,
  canSendMessage = false,
  handleStartNewConversation = () => {},
  handleSendMessage = () => {},
  conversationId = "",
  ongoingChatLoading = false,
  disableSendMessage = false,
  enableSendMessage = false,
  conversationChats = [],
  error = null,
  handleSendSelectedOption = () => {},
  handleSetPackageData = () => {},
  handleSendCounterData = () => {},
  selectedConversation = "",
  handleSetHotelData = () => {},
}) => {
  const isInputActive = canSendMessage && !ongoingChatLoading;
  const isAiSuggestionActive = false;
  const [inputValue, setInputValue] = useState("");
  const [isListening, setIsListening] = useState(false);
  const chatContainerRef = useRef(null);

  const {
    transcript,
    listening,
    browserSupportsSpeechRecognition,
    resetTranscript,
  } = useSpeechRecognition();

  // Update input value when transcript changes
  useEffect(() => {
    if (transcript) {
      setInputValue(transcript);
    }
  }, [transcript]);

  // Update listening state when speech recognition listening state changes
  useEffect(() => {
    setIsListening(listening);
  }, [listening]);

  const toggleListening = () => {
    if (listening) {
      SpeechRecognition.stopListening();
    } else {
      SpeechRecognition.startListening({ continuous: true });
    }
  };

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    setInputValue("");
    resetTranscript();
    setIsListening(false);
  }, [selectedConversation]);

  const sendMessage = () => {
    if (inputValue.trim() === "") return;

    // Stop listening if currently listening
    if (listening) {
      SpeechRecognition.stopListening();
      // reset transcript
      resetTranscript();
    }

    if (newChat) {
      handleStartNewConversation(inputValue);
    } else {
      handleSendMessage(inputValue, conversationId);
    }

    setInputValue("");
    // Reset transcript after sending message
    // resetTranscript();
  };

  const handleKeyDown = (e) => {
    // Stop listening if currently listening and any key is pressed
    if (listening) {
      SpeechRecognition.stopListening();
    }

    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleInputFocus = () => {
    // Stop listening when input is focused
    if (listening) {
      SpeechRecognition.stopListening();
    }
  };

  const handleInputChange = (e) => {
    // Stop listening when user starts typing
    if (listening) {
      SpeechRecognition.stopListening();
    }
    setInputValue(e.target.value);
  };

  // Scroll to bottom on first load
  useEffect(() => {
    scrollToBottom();
  }, []);

  // Scroll to bottom when new messages are added
  useEffect(() => {
    scrollToBottom();
  }, [conversationChats?.length]);

  // Show loading state
  if (ongoingChatLoading === "1") {
    return (
      <Box
        sx={{
          width: "100%",
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          flexDirection: "column",
          gap: 2,
        }}
      >
        <CircularProgress />
        <Typography variant="body2" color="text.secondary">
          Loading conversations...
        </Typography>
      </Box>
    );
  }

  // Show error state
  if (error) {
    return (
      <Box
        sx={{
          width: "100%",
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          flexDirection: "column",
          gap: 2,
        }}
      >
        <Typography variant="h6" color="error">
          Error loading conversations
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {error}
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        width: "100%",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        maxWidth: "790px",
        boxSizing: "border-box",
        mx: "auto",
      }}
    >
      <Box
        ref={chatContainerRef}
        sx={{
          height: isAiSuggestionActive
            ? "calc(100% - 98px - 40px)"
            : "calc(100% - 98px)",
          px: 2,
          boxSizing: "border-box",
          overflowY: "auto",
        }}
      >
        <ChatSection
          chats={conversationChats}
          openRightSidebar={openRightSidebar}
          handleStartNewConversation={handleStartNewConversation}
          handleSendMessage={handleSendMessage}
          disableSendMessage={disableSendMessage}
          enableSendMessage={enableSendMessage}
          newChat={newChat}
          ongoingChatLoading={ongoingChatLoading}
          conversationId={conversationId}
          handleSendSelectedOption={handleSendSelectedOption}
          handleSetPackageData={handleSetPackageData}
          handleSendCounterData={handleSendCounterData}
          handleSetHotelData={handleSetHotelData}
        />
      </Box>

      {/* bottom section: input */}
      <Box
        sx={{
          height: `calc(98px + ${isAiSuggestionActive ? "40px" : "0px"})`,
          pt: 1.5,
          display: "flex",
          flexDirection: "column",
          width: "100%",
          px: 2,
          boxSizing: "border-box",
        }}
      >
        {isAiSuggestionActive && (
          <Box
            sx={{
              mb: 1.5,
              width: "fit-content",
              maxWidth: "100%",
              mx: "auto",
              display: "flex",
              gap: "8px",
              boxSizing: "border-box",
              overflowX: "auto",
              scrollbarWidth: "none",
              "&::-webkit-scrollbar": {
                display: "block",
              },
            }}
          >
            {fixedKeyWords?.slice(0, 30).map((item, index) => (
              <FixedKeyWords key={index} item={item} />
            ))}
          </Box>
        )}

        <Box
          sx={{
            padding: "0px", // border thickness
            borderRadius: "12.5px",
            background: isInputActive
              ? "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)"
              : "#F6F6F6",
            width: "100%",
            // height: "100%",
            flex: 1,
            boxSizing: "border-box",
            boxShadow: "0px 0px 8px 1px #00000012",
          }}
        >
          <Box
            sx={{
              background: isInputActive ? "#FFFFFF" : "#F6F6F6",
              borderRadius: "11.5px",
              width: "100%",
              height: "100%",
              overflow: "hidden",
              p: 1.5,
              boxSizing: "border-box",
              display: "flex",
              gap: 1.5,
            }}
          >
            <Box
              sx={{
                width: "calc(100% - 40px - 12px)",
                height: "100%",
                display: "flex",
                flexDirection: "column",
              }}
            >
              <Box
                sx={{
                  width: "100%",
                  height: "calc(100% - 24px)",
                  maxHeight: "40px",
                  display: "flex",
                  flexDirection: "column",
                  boxSizing: "border-box",
                }}
              >
                {/* add text field mui */}
                <TextField
                  sx={{
                    height: "100%",
                    boxSizing: "border-box",
                    width: "100%",
                    "& .MuiInputBase-root": {
                      backgroundColor: "transparent",
                      height: "100%",
                      p: "2px",
                      display: "block",
                      overflowY: "auto",
                      fontFamily: "Roboto",
                      fontSize: "14px",
                      fontWeight: 400,
                      lineHeight: "16px",
                      color: "#000000",
                      "&::placeholder": {
                        color: "#706E75",
                      },
                    },

                    "& .MuiOutlinedInput-notchedOutline": {
                      border: "none",
                    },
                  }}
                  multiline
                  placeholder="Ask anything"
                  disabled={!isInputActive}
                  value={inputValue}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyDown}
                  onFocus={handleInputFocus}
                />
              </Box>
              <Box sx={{ width: "100%", height: "24px" }}>
                <IconButton sx={{ p: 0 }} disabled={!isInputActive}>
                  <Add width="20px" height="20px" disabled={!isInputActive} />
                </IconButton>
              </Box>
            </Box>
            <Box
              sx={{
                width: "64px",
                height: "100%",
                display: "flex",
                flexDirection: "row-reverse",
                gap: "10px",
                justifyContent: "space-between",
                alignItems: "flex-end",
              }}
            >
              <IconButton
                sx={{ p: 0 }}
                disabled={
                  !isInputActive || inputValue?.trim() === "" || !inputValue
                }
                onClick={sendMessage}
              >
                <Send
                  width="30px"
                  height="30px"
                  active={
                    isInputActive && inputValue?.trim() !== "" && inputValue
                  }
                />
              </IconButton>
              <IconButton
                sx={{
                  p: 0,
                  ...(isListening && {
                    animation: "pulse 1.5s ease-in-out infinite",
                    "@keyframes pulse": {
                      "0%": {
                        transform: "scale(1)",
                        opacity: 1,
                      },
                      "50%": {
                        transform: "scale(1.1)",
                        opacity: 0.7,
                      },
                      "100%": {
                        transform: "scale(1)",
                        opacity: 1,
                      },
                    },
                  }),
                }}
                disabled={!isInputActive || !browserSupportsSpeechRecognition}
                onClick={toggleListening}
              >
                <Mic active={isInputActive && isListening} />
              </IconButton>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ChatInterface;
