/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useRef, useEffect } from "react";
import {
  Box,
  IconButton,
  TextField,
  CircularProgress,
  Typography,
} from "@mui/material";
import SpeechRecognition, {
  useSpeechRecognition,
} from "react-speech-recognition";
import { Add, Mic, Send, CloseOutline } from "../../common/SvgIcon";
import ChatSection from "./ChatSection";
import { fixedKeyWords } from "./chatData";
import FixedKeyWords from "./suggestions/FixedKeyWords";

const ChatInterface = ({
  openRightSidebar = () => {},
  newChat = true,
  canSendMessage = false,
  handleStartNewConversation = () => {},
  handleSendMessage = () => {},
  conversationId = "",
  ongoingChatLoading = false,
  disableSendMessage = false,
  enableSendMessage = false,
  conversationChats = [],
  error = null,
  handleSendSelectedOption = () => {},
  handleSetPackageData = () => {},
  handleSendCounterData = () => {},
  selectedConversation = "",
  handleSetHotelData = () => {},
}) => {
  const isInputActive = canSendMessage && !ongoingChatLoading;
  const isAiSuggestionActive = false;
  const [inputValue, setInputValue] = useState("");
  const [isListening, setIsListening] = useState(false);
  const [mediaFile, setMediaFile] = useState(null);
  const chatContainerRef = useRef(null);
  const fileInputRef = useRef(null);

  const {
    transcript,
    listening,
    browserSupportsSpeechRecognition,
    resetTranscript,
  } = useSpeechRecognition();

  // Update input value when transcript changes
  useEffect(() => {
    if (transcript) {
      setInputValue(transcript);
    }
  }, [transcript]);

  // Update listening state when speech recognition listening state changes
  useEffect(() => {
    setIsListening(listening);
  }, [listening]);

  const toggleListening = () => {
    if (listening) {
      SpeechRecognition.stopListening();
    } else {
      SpeechRecognition.startListening({ continuous: true });
    }
  };

  const scrollToBottom = ({ behavior = "auto" } = {}) => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior,
      });
    }
  };

  useEffect(() => {
    setInputValue("");
    resetTranscript();
    setIsListening(false);
    setMediaFile(null);
  }, [selectedConversation]);

  // File upload handler
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Check if file is an image
    if (!file.type.startsWith("image/")) {
      alert("Only image files are supported");
      return;
    }

    // Check file size (10MB = 10 * 1024 * 1024 bytes)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      alert("File size must be less than 10MB");
      return;
    }

    // Create a preview URL for the image
    const reader = new FileReader();
    reader.onload = (e) => {
      setMediaFile(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  // Remove media file
  const handleRemoveMediaFile = () => {
    setMediaFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Trigger file input
  const handleAddFileClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const sendMessage = () => {
    if (inputValue.trim() === "") return;

    // Stop listening if currently listening
    if (listening) {
      SpeechRecognition.stopListening();
      // reset transcript
      resetTranscript();
    }

    if (newChat) {
      handleStartNewConversation(inputValue, mediaFile);
    } else {
      handleSendMessage(inputValue, conversationId, {}, mediaFile);
    }

    // Clear input and media file after sending
    setInputValue("");
    setMediaFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleKeyDown = (e) => {
    // Stop listening if currently listening and any key is pressed
    if (listening) {
      SpeechRecognition.stopListening();
    }

    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleInputFocus = () => {
    // Stop listening when input is focused
    if (listening) {
      SpeechRecognition.stopListening();
    }
  };

  const handleInputChange = (e) => {
    // Stop listening when user starts typing
    if (listening) {
      SpeechRecognition.stopListening();
    }
    setInputValue(e.target.value);
  };

  // Scroll to bottom on first load
  useEffect(() => {
    scrollToBottom();
  }, []);

  // Scroll to bottom when new messages are added
  useEffect(() => {
    scrollToBottom({ behavior: "smooth" });
  }, [conversationChats?.length, ongoingChatLoading]);

  // Show loading state
  if (ongoingChatLoading === "1") {
    return (
      <Box
        sx={{
          width: "100%",
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          flexDirection: "column",
          gap: 2,
        }}
      >
        <CircularProgress />
        <Typography variant="body2" color="text.secondary">
          Loading conversations...
        </Typography>
      </Box>
    );
  }

  // Show error state
  if (error) {
    return (
      <Box
        sx={{
          width: "100%",
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          flexDirection: "column",
          gap: 2,
        }}
      >
        <Typography variant="h6" color="error">
          Error loading conversations
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {error}
        </Typography>
      </Box>
    );
  }

  const getSuggestiveQuestions = () => {
    if (
      conversationChats?.length > 0 &&
      conversationChats[conversationChats?.length - 1]?.message_data
        ?.suggestive_questions?.length > 0
    ) {
      return conversationChats[conversationChats?.length - 1]?.message_data
        ?.suggestive_questions;
    }
    return [];
  };

  return (
    <Box
      sx={{
        width: "100%",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        maxWidth: "790px",
        boxSizing: "border-box",
        mx: "auto",
      }}
    >
      <Box
        ref={chatContainerRef}
        sx={{
          height: `calc(100% - 98px - ${isAiSuggestionActive ? "40px" : "0px"} - ${
            mediaFile ? "60px" : "0px"
          })`,
          px: 2,
          display: "flex",
          flexDirection: "column",
          boxSizing: "border-box",
          overflowY: "auto",
        }}
      >
        <ChatSection
          chats={conversationChats}
          openRightSidebar={openRightSidebar}
          handleStartNewConversation={handleStartNewConversation}
          handleSendMessage={handleSendMessage}
          disableSendMessage={disableSendMessage}
          enableSendMessage={enableSendMessage}
          newChat={newChat}
          ongoingChatLoading={ongoingChatLoading}
          conversationId={conversationId}
          handleSendSelectedOption={handleSendSelectedOption}
          handleSetPackageData={handleSetPackageData}
          handleSendCounterData={handleSendCounterData}
          handleSetHotelData={handleSetHotelData}
        />
        {getSuggestiveQuestions()?.length > 0 && (
          <Box sx={{ height: "fit-content", pt: 2.5, boxSizing: "border-box" }}>
            <Box
              sx={{
                display: "flex",
                width: "100%",
                flexDirection: "column",
                gap: 1.5,
                alignItems: "flex-end",
              }}
            >
              {getSuggestiveQuestions()?.map((item, index) => (
                <Box
                  key={index}
                  sx={{
                    height: "fit-content",
                    bgcolor: "blue",
                    p: "8px 12px",
                    borderRadius: "8px",
                    border: "1px solid #FF39510D",
                    background: "#FFFFFF",
                    width: "fit-content",
                    maxWidth: "50%",
                    boxSizing: "border-box",
                    cursor: "pointer",
                    fontFamily: "Roboto",
                    fontWeight: 400,
                    fontSize: "12px",
                    lineHeight: "14px",
                    color: "#333333",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      background: "#FF39510D",
                    },
                  }}
                  onClick={() => {
                    handleSendMessage(item, conversationId, {});
                  }}
                >
                  {typeof item === "string" ? item : ""}
                </Box>
              ))}
            </Box>
          </Box>
        )}
      </Box>

      {/* bottom section: input */}
      <Box
        sx={{
          height: `calc(98px + ${isAiSuggestionActive ? "40px" : "0px"} + ${
            mediaFile ? "60px" : "0px"
          })`,
          pt: 1.5,
          display: "flex",
          flexDirection: "column",
          width: "100%",
          px: 2,
          boxSizing: "border-box",
        }}
      >
        {isAiSuggestionActive && (
          <Box
            sx={{
              mb: 1.5,
              width: "fit-content",
              maxWidth: "100%",
              mx: "auto",
              display: "flex",
              gap: "8px",
              boxSizing: "border-box",
              overflowX: "auto",
              scrollbarWidth: "none",
              "&::-webkit-scrollbar": {
                display: "block",
              },
            }}
          >
            {fixedKeyWords?.slice(0, 30).map((item, index) => (
              <FixedKeyWords key={index} item={item} />
            ))}
          </Box>
        )}

        {mediaFile && (
          <Box
            sx={{
              py: "5px",
            }}
          >
            <Box
              sx={{
                width: "50px",
                height: "50px",
                position: "relative",
              }}
            >
              <Box
                component="img"
                src={mediaFile}
                alt="media-file"
                sx={{
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                  borderRadius: "4px",
                  boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
                }}
              />
              <IconButton
                onClick={handleRemoveMediaFile}
                sx={{
                  position: "absolute",
                  top: "-8px",
                  right: "-8px",
                  color: "white",
                  width: "24px",
                  height: "24px",
                  p: 0,
                }}
              >
                <CloseOutline
                  width="20px"
                  height="20px"
                  sx={{
                    background: "#FFF",
                    borderRadius: "50%",
                    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
                  }}
                />
              </IconButton>
            </Box>
          </Box>
        )}

        <Box
          sx={{
            padding: "0px", // border thickness
            borderRadius: "12.5px",
            background: isInputActive
              ? "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)"
              : "#F6F6F6",
            width: "100%",
            // height: "100%",
            flex: 1,
            boxSizing: "border-box",
            boxShadow: "0px 0px 8px 1px #00000012",
          }}
        >
          <Box
            sx={{
              background: isInputActive ? "#FFFFFF" : "#F6F6F6",
              borderRadius: "11.5px",
              width: "100%",
              height: "100%",
              overflow: "hidden",
              p: 1.5,
              boxSizing: "border-box",
              display: "flex",
              gap: 1.5,
            }}
          >
            <Box
              sx={{
                width: "calc(100% - 40px - 12px)",
                height: "100%",
                display: "flex",
                flexDirection: "column",
              }}
            >
              <Box
                sx={{
                  width: "100%",
                  height: "calc(100% - 24px)",
                  maxHeight: "40px",
                  display: "flex",
                  flexDirection: "column",
                  boxSizing: "border-box",
                }}
              >
                {/* add text field mui */}
                <TextField
                  sx={{
                    height: "100%",
                    boxSizing: "border-box",
                    width: "100%",
                    "& .MuiInputBase-root": {
                      backgroundColor: "transparent",
                      height: "100%",
                      p: "2px",
                      display: "block",
                      overflowY: "auto",
                      fontFamily: "Roboto",
                      fontSize: "14px",
                      fontWeight: 400,
                      lineHeight: "16px",
                      color: "#000000",
                      "&::placeholder": {
                        color: "#706E75",
                      },
                    },

                    "& .MuiOutlinedInput-notchedOutline": {
                      border: "none",
                    },
                  }}
                  multiline
                  placeholder="Ask anything"
                  disabled={!isInputActive}
                  value={inputValue}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyDown}
                  onFocus={handleInputFocus}
                />
              </Box>
              <Box sx={{ width: "100%", height: "24px" }}>
                <IconButton
                  sx={{ p: 0 }}
                  disabled={!isInputActive || !!mediaFile}
                  onClick={handleAddFileClick}
                >
                  <Add
                    width="20px"
                    height="20px"
                    disabled={!isInputActive || !!mediaFile}
                  />
                </IconButton>
                {/* Hidden file input */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  style={{ display: "none" }}
                />
              </Box>
            </Box>
            <Box
              sx={{
                width: "64px",
                height: "100%",
                display: "flex",
                flexDirection: "row-reverse",
                gap: "10px",
                justifyContent: "space-between",
                alignItems: "flex-end",
              }}
            >
              <IconButton
                sx={{ p: 0 }}
                disabled={!isInputActive || inputValue?.trim() === ""}
                onClick={sendMessage}
              >
                <Send
                  width="30px"
                  height="30px"
                  active={isInputActive && inputValue?.trim() !== ""}
                />
              </IconButton>
              <IconButton
                sx={{
                  p: 0,
                  ...(isListening && {
                    animation: "pulse 1.5s ease-in-out infinite",
                    "@keyframes pulse": {
                      "0%": {
                        transform: "scale(1)",
                        opacity: 1,
                      },
                      "50%": {
                        transform: "scale(1.1)",
                        opacity: 0.7,
                      },
                      "100%": {
                        transform: "scale(1)",
                        opacity: 1,
                      },
                    },
                  }),
                }}
                disabled={!isInputActive || !browserSupportsSpeechRecognition}
                onClick={toggleListening}
              >
                <Mic active={isInputActive && isListening} />
              </IconButton>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ChatInterface;
