import React from "react";
import { Box } from "@mui/material";

const LoadingMessage = () => {
  return (
    <Box
      sx={{
        background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
        backgroundClip: "text",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        animation: "gradient 2s ease infinite",
        backgroundSize: "200% auto",
        fontWeight: 500,
        letterSpacing: "0.02em",
        "@keyframes gradient": {
          "0%": { backgroundPosition: "0% 50%" },
          "50%": { backgroundPosition: "100% 50%" },
          "100%": { backgroundPosition: "0% 50%" },
        },
        "@keyframes pulse": {
          "0%, 100%": { opacity: 1 },
          "50%": { opacity: 0.2 },
        },
      }}
    >
      {"Generating with AI"}
      {["0.2s", "0.5s", "0.8s"].map((delay, idx) => (
        <Box
          key={idx}
          component="span"
          sx={{
            animation: `pulse 1s infinite`,
            animationDelay: delay,
            background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            fontWeight: 500,
            letterSpacing: "0.02em",
            fontSize: "24px",
            lineHeight: "20px",
            color: "#333333",
          }}
        >
          {"."}
        </Box>
      ))}
    </Box>
  );
};

export default LoadingMessage;
