import { Box, Typography, Button } from "@mui/material";
import { AIRobotHead } from "../../../common/SvgIcon";
import moment from "moment/moment";
import React from "react";
import { formatIndianNumber } from "../../../../utils/helper";
import generateCloudFrontURL from "../../../../utils";
import StarIcon from "@mui/icons-material/Star";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import MessageContent from "./MessageContent";

const HotelMessage = ({
  message,
  list = [],
  sender,
  time,
  chat,
  handleSetHotelData = () => {},
}) => {
  const isAI = sender === "agent";

  const noImage = generateCloudFrontURL({
    bucket: process.env.REACT_APP_BUCKET_NAME,
    key: "no-image.jpg",
    width: 100,
    height: 70,
    fit: "cover",
  });

  const filteredHotelList = list?.map((item) => {
    return {
      ...item,
      hotel_address: `${item?.address?.address_line}${item?.address?.city_name ? ", " + item?.address?.city_name : ""}${item?.address?.country_name ? ", " + item?.address?.country_name : ""}`,
      image:
        item?.media?.length > 0
          ? generateCloudFrontURL({
              bucket: process.env.REACT_APP_BUCKET_NAME,
              key: item.media[0],
              width: 100,
              height: 70,
              fit: "cover",
            })
          : noImage,
    };
  });

  return (
    <Box
      sx={{
        width: "100%",
        maxWidth: "88%",
        height: "fit-content",
        display: "flex",
        flexDirection: "row",
        gap: "12px",
        alignSelf: isAI ? "flex-start" : "flex-end",
      }}
    >
      {isAI && (
        <Box sx={{ width: "40px", height: "40px" }}>
          <AIRobotHead />
        </Box>
      )}
      <Box display="flex" width="100%" flexDirection="column" gap="8px">
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            background: isAI
              ? "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)"
              : "#F6F6F6",
            borderRadius: isAI ? "0px 12px 12px 12px" : "12px 12px 0px 12px",
            p: "9.5px 24px",
            boxSizing: "border-box",
            whiteSpace: "pre-wrap",
            wordBreak: "break-word",
            fontFamily: "Roboto",
            fontSize: "14px",
            fontWeight: 400,
            lineHeight: "20px",
            color: "#333333",
          }}
        >
          <MessageContent message={message} />
        </Box>

        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            fontSize: "12px",
            fontWeight: 400,
            lineHeight: "14px",
            color: "#706E75",
            alignSelf: isAI ? "flex-start" : "flex-end",
          }}
        >
          {moment(time).local().isSame(moment(), "day")
            ? `Today, ${moment(time).local().format("HH:mm")}`
            : moment(time).local().format("DD MMM YYYY, HH:mm")}
        </Box>

        <Box
          sx={{
            width: "100%",
            height: "fit-content",
            display: "flex",
            flexDirection: "column",
            gap: "8px",
            boxSizing: "border-box",
          }}
        >
          {filteredHotelList?.map((item, index) => (
            <Box
              sx={{
                borderRadius: "8px 8px 8px 8px",
                border: "1px solid #FF39510D",
                overflow: "hidden",
                transition: "height 0.3s ease",
                display: "flex",
                width: "100%",
                boxSizing: "border-box",
              }}
              key={index + "hotel-list"}
            >
              <Box
                sx={{
                  flex: 1,
                  boxSizing: "border-box",
                  p: "8px 12px 8px 8px",
                }}
              >
                <Box sx={{ display: "flex", gap: 1, mb: 1 }}>
                  <Box
                    sx={{
                      width: "94px",
                      height: "68px",
                      borderRadius: "8px",
                      objectFit: "cover",
                    }}
                    component="img"
                    src={item?.image}
                  />
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: "inline-flex", width: "100%" }}>
                      <Box
                        sx={{
                          display: "flex",
                          flex: 1,
                          flexDirection: "column",
                        }}
                      >
                        <Typography
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 500,
                            fontSize: "14px",
                            lineHeight: "22px",
                            color: "#333333",
                          }}
                        >
                          {item?.name || ""}
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          flexDirection: "column",
                          display: "flex",
                          alignItems: "flex-end",
                          gap: "2px",
                          justifyContent: "flex-start",
                        }}
                      >
                        {item?.rating && (
                          <Box
                            sx={{
                              display: "inline-flex",
                              alignItems: "center",
                              mb: 0.5,
                            }}
                          >
                            {Array.from({
                              length: Math.floor(item?.rating || 0),
                            }).map((_, index) => {
                              return (
                                <StarIcon
                                  sx={{ color: "#FF3951", fontSize: "12px" }}
                                  key={index}
                                />
                              );
                            })}
                          </Box>
                        )}
                      </Box>
                    </Box>

                    <Typography
                      sx={{
                        fontFamily: "Roboto",
                        fontWeight: 400,
                        fontSize: "12px",
                        lineHeight: "16px",
                        color: "#333333",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        display: "-webkit-box",
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: "vertical",
                      }}
                    >
                      {item?.hotel_address || ""}
                    </Typography>
                  </Box>
                </Box>

                {/* {item?.description && (
                  <Typography
                    sx={{
                      fontFamily: "Roboto",
                      fontWeight: 400,
                      fontSize: "12px",
                      lineHeight: "18px",
                      color: "#706E75",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      display: "-webkit-box",
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: "vertical",
                      mb: 1,
                    }}
                  >
                    {item?.description || ""}
                  </Typography>
                )} */}

                {item?.amenities?.length > 0 && (
                  <Typography
                    sx={{
                      fontFamily: "Roboto",
                      fontWeight: 400,
                      fontSize: "12px",
                      lineHeight: "18px",
                      color: "#706E75",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      display: "-webkit-box",
                      WebkitLineClamp: 1,
                      WebkitBoxOrient: "vertical",
                      mb: 1,
                    }}
                  >
                    {item?.amenities?.join(", ")}
                  </Typography>
                )}

                {/* <Typography
                  sx={{
                    fontFamily: "Roboto",
                    fontWeight: 400,
                    fontSize: "12px",
                    lineHeight: "18px",
                    color: "#333333",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    display: "-webkit-box",
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: "vertical",
                  }}
                >
                  <strong>Note:</strong>{" "}
                  {
                    "Exact hotels may vary based on availability; upgrades to 4-star/5-star options (e.g., Holiday Inn Resort Phuket) available at additional cost."
                  }
                </Typography> */}
              </Box>
              <Box
                sx={{
                  display: "flex",
                  boxSizing: "border-box",
                  width: "fit-content",
                  height: "100%",
                }}
              >
                <Box
                  sx={{
                    background: "#F9F9F9",
                    width: "92px",
                    minHeight: "92px",
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: "4px",
                    borderRadius: "0px 7px 7px 0px",
                  }}
                >
                  <Typography
                    sx={{
                      fontFamily: "Roboto",
                      fontWeight: 700,
                      fontStyle: "bold",
                      fontSize: "14px",
                      lineHeight: "120%",
                      letterSpacing: 0,
                      textAlign: "center",
                      color: "#000",
                    }}
                  >
                    {`₹ ${formatIndianNumber(Math.round(item?.base_price || 0))}`}
                  </Typography>
                  <Typography
                    sx={{
                      fontFamily: "Roboto",
                      fontWeight: 400,
                      fontStyle: "normal",
                      fontSize: "10px",
                      lineHeight: "120%",
                      letterSpacing: 0,
                      textAlign: "center",
                      color: "#000",
                    }}
                  >
                    per night
                  </Typography>
                  <Button
                    variant="text"
                    size="small"
                    sx={{
                      mt: 1,
                      fontSize: "10px",
                      textTransform: "none",
                      borderRadius: "6px",
                      minWidth: 0,
                      padding: "2px 8px",
                      display: "flex",
                      alignItems: "center",
                      gap: "0px",
                      "&:hover": {
                        background: "transparent",
                      },
                    }}
                    onClick={() => handleSetHotelData(item)}
                    endIcon={
                      <KeyboardArrowRightIcon
                        sx={{ fontSize: 14, ml: "-8px" }}
                      />
                    }
                  >
                    Details
                  </Button>
                </Box>
              </Box>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default HotelMessage;
