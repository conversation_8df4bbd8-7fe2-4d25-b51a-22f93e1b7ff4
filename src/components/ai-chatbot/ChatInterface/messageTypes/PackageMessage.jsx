import { Box, IconButton, Typography } from "@mui/material";
import { AIRobotH<PERSON>, ArrowNext, ArrowBack } from "../../../common/SvgIcon";
import moment from "moment/moment";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import generateCloudFrontURL from "../../../../utils";
import { formatIndianNumber } from "../../../../utils/helper";
import MessageContent from "./MessageContent";
import { v4 as uuidv4 } from "uuid";

const PackageMessage = ({
  list,
  sender,
  time,
  message = "",
  handleSetPackageData = () => {},
}) => {
  const isAI = sender === "agent";

  const noImage = generateCloudFrontURL({
    bucket: process.env.REACT_APP_BUCKET_NAME,
    key: "no-image.jpg",
    width: 300,
    height: 200,
    fit: "cover",
  });

  const uniqueId = uuidv4();

  const filteredList = [...list]?.map((item, index) => ({
    ...item,
    image:
      item?.package_media?.length > 0
        ? generateCloudFrontURL({
            bucket: process.env.REACT_APP_BUCKET_NAME,
            key: item?.package_media[0]?.file,
            width: 300,
            height: 200,
            fit: "cover",
          })
        : noImage,

    key: index + item?.title + "package",
  }));
  return (
    <Box
      sx={{
        width: "fit-content",
        maxWidth: "88%",
        height: "fit-content",
        display: "flex",
        flexDirection: "row",
        gap: "12px",
        alignSelf: isAI ? "flex-start" : "flex-end",
      }}
    >
      {isAI && (
        <Box sx={{ width: "40px", height: "40px" }}>
          <AIRobotHead />
        </Box>
      )}
      <Box display="flex" flexDirection="column" gap="8px">
        {message && (
          <Box
            sx={{
              width: "fit-content",
              height: "fit-content",
              background: isAI
                ? "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)"
                : "#F6F6F6",
              borderRadius: isAI ? "0px 12px 12px 12px" : "12px 12px 0px 12px",
              p: "9.5px 24px",
              boxSizing: "border-box",
              whiteSpace: "pre-wrap",
              wordBreak: "break-word",
              fontFamily: "Roboto",
              fontSize: "14px",
              fontWeight: 400,
              lineHeight: "20px",
              color: "#333333",
              mb: 0.5,
            }}
          >
            <MessageContent message={message} />
          </Box>
        )}
        <Box
          sx={{
            width: "fit-content",
            maxWidth: {
              xs: "calc(90dvw - 40px - 12px)",
              sm:
                filteredList?.length === 2
                  ? "450px"
                  : filteredList?.length >= 3
                    ? "650px"
                    : "250px",
            },
            height: "fit-content",
            background: isAI
              ? "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)"
              : "#F6F6F6",
            borderRadius: isAI ? "0px 12px 12px 12px" : "12px 12px 0px 12px",
            p: "9.5px 9.5px",
            boxSizing: "border-box",
            whiteSpace: "pre-wrap",
            wordBreak: "break-word",
            fontFamily: "Roboto",
            fontSize: "14px",
            fontWeight: 400,
            lineHeight: "16px",
            color: "#333333",
          }}
        >
          {/* <Box
            sx={{ width: "100%", display: "flex", justifyContent: "flex-end" }}
          >
            <Box
              sx={{
                width: "fit-content",
                fontSize: 10,
                fontWeight: 400,
                lineHeight: "12px",
                color: "#0083E7",
                letterSpacing: "0.02em",
                cursor: "pointer",
                mb: 1,
                mr: "22.5px",
                "&:hover": {
                  textDecoration: "underline",
                },
              }}
            >
              See all
            </Box>
          </Box> */}
          <Box
            sx={{
              width: "100%",
              display: "flex",
              alignItems: "center",
              gap: "7.5px",
              "& .swiper-button-disabled": {
                opacity: 0.5,
                cursor: "not-allowed",
                background: "none !important",
                "& svg": {
                  cursor: "not-allowed",
                  filter: "none !important",
                  transform: "none !important",
                  boxShadow: "none !important",
                },
              },
            }}
          >
            {filteredList?.length >= 4 && (
              <Box>
                <IconButton
                  sx={{
                    p: 0,
                    width: "30px",
                    height: "30px",
                  }}
                  className={`swiper-button-prev-package-chat-${uniqueId}`}
                >
                  <ArrowBack />
                </IconButton>
              </Box>
            )}
            <Swiper
              modules={[Navigation]}
              spaceBetween={7.5}
              slidesPerView={1}
              navigation={{
                nextEl: `.swiper-button-next-package-chat-${uniqueId}`,
                prevEl: `.swiper-button-prev-package-chat-${uniqueId}`,
              }}
              breakpoints={{
                640: {
                  slidesPerView: 1,
                  spaceBetween: 7.5,
                },
                768: {
                  slidesPerView: 3,
                  spaceBetween: 7.5,
                },
                1024: {
                  slidesPerView:
                    filteredList?.length >= 3
                      ? 3
                      : filteredList?.length === 2
                        ? 2
                        : 1,
                  spaceBetween: 7.5,
                },
              }}
              style={{
                width: "100%",
              }}
            >
              {filteredList?.map((item, index) => (
                <SwiperSlide key={index + item?.title + "package"}>
                  <Box
                    sx={{
                      width: "100%",
                      height: "100%",
                      background: "#FFFFFF",
                      borderRadius: "7.5px",
                      overflow: "hidden",
                    }}
                    key={index + item?.title + "package"}
                  >
                    <Box
                      sx={{
                        width: "100%",
                        aspectRatio: "141/80",
                        overflow: "hidden",
                        position: "relative",
                      }}
                    >
                      <Box
                        component="img"
                        src={item?.image || noImage}
                        sx={{
                          width: "100%",
                          height: "100%",
                          objectFit: "cover",
                        }}
                        alt={"package image"}
                      />
                      <Box
                        sx={{
                          position: "absolute",
                          top: "4px",
                          left: "4px",
                          width: "fit-content",
                          px: "4px",
                          py: "4px",
                          background: "#F9F9F9",
                          borderRadius: "2px",
                        }}
                      >
                        <Typography
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 500,
                            fontSize: "8px",
                            lineHeight: "9px",
                            letterSpacing: "0.02em",
                            textTransform: "capitalize",
                            color: "#333333",
                            textAlign: "center",
                          }}
                        >
                          {item?.type === "Custom AI" ||
                          item?.type === "Custom Admin"
                            ? "Customizable"
                            : "Fixed"}
                        </Typography>
                      </Box>
                    </Box>
                    <Box sx={{ p: "7.5px" }}>
                      <Typography
                        sx={{
                          fontSize: 10,
                          fontWeight: 500,
                          lineHeight: "12px",
                          color: "#333333",
                          letterSpacing: "0.02em",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          width: "100%",
                          mb: 0.5,
                        }}
                      >
                        {item?.title}
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: 10,
                          fontWeight: 500,
                          lineHeight: "12px",
                          color: "#333333",
                          mb: 0.5,
                        }}
                      >
                        {item?.duration || "N/A"}
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: 10,
                          fontWeight: 500,
                          lineHeight: "12px",
                          color: "#1E3A8A",
                        }}
                      >
                        {item?.type === "Custom AI" ||
                        item?.type === "Custom Admin"
                          ? `INR ${formatIndianNumber(
                              Math.round(item?.price_per_person || 0)
                            )}`
                          : `Starting from INR ${formatIndianNumber(
                              Math.round(item?.price_per_person || 0)
                            )}`}

                        {}
                      </Typography>
                      <Box
                        sx={{
                          width: "100%",
                          height: "1px",
                          borderBottom: "1px dashed #706E75",
                          my: "7.5px",
                        }}
                      />
                      <Box
                        sx={{
                          width: "100%",
                          boxSizing: "border-box",
                          display: "flex",
                          justifyContent: "center",
                        }}
                      >
                        <Box
                          sx={{
                            width: "fit-content",
                            fontSize: 10,
                            fontWeight: 500,
                            lineHeight: "12px",
                            color: "#0083E7",
                            cursor: "pointer",
                            "&:hover": {
                              textDecoration: "underline",
                            },
                          }}
                          onClick={() => handleSetPackageData(item)}
                        >
                          View Detail
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                </SwiperSlide>
              ))}
            </Swiper>

            {filteredList?.length >= 4 && (
              <Box>
                <IconButton
                  sx={{
                    p: 0,
                    width: "30px",
                    height: "30px",
                  }}
                  className={`swiper-button-next-package-chat-${uniqueId}`}
                >
                  <ArrowNext />
                </IconButton>
              </Box>
            )}
          </Box>
        </Box>
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            fontSize: "12px",
            fontWeight: 400,
            lineHeight: "14px",
            color: "#706E75",
            alignSelf: isAI ? "flex-start" : "flex-end",
          }}
        >
          {moment(time).local().isSame(moment(), "day")
            ? `Today, ${moment(time).local().format("HH:mm")}`
            : moment(time).local().format("DD MMM YYYY, HH:mm")}
        </Box>
      </Box>
    </Box>
  );
};

export default PackageMessage;
