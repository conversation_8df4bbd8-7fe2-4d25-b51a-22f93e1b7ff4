import { Box } from "@mui/material";
import {
  FlightWhite,
  HotelWhite,
  LocationWhite,
  UpgradeBooking,
  VisaWhite,
} from "../../../common/SvgIcon";
import { useSelector } from "react-redux";

const NewMessage = ({ handleStartNewConversation = () => {} }) => {
  const { userDetails } = useSelector((state) => state.authentication);
  const newMessageData = [
    {
      icon: <LocationWhite sx={{ flexShrink: 0 }} />,
      title: "Plan a trip",
      description:
        "I'm here to help you explore exciting travel options. Feel free to ask me anything about your next adventure!",
    },
    {
      icon: <FlightWhite sx={{ flexShrink: 0 }} />,
      title: "Flights",
      description:
        "I'm here to help you find the best flight options for your journey. Feel free to ask any questions about your travel plans!",
    },
    {
      icon: <VisaWhite sx={{ flexShrink: 0 }} />,
      title: "Visa",
      description:
        "Planning an international trip? We can help you with fast, reliable Visa services — so you’re ready to fly stress-free.",
    },
    {
      icon: <HotelWhite sx={{ flexShrink: 0 }} />,
      title: "Hotels",
      description:
        "I'm here to assist you in discovering amazing hotel options. Don't hesitate to reach out with any questions.",
    },
    {
      icon: <UpgradeBooking sx={{ flexShrink: 0 }} />,
      title: "Concierge - Upgrade Your Trip",
      description:
        "From seat selection and meals to airport transfers, trains, forex, and even excess baggage — our Concierge service takes care of 20+ travel needs in one go.Sit back, we’ll handle the rest.",
    },
  ];
  return (
    <Box
      className="new-message"
      sx={{
        width: "fit-content",
        maxWidth: "536px",
        height: "fit-content",
        display: "flex",
        flexDirection: "column",
        alignSelf: "center",
      }}
    >
      <Box
        sx={{
          fontFamily: "Roboto",
          fontWeight: 400,
          fontSize: "24px",
          lineHeight: "28px",
          color: "#1E3A8A",
        }}
      >
        Hello{" "}
        <Box component="span" sx={{ fontWeight: 700 }}>
          {userDetails?.user?.name},
        </Box>
      </Box>
      <Box
        sx={{
          mt: "8px",
          fontFamily: "Roboto",
          background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
          backgroundClip: "text",
          WebkitBackgroundClip: "text",
          WebkitTextFillColor: "transparent",
          fontSize: "24px",
          lineHeight: "28px",
          fontWeight: 500,
        }}
      >
        Planning travel just got weirdly easy.
      </Box>
      <Box
        sx={{
          mt: "8px",
          fontFamily: "Roboto",
          fontSize: "16px",
          lineHeight: "19px",
          color: "#333333",
        }}
      >
        Ask me anything. Except how to pack light — I still haven’t figured that
        out.
      </Box>
      <Box
        sx={{
          mt: "36px",
          display: "grid",
          gridTemplateColumns: "repeat(2, 1fr)",

          gap: "16px",
          width: "100%",
        }}
      >
        {newMessageData?.map((item, index) => (
          <Box
            key={index + "new_chat"}
            sx={{
              width: "100%",
              background: "#F9F9F9",
              borderRadius: "8px",
              padding: "18px 12px",
              boxSizing: "border-box",
              flex: 1,
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              gap: "12px",
              cursor: "pointer",
              transition: "all 0.3s ease",
              "&:hover": {
                background: "#FFF9F9",
              },

              // if last item and and odd then span column 2
              ...(index === newMessageData?.length - 1 &&
                newMessageData?.length % 2 === 1 && {
                  gridColumn: "span 2",
                }),
            }}
            onClick={() => {
              handleStartNewConversation(item?.title || "");
            }}
          >
            {item.icon || ""}
            <Box>
              <Box
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 500,
                  fontSize: "14px",
                  lineHeight: "16px",
                  color: "#333333",
                }}
              >
                {item.title}
              </Box>
              <Box
                sx={{
                  mt: "4px",
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "12px",
                  lineHeight: "14px",
                  color: "#706E75",
                }}
              >
                {item.description}
              </Box>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default NewMessage;
