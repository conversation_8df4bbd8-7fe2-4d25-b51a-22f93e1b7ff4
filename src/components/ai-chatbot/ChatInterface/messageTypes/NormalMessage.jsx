import { Box, CircularProgress } from "@mui/material";
import { AIRobotHead } from "../../../common/SvgIcon";
import moment from "moment/moment";
import LoadingMessage from "./LoadingMessage";
import MessageContent from "./MessageContent";
import generateCloudFrontURL from "../../../../utils";

const NormalMessage = ({
  message,
  imageUrl = null,
  sender,
  time,
  ongoing = false,
  isBeforeUpload = false,
}) => {
  const isAI = sender === "agent";
  const generatedImageUrl = isBeforeUpload
    ? imageUrl
    : generateCloudFrontURL({
        key: imageUrl,
        bucket: process.env.REACT_APP_CLOUD_FRONT_BUCKET,
        actual: true,
      });
  return (
    <Box
      sx={{
        width: "fit-content",
        maxWidth: "88%",
        height: "fit-content",
        display: "flex",
        flexDirection: "row",
        gap: "12px",
        alignSelf: isAI ? "flex-start" : "flex-end",
      }}
    >
      {isAI && (
        <Box sx={{ width: "40px", height: "40px" }}>
          <AIRobotHead />
        </Box>
      )}
      <Box display="flex" flexDirection="column" gap="8px">
        {(message || ongoing) && (
          <Box
            sx={{
              width: "fit-content",
              height: "fit-content",
              alignSelf: isAI ? "flex-start" : "flex-end",
              background: isAI
                ? "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)"
                : "#F6F6F6",
              borderRadius: isAI ? "0px 12px 12px 12px" : "12px 12px 0px 12px",
              p: "9.5px 24px",
              boxSizing: "border-box",
              whiteSpace: "pre-wrap",
              wordBreak: "break-word",
              fontFamily: "Roboto",
              fontSize: "14px",
              fontWeight: 400,
              lineHeight: "20px",
              color: "#333333",

              // "& p, & ul, & ol, & li, & h1, & h2, & h3, & h4, & h5, & h6": {
              //   margin: "0 !important",
              // },
              // "& ul, & ol": {
              //   padding: "0 !important",
              //   listStyleType: "none !important",
              // },
              // "& li": {
              //   padding: "0 !important",
              // },
            }}
          >
            {ongoing ? (
              <LoadingMessage />
            ) : (
              <MessageContent message={message} />
            )}
          </Box>
        )}
        {imageUrl && (
          <Box
            sx={{
              width: "fit-content",
              height: "fit-content",
              alignSelf: isAI ? "flex-start" : "flex-end",
              background: isAI
                ? "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)"
                : "#F6F6F6",
              borderRadius: isAI ? "0px 12px 12px 12px" : "12px 12px 0px 12px",
              p: "9.5px 24px",
              boxSizing: "border-box",
              whiteSpace: "pre-wrap",
              wordBreak: "break-word",
              fontFamily: "Roboto",
              fontSize: "14px",
              fontWeight: 400,
              lineHeight: "20px",
              color: "#333333",
            }}
          >
            <Box
              sx={{
                width: "120px",
                height: "120px",
                position: "relative",
              }}
            >
              <Box
                component="img"
                src={generatedImageUrl}
                alt="media-file"
                sx={{
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                }}
              />
              {isBeforeUpload && (
                <Box
                  sx={{
                    position: "absolute",
                    top: "0px",
                    right: "0px",
                    left: "0px",
                    bottom: "0px",
                    width: "100%",
                    height: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    zIndex: 1,
                    background: "#ffffff56",
                  }}
                >
                  <CircularProgress
                    sx={{
                      color: "#fff",
                      width: "16px",
                      height: "16px",
                    }}
                  />
                </Box>
              )}
            </Box>
          </Box>
        )}
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            fontSize: "12px",
            fontWeight: 400,
            lineHeight: "14px",
            color: "#706E75",
            alignSelf: isAI ? "flex-start" : "flex-end",
          }}
        >
          {moment(time).local().isSame(moment(), "day")
            ? `Today, ${moment(time).local().format("HH:mm")}`
            : moment(time).local().format("DD MMM YYYY, HH:mm")}
        </Box>
      </Box>
    </Box>
  );
};

export default NormalMessage;
