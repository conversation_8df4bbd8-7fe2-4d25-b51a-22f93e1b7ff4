import { Box } from "@mui/material";
import { AIRobotHead } from "../../../common/SvgIcon";
import moment from "moment/moment";
import MessageContent from "./MessageContent";

const SelectMessage = ({
  message,
  sender,
  time,
  options = [],
  handleSendSelectedOption = () => {},
  chat = {},
}) => {
  const isAI = sender === "agent";

  return (
    <Box
      sx={{
        width: "fit-content",
        maxWidth: "88%",
        height: "fit-content",
        display: "flex",
        flexDirection: "row",
        gap: "12px",
        alignSelf: isAI ? "flex-start" : "flex-end",
      }}
    >
      {isAI && (
        <Box sx={{ width: "40px", height: "40px" }}>
          <AIRobotHead />
        </Box>
      )}
      <Box display="flex" flexDirection="column" gap="8px">
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            background: isAI
              ? "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)"
              : "#F6F6F6",
            borderRadius: isAI ? "0px 12px 12px 12px" : "12px 12px 0px 12px",
            p: "9.5px 24px",
            boxSizing: "border-box",
            whiteSpace: "pre-wrap",
            wordBreak: "break-word",
            fontFamily: "Roboto",
            fontSize: "14px",
            fontWeight: 400,
            lineHeight: "20px",
            color: "#333333",
          }}
        >
          <MessageContent message={message} />
        </Box>
        <Box
          sx={{
            width: "100%",
            display: "flex",
            flexDirection: "row",
            gap: "12px",
            flexWrap: "wrap",
          }}
        >
          {options?.map((item, index) => (
            <Box
              key={index + "select"}
              className={`${item?.selected ? "active" : ""} ${
                item?.editable ? "" : "disabled"
              }`}
              onClick={() => {
                if (item?.editable && !item?.selected) {
                  handleSendSelectedOption(item, chat?.id);
                }
              }}
              sx={{
                border: "1px solid #FF39514D",
                background: "#FFF",
                borderRadius: "8px",
                padding: "7px 11px",
                display: "flex",
                alignItems: "center",
                maxWidth: "160px",
                boxSizing: "border-box",
                justifyContent: "space-between",
                gap: "8px",
                transition: "all 0.3s ease",
                "&:hover:not(.active):not(.disabled)": {
                  background: "#FF39510D",
                  cursor: "pointer",
                  border: "1px solid #FF3951",
                },
                "&.active": {
                  background: "#FF39510D",
                  border: "1px solid #FF39510D",
                  cursor: "default",
                },
                "&.disabled:not(.active)": {
                  background: "#F6F6F6",
                  border: "1px solid #F6F6F6",
                  cursor: "default",
                },
              }}
            >
              <Box
                sx={{
                  color: "#333333",
                  fontFamily: "Roboto",
                  fontSize: "12px",
                  fontWeight: 400,
                  lineHeight: "14px",
                }}
              >
                {item?.display || ""}
              </Box>
            </Box>
          ))}
        </Box>
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            fontSize: "12px",
            fontWeight: 400,
            lineHeight: "14px",
            color: "#706E75",
            alignSelf: isAI ? "flex-start" : "flex-end",
          }}
        >
          {moment(time).local().isSame(moment(), "day")
            ? `Today, ${moment(time).local().format("HH:mm")}`
            : moment(time).local().format("DD MMM YYYY, HH:mm")}
        </Box>
      </Box>
    </Box>
  );
};

export default SelectMessage;
