import { <PERSON>, <PERSON>po<PERSON>, Button, Fade } from "@mui/material";
import { AIRobotHead, FlightIcon } from "../../../common/SvgIcon";
import moment from "moment/moment";
import React from "react";
import { formatIndianNumber } from "../../../../utils/helper";
import { KeyboardArrowDown } from "@mui/icons-material";
import generateCloudFrontURL from "../../../../utils";
import MessageContent from "./MessageContent";

const FlightOneWayMessage = ({ message, list = [], sender, time }) => {
  const isAI = sender === "agent";
  const [openFareIndex, setOpenFareIndex] = React.useState(null);

  const noImage = generateCloudFrontURL({
    bucket: process.env.REACT_APP_BUCKET_NAME,
    key: "no-image.jpg",
    width: 46,
    height: 42,
    fit: "cover",
  });

  return (
    <Box
      sx={{
        width: "fit-content",
        maxWidth: "88%",
        height: "fit-content",
        display: "flex",
        flexDirection: "row",
        gap: "12px",
        alignSelf: isAI ? "flex-start" : "flex-end",
      }}
    >
      {isAI && (
        <Box sx={{ width: "40px", height: "40px" }}>
          <AIRobotHead />
        </Box>
      )}
      <Box display="flex" flexDirection="column" gap="8px">
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            background: isAI
              ? "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)"
              : "#F6F6F6",
            borderRadius: isAI ? "0px 12px 12px 12px" : "12px 12px 0px 12px",
            p: "9.5px 24px",
            boxSizing: "border-box",
            whiteSpace: "pre-wrap",
            wordBreak: "break-word",
            fontFamily: "Roboto",
            fontSize: "14px",
            fontWeight: 400,
            lineHeight: "20px",
            color: "#333333",
          }}
        >
          <MessageContent message={message} />
        </Box>

        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            fontSize: "12px",
            fontWeight: 400,
            lineHeight: "14px",
            color: "#706E75",
            alignSelf: isAI ? "flex-start" : "flex-end",
          }}
        >
          {moment(time).local().isSame(moment(), "day")
            ? `Today, ${moment(time).local().format("HH:mm")}`
            : moment(time).local().format("DD MMM YYYY, HH:mm")}
        </Box>

        <Box
          sx={{
            width: "100%",
            maxWidth: "518px",
            height: "fit-content",
            display: "flex",
            flexDirection: "column",
            gap: "8px",
            boxSizing: "border-box",
          }}
        >
          {list?.map((item, index) => (
            <Box
              sx={{
                borderRadius: "0px 8px 8px 8px",
                border: "1px solid #FF39510D",
                overflow: "hidden",
                transition: "height 0.3s ease",
              }}
              key={index + "flight-one-way-container"}
            >
              <Box>
                <Box
                  sx={{
                    width: "100%",
                    height: "fit-content",
                    display: "flex",
                    boxSizing: "border-box",
                  }}
                  key={index + "flight-one-way"}
                >
                  <Box
                    sx={{
                      flex: 1,
                      display: "flex",
                      height: "100%",
                      padding: "14px 13px",
                      width: "400px",
                      gap: "12px",
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "4px",
                        alignItems: "center",
                      }}
                    >
                      <Box
                        sx={{
                          width: "46px",
                          height: "42px",
                        }}
                        component="img"
                        src={noImage}
                        alt="no-image"
                      />
                      <Typography
                        sx={{
                          fontFamily: "Roboto",
                          fontWeight: 400,
                          fontSize: "12px",
                          lineHeight: "14px",
                          color: "#333333",
                        }}
                      >
                        {item?.flightNumber || ""}
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        flex: 1,
                        display: "flex",
                        gap: "8px",
                        alignItems: "center",
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          gap: "6px",
                        }}
                      >
                        <Box
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 700,
                            fontSize: "14px",
                            lineHeight: "16px",
                            color: "#333333",
                          }}
                        >
                          12:30
                        </Box>
                        <Box
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 400,
                            fontSize: "12px",
                            lineHeight: "14px",
                            color: "#706E75",
                          }}
                        >
                          Wed, 23 July
                        </Box>
                        <Box
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 400,
                            fontSize: "12px",
                            lineHeight: "14px",
                            color: "#706E75",
                          }}
                        >
                          GOI
                        </Box>
                      </Box>
                      <Box
                        sx={{
                          flex: 1,
                          position: "relative",
                        }}
                      >
                        <Box
                          sx={{
                            height: "1px",
                            width: "100%",
                            background: "#FF395180",
                          }}
                        />
                        <Box
                          sx={{
                            position: "absolute",
                            top: "calc(50% - 8px)",
                            left: "calc(50% - 8px)",
                          }}
                        >
                          <FlightIcon width="18px" height="18px" />
                          <Box
                            sx={{
                              position: "absolute",
                              top: "calc(50% + 20px)",
                              left: "50%",
                              transform: "translate(-50%, -50%)",
                              fontFamily: "Roboto",
                              fontWeight: 500,
                              fontSize: "8px",
                              lineHeight: "10px",
                              color: "#1E3A8A",
                            }}
                          >
                            Direct
                          </Box>
                        </Box>
                      </Box>
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          gap: "6px",
                        }}
                      >
                        <Box
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 700,
                            fontSize: "14px",
                            lineHeight: "16px",
                            color: "#333333",
                          }}
                        >
                          12:30
                        </Box>
                        <Box
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 400,
                            fontSize: "12px",
                            lineHeight: "14px",
                            color: "#706E75",
                          }}
                        >
                          Wed, 23 July
                        </Box>
                        <Box
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 400,
                            fontSize: "12px",
                            lineHeight: "14px",
                            color: "#706E75",
                          }}
                        >
                          GOI
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                  <Box
                    sx={{
                      background: "#F9F9F9",
                      width: "92px",
                      height: "92px",
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      justifyContent: "center",
                      gap: "4px",
                      borderRadius: "0px 7px 7px 0px",
                    }}
                  >
                    <Typography
                      sx={{
                        fontFamily: "Roboto",
                        fontWeight: 700,
                        fontStyle: "bold",
                        fontSize: "14px",
                        lineHeight: "120%",
                        letterSpacing: 0,
                        textAlign: "center",
                        color: "#000",
                      }}
                    >
                      {`₹ ${formatIndianNumber(Math.round(item?.price || 0))}`}
                    </Typography>
                    <Typography
                      sx={{
                        fontFamily: "Roboto",
                        fontWeight: 400,
                        fontStyle: "normal",
                        fontSize: "10px",
                        lineHeight: "120%",
                        letterSpacing: 0,
                        textAlign: "center",
                        color: "#000",
                      }}
                    >
                      per adult
                    </Typography>
                    <Button
                      variant="text"
                      size="small"
                      sx={{
                        mt: 1,
                        fontSize: "10px",
                        textTransform: "none",
                        borderRadius: "6px",
                        minWidth: 0,
                        padding: "2px 8px",
                        display: "flex",
                        alignItems: "center",
                        gap: "0px",
                        "&:hover": {
                          background: "transparent",
                        },
                      }}
                      onClick={() =>
                        setOpenFareIndex(openFareIndex === index ? null : index)
                      }
                      endIcon={<KeyboardArrowDown sx={{ fontSize: 14 }} />}
                    >
                      Details
                    </Button>
                  </Box>
                </Box>
              </Box>
              <Fade in={openFareIndex === index} timeout={400} unmountOnExit>
                <Box
                  sx={{
                    width: "100%",
                    height: "fit-content",
                    p: "8px 12px 12px 12px",
                    boxSizing: "border-box",
                  }}
                >
                  <Box
                    sx={{
                      fontFamily: "Roboto",
                      fontWeight: 400,
                      fontSize: "12px",
                      lineHeight: "14px",
                      color: "#1E3A8A",
                      mb: "8px",
                    }}
                  >
                    Fare options
                  </Box>
                  <Box
                    sx={{
                      display: "grid",
                      gridTemplateColumns: "1fr 1fr 1fr",
                      gap: "6px",
                    }}
                  >
                    {item?.fareOptions?.map((fare, fareIdx) => (
                      <Box
                        key={fareIdx + "fare"}
                        sx={{
                          borderRadius: "8px",
                          border: "1px solid #F6F6F6",
                          boxSizing: "border-box",
                          cursor: "pointer",
                          overflow: "hidden",
                          transition: "background 0.3s ease",
                          "&.selected, &:hover": {
                            background:
                              "linear-gradient(90deg, rgba(254, 235, 238, 0.6) 0%, rgba(242, 235, 250, 0.6) 125.89%)",
                          },
                        }}
                        className={`${fareIdx % 3 === 1 ? "selected" : ""}`}
                      >
                        <Box
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 400,
                            fontSize: "10px",
                            lineHeight: "12px",
                            background: "#FFF9F9",
                            textAlign: "center",
                            color: "#00AF31",
                            p: "5px 8px",
                          }}
                        >
                          {fare?.refundType || "Refundable"}
                        </Box>
                        <Box
                          sx={{
                            p: "7px",
                            display: "flex",
                            flexDirection: "column",
                            gap: "10px",
                          }}
                        >
                          <Box
                            sx={{
                              display: "flex",
                              gap: "4px",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <Box
                              sx={{
                                fontFamily: "Roboto",
                                fontWeight: 500,
                                fontSize: "10px",
                                lineHeight: "12px",
                                color: "#FF3951",
                              }}
                            >
                              {fare?.fareType || ""}
                            </Box>
                            <Box
                              sx={{
                                fontFamily: "Roboto",
                                fontWeight: 700,
                                fontSize: "14px",
                                lineHeight: "16px",
                                color: "#333333",
                              }}
                            >
                              {`${formatIndianNumber(Math.round(fare?.price || 0))} `}
                              <span
                                style={{
                                  fontFamily: "Roboto",
                                  fontWeight: 700,
                                  fontSize: "10px",
                                  lineHeight: "12px",
                                }}
                              >
                                per adult
                              </span>
                            </Box>
                          </Box>
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              gap: "6px",
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontSize: "10px",
                              lineHeight: "12px",
                            }}
                          >
                            <Box
                              sx={{
                                color: "#1E3A8A",
                              }}
                            >
                              Baggage
                            </Box>
                            <Box
                              sx={{
                                color: "#333333",
                              }}
                            >
                              {fare?.baggage || "10"} Kgs Cabin Baggage
                            </Box>
                            <Box
                              sx={{
                                color: "#333333",
                              }}
                            >
                              {fare?.checkIn || "15"} Kgs Check-in Baggage
                            </Box>
                          </Box>
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              gap: "6px",
                              fontFamily: "Roboto",
                              fontWeight: 400,
                              fontSize: "10px",
                              lineHeight: "12px",
                            }}
                          >
                            <Box
                              sx={{
                                color: "#1E3A8A",
                              }}
                            >
                              Flexibility
                            </Box>
                            <Box
                              sx={{
                                color: "#333333",
                              }}
                            >
                              Cancellation fee starts at ₹4,999 (up to 3 hrs
                              before departure)
                            </Box>
                            <Box
                              sx={{
                                color: "#333333",
                              }}
                            >
                              Date change fee starts at ₹2,999 up tp 3 hrs
                              before departure
                            </Box>
                          </Box>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </Box>
              </Fade>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default FlightOneWayMessage;
