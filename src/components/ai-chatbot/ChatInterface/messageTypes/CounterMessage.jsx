import { Box, IconButton } from "@mui/material";
import { Add, AIRobotHead, Send, Sub } from "../../../common/SvgIcon";
import moment from "moment/moment";
import { useEffect, useState } from "react";
import MessageContent from "./MessageContent";

const CounterMessage = ({
  message,
  sender,
  time,
  list = [],
  chat,
  handleSendCounterData = () => {},
}) => {
  const isAI = sender === "agent";

  console.log("list: ", list);
  const updateInitialCounts = list?.map((item) => ({
    ...item,
    editable: false,
  }));

  const [updateCounts, setUpdateCounts] = useState(updateInitialCounts);

  const isEditable = list?.some((item) => item?.editable);

  const [totalCount, setTotalCount] = useState(
    updateCounts?.reduce((acc, item) => acc + item?.count, 0)
  );

  useEffect(() => {
    setTotalCount(updateCounts?.reduce((acc, item) => acc + item?.count, 0));
  }, [updateCounts]);

  const handleIncreaseCounts = (item) => {
    setUpdateCounts((prev) =>
      prev?.map((personCount) =>
        personCount?.id === item?.id
          ? { ...personCount, count: personCount?.count + 1 }
          : personCount
      )
    );
  };

  const handleDecreaseCounts = (item) => {
    setUpdateCounts((prev) =>
      prev?.map((personCount) =>
        personCount?.id === item?.id
          ? { ...personCount, count: personCount?.count - 1 }
          : personCount
      )
    );
  };

  const handleSend = (list) => {
    handleSendCounterData(list, chat?.id);
  };
  return (
    <Box
      sx={{
        width: "fit-content",
        maxWidth: "88%",
        height: "fit-content",
        display: "flex",
        flexDirection: "row",
        gap: "12px",
        alignSelf: isAI ? "flex-start" : "flex-end",
      }}
    >
      {isAI && (
        <Box sx={{ width: "40px", height: "40px" }}>
          <AIRobotHead />
        </Box>
      )}
      <Box display="flex" flexDirection="column" gap="8px">
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            background: isAI
              ? "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)"
              : "#F6F6F6",
            borderRadius: isAI ? "0px 12px 12px 12px" : "12px 12px 0px 12px",
            p: "9.5px 24px",
            boxSizing: "border-box",
            whiteSpace: "pre-wrap",
            wordBreak: "break-word",
            fontFamily: "Roboto",
            fontSize: "14px",
            fontWeight: 400,
            lineHeight: "20px",
            color: "#333333",
          }}
        >
          <MessageContent message={message} />
        </Box>
        <Box
          sx={{
            width: "100%",
            display: "flex",
            flexDirection: "row",
            gap: "8px",
            flexWrap: "wrap",
          }}
        >
          {list?.map((item, index) => (
            <Box
              key={index + "counter"}
              className={`${item?.editable ? "" : "disabled"}`}
              sx={{
                border: "1px solid #FF39514D",
                background: "#FFF",
                borderRadius: "8px",
                padding: "7px 11px",
                display: "flex",
                alignItems: "center",
                boxSizing: "border-box",
                justifyContent: "space-between",
                gap: "8px",
                transition: "all 0.3s ease",
                minWidth: "162px",
                "&.disabled": {
                  opacity: 0.5,
                  pointerEvents: "none",
                  background: "#F6F6F6",
                  cursor: "not-allowed",
                },
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "2px",
                }}
              >
                <Box
                  sx={{
                    color: "#333333",
                    fontFamily: "Roboto",
                    fontSize: "12px",
                    fontWeight: 400,
                    lineHeight: "14px",
                  }}
                >
                  {item?.label || ""}
                </Box>
                <Box
                  sx={{
                    color: "#706E75",
                    fontFamily: "Roboto",
                    fontSize: "10px",
                    fontWeight: 400,
                    lineHeight: "12px",
                    width: "max-content",
                  }}
                >
                  {item?.subtitle || ""}
                </Box>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  gap: "6px",
                }}
              >
                <IconButton
                  variant="text"
                  color="default"
                  sx={{ p: "0px" }}
                  disabled={
                    updateCounts[index]?.count >= item?.max || !item?.editable
                  }
                  onClick={() => {
                    handleIncreaseCounts(item);
                  }}
                >
                  <Add
                    type="secondary"
                    disabled={
                      updateCounts[index]?.count >= item?.max || !item?.editable
                    }
                  />
                </IconButton>
                <Box
                  sx={{
                    color: "#333333",
                    fontFamily: "Roboto",
                    fontSize: "14px",
                    fontWeight: 400,
                    lineHeight: "16px",
                    minWidth: "20px",
                    textAlign: "center",
                  }}
                >
                  {updateCounts[index]?.count || "0"}
                </Box>
                <IconButton
                  variant="text"
                  color="default"
                  sx={{ p: "0px" }}
                  disabled={
                    updateCounts[index]?.count <= item?.min || !item?.editable
                  }
                  onClick={() => {
                    handleDecreaseCounts(item);
                  }}
                >
                  <Sub
                    sx={{ width: "16px", height: "16px" }}
                    active
                    disabled={
                      updateCounts[index]?.count <= item?.min || !item?.editable
                    }
                  />
                </IconButton>
              </Box>
            </Box>
          ))}
          <IconButton
            sx={{ p: 0, "&:hover": { background: "transparent" } }}
            disabled={!isEditable || totalCount <= 0}
            onClick={() => {
              handleSend(updateCounts);
            }}
          >
            <Send
              width="25px"
              height="25px"
              active={isEditable && totalCount > 0}
              secondary={true}
            />
          </IconButton>
        </Box>
        <Box
          sx={{
            width: "fit-content",
            height: "fit-content",
            fontSize: "12px",
            fontWeight: 400,
            lineHeight: "14px",
            color: "#706E75",
            alignSelf: isAI ? "flex-start" : "flex-end",
          }}
        >
          {moment(time).local().isSame(moment(), "day")
            ? `Today, ${moment(time).local().format("HH:mm")}`
            : moment(time).local().format("DD MMM YYYY, HH:mm")}
        </Box>
      </Box>
    </Box>
  );
};

export default CounterMessage;
