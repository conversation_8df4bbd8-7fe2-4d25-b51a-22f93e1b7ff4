import React, { Fragment } from "react";
import { Box } from "@mui/material";
import NormalMessage from "./messageTypes/NormalMessage";
import PackageMessage from "./messageTypes/PackageMessage";
import SelectMessage from "./messageTypes/SelectMessage";
import CenterMessage from "./messageTypes/CenterMessage";
import CounterMessage from "./messageTypes/CounterMessage";
import WeatherMultiMessage from "./messageTypes/WeatherMultiMessage";
import WeatherSingleMessage from "./messageTypes/WeatherSingleMessage";
import DestinationMessage from "./messageTypes/DestinationMessage";
import FlightOneWayMessage from "./messageTypes/FlightOneWayMessage";
import NewMessage from "./messageTypes/NewMessage";
import HotelMessage from "./messageTypes/HotelMessage";

const ChatSection = ({
  chats = [],
  openRightSidebar = () => {},
  handleStartNewConversation = () => {},
  handleSendMessage = () => {},
  disableSendMessage = false,
  enableSendMessage = false,
  newChat = true,
  ongoingChatLoading = false,
  conversationId = "",
  handleSendSelectedOption = () => {},
  handleSetPackageData = () => {},
  handleSendCounterData = () => {},
  handleSetHotelData = () => {},
}) => {
  return (
    <Box
      sx={{
        width: "100%",
        height: "auto",
        minHeight: "100%",
        display: "flex",
        flexDirection: "column",
        gap: "24px",
        boxSizing: "border-box",
        pt: "16px",
        pb: "16px",
        ...(newChat && {
          justifyContent: "center",
          alignItems: "center",
        }),
      }}
    >
      {newChat && (
        <NewMessage handleStartNewConversation={handleStartNewConversation} />
      )}
      {!newChat &&
        chats?.map((chat, index) => (
          <Fragment
            key={
              +"message_new" +
              chat?.conversation_id +
              index +
              chat?.id +
              chat?.created_at
            }
          >
            {[
              "travel_type",
              "budget",
              "duration",
              "destination_or_vibe",
            ].includes(chat?.message_data?.ui_component?.type) ? (
              <SelectMessage
                message={chat?.message_text || ""}
                options={chat?.message_data?.ui_component?.options || []}
                sender={"agent"}
                time={chat.created_at}
                chat={chat}
                handleSendSelectedOption={handleSendSelectedOption}
              />
            ) : chat?.message_data?.response_component?.type === "packages" ? (
              <PackageMessage
                message={chat.message_text}
                list={chat?.message_data?.response_component?.data || []}
                sender={"agent"}
                time={chat.created_at}
                openRightSidebar={openRightSidebar}
                handleSetPackageData={handleSetPackageData}
              />
            ) : chat?.message_data?.response_component?.type === "hotels" ? (
              <HotelMessage
                message={chat.message_text}
                list={chat?.message_data?.response_component?.data || []}
                chat={chat}
                sender={"agent"}
                time={chat.created_at}
                openRightSidebar={openRightSidebar}
                handleSetHotelData={handleSetHotelData}
              />
            ) : ["travelers_count"].includes(
                chat?.message_data?.ui_component?.type
              ) ? (
              <CounterMessage
                message={chat.message_text}
                sender={"agent"}
                list={chat?.message_data?.ui_component?.options}
                chat={chat}
                time={chat.created_at}
                handleSendCounterData={handleSendCounterData}
              />
            ) : ["weather_forecast_api"].includes(
                chat?.message_data?.response_component?.type
              ) ? (
              <WeatherMultiMessage
                message={chat.message_text}
                sender={"agent"}
                data={chat?.message_data?.response_component?.data}
                chat={chat}
                time={chat.created_at}
              />
            ) : ["weather_forecast_seasonal"].includes(
                chat?.message_data?.response_component?.type
              ) ? (
              <WeatherSingleMessage
                message={chat.message_text}
                sender={"agent"}
                chat={chat}
                data={chat?.message_data?.response_component?.data}
                time={chat.created_at}
              />
            ) : chat.messageType === "center" ? (
              <CenterMessage
                message={chat.message_text}
                sender={chat.sender}
                time={chat.created_at}
              />
            ) : chat.messageType === "destination" ? (
              <DestinationMessage
                list={chat.list}
                sender={"agent"}
                time={chat.created_at}
              />
            ) : chat.messageType === "flight-one-way" ? (
              <FlightOneWayMessage
                message={chat.message_text}
                list={chat.list}
                sender={"agent"}
                time={chat.created_at}
              />
            ) : (
              <NormalMessage
                message={chat?.message_text || ""}
                sender={chat.sender}
                time={chat.created_at}
              />
            )}
          </Fragment>
        ))}
      {ongoingChatLoading && (
        <NormalMessage
          message={`Generating response...`}
          sender={"agent"}
          ongoing={true}
          time={new Date().toISOString()}
        />
      )}
    </Box>
  );
};

export default ChatSection;
