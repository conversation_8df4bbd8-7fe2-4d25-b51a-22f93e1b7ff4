.package-slider-container {
  .slick-track {
    display: flex;
    align-items: center;
    min-height: 340px;
    max-height: fit-content;
    overflow-y: visible;
    padding: 12px 0;

    @media (max-width: 600px) {
        height: 600px;
        max-height: fit-content;
    }
  }
  
  .slick-slide {
    width: 100%;
    height: fit-content;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    transform: scale(0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0.7;
    
    @media (max-width: 600px) {
      transform: scale(0.9);
    }

    & > div {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      margin: 4px;
    }

    &.slick-center {
      z-index: 100;
      transform: scale(1.2) !important;
      margin: 12px;
      opacity: 1;

      @media (max-width: 600px) {
        transform: scale(1) !important;
        margin: 8px;
      }

      & > div {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        margin: 12px;
        
        @media (max-width: 600px) {
          margin: 8px;
        }
    
      }
    }

  }
  
 
}