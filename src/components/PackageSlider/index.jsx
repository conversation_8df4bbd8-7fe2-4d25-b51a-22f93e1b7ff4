import React, { useEffect, useState } from "react";
import { Box, IconButton, Typography } from "@mui/material";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import "./style.css";

import LocationOnIcon from "@mui/icons-material/LocationOn";
import { fetchTrendingDestinations } from "../../store/reducers/Home";
import { useDispatch } from "react-redux";
import { ToastNotifyError } from "../Toast/ToastNotify";
import generateCloudFrontURL from "../../utils";
import ROUTE_PATH from "../../constants/route";
import { useNavigate } from "react-router-dom";
import PanoramaSliderSkeleton from "../Skeleton/PanoramaSliderSkeleton";
import { formatIndianNumber } from "../../utils/helper";
import { ArrowBack, ArrowNext } from "../common/SvgIcon";

const PackageSlider = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [destinations, setDestinations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sliderRef, setSliderRef] = useState(null);

  useEffect(() => {
    const getDestinations = async () => {
      try {
        setLoading(true);
        const response = await dispatch(fetchTrendingDestinations());
        const { data, message, status } = response?.payload?.data;
        if (status === "success") {
          const formattedDestinations = data?.destinations?.map(
            (destination) => ({
              external_id: destination.external_id,
              description: destination.description,
              title: destination.title,
              starting_price: destination.starting_price || 0,
              media:
                destination?.media?.length > 0
                  ? generateCloudFrontURL({
                      bucket: process.env.REACT_APP_BUCKET_NAME,
                      key: destination.media[0].media,
                      width: 800,
                      height: 500,
                      fit: "cover",
                      actual: true,
                    })
                  : generateCloudFrontURL({
                      bucket: process.env.REACT_APP_BUCKET_NAME,
                      key: "no-image.jpeg",
                      width: 800,
                      height: 500,
                      fit: "cover",
                      actual: true,
                    }),
            })
          );
          setDestinations(formattedDestinations);
        } else {
          ToastNotifyError(message);
        }
      } catch (error) {
        console.log(error);
      } finally {
        setTimeout(() => {
          setLoading(false);
        }, 2000);
      }
    };
    getDestinations();
  }, [dispatch]);

  // Show skeleton while loading
  if (loading) {
    return <PanoramaSliderSkeleton />;
  }

  const settings = {
    // dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 5,
    slidesToScroll: 1,
    centerMode: true,
    centerPadding: "0px",
    autoplay: true,
    autoplaySpeed: 3000,
    pauseOnHover: true,

    responsive: [
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 5,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 900,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 680,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  return (
    <Box
      sx={{
        width: "100%",
        maxWidth: "1440px",
        mx: "auto",
        height: "100%",
        position: "relative",
      }}
    >
      <Slider
        ref={setSliderRef}
        {...settings}
        className="package-slider-container"
      >
        {destinations.map((slide, index) => (
          <Box
            key={`${slide.external_id}-${index}`}
            onClick={() => {
              navigate(ROUTE_PATH.SELECTED_PACKAGE, {
                state: {
                  externalid: slide?.external_id,
                  title: slide?.title,
                  type: "destination",
                  hasVisibilityScroll: true,
                },
              });
            }}
            sx={{
              position: "relative",
              width: "100%",
              aspectRatio: "1/1",
              borderRadius: "8px",
              cursor: "pointer",
              transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
              zIndex: 1,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              "&:hover": {
                opacity: 0.7,
              },
            }}
          >
            <Box
              className="slide-content"
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                borderRadius: "12px",
                background:
                  "linear-gradient(180deg, rgba(255,255,255,0) 41.63%, rgba(0,0,0,0.8) 107.72%)",
                zIndex: 2,
              }}
            >
              {/* Content for center slide */}
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  height: "100%",
                  justifyContent: "space-between",
                  boxSizing: "border-box",
                  p: 1.5,
                  zIndex: 3,
                }}
              >
                <Box
                  sx={{
                    borderRadius: "32px",
                    background: "white",
                    p: "6px 12px 6px 8px",
                    display: "flex",
                    alignItems: "center",
                    gap: 0.25,
                    width: "fit-content",
                  }}
                >
                  <LocationOnIcon sx={{ fontSize: "14px", color: "#FF3951" }} />
                  <Typography
                    sx={{
                      fontSize: "10px",
                      fontWeight: 500,
                      fontFamily: "Roboto",
                      color: "#1E3A8A",
                      textTransform: "capitalize",
                      textAlign: "left",
                    }}
                  >
                    {slide?.title}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: "fit-content",
                    height: "fit-content",
                    boxSizing: "border-box",
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: "10px",
                      fontWeight: 500,
                      fontFamily: "Roboto",
                      color: "white",
                      textAlign: "left",
                    }}
                  >
                    Starting at
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: "12px",
                      fontWeight: 500,
                      fontFamily: "Roboto",
                      color: "white",
                      textAlign: "left",
                    }}
                  >
                    ₹
                    {formatIndianNumber(Math.round(slide?.starting_price || 0))}{" "}
                    per person
                  </Typography>
                </Box>
              </Box>
            </Box>
            <img
              src={slide?.media}
              alt="destination"
              style={{
                objectFit: "cover",
                borderRadius: "12px",
                width: "100%",
                height: "100%",
                position: "absolute",
                top: 0,
                left: 0,
                zIndex: 1,
              }}
              loading="lazy"
            />
          </Box>
        ))}
      </Slider>

      {/* Custom Navigation Buttons */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          gap: 2,
          mt: 4,
          mb: 4.5,
          position: "relative",
          zIndex: 20,
        }}
      >
        <IconButton
          onClick={() => sliderRef?.slickPrev()}
          sx={{
            p: 0,

            borderRadius: "50%",
            "&:hover": {
              transform: "scale(1.05)",
            },
            transition: "all 0.3s ease",
          }}
        >
          <ArrowBack />
        </IconButton>
        <IconButton
          onClick={() => sliderRef?.slickNext()}
          sx={{
            p: 0,

            borderRadius: "50%",

            "&:hover": {
              transform: "scale(1.05)",
            },
            transition: "all 0.3s ease",
          }}
        >
          <ArrowNext />
        </IconButton>
      </Box>
    </Box>
  );
};

export default PackageSlider;
