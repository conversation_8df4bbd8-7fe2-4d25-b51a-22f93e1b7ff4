import React from "react";
import { Box, Typography } from "@mui/material";
import StyledWhyChooseZuumm from "./StyledWhyChooseZuumm";
import logo from "../../assets/svg/logo.svg";
import TicketIcon from '../../assets/svg/ticketIcon.svg';
import BalloonIcon from '../../assets/svg/hotAirBallonIcon.svg';
import DiamondIcon from '../../assets/svg/diamond.svg';
import VisaIcon from '../../assets/svg/visaIcon.svg';
import ImmersiveIcon from '../../assets/svg/immerseIcon.svg';

const WhyChooseZuumm = () => {
  const features = [
    {
        icon: TicketIcon,
      title: "Compare Prices Instantly",
      description: "See real-time prices from top sites — all in one place.",
    },
    {
        icon: BalloonIcon,
      title: "Price Transparency You Can Trust",
      description:
        "When you can compare, you can trust you're getting the best deal.",
    },
    {
        icon: DiamondIcon,
      title: "Tailored to Your<br/>Intent",
      description:
        "Dynamically shape your trip based on your travel goals — beach, luxury, culture, or budget.",
    },
    {
        icon: VisaIcon,
      title: "Visa<br/>Guarantee",
      description:
        "99.8% Visa Guarantee.We refund your flight ticket in full. Travel with peace of mind.",
    },
    {
        icon: ImmersiveIcon,
      title: "Immerse Before You Commit",
      description:
        "Take a 360° tour of your destination, hotel, and experiences — feel the vibe before you finalize your trip.",
    },
  ];

  return (
    <StyledWhyChooseZuumm>
      <Box className='section-title'>
        <Typography className='section-title-text'>Why choose</Typography>
          <img
            src={logo}
            alt='Zuumm'
            style={{ width: "150px", height: "32px" }}
          />
      </Box>

      <Box className='features-grid'>
        {features.map((feature, index) => (
          <Box key={index} className='feature-card'>
            <img
              src={feature.icon}
              alt={feature.title}
              className='feature-icon'
            />
            <Typography 
              className='feature-title'
              dangerouslySetInnerHTML={{ __html: feature.title }}
            />
            <Typography className='feature-description'>
              {feature.description}
            </Typography>
          </Box>
        ))}
      </Box>
    </StyledWhyChooseZuumm>
  );
};

export default WhyChooseZuumm;
