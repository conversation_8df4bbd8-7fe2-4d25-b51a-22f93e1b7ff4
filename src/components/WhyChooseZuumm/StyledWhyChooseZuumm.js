import { styled } from "@mui/material/styles";

const StyledWhyChooseZuumm = styled("div")(
  ({ theme }) => `
  padding: 30px 80px;
  text-align: center;
  background: #FFF9F9;

  @media (max-width: 600px) {
    padding: 25px 20px !important;
  }
  
  .section-title {
    display: flex;
    gap: 10px;
    justify-content: center;
    align-items: center;
    margin-bottom: 40px;
  }

  .section-title-text {
    font-family: 'Lora', serif;
    font-size: 28px;
    line-height: 45px;
    font-weight: 700;
    color: ${theme.palette.secondary.main};

    .highlight {
      color: #FF3951;
    }
  }

  .features-grid {
    text-align: left;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 32px;
    margin-top: 40px;
  }

  .feature-card {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
    padding: 8px;

    .feature-icon {
      text-align: left;
      width: 46px;
      height: 44px;
      margin-bottom: 10px;
      color: #FF3951;
    }

    .feature-title {
      font-family: '<PERSON>ra', serif;
      font-size: 16px;
      font-weight: 600;
      color: ${theme.palette.custom.darkBlue};
      margin-bottom: 4px;
      line-height: 24px;
    }

    .feature-description {
      font-family: 'Roboto', sans-serif;
      font-size: 14px;
      font-weight: 400;
      color: ${theme.palette.custom.subText};
      line-height: 24px;
    }
  }

  @media (max-width: 1200px) {
    .features-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: 768px) {
    padding: 60px 24px;
    
    .features-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 24px;
    }

    .section-title {
      font-size: 36px;
      margin-bottom: 40px;
    }
  }

  @media (max-width: 600px) {
    padding: 25px 20px !important;
    .section-title {
      flex-direction: column;
      align-items: flex-start;
    }
    .section-title-text {
      font-size: 24px;
      line-height: 36px;
    }  
  .feature-card {
    padding: 24px 24px 0px 0px !important;
  }

  @media (max-width: 480px) {
    .features-grid {
      grid-template-columns: 1fr;
    }
  }
`
);

export default StyledWhyChooseZuumm; 