/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState } from 'react';
import { Box, Typography, TextField, Button } from '@mui/material';
import StyledJoinNewsletter from './StyledJoinNewsletter';
import leftBg from '../../assets/png/joinZuummBg1.png';
import balloonBg from '../../assets/png/joinZuummBg2.png';
import newJoiningRobot from '../../assets/svg/joinRobot.svg';
import { ToastNotifyError, ToastNotifySuccess } from '../Toast/ToastNotify';
import { useDispatch } from 'react-redux';
import { fetchJoinNewsletter } from '../../store/reducers/Home';

const JoinNewsletter = () => {
  const [email, setEmail] = useState('');
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleEmailChange = (e) => {
    setEmail(e.target.value);
  };

  const handleJoinNewsletter = async () => {
    // Trim whitespace
    const trimmedEmail = email.trim();
    
    // Validation checks
    if (!trimmedEmail) {
      ToastNotifyError('Please enter your email address to join');
      return;
    }

    if (!validateEmail(trimmedEmail)) {
      ToastNotifyError('Please enter a valid email address');
      return;
    }

    setIsLoading(true);

    try {
      const response = await dispatch(fetchJoinNewsletter({ email: trimmedEmail }));
      const  {message, status} = response?.payload?.data;
      if(status === 'success') {
        ToastNotifySuccess(message);
        setEmail('');
      } else {
        ToastNotifyError(message);
      }
    } catch (error) {
      ToastNotifyError('Failed to join newsletter. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleJoinNewsletter();
    }
  };

  return (
    <StyledJoinNewsletter>
      <Box className="content-wrapper">
        <Box className="left-section">
          <img src={leftBg} alt="AI Assistant" className="bg-image" />
          <Box className="text-content">
            <Typography className="title">
              Join Thousands Who Trust Zuumm. Get
              early access to new packages, AI tools,
              and exclusive offers.
            </Typography>
            
            <Box className="input-wrapper">
              <TextField 
                placeholder="Enter your Email"
                className="email-input"
                fullWidth
                value={email}
                onChange={handleEmailChange}
                onKeyPress={handleKeyPress}
                disabled={isLoading}
                InputProps={{
                  endAdornment: (
                    <Button 
                      className="join-button"
                      onClick={handleJoinNewsletter}
                      disabled={isLoading}
                    >
                      {isLoading ? 'Joining...' : 'Join'}
                    </Button>
                  )
                }}
              />
            </Box>
          </Box>
          <Box className="ai-robot-wrap">
            <img src={newJoiningRobot} alt='ai-robot' className='ai-robot' />
          </Box>
        </Box>
        
        <Box className="right-section">
          <img src={balloonBg} alt="Hot Air Balloons" className="bg-image" />
        </Box>
      </Box>
    </StyledJoinNewsletter>
  );
};

export default JoinNewsletter; 