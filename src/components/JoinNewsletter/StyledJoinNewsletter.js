import { styled } from "@mui/material/styles";

const StyledJoinNewsletter = styled("div")(
  ({ theme }) => `
  padding: 0;
  background: ${theme?.palette.custom.white};
  position: relative;
  overflow: hidden;

  .content-wrapper {
    width: 85%;
    margin: 0 auto;
    display: flex;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.05);
  }

  .left-section {
    position: relative;
    width: 58%;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    overflow: hidden;
  }

  .right-section {
    position: relative;
    width: 42%;
    min-height: 400px;
    display: flex;
    align-items: center;
  }

  .ai-robot-wrap {
    position: absolute;
    right: 11px;
    bottom: 0px;
    z-index: 2;
  }

  .bg-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .text-content {
    position: relative;
    z-index: 2;
    padding: 0px 0px 0px 40px;
    max-width: 500px;
    background: transparent;
    border-radius: 12px;
    backdrop-filter: blur(8px);
  }

  .title {
    font-family: 'Lora', serif;
    font-size: 24px;
    font-weight: 700;
    color: ${theme.palette.secondary.main};
    line-height: 40px;
    margin-bottom: 32px;
  }

  .input-wrapper {
    max-width: 400px;

    .email-input {
      .MuiOutlinedInput-root {
        background: #FFFFFF;
        border-radius: 8px;
        height: 48px;
        padding-right: 6px;
        padding-left: 10px;
        width: 90% !important!

        fieldset {
          border: 1px solid #E5E7EB;
        }

        &:hover fieldset {
          border-color: ${theme.palette.custom.lightred};
        }

        &.Mui-focused fieldset {
          border-color: ${theme.palette.custom.lightred};
        }

        input {
          font-family: 'Roboto', sans-serif;
          font-size: 14px;
          padding: 12px 16px;
          
          &::placeholder {
            color: ${theme.palette.custom.subText};
          }
        }
      }
    }

    .join-button {
      min-width: 80px;
      height: 36px;
      background: ${theme.palette.custom.lightred};
      border-radius: 6px;
      font-family: 'Roboto', sans-serif;
      font-weight: 500;
      font-size: 14px;
      color: #FFFFFF;
      text-transform: none;
      transition: all 0.3s ease;
      margin: 0px;
      padding: 0 16px;

      &:hover {
        background: ${theme.palette.custom.lightred};
        opacity: 0.9;
      }
    }
  }

  .ai-robot {
    width: 166px;
    height: 166px;
  }

  @media (max-width: 1024px) {
    .ai-robot-wrap {
      display: none;
    }
  }

  @media (max-width: 786px) {
    .content-wrapper {
      flex-direction: column;
      width: 90%;
    }

    .left-section, .right-section {
      width: 100%;
      min-height: 300px;
    }

    .text-content {
      width: 100%;
    }
    .ai-robot-wrap {
      display: block;
    }
  }


  @media (max-width: 600px) {
      .ai-robot-wrap {
      display: none;
    }  
    .right-section {
      display: none;
    }  

    .title {
      font-size: 14px;
      line-height: 20px;
    }
    .text-content {
      padding: 0px 0px 0px 10px;
    }
  }

  @media (max-width: 768px) {
    padding: 0px 0;
  }
`
);

export default StyledJoinNewsletter; 