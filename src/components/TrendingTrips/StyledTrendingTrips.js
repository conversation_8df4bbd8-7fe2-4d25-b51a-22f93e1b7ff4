import { styled } from "@mui/material/styles";

const StyledTrendingTrips = styled("div")(
  ({ theme }) => `
  padding: 40px 0;
  background: linear-gradient(to right, #FFF9F9 50%, #FFF 50%);
  overflow: hidden;
  position: relative;


  @media (max-width: 600px) {
    padding: 25px 0px !important;
    margin-top: 20px;
  }

  // &:before {
  //   content: '';
  //   position: absolute;
  //   left: 0;
  //   top: 0;
  //   width: 50%;
  //   height: 100%;
  //   background: #FFF9F9;
  //   z-index: 0;
  // }

  .content-wrapper {
    max-width: 100%;
    margin: 0px;
    padding: 0 0px;
    position: relative;
    z-index: 1;
  }

  // .mySwiper {
  // padding: 12px 0px;
  // }

  // .swiper-wrapper {
  //   align-items: center;
  // }

  // .swiper-slide {
  //   transition: all 0.3s ease;
  //   display: flex;
  //   justify-content: center;
  //   width: fit-content !important;
  // }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    max-width: 100%;
    margin: 0 auto 40px;
    padding: 0 54px 0 24px;

    @media (max-width: 1200px) {
      padding: 0 24px;
    }

    @media (max-width: 768px) {
      padding: 0 16px;
    }
  }

  .title {
    font-family: 'Lora', serif;
    font-size: 28px;
    font-weight: 700;
    color: ${theme.palette.secondary.main};
    line-height: 40px;

    .highlight {
      background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .navigation-buttons {
    display: flex;
    gap: 16px;
    z-index: 10;
  }

  

  .trip-card {
    cursor: pointer;
    width: 226px;
    height: 300px;
    border-radius: 24px 24px 0 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: #FFF9F9;
    box-shadow: 0px 4px 8px 0px #00000012;

    &.opacity-card {
      opacity: 0.5;
      transition: opacity 0.3s ease;
    }

    &:hover {
      box-shadow: 0px 4px 14px rgba(0, 0, 0, 0.1);
    }

    img {
      width: 100%;
      height: 252px;
      object-fit: cover;
    }

    .trip-info {
      padding: 13.5px 10px;
    }

    .country {
      font-family: 'Roboto', sans-serif;
      font-size: 18px;
      font-weight: 600;
      color: ${theme.palette.custom.subText};
      margin-bottom: 8px;
      line-height: 21px;
      text-transform: capitalize;
      letter-spacing: 0.02em;
      margin:0px;
    }

    .price {
      font-family: 'Roboto', sans-serif;
      font-weight: 500;
      font-size: 14px;
      color: ${theme.palette.secondary.main};
      line-height: 60%;
      letter-spacing: 0.02em;
    }

    // Skeleton styles
    &.skeleton-card {
      cursor: default;
      background: #FFF9F9;
      
      .skeleton-image {
        border-radius: 0;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
      }

      .skeleton-title {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
        border-radius: 4px;
      }
    }
  }

  // Custom carousel styles
  .custom-carousel {
    overflow: hidden;
    position: relative;
    width: 100%;
    padding: 20px 0;
  }

  .carousel-track {
    display: flex;
    gap: 24px;
    transition: scroll-left 0.5s ease-in-out;
    width: fit-content;
    scroll-behavior: smooth;
  }

  .carousel-item {
    flex-shrink: 0;
    width: 226px;
  }

  

  .blur-left {
    position: absolute;
    left: 0;
    top: 12px;
    width: 8%;
    height: calc(100% - 24px);
    background: #FFFFFF;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    z-index: 20;
    opacity: 0.6;
  }

  .blur-right {
    position: absolute;
    right: 0;
    top: 12px;
    width: 8%;
    height: calc(100% - 24px);
    background: #FFFFFF;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    z-index: 20;
    opacity: 0.6;
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @media (max-width: 1200px) {
    .content-wrapper {
      padding: 0 16px;
    }
    
    .header {
      padding: 0 16px;
    }

   

  }

  @media (max-width: 768px) {
    .navigation-buttons {
      justify-content: center;
    }

    .trip-card {

      .country {
        font-size: 16px;
      }
    }

    .mySwiper {
      margin: 0 -5%;
      width: 110%;
    }
  }

  @media (max-width: 480px) {
    .trip-card {

      .country {
        font-size: 15px;
      }

      .price {
        font-size: 13px;
      }
    }

  }

  @media (max-width: 600px) {
    .header {
      flex-direction: column;
      gap: 20px;
      text-align: left;
      margin-bottom: 30px;
    }

    .title {
      font-size: 24px;
      line-height: 35px;
    }
  }
`
);

export default StyledTrendingTrips; 