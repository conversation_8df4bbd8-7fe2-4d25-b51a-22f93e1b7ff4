import React from "react";
import { Box, Typography } from "@mui/material";

const TrendingCard = ({
  image,
  title,
  author,
  timestamp,
  category,
  onClick,
  isHover = true,
}) => {
  return (
    <Box
      onClick={onClick}
      sx={{
        cursor: onClick ? "pointer" : "default",
        backgroundColor: "#FFF9F9",
        borderRadius: "12px 12px 0 0",
        overflow: "hidden",
        transition: "transform 0.2s ease-in-out",
        "&:hover": {
          transition: "transform 0.2s ease-in-out",
          boxShadow: "0px 4px 14px rgba(0, 0, 0, 0.1)",
        },

        boxSizing: "border-box",

        width: "100%",
        height: "100%",
      }}
    >
      {/* Top Image */}
      <Box
        sx={{
          position: "relative",
          width: "100%",
          height: "252px",
          overflow: "hidden",
          flex: 1,
        }}
      >
        <Box
          component="img"
          src={image}
          alt={title}
          sx={{
            width: "100%",
            height: "100%",
            objectFit: "cover",
            borderRadius: "12px 12px 0 0",
          }}
        />
      </Box>

      <Box>
        {/* Title */}
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontSize: "16px",
            fontWeight: "500",
            lineHeight: "21px",
            color: "#333333",
            display: "-webkit-box",
            WebkitLineClamp: 1,
            WebkitBoxOrient: "vertical",
            overflow: "hidden",
            textOverflow: "ellipsis",
            padding: "13.5px 10px",
          }}
        >
          {title}
        </Typography>
      </Box>
    </Box>
  );
};

export default TrendingCard;
