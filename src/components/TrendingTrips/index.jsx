/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState, useRef } from "react";
import { Box, Typography, IconButton } from "@mui/material";
import StyledTrendingTrips from "./StyledTrendingTrips";
import { useDispatch } from "react-redux";
import { fetchDestinations } from "../../store/reducers/Home";
import { ToastNotifyError } from "../Toast/ToastNotify";
import generateCloudFrontURL from "../../utils";
import { useNavigate } from "react-router-dom";
import ROUTE_PATH from "../../constants/route";
import TrendingCard from "./TrendingCard";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import { ArrowBack, ArrowNext } from "../common/SvgIcon";

const TrendingTrips = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [trendingDestinations, setTrendingDestinations] = useState([]);
  const [loading, setLoading] = useState(true);
  const swiperRef = useRef(null);

  const getTrendingDestinations = async () => {
    try {
      setLoading(true);
      const response = await dispatch(fetchDestinations());
      const { data, message, status } = response?.payload?.data;
      if (status === "success") {
        setTrendingDestinations(data?.categories);
      } else {
        ToastNotifyError(message);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 2000);
    }
  };

  const renderImage = (mediaKey) => {
    try {
      return generateCloudFrontURL({
        bucket: process.env.REACT_APP_BUCKET_NAME,
        key: mediaKey,
        width: 400,
        height: 300,
        fit: "cover",
      });
    } catch (error) {
      console.error("Error generating CloudFront URL:", error);
      return mediaKey; // Fallback to original key
    }
  };

  useEffect(() => {
    getTrendingDestinations();
  }, []);

  const handleNext = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideNext();
    }
  };

  const handlePrev = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slidePrev();
    }
  };

  // Shimmer loading component for trip cards
  const ShimmerCard = () => (
    <div className="trip-card skeleton-card">
      <div
        className="skeleton-image"
        style={{ height: "252px", width: "100%" }}
      />
      <div className="trip-info">
        <div
          className="skeleton-title"
          style={{ height: "16px", width: "80%", marginBottom: "8px" }}
        />
      </div>
    </div>
  );

  // Render shimmer loading state
  const renderShimmerCarousel = () => (
    <Box
      sx={{
        margin: "0 auto",
        padding: "40px 20px",
        boxSizing: "border-box",
        width: "100%",
      }}
    >
      <Box>
        <Swiper
          ref={swiperRef}
          modules={[Navigation]}
          spaceBetween={24}
          slidesPerView={1}
          navigation={{
            nextEl: ".swiper-button-next-trending",
            prevEl: ".swiper-button-prev-trending",
          }}
          breakpoints={{
            320: {
              slidesPerView: 1,
              spaceBetween: 24,
              slidesPerGroup: 1,
            },
            640: {
              slidesPerView: 2,
              spaceBetween: 24,
              slidesPerGroup: 1,
            },
            768: {
              slidesPerView: 3,
              spaceBetween: 24,
              slidesPerGroup: 1,
            },
            1024: {
              slidesPerView: 6,
              spaceBetween: 24,
              slidesPerGroup: 1,
            },
            1500: {
              slidesPerView: 7,
              spaceBetween: 24,
              slidesPerGroup: 1,
            },
          }}
          style={{
            width: "100%",
            paddingBottom: "16px",
          }}
        >
          {[...Array(12)].map((_, index) => (
            <SwiperSlide key={index}>
              <ShimmerCard />
            </SwiperSlide>
          ))}
        </Swiper>
      </Box>
    </Box>
  );

  return (
    <StyledTrendingTrips>
      <Box className="content-wrapper">
        <Box className="header">
          <Typography className="title">
            Trending Trips You Can{" "}
            <span className="highlight">Book Right Now</span>
          </Typography>
          <Box className="navigation-buttons">
            <IconButton
              sx={{
                p: 0,
              }}
              className="swiper-button-prev-trending"
              onClick={handlePrev}
              disabled={loading}
            >
              <ArrowBack disabled={loading} />
            </IconButton>
            <IconButton
              sx={{
                p: 0,
              }}
              className="swiper-button-next-trending"
              onClick={handleNext}
              disabled={loading}
            >
              <ArrowNext disabled={loading} />
            </IconButton>
          </Box>
        </Box>

        {loading ? (
          renderShimmerCarousel()
        ) : (
          <Box
            sx={{
              margin: "0 auto",
              padding: "40px 20px",
              boxSizing: "border-box",
              width: "100%",
            }}
          >
            <Box>
              <Swiper
                ref={swiperRef}
                loop={true}
                modules={[Navigation]}
                spaceBetween={24}
                slidesPerView={1}
                navigation={{
                  nextEl: ".swiper-button-next-trending",
                  prevEl: ".swiper-button-prev-trending",
                }}
                breakpoints={{
                  320: {
                    slidesPerView: 1,
                    spaceBetween: 24,
                    slidesPerGroup: 1,
                  },
                  640: {
                    slidesPerView: 2,
                    spaceBetween: 24,
                    slidesPerGroup: 1,
                  },
                  768: {
                    slidesPerView: 3,
                    spaceBetween: 24,
                    slidesPerGroup: 1,
                  },
                  1024: {
                    slidesPerView: 6,
                    spaceBetween: 24,
                    slidesPerGroup: 1,
                  },
                  1500: {
                    slidesPerView: 7,
                    spaceBetween: 24,
                    slidesPerGroup: 1,
                  },
                }}
                style={{
                  width: "100%",
                  paddingBottom: "16px",
                }}
              >
                {trendingDestinations?.map((trip, index) => (
                  <SwiperSlide key={index}>
                    <TrendingCard
                      image={renderImage(trip?.media?.[0]?.media)}
                      title={trip.title}
                      onClick={() =>
                        navigate(ROUTE_PATH.SELECTED_DESTINATION, {
                          state: {
                            externalid: trip?.external_id,
                            title: trip?.title,
                            hasVisibilityScroll: true,
                          },
                        })
                      }
                      isHover={false}
                    />
                  </SwiperSlide>
                ))}
              </Swiper>
            </Box>
          </Box>
        )}
      </Box>
    </StyledTrendingTrips>
  );
};

export default TrendingTrips;
