import React from "react";
import { Box, Stack, styled } from "@mui/material";
import { useNavigate } from "react-router-dom";
import PrimaryButton from "../../common/Button/PrimaryButton";
import { useSelector } from "react-redux";
import { useOnboardingModal } from "../../../hooks/useOnboardingModal";

const FeatureSection = ({ basicData = {} }) => {
  const navigate = useNavigate();
  const { userDetails } = useSelector((state) => state.authentication);
  const { open } = useOnboardingModal();

  const handlePlanWithAI = () => {
    if (userDetails?.user) {
      const visibility = process.env.REACT_APP_ENV === "production";
      if (!visibility) {
        navigate("/ai-chat");
      } 
    } else {
      open();
    }
  };

  const highlightsIconUrl = "/static/package/highlight";

  const highlightsIcons = [
    "island",
    "beach",
    "ship",
    "passenger",
    "breakfast",
    "taxi",
    "bus",
    "hotel",
    "flight",
    "visa",
    "forex_card",
    "insurance",
    "common",
  ];

  const getHighlightIconUrl = (icon) => {
    let url = "";
    if (highlightsIcons.includes(icon)) {
      url = `${highlightsIconUrl}/${icon}.png`;
    } else {
      url = `${highlightsIconUrl}/common.png`;
    }
    return url;
  };

  const highlightsData = basicData?.highlights;
  const inclusionsData = basicData?.inclusions;
  const exclusionsData = basicData?.exclusions;

  const HeaderText = styled(Box)(({ theme }) => ({
    fontFamily: "Roboto",
    fontWeight: 500,
    fontSize: "20px",
    lineHeight: "30px",
    color: "#333333",

    [theme.breakpoints.down("sm")]: {
      fontSize: "16px",
      lineHeight: "24px",
    },
  }));

  const DescriptionText = styled(Box)(({ theme }) => ({
    fontFamily: "Roboto",
    fontWeight: 400,
    fontSize: "16px",
    lineHeight: "24px",
    color: "#333333",
    whiteSpace: "pre-line",

    [theme.breakpoints.down("sm")]: {
      fontSize: "14px",
      lineHeight: "20px",
    },
  }));

  const Separator = styled(Box)(({ theme }) => ({
    width: "100%",
    height: "2px",
    background: "#333333",
    opacity: 0.05,
    marginTop: "16px",
    marginBottom: "16px",
  }));

  return (
    <Box
      className="package-feature-container"
      sx={{
        display: "flex",
        width: "100%",
        position: "relative",
        gap: 2,
        marginTop: { xs: "16px", md: "36px" },
        flexDirection: { xs: "column", md: "row" },
      }}
    >
      <Box
        sx={{
          display: "flex",
          width: { xs: "100%", md: "calc(100% - 416px - 16px)" },
          gap: 2,
          alignItems: "flex-start",
          justifyContent: "space-between",
        }}
      >
        <Box>
          <Box>
            <HeaderText sx={{ marginBottom: "12px" }}>Tour Overview</HeaderText>
            <DescriptionText>{basicData?.about_this_tour}</DescriptionText>
          </Box>
          <Box mt={2}>
            <HeaderText sx={{ marginBottom: "12px" }}>
              Tour Highlights
            </HeaderText>
            <Stack direction="column" spacing={"2px"}>
              {highlightsData?.map((item, index) => (
                <Box
                  key={index + "highlights"}
                  sx={{ display: "flex", gap: 1 }}
                >
                  <img
                    src={getHighlightIconUrl(item.icon_class)}
                    alt="A"
                    style={{ width: "24px", height: "24px" }}
                  />
                  <DescriptionText>{item.value}</DescriptionText>
                </Box>
              ))}
            </Stack>
          </Box>
          <Separator />
          <Box>
            <HeaderText sx={{ marginBottom: "12px" }}>Inclusions</HeaderText>
            <Stack direction="column" spacing={"2px"}>
              {inclusionsData?.map((item, index) => (
                <Box
                  key={index + "inclusions"}
                  sx={{ display: "flex", gap: 1 }}
                >
                  <img
                    src={getHighlightIconUrl(item.icon_class)}
                    alt="feature"
                    style={{ width: "24px", height: "24px" }}
                  />
                  <DescriptionText>{item.value}</DescriptionText>
                </Box>
              ))}
            </Stack>
          </Box>
          <Separator />
          <Box>
            <HeaderText sx={{ marginBottom: "12px" }}>Exclusions</HeaderText>
            <Box
              sx={{
                display: "flex",
                flexWrap: "wrap",
                gap: { xs: 1, md: 2 },
                flexDirection: { xs: "column", md: "row" },
              }}
            >
              {exclusionsData?.map((item, index) => (
                <Box
                  key={"exclusions-" + index}
                  sx={{
                    display: "flex",
                    alignItems: { xs: "flex-start", md: "center" },
                    gap: 1,
                  }}
                >
                  <img
                    src={"/static/common/close.png"}
                    alt="feature"
                    style={{ width: "22px", height: "22px" }}
                  />
                  <DescriptionText>{item}</DescriptionText>
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>
      {/* right section */}
      <Box
        sx={{
          width: { xs: "calc(100% - 0px)", md: "416px" },
          boxSizing: "border-box",
          height: "fit-content",
          position: "sticky",
          top: "80px",
          borderRadius: "12px",
          background:
            "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 100%)",
          padding: "30px 23px",
        }}
      >
        <Box
          sx={{
            fontFamily: "Lora",
            fontWeight: 500,
            fontSize: "32px",
            lineHeight: "44px",
            letterSpacing: "0%",
            textAlign: "center",
            background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
          }}
        >
          Zippy's on vacation!
        </Box>
        <Box
          sx={{
            fontFamily: "Lora",
            fontWeight: 500,
            fontSize: "32px",
            lineHeight: "44px",
            letterSpacing: "0%",
            textAlign: "center",
            color: "#1E3A8A",
          }}
        >
          coming soon to guide your next adventure!
        </Box>
        <Box
          sx={{
            fontFamily: "Lora",
            fontWeight: 500,
            fontSize: "20px",
            lineHeight: "36px",
            letterSpacing: "0%",
            textAlign: "center",
            color: "#1E3A8A",
            marginTop: "12px",
          }}
        >
          The Chill Way to Plan Trips
        </Box>
        <Box
          sx={{
            fontFamily: "Roboto",
            fontWeight: 400,
            fontSize: "16px",
            lineHeight: "24px",
            letterSpacing: "0%",
            textAlign: "center",
            color: "#333333",
            marginTop: "8px",
          }}
        >
          Plan smarter, faster, and stress-free—our AI agent will help you build
          your perfect trip in just a few clicks.
        </Box>
        <Box
          sx={{ marginTop: "24px", display: "flex", justifyContent: "center" }}
        >
          <PrimaryButton onClick={handlePlanWithAI}>Plan with AI</PrimaryButton>
        </Box>
      </Box>
    </Box>
  );
};

export default FeatureSection;
