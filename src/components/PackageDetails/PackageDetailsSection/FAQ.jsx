import React, { useState } from "react";
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
const FAQ = ({ faqData }) => {
  const [expanded, setExpanded] = useState(null);

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : null);
  };
  return (
    <Box>
      <Typography
        sx={{
          mb: 4,
          fontWeight: 600,
          textAlign: "center",
          fontFamily: "Lora",
          fontSize: "20px",
          color: "#1E3A8A",
        }}
      >
        FAQ's
      </Typography>

      {faqData.map((faq, index) => (
        <Accordion
          key={index}
          sx={{
            mb: 2,
            boxShadow: "0px 4px 8px 0px #1E3A8A0F",
            borderRadius: "8px",
            "&:before": {
              display: "none",
            },

            //remove before style
          }}
          expanded={expanded === faq.external_id}
          onChange={handleChange(faq.external_id)}
        >
          <AccordionSummary
            expandIcon={
              expanded === faq.external_id ? (
                <RemoveIcon sx={{ color: "#FF3951" }} />
              ) : (
                <AddIcon sx={{ color: "#FF3951" }} />
              )
            }
          >
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 600,
                fontFamily: "Lora",
                fontSize: "16px",
                color: "#333333",
              }}
            >
              {faq.question}
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography
              variant="body2"
              sx={{
                fontFamily: "Lora",
                fontSize: "16px",
                color: "#333333",
                fontWeight: 500,
              }}
            >
              {faq.answer}
            </Typography>
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  );
};

export default FAQ;
