import React, { useState, useEffect, useRef } from "react";
import { Box, styled, Tabs, Tab, useMediaQuery, useTheme } from "@mui/material";
import Policies from "./Policies";
import FAQ from "./FAQ";
import Itinerary from "./Itinerary";

const PackageDetailsSection = ({
  destinationFaq,
  basicData,
  itineraryData,
  formattedActivities,
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [activeChildTab, setActiveChildTab] = useState(0);
  const [isStickyInitial, setIsStickyInitial] = useState(false);
  const childTabsRef = useRef(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const getScrollPositionRef = useRef(null);

  const itineraryRef = useRef(null);
  const hotelsRef = useRef(null);
  const addsOnRef = useRef(null);
  const destinationRef = useRef(null);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleChildTabChange = (event, newValue) => {
    setActiveChildTab(newValue);

    const scrollWithOffset = (ref) => {
      if (ref.current) {
        const y =
          ref.current.getBoundingClientRect().top + window.pageYOffset - 140;
        window.scrollTo({ top: y, behavior: "smooth" });
      }
    };

    if (newValue === 0) {
      scrollWithOffset(itineraryRef);
    } else if (newValue === 1) {
      scrollWithOffset(hotelsRef);
    } else if (newValue === 2) {
      scrollWithOffset(addsOnRef);
    } else if (newValue === 3) {
      scrollWithOffset(destinationRef);
    }
  };

  const StyledTab = styled(Tab)(({ theme }) => ({
    fontFamily: "Lora",
    fontWeight: 600,
    fontSize: "18px",
    lineHeight: "100%",
    letterSpacing: "0%",
    textTransform: "none",
    height: "64px",
    padding: "16px 24px",
    minWidth: "auto",
    color: "#333333",
    "&.Mui-selected": {
      color: "#1E3A8A",
    },
    [theme.breakpoints.down("md")]: {
      fontSize: "14px",
      padding: "8px 12px",
      flex: "1 1 33.33%",
      maxWidth: "33.33%",
    },
  }));

  const StyledChildTab = styled(Tab)(({ theme }) => ({
    fontFamily: "Lora",
    fontWeight: 600,
    fontSize: "16px",
    lineHeight: "100%",
    letterSpacing: "0%",
    textTransform: "none",
    padding: "12px 20px",
    height: "64px",
    minWidth: "auto",
    color: "#706E75",
    "&.Mui-selected": {
      color: "#FF3951",
    },
    [theme.breakpoints.down("md")]: {
      fontSize: "13px",
      padding: "6px 8px",
      flex: "1 1 25%",
      maxWidth: "25%",
    },
  }));

  const StyledTabs = styled(Tabs)(({ theme }) => ({
    "& .MuiTabs-indicator": {
      backgroundColor: "#FF3951",
      height: "4px",
    },
  }));

  const StyledChildTabs = styled(Tabs)(({ theme }) => ({
    "& .MuiTabs-indicator": {
      backgroundColor: "#FF3951",
      height: "4px",
    },
  }));

  // Extract data from API responses
  const faqData = destinationFaq?.data?.faqs || [];

  const initialTopRef = useRef(null); // store initial top offset

  useEffect(() => {
    if (getScrollPositionRef.current) {
      setTimeout(() => {
        const top =
          getScrollPositionRef.current?.getBoundingClientRect().top +
          window.scrollY;
        initialTopRef.current = top;
      }, 1000);
    }

    const handleScroll = () => {
      const currentScroll = window.scrollY;
      if (
        initialTopRef.current !== null &&
        currentScroll >= initialTopRef.current
      ) {
        setIsStickyInitial(true);
      } else {
        setIsStickyInitial(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <Box
      className="package-details-sticky-container"
      sx={{
        width: "100%",
        marginTop: "40px",
      }}
      ref={getScrollPositionRef}
    >
      <Box
        sx={{
          position: isStickyInitial ? "fixed" : "relative",
          top: isStickyInitial ? 0 : "auto",
          left: 0,
          width: isStickyInitial
            ? "100%"
            : { xs: "calc(100% + 40px)", md: "calc(100% + 160px)" },
          marginLeft: isStickyInitial ? 0 : { xs: "-20px", md: "-80px" },
          zIndex: 1000,
          background: "linear-gradient(90deg, #FEEBEE 0%, #F2EBFA 125.89%)",
          // boxShadow: isSticky ? "0px 2px 4px rgba(0, 0, 0, 0.1)" : "none",
          transition: "all 0.3s ease",
          display: "flex",
          justifyContent: "center",
          border: "1px solid #FF39510D",
          boxSizing: "border-box",
        }}
      >
        <Box
          sx={{
            maxWidth: { xs: "100%", md: "calc(100% - 160px)" },
            width: "100%",
            display: "flex",
            justifyContent: "center",
          }}
        >
          <StyledTabs
            value={activeTab}
            onChange={handleTabChange}
            variant={isMobile ? "fullWidth" : "standard"}
            centered={!isMobile}
            sx={{
              height: "64px",
              width: isMobile ? "100%" : "auto",
              "& .MuiTabs-list": {
                gap: { xs: 0, md: 2 },
              },
            }}
          >
            <StyledTab label="Package Details" />
            <StyledTab label="Policies, Terms & Conditions" />
            <StyledTab label="FAQ's" />
          </StyledTabs>
        </Box>
      </Box>

      {/* Add padding when header is sticky to prevent content jump */}
      {isStickyInitial && <Box sx={{ height: "64px" }} />}

      {/* Tab content sections go here */}
      <Box sx={{ display: activeTab === 0 ? "block" : "none" }}>
        <Box
          ref={childTabsRef}
          sx={{
            position: isStickyInitial ? "fixed" : "relative",
            top: isStickyInitial ? "64px" : "auto",
            left: 0,
            width: isStickyInitial
              ? { xs: "100%", md: "calc(100% - 160px)" }
              : { xs: "calc(100% + 40px)", md: "calc(100%)" },
            marginLeft: isStickyInitial
              ? { xs: "0px", md: "80px" }
              : { xs: "-20px", md: "0px" },
            zIndex: 999,
            background: "#FFF9F9",
            borderWidth: "0px 1px 1px 1px",
            borderStyle: "solid",
            borderColor: "#FF39510D",
            borderRadius: "0px 0px 8px 8px",

            display: "flex",
            justifyContent: "center",
          }}
        >
          <Box
            sx={{
              maxWidth: { xs: "100%", md: "calc(100% - 160px)" },
              width: "100%",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <StyledChildTabs
              value={activeChildTab}
              onChange={handleChildTabChange}
              variant={isMobile ? "fullWidth" : "standard"}
              centered={!isMobile}
              sx={{
                height: "64px",
                width: isMobile ? "100%" : "auto",
                "& .MuiTabs-list": {
                  gap: { xs: 0, md: 2 },
                },
              }}
            >
              <StyledChildTab label="Itinerary" />
              <StyledChildTab label="Hotels" />
              {itineraryData?.addons?.length > 0 && (
                <StyledChildTab label="Add-ons" />
              )}
              <StyledChildTab label="Meet your destination" />
            </StyledChildTabs>
          </Box>
        </Box>

        {/* Add padding when child header is sticky to prevent content jump */}
        {isStickyInitial && <Box sx={{ height: "48px" }} />}

        {/* Itinerary Tab */}
        <Box
          sx={{
            mt: 2,
            display: "block",
          }}
        >
          <Itinerary
            basicData={basicData}
            itineraryData={itineraryData}
            activeChildTab={activeChildTab}
            setActiveChildTab={setActiveChildTab}
            itineraryRef={itineraryRef}
            hotelsRef={hotelsRef}
            addsOnRef={addsOnRef}
            destinationRef={destinationRef}
            formattedActivities={formattedActivities}
          />
        </Box>
      </Box>

      {/* Policies & Terms Tab */}
      <Box
        sx={{
          mt: 3,
          display: activeTab === 1 ? "block" : "none",
        }}
      >
        <Policies />
      </Box>

      {/* FAQ Tab */}
      <Box
        sx={{
          mt: 3,
          display: activeTab === 2 ? "block" : "none",
        }}
      >
        <FAQ faqData={faqData} />
      </Box>
    </Box>
  );
};

export default PackageDetailsSection;
