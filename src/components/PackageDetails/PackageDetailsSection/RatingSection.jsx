import { Box, Typography } from "@mui/material";
import StarIcon from "@mui/icons-material/Star";
import StarBorderIcon from "@mui/icons-material/StarBorder";
import StarHalfIcon from "@mui/icons-material/StarHalf";

const RatingSection = ({ rating = 0, desc = "" }) => {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: { xs: "column", sm: "row" },
        padding: "24px",
        background:
          "linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 125.89%)",
        borderRadius: "8px",
        alignItems: { xs: "flex-start", sm: "center" },
        gap: { xs: 2, sm: 4 },
      }}
    >
      <Box sx={{ minWidth: { xs: "100%", sm: "160px" } }}>
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "18px",
            lineHeight: "100%",
            letterSpacing: "0%",
            verticalAlign: "middle",
            marginBottom: 1,
          }}
        >
          Rating
        </Typography>

        <Box sx={{ display: "flex", marginBottom: 1 }}>
          {Array.from({ length: 5 }).map((_, index) => {
            if (index < Math.floor(rating)) {
              return <StarIcon sx={{ color: "#00AF31" }} key={index} />;
            } else if (index === Math.floor(rating) && rating % 1 !== 0) {
              return <StarHalfIcon sx={{ color: "#00AF31" }} key={index} />;
            } else {
              return <StarBorderIcon sx={{ color: "#00AF31" }} key={index} />;
            }
          })}
        </Box>

        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "16px",
            lineHeight: "100%",
          }}
        >
          {rating}/5
        </Typography>
      </Box>

      <Typography
        sx={{
          fontFamily: "Roboto",
          fontWeight: 400,
          fontSize: "14px",
          lineHeight: "23px",
          letterSpacing: "0%",
          verticalAlign: "middle",
          flex: 1,
        }}
      >
        {desc}
      </Typography>
    </Box>
  );
};

export default RatingSection;
