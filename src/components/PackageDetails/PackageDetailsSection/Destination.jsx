import React, { useMemo } from "react";
import {
  Box,
  Typography,
  styled,
  // List,
  // ListItem,
  // ListItemText,
  // ListItemIcon,
} from "@mui/material";
// import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";

const Separator = styled(Box)(({ theme }) => ({
  width: "100%",
  height: "2px",
  background: "#333333",
  opacity: 0.05,
  marginTop: "16px",
  marginBottom: "16px",
}));

const Destination = ({ itineraryData }) => {
  const mappedDestinationData = useMemo(() => {
    const data = [];

    if (itineraryData?.best_time_to_visit) {
      data.push({
        type: "best_time_to_visit",
        name: "Best time to visit",
        description: itineraryData?.best_time_to_visit,
      });
    }

    if (itineraryData?.destination_safety) {
      data.push({
        type: "destination_safety",
        name: "Destination Safety",
        description: itineraryData?.destination_safety,
      });
    }

    if (itineraryData?.cultural_info) {
      data.push({
        type: "cultural_info",
        name: "Cultural Information",
        description: itineraryData?.cultural_info,
      });
    }

    if (itineraryData?.what_to_shop) {
      data.push({
        type: "what_to_shop",
        name: "What To Shop",
        description: itineraryData?.what_to_shop,
      });
    }

    if (itineraryData?.what_to_pack) {
      data.push({
        type: "what_to_pack",
        name: "What To Pack",
        description: itineraryData?.what_to_pack,
      });
    }

    return data;
  }, [itineraryData]);

  // const packingList = [
  //   {
  //     name: "Packing List",
  //     details: [
  //       {
  //         title: "Passport",
  //         keyPoints: [
  //           "Valid passport",
  //           "Valid visa",
  //           "Valid travel insurance",
  //           "Valid travel insurance",
  //           "Valid travel insurance",
  //         ],
  //       },
  //       {
  //         title: "Passport",
  //         keyPoints: [
  //           "Valid passport",
  //           "Valid visa",
  //           "Valid travel insurance",
  //           "Valid travel insurance",
  //         ],
  //       },
  //     ],
  //   },
  // ];

  return (
    <Box>
      <Typography
        sx={{
          fontFamily: "Lora",
          fontWeight: 600,
          fontSize: "20px",
          lineHeight: "100%",
          letterSpacing: "0%",
          verticalAlign: "middle",
          color: "#1E3A8A",
          mb: 3,
        }}
      >
        Meet your destination
      </Typography>
      <Box>
        {mappedDestinationData.map((destination, index) => (
          <Box key={index}>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1.5,
                mb: 1.5,
              }}
            >
              <Box
                sx={{
                  backgroundColor: "#FF3951",
                  width: 4,
                  height: "32px",
                  borderRadius: "2px",
                }}
              />
              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 500,
                  fontSize: "20px",
                  lineHeight: "22px",
                  letterSpacing: "0%",
                  verticalAlign: "middle",
                  color: "#000",
                }}
              >
                {destination.name}
              </Typography>
            </Box>
            <Box>
              {destination.type === "what_to_pack" ? (
                <Box
                  sx={{
                    "& h3": {
                      fontFamily: "Roboto",
                      fontWeight: 500,
                      color: "#000000",
                      mb: 2,
                    },
                    "& p": {
                      mb: 2,
                      lineHeight: 1.6,
                    },
                    "& ul, & ol": {
                      mb: 2,
                      pl: 3,
                    },
                    "& li": {
                      mb: 1,
                      lineHeight: 1.6,
                    },
                  }}
                  dangerouslySetInnerHTML={{ __html: destination.description }}
                />
              ) : (
                <Box>
                  <Typography
                    sx={{
                      fontFamily: "Roboto",
                      fontWeight: 400,
                      fontSize: "16px",
                      lineHeight: "22px",
                      whiteSpace: "pre-line",
                    }}
                  >
                    {destination.description}
                  </Typography>
                </Box>
              )}
            </Box>
            <Separator />
          </Box>
        ))}

        {/* packing list */}
        {/* {packingList.map((packing, index) => (
          <Box key={index}>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1.5,
                mb: 1.5,
              }}
            >
              <Box
                sx={{
                  backgroundColor: "#FF3951",
                  width: 4,
                  height: "32px",
                  borderRadius: "2px",
                }}
              />
              <Typography
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 500,
                  fontSize: "20px",
                  lineHeight: "22px",
                  letterSpacing: "0%",
                  verticalAlign: "middle",
                  color: "#000",
                }}
              >
                {packing.name}
              </Typography>
            </Box>
            <Box sx={{ width: "100%", mt: 1.5 }}>
              <Box
                sx={{
                  width: "100%",
                  display: "flex",
                  flexDirection: { xs: "column", md: "row" },
                  gap: 2,
                }}
              >
                {packing.details.map((detail, index) => (
                  <Box
                    sx={{ width: { xs: "100%", md: "calc(50% - 8px)" } }}
                    key={index}
                  >
                    <Box>
                      <Typography
                        sx={{
                          fontFamily: "Roboto",
                          fontWeight: 500,
                          fontSize: "16px",
                          mb: 1,
                        }}
                      >
                        {detail.title}:
                      </Typography>
                      <List>
                        {detail.keyPoints.map((point, idx) => (
                          <ListItem
                            key={idx}
                            alignItems="flex-start"
                            sx={{ p: 0, mb: 1 }}
                          >
                            <ListItemIcon sx={{ minWidth: "24px", mt: 1.5 }}>
                              <FiberManualRecordIcon
                                sx={{ fontSize: 8, color: "#000" }}
                              />
                            </ListItemIcon>
                            <ListItemText primary={point} />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Box>

            <Separator />
          </Box>
        ))} */}
      </Box>
    </Box>
  );
};

export default Destination;
