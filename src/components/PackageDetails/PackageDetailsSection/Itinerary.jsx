import React from "react";
import {
  Box,
  Button,
  styled,
  Typography,
  // List,
  // ListItem,
  // ListItemIcon,
  // ListItemText,
} from "@mui/material";
// import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";
import Hotels from "./Hotels";
import AddsOn from "./AddsOn";
import Destination from "./Destination";
import RatingSection from "./RatingSection";
import ActivitiesSection from "./ActivitiesSection";
import NearbyRestaurants from "./NearbyRestaurants";

const Separator = styled(Box)(({ theme }) => ({
  width: "100%",
  height: "2px",
  background: "#333333",
  opacity: 0.05,
  marginTop: "20px",
  marginBottom: "20px",
}));

const Itinerary = ({
  itineraryData,
  basicData,
  itineraryRef,
  hotelsRef,
  addsOnRef,
  destinationRef,
  activeChildTab,
  setActiveChildTab,
  formattedActivities,
}) => {
  const apiItineraryData = itineraryData?.itinerary;
  const customPackage =
    itineraryData?.type === "Custom AI" ||
    itineraryData?.type === "Custom Admin";

  const openChat = () => {
    if (window?.userlike) {
      // const getPath = window.location.pathname;
      // const text = `💬 Love this vibe? You're on the right page — ${getPath}. \n Tell me how you'd like to personalize it, and I'll take care of the rest!`;

      // Open the chat widget
      window.userlike.userlikeStartChat();
    }
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: { xs: "column-reverse", lg: "row" },
        gap: 2,
        mt: { xs: 0, md: 4 },
        position: "relative",
      }}
    >
      {/* left side */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: 2,
          mt: 4,
          width: { xs: "100%", lg: "calc(100% - 416px - 16px)" },
        }}
      >
        {/* itinerary */}
        <Box>
          <Typography
            sx={{
              fontFamily: "Lora",
              fontWeight: 600,
              fontSize: "20px",
              lineHeight: "100%",
              letterSpacing: "0%",
              verticalAlign: "middle",
              color: "#1E3A8A",
              mb: 3,
            }}
            ref={itineraryRef}
          >
            The Roadmap Ahead
          </Typography>

          {apiItineraryData && !customPackage && (
            <Box
              sx={{
                "& h3": {
                  fontFamily: "Roboto",
                  fontWeight: 500,
                  color: "#000000",
                  mb: 2,
                },
                "& p": {
                  mb: 2,
                  lineHeight: 1.6,
                },
                "& ul, & ol": {
                  mb: 2,
                  pl: 3,
                },
                "& li": {
                  mb: 1,
                  lineHeight: 1.6,
                },
              }}
              dangerouslySetInnerHTML={{ __html: apiItineraryData }}
            />
          )}
          {apiItineraryData &&
            apiItineraryData?.length > 0 &&
            customPackage && (
              <Box
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 500,
                  "& h3": {
                    fontFamily: "Roboto",
                    fontWeight: 500,
                    fontSize: "20px",
                    lineHeight: "22px",
                    color: "#000000",
                    mb: 2,
                  },
                  "& li": {
                    fontFamily: "Roboto",
                    fontWeight: 400,
                    fontSize: "16px",
                    lineHeight: "32px",
                    color: "#000",
                  },
                }}
              >
                {apiItineraryData?.map((item, index) => (
                  <>
                    <Box key={index}>
                      <Typography variant="h3">
                        Day {item?.day_number} - {item?.day_title}
                      </Typography>
                      <ul>
                        {item?.day_items?.map((desc, index) => (
                          <li key={index}>
                            <strong>{desc?.title}</strong>:{" "}
                            <span>{desc?.description}</span>
                          </li>
                        ))}
                      </ul>
                      <ul>
                        {item?.day_key_points?.map((keyPoint, index) => (
                          <li key={index}>{keyPoint}</li>
                        ))}
                      </ul>
                    </Box>
                    {index < apiItineraryData?.length - 1 && <Separator />}
                  </>
                ))}
              </Box>
            )}
          <Separator />

          {/* {itinerary.map((day, index) => (
            <Box
              key={index}
              sx={{
                mb: 4,
                py: 1,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1.5,
                  mb: 1.5,
                }}
              >
                <Box
                  sx={{
                    backgroundColor: "#FF3951",
                    width: 4,
                    height: "32px",
                    borderRadius: "2px",
                  }}
                />
                <Typography
                  sx={{
                    fontFamily: "Roboto",
                    fontWeight: 500,
                    fontSize: "20px",
                    lineHeight: "22px",
                    letterSpacing: "0%",
                    verticalAlign: "middle",
                    color: "#000",
                  }}
                >
                  {`Day ${day.day} - ${day.desc}`}
                </Typography>
              </Box>

              <List sx={{ pl: 1 }}>
                {day.keyPoints?.map((point, i) => (
                  <ListItem
                    key={i}
                    alignItems="flex-start"
                    sx={{ p: 0, mb: 1 }}
                  >
                    <ListItemIcon sx={{ minWidth: "24px", mt: 1.5 }}>
                      <FiberManualRecordIcon
                        sx={{ fontSize: 8, color: "#000" }}
                      />
                    </ListItemIcon>
                    <ListItemText
                      sx={{
                        m: 0,
                      }}
                      primary={
                        <Typography
                          sx={{
                            fontFamily: "Roboto",
                            fontWeight: 400,
                            fontSize: "16px",
                            lineHeight: "32px",
                            letterSpacing: "0%",
                            verticalAlign: "middle",
                            color: "#000",
                          }}
                        >
                          {`${point.trim()}.`}
                        </Typography>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          ))} */}
        </Box>
        {/* hotels */}
        <Box ref={hotelsRef}>
          <Hotels itineraryData={itineraryData} />
        </Box>
        {/* adds on */}
        {itineraryData?.addons?.length > 0 && (
          <Box ref={addsOnRef}>
            <AddsOn itineraryData={itineraryData} />
          </Box>
        )}

        {/* destination */}
        <Box ref={destinationRef}>
          <Destination itineraryData={itineraryData} />
        </Box>

        {/* rating */}
        {basicData?.rating > 0 && (
          <Box>
            <RatingSection
              rating={basicData?.rating}
              desc={basicData?.rating_description}
            />
          </Box>
        )}

        {/* activities */}
        <Box>
          <ActivitiesSection
            basicData={basicData}
            formattedActivities={formattedActivities}
          />
        </Box>

        {/* nearby restaurants */}
        <Box>
          <NearbyRestaurants basicData={basicData} />
        </Box>
      </Box>

      <Box
        sx={{
          width: { xs: "100%", lg: "416px" },
          position: { xs: "relative", lg: "sticky" },
          top: { xs: 0, lg: "200px" },
          boxSizing: "border-box",
          display: "flex",
          flexDirection: "column",
          height: "fit-content",
          alignItems: "center",
          justifyContent: "center",
          gap: "20px",
          px: { xs: 2, md: 6 },
          py: { xs: 3, md: 3 },
          borderRadius: "12px",
          backgroundColor: "#FFF9F9",
        }}
      >
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "20px",
            lineHeight: "36px",
            letterSpacing: "0%",
            textAlign: "center",
            color: "#333",
          }}
        >
          Love the Vibe?
        </Typography>

        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "20px",
            lineHeight: "36px",
            letterSpacing: "0%",
            textAlign: "center",
            color: "#FF3951",
          }}
        >
          Let's Customize It for You!
        </Typography>

        <Button
          variant="contained"
          sx={{
            background: "linear-gradient(90deg, #FF3951 0%, #FF3951 100%)",
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "16px",
            lineHeight: "100%",
            letterSpacing: "5%",
            textTransform: "capitalize",
            padding: "14px 40px",
            gap: "4px",
            color: "#fff",
            boxShadow: "none",
            transition: "background 0.3s ease",
            "&:hover": {
              background: "linear-gradient(90deg, #FF3951 0%, #9747FF 100%)",
              transition: "background 0.3s ease",

              boxShadow: "none",
            },
          }}
          onClick={openChat}
        >
          Talk to an Expert
        </Button>
      </Box>
    </Box>
  );
};

export default Itinerary;
