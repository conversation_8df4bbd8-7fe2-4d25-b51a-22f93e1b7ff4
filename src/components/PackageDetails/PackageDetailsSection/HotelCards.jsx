import React from "react";
import { Box, Typography } from "@mui/material";
import StarIcon from "@mui/icons-material/Star";
import StarHalfIcon from "@mui/icons-material/StarHalf";
import StarBorderIcon from "@mui/icons-material/StarBorder";

const HotelCards = ({ hotel }) => {
  const rating = hotel?.rating || 0;
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: { xs: "column", sm: "row" },
        gap: 3,
        overflow: "hidden",
      }}
    >
      <Box
        sx={{
          width: { xs: "100%", sm: "290px" },
          height: { xs: "200px", sm: "212px" },
          position: "relative",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          overflow: "hidden",
        }}
      >
        <img
          src={hotel?.image || "/static/common/no_image.png"}
          alt={hotel?.name || ""}
          style={{
            width: "100%",
            height: "100%",
            objectFit: "cover",
            position: "absolute",
            top: 0,
            left: 0,
            borderRadius: "12px",
          }}
        />
      </Box>

      {/* <Box sx={{ flex: 1 }}>
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "20px",
            lineHeight: "22px",
            letterSpacing: "0%",
            verticalAlign: "middle",
            color: "#333333",
          }}
        >
          {hotel?.name || ""}
        </Typography>
      </Box> */}

      <Box
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
        }}
      >
        <Box>
          {/* <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "16px",
              lineHeight: "22px",
              letterSpacing: "0%",
              verticalAlign: "middle",
              color: "#1E3A8A",
              mb: 1,
            }}
          >
            Day 1- Day 2
          </Typography> */}

          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 2,
            }}
          >
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 500,
                fontSize: "20px",
                lineHeight: "22px",
                letterSpacing: "0%",
                verticalAlign: "middle",
                color: "#333333",
              }}
            >
              {hotel.hotel}
            </Typography>

            <Box>
              {Array.from({ length: 5 }).map((_, index) => {
                if (index < Math.floor(rating)) {
                  return <StarIcon sx={{ color: "#00AF31" }} key={index} />;
                } else if (index === Math.floor(rating) && rating % 1 !== 0) {
                  return <StarHalfIcon sx={{ color: "#00AF31" }} key={index} />;
                } else {
                  return (
                    <StarBorderIcon sx={{ color: "#00AF31" }} key={index} />
                  );
                }
              })}
            </Box>
          </Box>

          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              lineHeight: "16px",
              letterSpacing: "0%",
              verticalAlign: "middle",
              color: "#333333",
              // ellipsis
              overflow: "hidden",
              textOverflow: "ellipsis",
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
              mb: 1,
            }}
          >
            {hotel.address}
          </Typography>

          {hotel?.description && (
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: "16px",
                lineHeight: "22px",
                letterSpacing: "0%",
                verticalAlign: "middle",
                color: "#333333",
                // ellipsis
                overflow: "hidden",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                mb: 1.5,
              }}
            >
              {hotel?.description || ""}
            </Typography>
          )}

          {hotel?.amenities?.length > 0 && (
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 500,
                fontSize: "14px",
                lineHeight: "16px",
                letterSpacing: "0.02em",
                verticalAlign: "middle",
                color: "#333333",
                // ellipsis
                overflow: "hidden",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                mb: 1.5,
              }}
            >
              {hotel?.amenities?.join(", ")}
            </Typography>
          )}

          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 400,
              fontSize: "14px",
              lineHeight: "20px",
              color: "#333333",
              // ellipsis
              overflow: "hidden",
              textOverflow: "ellipsis",
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
            }}
          >
            <strong>Note:</strong>{" "}
            {
              "Exact hotels may vary based on availability; upgrades to 4-star/5-star options (e.g., Holiday Inn Resort Phuket) available at additional cost."
            }
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default HotelCards;
