import React from "react";
import { Box, styled, Typography } from "@mui/material";
import HotelCards from "./HotelCards";
import generateCloudFrontURL from "../../../utils";

const Separator = styled(Box)(({ theme }) => ({
  width: "100%",
  height: "2px",
  background: "#333333",
  opacity: 0.05,
  marginTop: "20px",
  marginBottom: "20px",
}));
const Hotels = ({ itineraryData }) => {
  const hotelData = itineraryData?.hotels || [];
  const customPackage =
    itineraryData?.type === "Custom AI" ||
    itineraryData?.type === "Custom Admin";

  const customHotel =
    itineraryData?.itinerary?.length && customPackage
      ? Array.from(
          new Map(
            itineraryData.itinerary
              ?.flatMap((day) => day?.day_items || [])
              ?.filter((item) => item?.type === "Hotel" && item?.hotel_data)
              ?.map((hotel) => {
                const hotelData = hotel?.hotel_data;

                return {
                  name: hotelData?.hotel_name || null,
                  rating: hotelData?.rating || hotelData?.hotel || null,
                  address: hotelData?.address
                    ? [
                        hotelData?.address?.address_line,
                        hotelData?.address?.postal_code,
                        hotelData?.address?.city,
                        hotelData?.address?.state,
                        hotelData?.address?.country,
                      ]
                        .filter(Boolean)
                        .join(", ")
                    : null,
                  description:
                    hotel?.description || hotelData?.description || null,
                  amenities: hotelData?.amenities
                    ? hotelData?.amenities.flatMap((a) => a?.amenities || [])
                    : null,
                  images: hotelData?.images
                    ? hotelData?.images
                        .map((img) => img?.media || null)
                        .filter(Boolean)
                    : null,
                };
              })
              .filter((h) => h?.name) // ensure only items with a name are included
              .map((hotel) => [hotel.name, hotel]) // key by name
          ).values()
        )
      : [];

  if (!customPackage && hotelData?.length === 0) {
    return null;
  }

  if (customPackage && customHotel?.length === 0) {
    return null;
  }

  return (
    <Box>
      <Typography
        sx={{
          fontFamily: "Lora",
          fontWeight: 600,
          fontSize: "20px",
          lineHeight: "100%",
          letterSpacing: "0%",
          verticalAlign: "middle",
          color: "#1E3A8A",
          mb: 3,
        }}
      >
        Where You’ll Be Staying
      </Typography>

      {customPackage ? (
        <>
          {customHotel.map((hotel, index) => {
            // Generate image URL using generateCloudFrontURL
            const image =
              hotel.images && hotel.images.length > 0
                ? generateCloudFrontURL({
                    bucket: process.env.REACT_APP_BUCKET_NAME,
                    key: hotel.images[0],
                    width: 400,
                    height: 300,
                    fit: "cover",
                  })
                : generateCloudFrontURL({
                    bucket: process.env.REACT_APP_BUCKET_NAME,
                    key: "no-image.jpeg",
                    width: 800,
                    height: 500,
                    fit: "cover",
                    actual: true,
                  });
            return (
              <React.Fragment key={hotel.external_id + index}>
                <HotelCards
                  hotel={{
                    hotel: hotel.name,
                    rating: Number(hotel?.rating || 0),
                    address: hotel?.address || "",
                    description: hotel?.description || "",
                    amenities: hotel?.amenities || [],
                    image,
                  }}
                />
                <Separator />
              </React.Fragment>
            );
          })}
        </>
      ) : (
        <>
          {hotelData.map((hotel, index) => {
            // Generate image URL using generateCloudFrontURL
            const image =
              hotel.image_urls && hotel.image_urls.length > 0
                ? generateCloudFrontURL({
                    bucket: process.env.REACT_APP_BUCKET_NAME,
                    key: hotel.image_urls[0],
                    width: 400,
                    height: 300,
                    fit: "cover",
                  })
                : generateCloudFrontURL({
                    bucket: process.env.REACT_APP_BUCKET_NAME,
                    key: "no-image.jpeg",
                    width: 800,
                    height: 500,
                    fit: "cover",
                    actual: true,
                  });
            return (
              <React.Fragment key={hotel.external_id + index}>
                <HotelCards
                  hotel={{
                    hotel: hotel.name,
                    rating: Number(hotel?.rating || 0),
                    address: hotel?.address || "",
                    description: hotel?.description || "",
                    amenities: hotel?.amenities || [],
                    image,
                  }}
                />
                <Separator />
              </React.Fragment>
            );
          })}
        </>
      )}
    </Box>
  );
};

export default Hotels;
