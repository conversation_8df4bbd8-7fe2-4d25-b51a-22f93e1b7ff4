import React, { useMemo } from "react";
import { Box, Typography } from "@mui/material";
import generateCloudFrontURL from "../../../utils";

const NearbyRestaurants = ({ basicData }) => {
  const hotelList = useMemo(
    () => basicData?.restaurants || [],
    [basicData?.restaurants]
  );

  const formattedHotelList = useMemo(
    () =>
      hotelList.map((restaurant) => {
        let image = "/static/common/no_image.png";
        if (restaurant.image_urls && restaurant.image_urls.length > 0) {
          image = generateCloudFrontURL({
            bucket: process.env.REACT_APP_BUCKET_NAME,
            key: restaurant.image_urls[0],
            width: 400,
            height: 200,
            fit: "cover",
            actual: false,
          });
        } else {
          image = generateCloudFrontURL({
            bucket: process.env.REACT_APP_BUCKET_NAME,
            key: "no-image.jpeg",
            width: 400,
            height: 200,
            fit: "cover",
            actual: true,
          });
        }
        return {
          ...restaurant,
          image,
        };
      }),
    [hotelList]
  );

  if (hotelList?.length === 0) {
    return null;
  }

  return (
    <Box>
      <Typography
        sx={{
          fontFamily: "Lora",
          fontWeight: 600,
          fontSize: "20px",
          lineHeight: "100%",
          letterSpacing: "0%",
          verticalAlign: "middle",
          color: "#1E3A8A",
          mb: 5,
        }}
      >
        Nearby Restaurants
      </Typography>

      {/* <Box>
        <ul style={{ paddingLeft: "20px" }}>
          {hotelList.map((restaurant, index) => (
            <li key={index}>
              <Typography
                sx={{
                  fontFamily: "Lora",
                  fontWeight: 500,
                  fontSize: "14px",
                  lineHeight: "20px",
                  width: "100%",
                  color: "#000000",
                  mb: 0.5,
                }}
              >
                {restaurant}
              </Typography>
            </li>
          ))}
        </ul>
      </Box> */}
      <Box
        sx={{
          display: "flex",
          gap: 2,
          width: "100%",
          flexWrap: "wrap",
        }}
      >
        {formattedHotelList.map((restaurant, index) => (
          <Box
            key={index}
            sx={{
              width: { xs: "calc(50% - 8px)", sm: "calc(25% - 14px)" },
              height: "100%",
            }}
          >
            <img
              src={restaurant.image}
              alt={restaurant?.name || ""}
              style={{
                width: "100%",
                height: "200px",
                objectFit: "cover",
                borderRadius: "8px",
                maxHeight: "200px",
              }}
            />

            <Typography
              sx={{
                mt: 0.5,
                fontFamily: "Lora",
                fontWeight: 600,
                fontSize: "14px",
                lineHeight: "20px",
                width: "100%",
                textOverflow: "ellipsis",
                overflow: "hidden",
                whiteSpace: "nowrap",
                color: "#000000",
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                height: "40px",
              }}
            >
              {restaurant?.name || ""}
            </Typography>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default NearbyRestaurants;
