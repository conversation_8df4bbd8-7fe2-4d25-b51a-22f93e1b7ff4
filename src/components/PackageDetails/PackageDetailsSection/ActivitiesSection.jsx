import React from "react";
import { Box, Typography, styled } from "@mui/material";

const Separator = styled(Box)(({ theme }) => ({
  width: "100%",
  height: "2px",
  background: "#333333",
  opacity: 0.05,
  marginTop: "16px",
  marginBottom: "16px",
}));

const ActivitiesSection = ({ formattedActivities }) => {
  const activitiesApiData = formattedActivities || [];

  if (activitiesApiData?.length === 0) {
    return null;
  }

  return (
    <Box>
      <Typography
        sx={{
          fontFamily: "Lora",
          fontWeight: 600,
          fontSize: "20px",
          lineHeight: "100%",
          letterSpacing: "0%",
          verticalAlign: "middle",
          color: "#1E3A8A",
          mb: 3,
        }}
      >
        Popular Activities
      </Typography>
      <Box
        sx={{
          display: "flex",
          gap: 2,
          width: "100%",
          flexWrap: "wrap",
        }}
      >
        {activitiesApiData?.map((activity, index) => (
          <Box
            key={index + "activity"}
            sx={{
              position: "relative",
              width: { xs: "calc(50% - 8px)", sm: "calc(25% - 14px)" },
              height: "200px",
            }}
          >
            <img
              src={activity.media || "/static/common/no_image.png"}
              alt={activity.title}
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
                borderRadius: "8px",
              }}
            />

            <Typography
              sx={{
                fontFamily: "Lora",
                fontWeight: 600,
                fontSize: "14px",
                lineHeight: "20px",
                position: "absolute",
                bottom: "12px",
                width: "100%",
                textOverflow: "ellipsis",
                overflow: "hidden",
                whiteSpace: "nowrap",
                color: activity.media ? "#FFFFFF" : "#1E3A8A",
                textAlign: "center",
                textTransform: "capitalize",
              }}
            >
              {activity.title}
            </Typography>
          </Box>
        ))}
      </Box>
      <Separator />
    </Box>
  );
};

export default ActivitiesSection;
