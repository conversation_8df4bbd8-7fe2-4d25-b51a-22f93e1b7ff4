import React, { useState } from "react";
import {
  Accordion,
  AccordionSummary,
  Box,
  Button,
  Typography,
} from "@mui/material";
import { AccordionDetails } from "@mui/material";
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";

const Policies = () => {
  const [expanded, setExpanded] = useState(null);

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : null);
  };

  const openChat = () => {
    if (window?.userlike) {
      // const getPath = window.location.pathname;
      // const text = `💬 Love this vibe? You're on the right page — ${getPath}. \n Tell me how you'd like to personalize it, and I'll take care of the rest!`;

      // Open the chat widget
      window.userlike.userlikeStartChat();
    }
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: { xs: "column-reverse", lg: "row" },
        gap: 2,
        mt: { xs: 0, md: 4 },
        position: "relative",
      }}
    >
      {/* left side */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: 2,
          // mt: 4,
          width: { xs: "100%", lg: "calc(100% - 416px - 16px)" },
        }}
      >
        <Box>
          <Accordion
            sx={{
              mb: 3,
              boxShadow: "0px 4px 8px 0px #1E3A8A0F",
              borderRadius: "8px",
              "&:before": {
                display: "none",
              },

              //remove before style
            }}
            expanded={expanded === "policies"}
            onChange={handleChange("policies")}
          >
            <AccordionSummary
              expandIcon={
                expanded === "policies" ? (
                  <RemoveIcon sx={{ color: "#FF3951" }} />
                ) : (
                  <AddIcon sx={{ color: "#FF3951" }} />
                )
              }
              sx={{
                "& .MuiAccordionSummary-content": {
                  margin: "20px 0px",
                },
              }}
            >
              <Typography
                variant="subtitle1"
                sx={{
                  fontWeight: 600,
                  fontFamily: "Lora",
                  fontSize: "20px",
                  color: "#333333",
                }}
              >
                Zuumm – Terms & Conditions & Important Information
              </Typography>
            </AccordionSummary>
            <AccordionDetails
              sx={{
                paddingTop: "0px",
              }}
            >
              <ol
                style={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "32px",
                  color: "#000000",
                  paddingLeft: "20px",
                  marginTop: "0px",
                }}
              >
                <li>
                  The package price is calculated per person, based on a minimum
                  of two travelers.
                </li>
                <li>
                  Visa Assistance: Zuumm supports with visa applications, but
                  final approval is at the sole discretion of the
                  embassy/consulate.
                </li>
                <li>
                  Passport Validity: Ensure your passport is valid for at least
                  6 months from your return date.
                </li>
                <li>
                  Flight Schedule: Always check flight status 24 hours before
                  departure as schedules can change without notice.
                </li>
                <li>
                  Correct Names: Names on bookings must exactly match the travel
                  documents (passport, etc.).
                </li>
                <li>
                  Airport Transfers: After baggage claim, head to the assigned
                  exit and meet our representative within 60 minutes of landing.
                </li>
                <li>
                  Hotel Check-In: Standard check-in is 2 PM, check-out is 11 AM.
                  Early/late access is subject to hotel availability and may be
                  chargeable.
                </li>
                <li>
                  Hotel Deposits: Some hotels may ask for a refundable security
                  deposit at check-in.
                </li>
                <li>
                  Package Exclusions:
                  <ul style={{ paddingLeft: "20px", marginTop: "8px" }}>
                    <li>Visa fee is not included.</li>
                    <li>
                      Gala dinner charges (e.g., Christmas/New Year) are not
                      included.
                    </li>
                    <li>
                      Personal expenses like room service, laundry, or mini-bar
                      are excluded.
                    </li>
                  </ul>
                </li>
                <li>
                  Customizations & Vouchers: Packages can be customized. Final
                  itinerary and vouchers will be shared 72 hours prior to
                  departure after full payment.
                </li>
                <li>
                  Natural Disruptions: In case of delays or cancellations due to
                  weather or other unavoidable issues, refunds will be processed
                  as per vendor policies.
                </li>
                <li>
                  Low-Cost Flights: For low-cost airlines, baggage is not
                  included unless added separately during booking. So ensure
                  baggage policy before confirming the flights.
                </li>
                <li>
                  Hotel Substitution: If the selected hotel is unavailable, a
                  similar category hotel will be provided.
                </li>
                <li>
                  Punctuality: Be on time for activities and transfers. Delays
                  may result in missed services without refund.
                </li>
                <li>
                  Special Event Charges: Additional charges may apply at hotels
                  during festivals/peak dates. Zuumm will inform you wherever
                  possible.
                </li>
                <li>
                  Activity Refunds: Paid activities that are cancelled will be
                  refunded within 30 days. Complimentary activities are
                  non-refundable.
                </li>
                <li>
                  Itinerary Changes: Zuumm may adjust the itinerary due to
                  flight delays, closures, weather, or other disruptions.
                </li>
              </ol>
            </AccordionDetails>
          </Accordion>

          <Accordion
            sx={{
              mb: 3,
              boxShadow: "0px 4px 8px 0px #1E3A8A0F",
              borderRadius: "8px",
              "&:before": {
                display: "none",
              },

              //remove before style
            }}
            expanded={expanded === "cancellation"}
            onChange={handleChange("cancellation")}
          >
            <AccordionSummary
              expandIcon={
                expanded === "cancellation" ? (
                  <RemoveIcon sx={{ color: "#FF3951" }} />
                ) : (
                  <AddIcon sx={{ color: "#FF3951" }} />
                )
              }
              sx={{
                "& .MuiAccordionSummary-content": {
                  margin: "20px 0px",
                },
              }}
            >
              <Typography
                variant="subtitle1"
                sx={{
                  fontWeight: 600,
                  fontFamily: "Lora",
                  fontSize: "20px",
                  color: "#333333",
                }}
              >
                Zuumm Holiday Packages – Cancellation Policy
              </Typography>
            </AccordionSummary>
            <AccordionDetails
              sx={{
                paddingTop: "0px",
              }}
            >
              <ol
                style={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "32px",
                  color: "#000000",
                  paddingLeft: "20px",
                  marginTop: "0px",
                }}
              >
                <li>
                  Without Flights: Cancellations allowed only if made 30+ days
                  before the travel date.
                </li>
                <li>
                  Less than 30 Days: Bookings made less than 30 days before
                  travel are non-refundable.
                </li>
                <li>
                  With Flights: Cancellations/reschedules follow the airline's
                  policy.
                </li>
                <li>
                  Land Services: Hotels, transfers, and activities follow
                  Zuumm’s standard cancellation policy.
                </li>
                <li>
                  TCS (Tax Collected at Source) is non-refundable, but can be
                  claimed when filing your income tax return.
                </li>
                <li>
                  A 5% GST will be charged on the land package value, excluding
                  any flight costs.
                </li>
              </ol>
            </AccordionDetails>
          </Accordion>

          <Accordion
            sx={{
              mb: 3,
              boxShadow: "0px 4px 8px 0px #1E3A8A0F",
              borderRadius: "8px",
              "&:before": {
                display: "none",
              },

              //remove before style
            }}
            expanded={expanded === "tcs"}
            onChange={handleChange("tcs")}
          >
            <AccordionSummary
              expandIcon={
                expanded === "tcs" ? (
                  <RemoveIcon sx={{ color: "#FF3951" }} />
                ) : (
                  <AddIcon sx={{ color: "#FF3951" }} />
                )
              }
              sx={{
                "& .MuiAccordionSummary-content": {
                  margin: "20px 0px",
                },
              }}
            >
              <Typography
                variant="subtitle1"
                sx={{
                  fontWeight: 600,
                  fontFamily: "Lora",
                  fontSize: "20px",
                  color: "#333333",
                }}
              >
                TCS Conditions
              </Typography>
            </AccordionSummary>
            <AccordionDetails
              sx={{
                paddingTop: "0px",
              }}
            >
              <ol
                style={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "32px",
                  color: "#000000",
                  paddingLeft: "20px",
                  marginTop: "0px",
                }}
              >
                <li>
                  If you haven’t filed ITRs for the past 2 years and your
                  TDS/TCS exceeds ₹50,000, TCS will be charged at 10% on
                  international bookings.
                </li>
                <li>
                  If PAN validation fails due to non-filing, additional TCS @5%
                  will apply, or the booking may be cancelled as per policy.
                </li>
                <li>PAN card is mandatory.</li>
              </ol>
            </AccordionDetails>
          </Accordion>

          <Accordion
            sx={{
              mb: 3,
              boxShadow: "0px 4px 8px 0px #1E3A8A0F",
              borderRadius: "8px",
              "&:before": {
                display: "none",
              },

              //remove before style
            }}
            expanded={expanded === "country"}
            onChange={handleChange("country")}
          >
            <AccordionSummary
              expandIcon={
                expanded === "country" ? (
                  <RemoveIcon sx={{ color: "#FF3951" }} />
                ) : (
                  <AddIcon sx={{ color: "#FF3951" }} />
                )
              }
              sx={{
                "& .MuiAccordionSummary-content": {
                  margin: "20px 0px",
                },
              }}
            >
              <Typography
                variant="subtitle1"
                sx={{
                  fontWeight: 600,
                  fontFamily: "Lora",
                  fontSize: "20px",
                  color: "#333333",
                }}
              >
                Country-Specific Tourism Fees and Policies
              </Typography>
            </AccordionSummary>
            <AccordionDetails
              sx={{
                paddingTop: "0px",
              }}
            >
              <Box
                sx={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "24px",
                  color: "#000000",
                  marginTop: "0px",
                  "& h4": {
                    m: "8px 0px 4px 0px",
                  },
                }}
              >
                <h4>United Arab Emirates (Dubai)</h4>
                <ul>
                  <li>
                    Tourism Dirham fee applies per night, based on hotel
                    category.
                  </li>
                  <li>Fee ranges from AED 7 to AED 20 per room per night.</li>
                </ul>

                <h4>Indonesia (Bali)</h4>
                <ul>
                  <li>
                    A tourist levy of IDR 150,000 (~USD 10) is applicable per
                    person.
                  </li>
                  <li>
                    Payable online via the LoveBali app/website or at the
                    airport.
                  </li>
                </ul>

                <h4>Sri Lanka</h4>
                <ul>
                  <li>No national tourism tax.</li>
                  <li>Standard visa fees apply.</li>
                  <li>
                    Some hotels may charge service fees or additional taxes
                    locally.
                  </li>
                </ul>

                <h4>Maldives</h4>
                <ul>
                  <li>
                    A "Green Tax" of USD 6 per person per night is charged at
                    resorts, hotels, and guesthouses.
                  </li>
                  <li>
                    Seaplane or speedboat transfers may have additional costs
                    and taxes.
                  </li>
                </ul>

                <h4>Nepal</h4>
                <ul>
                  <li>No tourism tax at national level.</li>
                  <li>
                    Trekking permits and national park entry fees apply in
                    specific regions.
                  </li>
                </ul>

                <h4>United States</h4>
                <ul>
                  <li>
                    Some cities/states impose "occupancy tax" or "hotel tax".
                  </li>
                  <li>
                    Hotels may charge daily resort fees (common in Las Vegas,
                    Florida, Hawaii).
                  </li>
                  <li>
                    Hawaii plans to introduce a 0.75% climate tax on
                    accommodation charges from 2026.
                  </li>
                </ul>

                <h4>Thailand</h4>
                <ul>
                  <li>
                    Proposed tourism fee of THB 300 (~USD 8) for air arrivals
                    (pending implementation).
                  </li>
                  <li>No tourism tax currently in place as of 2025.</li>
                </ul>

                <h4>Japan</h4>
                <ul>
                  <li>
                    "Sayonara Tax" of JPY 1,000 applies when departing Japan.
                  </li>
                  <li>
                    Some cities like Tokyo and Osaka charge accommodation taxes
                    ranging from JPY 100 to JPY 300 per person per night.
                  </li>
                </ul>

                <h4>Switzerland</h4>
                <ul>
                  <li>
                    Most cities apply a tourist tax, usually CHF 2–4 per person
                    per night.
                  </li>
                  <li>
                    The fee is usually collected by the hotel at check-in or
                    check-out.
                  </li>
                </ul>

                <h4>Europe (General)</h4>
                <ul>
                  <li>
                    Most European countries/cities charge a tourist or city tax:
                  </li>
                  <li>Italy (Rome, Venice): €1–€5 per person per night.</li>
                  <li>France (Paris): €0.80–€4.00 per night.</li>
                  <li>Netherlands (Amsterdam): 7%–12.5% of room cost.</li>
                  <li>Spain (Barcelona): €0.75–€3.50 per night.</li>
                </ul>

                <h4>Turkey</h4>
                <ul>
                  <li>Accommodation tax of 2% is charged on the room rate.</li>
                </ul>

                <h4>Singapore</h4>
                <ul>
                  <li>
                    No tourism tax, but 8% GST (Goods and Services Tax) applies
                    on hotel and service bills.
                  </li>
                </ul>

                <h4>Vietnam</h4>
                <ul>
                  <li>No specific tourism tax.</li>
                  <li>8% VAT is typically added to hotel and service bills.</li>
                </ul>

                <h4>Finland</h4>
                <ul>
                  <li>No national tourism tax.</li>
                  <li>Some cities may charge a nominal tourist fee.</li>
                </ul>

                <h4>New Zealand</h4>
                <ul>
                  <li>
                    International Visitor Conservation and Tourism Levy (IVL) of
                    NZD 100 (~USD 62) is charged at the time of applying for a
                    visa or NZeTA.
                  </li>
                </ul>

                <h4>South America (varies by country)</h4>
                <ul>
                  <li>
                    Argentina, Peru, Chile, and Brazil may charge hotel taxes.
                  </li>
                  <li>
                    Foreigners in some countries are exempt if paying in foreign
                    currency and showing a passport.
                  </li>
                </ul>

                <h4>Hong Kong</h4>
                <ul>
                  <li>No tourism or hotel tax currently imposed.</li>
                </ul>

                <h4>Azerbaijan</h4>
                <ul>
                  <li>
                    A tourist tax of AZN 1.30 per person per night is charged at
                    most accommodations.
                  </li>
                </ul>
              </Box>
            </AccordionDetails>
          </Accordion>
        </Box>
      </Box>

      {/* right side */}

      <Box
        sx={{
          width: { xs: "100%", lg: "416px" },
          position: { xs: "relative", lg: "sticky" },
          top: { xs: 0, lg: "200px" },
          boxSizing: "border-box",
          display: "flex",
          flexDirection: "column",
          height: "fit-content",
          alignItems: "center",
          justifyContent: "center",
          gap: "20px",
          px: { xs: 2, md: 6 },
          py: { xs: 3, md: 3 },
          borderRadius: "12px",
          backgroundColor: "#FFF9F9",
        }}
      >
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "20px",
            lineHeight: "36px",
            letterSpacing: "0%",
            textAlign: "center",
            color: "#333",
          }}
        >
          Love the Vibe?
        </Typography>

        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "20px",
            lineHeight: "36px",
            letterSpacing: "0%",
            textAlign: "center",
            color: "#FF3951",
          }}
        >
          Let's Customize It for You!
        </Typography>

        <Button
          variant="contained"
          sx={{
            background: "linear-gradient(90deg, #FF3951 0%, #FF3951 100%)",
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "16px",
            lineHeight: "100%",
            letterSpacing: "5%",
            textTransform: "capitalize",
            padding: "14px 40px",
            gap: "4px",
            color: "#fff",
            boxShadow: "none",
            transition: "background 0.3s ease",
            "&:hover": {
              background: "linear-gradient(90deg, #FF3951 0%, #9747FF 100%)",
              transition: "background 0.3s ease",

              boxShadow: "none",
            },
          }}
          onClick={openChat}
        >
          Talk to an Expert
        </Button>
      </Box>
    </Box>
  );
};

export default Policies;
