/* eslint-disable react-hooks/exhaustive-deps */
import React from "react";
import { Box, Typography, styled } from "@mui/material";
import PrimaryButton from "../../common/Button/PrimaryButton";

const Separator = styled(Box)(({ theme }) => ({
  width: "100%",
  height: "2px",
  background: "#333333",
  opacity: 0.05,
  marginTop: "20px",
  marginBottom: "20px",
}));
const AddsOn = ({ itineraryData }) => {
  const highlightsIconUrl = "/static/package/addson";

  const highlightsIcons = [
    "island",
    "beach",
    "ship",
    "passenger",
    "breakfast",
    "taxi",
    "bus",
    "hotel",
    "flight",
    "visa",
    "forex_card",
    "insurance",
    "common",
  ];

  const handleAddOnClick = (value) => {
    if (window?.userlike) {
      // const getPath = window.location.pathname;
      // const text = `Hi there! 👋 I'm on the package page — ${getPath}. \n I want to add this add-on: "${value}". \n Please check the add-on.`;

      // Open the chat widget
      window.userlike.userlikeStartChat();
    }
  };

  const getHighlightIconUrl = (icon) => {
    let url = "";
    if (highlightsIcons.includes(icon)) {
      url = `${highlightsIconUrl}/${icon}.png`;
    } else {
      url = `${highlightsIconUrl}/common.png`;
    }
    return url;
  };
  const addOnsData = itineraryData?.addons || [];

  return (
    <Box>
      <Typography
        sx={{
          fontFamily: "Lora",
          fontWeight: 600,
          fontSize: "20px",
          lineHeight: "100%",
          letterSpacing: "0%",
          verticalAlign: "middle",
          color: "#1E3A8A",
          mb: 3,
        }}
      >
        Add-ons
      </Typography>
      <Box
        sx={{ display: "flex", flexDirection: "row", gap: 2, flexWrap: "wrap" }}
      >
        {addOnsData?.map((addon, index) => (
          <Box
            key={index}
            sx={{
              width: "200px",
              height: "160px",
              p: "20px",
              boxSizing: "border-box",
              background: "#FFF9F9",
              borderRadius: "12px",
              display: "flex",
              alignItems: "center",
              // justifyContent: "center",
              flexDirection: "column",
              gap: "16px",
            }}
          >
            <Box>
              <Box
                component="img"
                sx={{
                  width: { xs: "30px", md: "60px" },
                  height: { xs: "30px", md: "60px" },
                }}
                src={getHighlightIconUrl(addon.icon_class)}
                alt={"addon" + index}
              />
            </Box>

            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 500,
                fontSize: "16px",
                lineHeight: "22px",
                color: "#333333",
                textAlign: "center",
                overflow: "hidden",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                maxWidth: "100%",
              }}
            >
              {addon.value}
            </Typography>
          </Box>
        ))}
      </Box>
      <Box
        sx={{
          display: "flex",
          mt: 2.5,
          alignItems: "center",
          flexDirection: "row",
          gap: 2,
          flexWrap: "wrap",
        }}
      >
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 500,
            fontSize: "16px",
            lineHeight: "22px",
            color: "#333333",
            flex: 1,
          }}
        >
          One package, endless possibilities—chat with an expert to tailor it
          your way!
        </Typography>
        <PrimaryButton onClick={handleAddOnClick}>Talk to Expert</PrimaryButton>
      </Box>
      <Separator />
    </Box>
  );
};

export default AddsOn;
