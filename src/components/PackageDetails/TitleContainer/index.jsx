import React, { useState } from "react";
import { Box } from "@mui/material";
import StarIcon from "@mui/icons-material/Star";
import StarBorderIcon from "@mui/icons-material/StarBorder";
import StarHalfIcon from "@mui/icons-material/StarHalf";
const TitleContainer = ({ rating = 0, title = "" }) => {
  const [expanded, setExpanded] = useState(false);
  return (
    <Box
      className="package-title-container"
      sx={{
        display: "flex",
        flexDirection: { xs: "column", md: "row" },
        alignItems: "flex-start",
        gap: { xs: 1, md: 2 },
        justifyContent: "space-between",
      }}
    >
      <Box
        onClick={() => setExpanded((prev) => !prev)}
        sx={{
          flex: 1,
          fontSize: { xs: "24px", md: "36px" },
          fontWeight: 600,
          lineHeight: { xs: "32px", md: "52px" },
          maxWidth: { xs: "100%", md: "calc(100% - 120px - 120px)" },
          color: "#333333",
          overflow: "hidden",
          display: "-webkit-box",
          WebkitBoxOrient: "vertical",
          WebkitLineClamp: expanded ? "none" : { xs: 3, md: 2 },
          textOverflow: "ellipsis",
          whiteSpace: expanded ? "normal" : "initial",
        }}
      >
        {title}
      </Box>
      <Box>
        <Box
          sx={{
            fontSize: { xs: "14px", md: "16px" },
            fontWeight: "500",
            lineHeight: { xs: "20px", md: "24px" },
            color: "#333333",
            textAlign: { xs: "left", md: "right" },
          }}
        >
          Star Rating: {rating}
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: { xs: "flex-start", md: "flex-end" },
          }}
        >
          {Array.from({ length: 5 }).map((_, index) => {
            if (index < Math.floor(rating)) {
              return <StarIcon sx={{ color: "#00AF31" }} key={index} />;
            } else if (index === Math.floor(rating) && rating % 1 !== 0) {
              return <StarHalfIcon sx={{ color: "#00AF31" }} key={index} />;
            } else {
              return <StarBorderIcon sx={{ color: "#00AF31" }} key={index} />;
            }
          })}
        </Box>
      </Box>
    </Box>
  );
};

export default TitleContainer;
