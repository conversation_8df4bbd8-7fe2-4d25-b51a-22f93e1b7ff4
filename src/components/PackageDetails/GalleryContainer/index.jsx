import { Box } from "@mui/material";

const GalleryContainer = ({ gallery = [], setIsGalleryOpen }) => {
  const areaMap = ["a", "b", "c", "d"];

  return (
    <Box
      className="gallery-container"
      sx={{
        display: "grid",
        gridTemplateColumns: "repeat(4, 1fr)",
        gridTemplateRows: "repeat(2, 1fr)",
        gridTemplateAreas: `
          "a a b b"
          "a a c d"
        `,
        gap: { xs: 0.5, md: 1.5 },
        width: "100%",
        aspectRatio: "5 / 2",
        marginTop: { xs: 2, md: 3 },
        borderRadius: 1.5,
        overflow: "hidden",
        position: "relative",
      }}
    >
      {gallery.slice(0, 4).map((item, index) => (
        <Box
          key={"gallery_slice_item_" + index}
          sx={{
            gridArea: areaMap[index],
            overflow: "hidden",
            "& img": {
              width: "100%",
              height: "100%",
              objectFit: "cover",
              display: "block",
            },
          }}
        >
          <img src={item.file} alt={item.id} />
        </Box>
      ))}
      <Box
        sx={{
          position: "absolute",
          bottom: { xs: "10px", md: "20px" },
          right: { xs: "10px", md: "20px" },
          width: "fit-content",
          height: "fit-content",
          backgroundColor: "#1E3A8A",
          color: "white",
          padding: { xs: "6px 10px", md: "12px 24px" },
          borderRadius: 1.5,
          fontSize: { xs: "12px", md: "16px" },
          fontWeight: 600,
          lineHeight: { xs: "16px", md: "24px" },
          textAlign: "center",
          textTransform: "capitalize",
          cursor: "pointer",
          "&:hover": {
            backgroundColor: "#1e3b8ab0",
          },
        }}
        onClick={() => setIsGalleryOpen(true)}
      >
        Gallery
      </Box>
    </Box>
  );
};

export default GalleryContainer;
