import React from "react";
import { Box, I<PERSON><PERSON><PERSON>on, <PERSON><PERSON><PERSON>, Typography } from "@mui/material";
import InfoOutlineIcon from "@mui/icons-material/InfoOutline";
const PackageInfoSection = ({ infoItems = [] }) => {
  const duplicatedItems = infoItems.length > 0 ? [...infoItems] : [];

  return (
    <Box
      display="flex"
      width="100%"
      flexDirection={{ xs: "column", md: "row" }}
      justifyContent="space-between"
      alignItems="center"
      columnGap={2}
    >
      <Box
        sx={{
          width: { xs: "100%" },
          boxSizing: "border-box",
          pt: { xs: 3, md: 4 },
          pr: 2.5,
          overflow: "hidden",
          position: "relative",
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            gap: "16px",
          }}
        >
          {duplicatedItems.map((item, index) => (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: "8px",
                flex: "0 0 auto",
                width: "fit-content",
              }}
              key={index + item.title}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <img src={item.icon} alt={item.title} width={46} height={46} />
              </Box>
              <Box sx={{ display: "flex", flexDirection: "column" }}>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: "4px",
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{ color: "text.secondary", fontSize: "12px" }}
                  >
                    {item.title}
                  </Typography>

                  {item.type === "type" && (
                    <Tooltip
                      title="Talk to an agent to customize this"
                      enterTouchDelay={0}
                    >
                      <IconButton sx={{ p: "4px" }}>
                        <InfoOutlineIcon
                          sx={{ fontSize: "16px", color: "#706E75" }}
                        />
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>

                <Typography
                  variant="body1"
                  sx={{
                    color: "#1E3A8A",
                    fontWeight: 600,
                    fontSize: { xs: "14px", md: "16px" },
                  }}
                >
                  {item.value}
                </Typography>
              </Box>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default PackageInfoSection;
