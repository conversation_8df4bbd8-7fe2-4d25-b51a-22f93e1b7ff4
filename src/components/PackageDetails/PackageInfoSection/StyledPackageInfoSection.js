import { styled } from "@mui/material/styles";
import { Box } from "@mui/material";

const StyledPackageInfoSection = styled(Box)(({ theme }) => ({
  display: "flex",
  flexWrap: "wrap",
  gap: theme.spacing(2),
  padding: theme.spacing(3, 2),
  backgroundColor: "#f8f9fa",
  borderRadius: theme.spacing(1),
  border: "1px solid #e0e0e0",

  [theme.breakpoints.up("sm")]: {
    gap: theme.spacing(3),
    padding: theme.spacing(3),
  },

  [theme.breakpoints.up("md")]: {
    gap: theme.spacing(4),
  },

  "& .info-item": {
    display: "flex",
    alignItems: "center",
    gap: theme.spacing(1),
    minWidth: "100%",
    flexGrow: 1,
    flexBasis: 0,

    [theme.breakpoints.up("sm")]: {
      minWidth: "calc(50% - 12px)",
    },

    [theme.breakpoints.up("md")]: {
      minWidth: "calc(33.333% - 16px)",
    },

    [theme.breakpoints.up("lg")]: {
      minWidth: "auto",
    },
  },

  "& .info-icon": {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    minWidth: 24,
    height: 24,

    "& svg": {
      color: "#FF3951",
      fontSize: 20,
    },
  },

  "& .info-content": {
    display: "flex",
    flexDirection: "column",
    minWidth: 0,
  },

  "& .info-label": {
    color: theme.palette.text.secondary,
    fontSize: "12px",
    fontWeight: 400,
    lineHeight: 1.2,
    marginBottom: theme.spacing(0.25),
  },

  "& .info-value-container": {
    display: "flex",
    alignItems: "baseline",
    gap: theme.spacing(0.5),
  },

  "& .info-value": {
    color: theme.palette.primary.main,
    fontSize: "14px",
    fontWeight: 600,
    lineHeight: 1.2,
    wordBreak: "break-word",

    [theme.breakpoints.up("sm")]: {
      fontSize: "15px",
    },

    [theme.breakpoints.up("md")]: {
      fontSize: "16px",
    },
  },

  "& .info-unit": {
    color: theme.palette.text.secondary,
    fontSize: "11px",
    fontWeight: 400,
    whiteSpace: "nowrap",
  },
}));

export default StyledPackageInfoSection;
