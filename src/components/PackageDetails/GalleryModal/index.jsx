/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react";
import { Box, Modal, Typography, IconButton, styled } from "@mui/material";

const StyledModal = styled(Modal)({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  backdropFilter: "blur(16px)",
  background: "#1E3A8A33",
});

const GalleryModal = ({ open, onClose, images = [] }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [visibleImages, setVisibleImages] = useState([]);

  useEffect(() => {
    updateVisibleImages(currentIndex);
  }, [currentIndex, images]);

  const updateVisibleImages = (activeIndex) => {
    const totalImages = images.length;
    let start = Math.max(0, Math.min(activeIndex - 2, totalImages - 5));
    let end = Math.min(start + 5, totalImages);

    // Adjust start if we're near the end
    if (end - start < 5 && start > 0) {
      start = Math.max(0, end - 5);
    }

    setVisibleImages(images.slice(start, end));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % images.length);
  };

  const handlePrev = () => {
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const handleThumbnailClick = (index) => {
    setCurrentIndex(index);
  };

  return (
    <StyledModal open={open} onClose={onClose}>
      <Box
        sx={{
          position: "relative",
          width: "90%",
          maxWidth: "680px",
          maxHeight: "90vh",
          bgcolor: "white",
          borderRadius: "8px",
          py: { xs: 2, md: 3 },
          display: "flex",
          flexDirection: "column",
          outline: "0px !important",
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            pb: 3,
            px: { xs: 2, md: 5 },
            borderBottom: "1px solid rgba(51, 51, 51, 0.05)",
          }}
        >
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontSize: "24px",
              fontWeight: 500,
              color: "#333333",
            }}
          >
            Gallery
          </Typography>
          <IconButton onClick={onClose}>
            <Box
              component="img"
              src={"/static/common/closeBlue.png"}
              sx={{ width: "28px", height: "28px" }}
              alt="close modal"
            />
          </IconButton>
        </Box>

        {/* Main Image */}
        <Box
          sx={{
            position: "relative",
            width: "100%",
            height: "100%",
            maxHeight: { xs: "50dvh", md: "400px" },
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            px: { xs: 2, md: 5 },
            pt: { xs: 2, md: 3 },
            boxSizing: "border-box",
          }}
        >
          <IconButton
            onClick={handlePrev}
            sx={{
              position: "absolute",
              left: { xs: "24px", md: "52px" },
            }}
          >
            <Box
              component="img"
              src={"/static/common/prevImg.png"}
              sx={{ width: "24px", height: "24px" }}
              alt="previous image"
            />
          </IconButton>

          <Box
            sx={{
              width: "100%",
              minHeight: { xs: "50dvh", md: "400px" },
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Box
              component="img"
              src={images[currentIndex]?.file}
              alt={`Gallery image ${currentIndex + 1}`}
              sx={{
                maxHeight: { xs: "50dvh", md: "400px" },
                maxWidth: "100%",
                height: "100%",
                objectFit: "cover",
              }}
            />
          </Box>

          <IconButton
            onClick={handleNext}
            sx={{
              position: "absolute",
              right: { xs: "24px", md: "52px" },
            }}
          >
            <Box
              component="img"
              src={"/static/common/nextImg.png"}
              sx={{ width: "24px", height: "24px" }}
              alt="next image"
            />
          </IconButton>
        </Box>

        {/* Image Counter */}
        <Typography
          sx={{
            textAlign: "center",
            color: "#1E3A8A",
            fontFamily: "Roboto",
            fontSize: "16px",
            fontWeight: 500,
            mt: 2,
          }}
        >
          {currentIndex + 1}/{images.length}
        </Typography>

        {/* Thumbnail List */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            gap: { xs: 1, md: 1.5 },
            overflowX: "hidden",
            mt: 2,
          }}
        >
          {visibleImages.map((image, idx) => {
            const actualIndex = images.findIndex(
              (img) => img.external_id === image.external_id
            );
            return (
              <Box
                key={image.external_id + idx}
                component="img"
                src={image.file}
                alt={`Thumbnail ${actualIndex + 1}`}
                onClick={() => handleThumbnailClick(actualIndex)}
                sx={{
                  width: { xs: "40px", md: "80px" },
                  height: { xs: "40px", md: "80px" },
                  objectFit: "cover",
                  cursor: "pointer",
                  opacity: actualIndex === currentIndex ? 1 : 0.5,
                  transition: "opacity 0.3s ease",
                }}
              />
            );
          })}
        </Box>
      </Box>
    </StyledModal>
  );
};

export default GalleryModal;
