import { styled } from "@mui/material/styles";

const StyledFeaturesTailored = styled("div")(
  ({ theme }) => `
  padding: 60px 80px;
  background: #FFFFFF;

  .content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  .title {
    font-family: 'Lora', serif;
    font-size: 32px;
    font-weight: 700;
    line-height: 56px;
    text-align: center;
    margin-bottom: 28px;
    color: ${theme.palette.secondary.main};

    .highlight {
      background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .main-container {
    position: relative;
    padding: 12px;
    margin-top: 24px;
  }

  .bg-container {
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    right: 0;
    height: calc(100% - 24px);
    background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
    border-radius: 24px;
  }  

  .features-container {
    position: relative;
    background: #FFFFFF;
    border-radius: 24px;
    padding: 48px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 48px;
    box-shadow: 0px 4px 24px rgba(0, 0, 0, 0.08);
    margin-bottom: -12px;
  }

  .feature-group {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .feature-title {
    text-align: center;
    font-family: 'Lora', serif;
    font-size: 20px;
    font-weight: 500;
    line-height: 100%;
    color: ${theme.palette.secondary.main};
    
    .highlight {
      font-weight: 600;
      color: #FF3951;
    }
  }

  .feature-desc {
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 18px;
    color: ${theme.palette.custom.subText};
  }

  .feature-items {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .feature-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;

    .check-icon {
      width: 32px;
      height: 32px;
      margin-top: 2px;
    }

    p {
      font-family: 'Roboto', sans-serif;
      font-size: 16px;
      line-height: 150%;
      color: ${theme.palette.custom.subText};
    }
  }

  @media (max-width: 1024px) {
    .features-container {
      grid-template-columns: 1fr;
      gap: 32px;
      padding: 32px;
    }
  }

  @media (max-width: 768px) {
    padding: 60px 0;

    .title {
      font-size: 24px;
      line-height: 30px;
      margin-bottom: 10px;
      text-align: center;
    }

    .features-container {
      padding: 24px;
      gap: 24px;
    }

    .feature-title {
      font-size: 20px;
    }

    .feature-item p {
      font-size: 14px;
    }
    .feature-items{
      align-items: center;
    }  
  }

  @media (max-width: 600px) {
    .feature-title {
      font-size: 16px;
    }
    .feature-item p {
      font-size: 12px;
    }
  }
`
);

export default StyledFeaturesTailored; 