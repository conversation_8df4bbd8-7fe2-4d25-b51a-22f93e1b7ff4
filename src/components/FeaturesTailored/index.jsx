import React from "react";
import { Box, Typography } from "@mui/material";
import StyledFeaturesTailored from "./StyledFeaturesTailored";
import planeBulletsIcon from "../../assets/svg/planeBulletsIcon.svg";

const FeaturesTailored = () => {
  const features = [
    {
      title: ["Seamless Package", "Management"],
      items: [
        "Upload and manage your own travel packages in minutes",
        "Or instantly access and sell from ZUUMM's curated catalog",
      ],
    },
    {
      title: ["AI-Driven", "Client Interaction"],
      items: [
        "Engage customers 24/7 with AI-powered chat and support",
        "Auto-generate personalized itineraries based on user intent",
      ],
    },
    {
      title: ["Smart Dashboard", "& Analytics"],
      items: [
        "Track bookings, revenue, and customer activity in real time",
        "Leverage insights to understand trends and optimize performance",
      ],
    },
    {
      title: ["Flexible Integration", "Options"],
      items: [
        "Use ZUUMM as your full platform or integrate with your existing site",
        "Developer-friendly API access for advanced customization",
      ],
    },
  ];

  return (
    <StyledFeaturesTailored>
      <Box className='content-wrapper'>
        <Typography className='title'>
          <span className='highlight'>Features</span> Tailored for You
        </Typography>

        <Box className='main-container'>
          <Box className='bg-container'></Box>
          <Box className='features-container'>
            {features.map((feature, index) => (
              <Box key={index} className='feature-group'>
                <Typography className='feature-title'>
                  <span className='highlight'>{feature.title[0]}</span>{" "}
                  {feature.title[1]}
                </Typography>
                <Box className='feature-items'>
                  {feature.items.map((item, itemIndex) => (
                    <Box key={itemIndex} className='feature-item'>
                      <Box
                        component='img'
                        src={planeBulletsIcon}
                        alt='check'
                        className='check-icon'
                      />
                      <Typography className="feature-desc">{item}</Typography>
                    </Box>
                  ))}
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
      </Box>
    </StyledFeaturesTailored>
  );
};

export default FeaturesTailored;
