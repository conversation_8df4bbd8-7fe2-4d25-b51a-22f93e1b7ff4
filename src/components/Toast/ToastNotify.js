import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const options = {
  position: "top-right",
  autoClose: 3000,
  hideProgressBar: true,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
};

export const ToastNotifyInfo = (message, id) => {
  toast.info(message, { ...options, toastId: `info-${id}` });
};

export const ToastNotifySuccess = (message, id) => {
  toast.success(message, { ...options, toastId: `success-${id}` });
};

export const ToastNotifyError = (message, id) => {
  toast.error(message, { ...options, toastId: `error-${id}` });
};
