/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useRef } from "react";
import { Box, Typography } from "@mui/material";
import StyledDemocratizingTravel from "./StyledDemocratizingTravel";
import playIcon from "../../assets/svg/playButton.svg";
import pauseIcon from "../../assets/svg/pauseButton.svg";
import { useDispatch } from "react-redux";
import { fetchBlogPodcasts } from "../../store/reducers/Blog/apiThunk";
import { ToastNotifyError } from "../Toast/ToastNotify";
import generateCloudFrontURL from "../../utils";
import { Swiper } from "swiper/react";
import { Navigation } from "swiper/modules";
import { SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/navigation";
import PodcastSliderSkeleton from "../Skeleton/PodcastSliderSkeleton";

const DemocratizingTravel = () => {
  const [podcastPosts, setPodcastPosts] = useState([
    // {
    //   title: "Episode 1",
    //   audio: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3",
    //   banner:
    //     "https://images.pexels.com/photos/20183314/pexels-photo-20183314.jpeg",
    //   tags: ["Travel"],
    // },
    // {
    //   title: "Episode 2",
    //   audio: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3",
    //   banner:
    //     "https://images.pexels.com/photos/20183314/pexels-photo-20183314.jpeg",
    //   tags: ["Travel"],
    // },
    // {
    //   title: "Episode 3",
    //   audio: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3",
    //   banner:
    //     "https://images.pexels.com/photos/20183314/pexels-photo-20183314.jpeg",
    //   tags: ["Podcast"],
    // },
    // {
    //   title: "Episode 4",
    //   audio: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3",
    //   banner:
    //     "https://images.pexels.com/photos/20183314/pexels-photo-20183314.jpeg",
    //   tags: ["Podcast"],
    // },
    // {
    //   title: "Episode 5",
    //   audio: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3",
    //   banner:
    //     "https://images.pexels.com/photos/20183314/pexels-photo-20183314.jpeg",
    //   tags: ["Podcast"],
    // },
    // {
    //   title: "Episode 6",
    //   audio: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3",
    //   banner:
    //     "https://images.pexels.com/photos/20183314/pexels-photo-20183314.jpeg",
    //   tags: ["Podcast"],
    // },
  ]);
  const [loading, setLoading] = useState(false);
  const [currentlyPlaying, setCurrentlyPlaying] = useState(null);
  const audioRefs = useRef({});
  const dispatch = useDispatch();

  const fetchBlogPosts = async () => {
    setLoading(true);

    try {
      const res = await dispatch(fetchBlogPodcasts()).unwrap();

      const { data, status } = res?.data;
      const blogs = data?.data || [];

      if (status === "success" && blogs) {
        // Transform API data to match your component structure
        const transformedPosts = blogs?.map((post) => ({
          ...post,
          banner: generateCloudFrontURL({
            bucket: process.env.REACT_APP_BUCKET_NAME,
            key: post.banner,
            width: 252,
            height: 252,
            fit: "cover",
            actual: false,
          }),
        }));

        setPodcastPosts(transformedPosts);
      }
    } catch (err) {
      console.error("Error fetching blog posts:", err);
      ToastNotifyError("Failed to fetch blog podcasts");
    } finally {
      setLoading(false);
    }
  };

  const handlePlayPause = (index, audioUrl) => {
    const audioElement = audioRefs.current[index];

    if (!audioElement) return;

    if (currentlyPlaying === index) {
      // Pause current audio
      audioElement.pause();
      setCurrentlyPlaying(null);
    } else {
      // Stop any currently playing audio
      if (currentlyPlaying !== null) {
        const currentAudio = audioRefs.current[currentlyPlaying];
        if (currentAudio) {
          currentAudio.pause();
          currentAudio.currentTime = 0;
        }
      }

      // Play new audio
      audioElement.play();
      setCurrentlyPlaying(index);
    }
  };

  // Handle audio ended event
  const handleAudioEnded = (index) => {
    setCurrentlyPlaying(null);
  };

  useEffect(() => {
    fetchBlogPosts();
  }, []);

  return (
    <StyledDemocratizingTravel>
      <Box className="content-wrapper">
        <Typography className="title">
          <span className="highlight">Democratizing Travel</span>, one episode
          at a time
        </Typography>

        <Box className="episodes-carousel">
          {loading ? (
            <PodcastSliderSkeleton />
          ) : (
            <Box sx={{ width: "100%" }}>
              <Swiper
                modules={[Navigation]}
                spaceBetween={24}
                slidesPerView={1}
                navigation={{
                  nextEl: ".swiper-button-next-podcast",
                  prevEl: ".swiper-button-prev-podcast",
                }}
                breakpoints={{
                  640: {
                    slidesPerView: 2,
                    spaceBetween: 24,
                  },
                  768: {
                    slidesPerView: 3,
                    spaceBetween: 24,
                  },
                  1024: {
                    slidesPerView: 5,
                    spaceBetween: 24,
                  },
                }}
                style={{
                  width: "100%",
                  paddingBottom: "16px",
                }}
              >
                {podcastPosts?.map((episode, index) => (
                  <SwiperSlide key={index}>
                    <Box
                      className="episode-card"
                      // component='a'
                      // href={episode.link}
                    >
                      <Box
                        component="img"
                        src={episode.banner}
                        alt={episode.title}
                        className="episode-image"
                      />
                      <Box className="episode-content">
                        <Typography className="episode-type">
                          {episode?.tags?.length > 0
                            ? episode?.tags[0]?.name
                            : "Post"}
                        </Typography>
                        <Box className="play-icon-wrapper">
                          <Typography className="episode-title">
                            {episode.title}
                          </Typography>
                          <Box
                            className="play-icon"
                            onClick={() =>
                              handlePlayPause(index, episode.audio)
                            }
                            sx={{
                              cursor: "pointer",
                              transition: "transform 0.2s ease",
                              "&:hover": {
                                transform: "scale(1.1)",
                              },
                            }}
                          >
                            {/* Hidden audio element */}
                            <audio
                              ref={(el) => (audioRefs.current[index] = el)}
                              src={episode.audio_file}
                              onEnded={() => handleAudioEnded(index)}
                              style={{ display: "none" }}
                            />
                            <img
                              src={
                                currentlyPlaying === index
                                  ? pauseIcon
                                  : playIcon
                              }
                              alt={
                                currentlyPlaying === index ? "pause" : "play"
                              }
                              style={{
                                width: "24px",
                                height: "24px",
                                opacity: currentlyPlaying === index ? 0.7 : 1,
                              }}
                            />
                          </Box>
                        </Box>
                      </Box>
                    </Box>
                  </SwiperSlide>
                ))}
              </Swiper>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  gap: 2,
                }}
              >
                <img
                  className="swiper-button-prev-podcast"
                  src={"/static/common/slidePrev.png"}
                  alt="prev"
                  style={{ cursor: "pointer", width: "34px", height: "34px" }}
                />
                <img
                  className="swiper-button-next-podcast"
                  src={"/static/common/slideNext.png"}
                  alt="next"
                  style={{ cursor: "pointer", width: "34px", height: "34px" }}
                />
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </StyledDemocratizingTravel>
  );
};

export default DemocratizingTravel;
