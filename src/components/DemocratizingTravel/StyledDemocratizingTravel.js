import { styled } from "@mui/material/styles";

const StyledDemocratizingTravel = styled("div")(
  ({ theme }) => `
  padding: 0px 0;
  background: #FFFFFF;
  overflow: hidden;

  .content-wrapper {
    max-width: 90%;
    margin: 0 auto;
    padding: 0 24px;
  }

  .title {
    font-family: 'Lora', serif;
    font-size: 32px;
    font-weight: 700;
    line-height: 56px;
    text-align: center;
    margin-bottom: 28px;
    color: ${theme.palette.secondary.main};

    .highlight {
      background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .episodes-carousel {
    display: flex;
    gap: 24px;
    overflow-x: auto;
    padding: 8px 4px;
    margin: 0 -24px;
    padding: 0 24px;
    height: 400px;
  }

  .episode-card {
    // width: 20%;
    height: 354px;
    text-decoration: none;
    border-radius: 24px 24px 0px 0px;
    overflow: hidden;
    background: #FFF9F9;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.05);
  }

  .episode-image {
    width: 100%;
    height: 252px;
    object-fit: cover;
  }

  .episode-content {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .episode-type {
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    font-weight: 600;
    line-height: 130%;
    letter-spacing: 0.05em;
    color: ${theme.palette.custom.lightred};
    margin-bottom: 8px;
  }

  .episode-title {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 700;
    line-height: 130%;
    letter-spacing: 0.05em;
    color: ${theme.palette.custom.subText};
    margin-right: 32px;
  }

  .play-icon-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }  

  .play-icon {
    cursor: pointer;
    display: flex;
    align-items: center;
    
    img {
      width: 32px;
      height: 32px;
    }
  }

  @media (max-width: 768px) {
    padding: 60px 0;

    .title {
      font-size: 28px;
      line-height: 40px;
      margin-bottom: 32px;
    }

    .episodes-carousel {
      gap: 16px;
    }

    .episode-card {
      min-width: 260px;
    }
  }
`
);

export default StyledDemocratizingTravel; 