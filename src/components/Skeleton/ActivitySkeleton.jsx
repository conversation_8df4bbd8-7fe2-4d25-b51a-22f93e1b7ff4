import React from "react";
import { Box, Skeleton } from "@mui/material";

const ActivitySkeleton = ({ variant = "large" }) => {
  if (variant === "large") {
    return (
      <Box className="activity-card">
        <Skeleton
          variant="rectangular"
          width="100%"
          height="100%"
          sx={{ borderRadius: "12px" }}
        />
        <Box sx={{ position: "absolute", bottom: 16, left: 16, right: 16 }}>
          <Skeleton
            variant="text"
            width="70%"
            height={20}
            sx={{ color: "white" }}
          />
        </Box>
      </Box>
    );
  }

  return (
    <Box className="activity-card2">
      <Skeleton
        variant="rectangular"
        width="100%"
        height="100%"
        sx={{ borderRadius: "12px" }}
      />
      <Box sx={{ position: "absolute", bottom: 12, left: 12, right: 12 }}>
        <Skeleton
          variant="text"
          width="80%"
          height={16}
          sx={{ color: "white" }}
        />
      </Box>
    </Box>
  );
};

export default ActivitySkeleton;
