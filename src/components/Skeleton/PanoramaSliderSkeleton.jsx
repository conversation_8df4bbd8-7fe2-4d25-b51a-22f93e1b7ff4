import React from "react";
import { Box, Skeleton } from "@mui/material";

const getSlideStyle = (index) => {
  // Center slide is index 2 (out of 0-4)
  if (index === 2) {
    return {
      width: { xs: "80%", md: "300px" },
      aspectRatio: "1/1",
      borderRadius: "12px",
      position: "relative",
      zIndex: 10,
      opacity: 1,
      transform: {
        xs: "scale(1)",
        md: "scale(1.2)",
      },
      boxShadow: 3,
      background: "#fff",
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
    };
  } else {
    return {
      width: { xs: "40%", md: "250px" },
      aspectRatio: "1/1",
      borderRadius: "12px",
      position: "relative",
      zIndex: 1,
      opacity: 0.7,
      transform: {
        xs: "scale(0.9)",
        md: "scale(0.9)",
      },
      background: "#fff",
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
    };
  }
};

const PanoramaSliderSkeleton = () => {
  return (
    <Box sx={{ width: "100%", maxWidth: "1440px", mx: "auto", height: "100%" }}>
      {/* Slider Container */}
      <Box
        sx={{
          display: "flex",
          gap: 4,
          justifyContent: "center",
          alignItems: "center",
          height: { xs: "180px", md: "320px" },
          overflow: "visible",
        }}
      >
        {[0, 1, 2, 3, 4].map((i) => (
          <Box key={i} sx={getSlideStyle(i)}>
            <Skeleton
              variant="rectangular"
              width="100%"
              height="100%"
              sx={{ borderRadius: "12px", minHeight: 120 }}
            />
            {/* Center slide gets extra skeletons for badge and price */}
            {i === 2 && (
              <>
                {/* Location Badge Skeleton */}
                <Box
                  sx={{
                    position: "absolute",
                    top: "16px",
                    left: "16px",
                    borderRadius: "32px",
                    background: "white",
                    p: 1,
                    display: "flex",
                    alignItems: "center",
                    gap: 0.5,
                    zIndex: 3,
                  }}
                >
                  <Skeleton
                    variant="circular"
                    width={20}
                    height={20}
                    sx={{ color: "#FF3951" }}
                  />
                  <Skeleton
                    variant="text"
                    width={60}
                    height={16}
                    sx={{ color: "#1E3A8A" }}
                  />
                </Box>
                {/* Price Info Skeleton */}
                <Box
                  sx={{
                    position: "absolute",
                    bottom: "16px",
                    left: "16px",
                    zIndex: 3,
                  }}
                >
                  <Skeleton
                    variant="text"
                    width={50}
                    height={16}
                    sx={{ color: "white", mb: 0.5 }}
                  />
                  <Skeleton
                    variant="text"
                    width={90}
                    height={18}
                    sx={{ color: "white" }}
                  />
                </Box>
              </>
            )}
          </Box>
        ))}
      </Box>
      {/* Navigation Buttons Skeleton */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          gap: 2,
          mt: 4,
          mb: 4.5,
        }}
      >
        <Skeleton
          variant="rectangular"
          width={34}
          height={34}
          sx={{ borderRadius: "50%" }}
        />
        <Skeleton
          variant="rectangular"
          width={34}
          height={34}
          sx={{ borderRadius: "50%" }}
        />
      </Box>
    </Box>
  );
};

export default PanoramaSliderSkeleton;
