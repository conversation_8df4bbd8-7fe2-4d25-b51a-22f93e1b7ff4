import React from "react";
import { Box, Skeleton } from "@mui/material";

const CategorySkeleton = () => {
  return (
    <Box className="category-item">
      <Box className="main-category-item">
        <Box className="category-item-image">
          <Skeleton
            variant="rectangular"
            width="100%"
            height="100%"
            sx={{ borderRadius: "8px 8px 0 0" }}
          />
        </Box>
        <Box className="category-item-content">
          <Skeleton variant="text" width="80%" height={24} sx={{ mb: 1 }} />
          <Skeleton variant="text" width="100%" height={16} sx={{ mb: 1 }} />
          <Skeleton variant="text" width="100%" height={16} sx={{ mb: 1 }} />
          <Skeleton variant="text" width="100%" height={16} sx={{ mb: 1 }} />
          <Skeleton variant="text" width="100%" height={16} sx={{ mb: 2 }} />
        </Box>
      </Box>
    </Box>
  );
};

export default CategorySkeleton;
