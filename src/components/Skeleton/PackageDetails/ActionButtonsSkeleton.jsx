import React from "react";
import { Box, Skeleton } from "@mui/material";

const ActionButtonsSkeleton = () => {
  return (
    <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
      <Box
        display="flex"
        justifyContent="flex-end"
        width="fit-content"
        ml={{ xs: "auto", md: 0 }}
        mt={{ xs: 2, md: 2.5 }}
        gap={2}
      >
        <Box display="flex" alignItems="center" gap={1}>
          <Skeleton
            variant="rectangular"
            width={24}
            height={24}
            sx={{ borderRadius: 0.5 }}
          />
          <Skeleton variant="text" width={80} height={24} />
        </Box>
        <Box display="flex" alignItems="center" gap={1}>
          <Skeleton
            variant="rectangular"
            width={24}
            height={24}
            sx={{ borderRadius: 0.5 }}
          />
          <Skeleton variant="text" width={60} height={24} />
        </Box>
      </Box>
    </Box>
  );
};

export default ActionButtonsSkeleton;
