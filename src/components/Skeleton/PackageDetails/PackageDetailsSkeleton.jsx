import React from "react";
import { Box, Skeleton } from "@mui/material";

const PackageDetailsSkeleton = () => {
  return (
    <Box
      sx={{
        display: "flex",
        mt: 4,
        width: "100%",
        boxSizing: "border-box",
        gap: 2,
        flexDirection: { xs: "column", md: "row" },
      }}
    >
      <Box sx={{ width: { xs: "100%", md: "calc(100% - 432px)" } }}>
        {/* Description Section */}
        <Box sx={{ mb: 4 }}>
          <Skeleton variant="text" width={150} height={32} sx={{ mb: 2 }} />
          <Skeleton variant="text" width="100%" height={20} sx={{ mb: 1 }} />
          <Skeleton variant="text" width="90%" height={20} sx={{ mb: 1 }} />
          <Skeleton variant="text" width="95%" height={20} sx={{ mb: 1 }} />
          <Skeleton variant="text" width="85%" height={20} />
        </Box>

        {/* Itinerary Section */}
        <Box sx={{ mb: 4 }}>
          <Skeleton variant="text" width={120} height={32} sx={{ mb: 2 }} />
          {[...Array(5)].map((_, index) => (
            <Box key={index} sx={{ mb: 2 }}>
              <Skeleton variant="text" width={100} height={24} sx={{ mb: 1 }} />
              <Skeleton
                variant="text"
                width="100%"
                height={16}
                sx={{ mb: 0.5 }}
              />
              <Skeleton variant="text" width="90%" height={16} />
            </Box>
          ))}
        </Box>

        {/* Activities Section */}
        <Box sx={{ mb: 4 }}>
          <Skeleton variant="text" width={140} height={32} sx={{ mb: 2 }} />
          <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
            {[...Array(4)].map((_, index) => (
              <Box key={index} sx={{ width: 200 }}>
                <Skeleton
                  variant="rectangular"
                  width="100%"
                  height={150}
                  sx={{ borderRadius: 1, mb: 1 }}
                />
                <Skeleton variant="text" width="80%" height={20} />
              </Box>
            ))}
          </Box>
        </Box>

        {/* FAQ Section */}
        <Box>
          <Skeleton variant="text" width={100} height={32} sx={{ mb: 2 }} />
          {[...Array(3)].map((_, index) => (
            <Box key={index} sx={{ mb: 2 }}>
              <Skeleton variant="text" width="70%" height={24} sx={{ mb: 1 }} />
              <Skeleton
                variant="text"
                width="100%"
                height={16}
                sx={{ mb: 0.5 }}
              />
              <Skeleton variant="text" width="90%" height={16} />
            </Box>
          ))}
        </Box>
      </Box>
      <Box sx={{ width: { xs: "100%", md: "416px", position: "relative" } }}>
        <Skeleton
          variant="rectangular"
          width="100%"
          height={388}
          sx={{
            borderRadius: "12px",
            position: "sticky",
            top: "80px",
          }}
        />
      </Box>
    </Box>
  );
};

export default PackageDetailsSkeleton;
