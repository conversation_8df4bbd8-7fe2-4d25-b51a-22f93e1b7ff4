import React from "react";
import { Box } from "@mui/material";
import TitleSkeleton from "./TitleSkeleton";
import ActionButtonsSkeleton from "./ActionButtonsSkeleton";
import GallerySkeleton from "./GallerySkeleton";
import PackageInfoSkeleton from "./PackageInfoSkeleton";
import PackageDetailsSkeleton from "./PackageDetailsSkeleton";

const PackageDetailsMainSkeleton = () => {
  return (
    <Box>
      <Box
        sx={{
          padding: { xs: "24px 20px", md: "24px 80px" },
        }}
      >
        <TitleSkeleton />
        <ActionButtonsSkeleton />
        <GallerySkeleton />
        <PackageInfoSkeleton />
        <PackageDetailsSkeleton />
      </Box>
    </Box>
  );
};

export default PackageDetailsMainSkeleton;
