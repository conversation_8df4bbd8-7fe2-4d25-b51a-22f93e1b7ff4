import React from "react";
import { Box, Skeleton } from "@mui/material";

const GallerySkeleton = () => {
  return (
    <Box
      className="gallery-container"
      sx={{
        display: "grid",
        gridTemplateColumns: "repeat(4, 1fr)",
        gridTemplateRows: "repeat(2, 1fr)",
        gridTemplateAreas: `
          "a a b b"
          "a a c d"
        `,
        gap: { xs: 0.5, md: 1.5 },
        width: "100%",
        aspectRatio: "5 / 2",
        marginTop: { xs: 2, md: 3 },
        borderRadius: 1.5,
        overflow: "hidden",
        position: "relative",
      }}
    >
      {[...Array(4)].map((_, index) => (
        <Box
          key={index}
          sx={{
            gridArea:
              index === 0 ? "a" : index === 1 ? "b" : index === 2 ? "c" : "d",
            overflow: "hidden",
          }}
        >
          <Skeleton
            variant="rectangular"
            width="100%"
            height="100%"
            sx={{ borderRadius: 0 }}
          />
        </Box>
      ))}
      <Box
        sx={{
          position: "absolute",
          bottom: { xs: "10px", md: "20px" },
          right: { xs: "10px", md: "20px" },
        }}
      >
        <Skeleton
          variant="rectangular"
          width={80}
          height={40}
          sx={{ borderRadius: 1.5 }}
        />
      </Box>
    </Box>
  );
};

export default GallerySkeleton;
