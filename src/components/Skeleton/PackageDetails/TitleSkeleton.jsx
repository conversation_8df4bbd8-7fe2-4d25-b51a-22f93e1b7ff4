import React from "react";
import { Box, Skeleton } from "@mui/material";

const TitleSkeleton = () => {
  return (
    <Box
      className="package-title-container"
      sx={{
        display: "flex",
        flexDirection: { xs: "column", md: "row" },
        alignItems: "flex-start",
        gap: { xs: 1, md: 2 },
        justifyContent: "space-between",
      }}
    >
      <Box
        sx={{
          flex: 1,
          width: { xs: "100%", md: "calc(100% - 120px - 120px)" },
        }}
      >
        <Skeleton variant="text" width="80%" height={52} sx={{ mb: 1 }} />
      </Box>
      <Box>
        <Skeleton variant="text" width={100} height={24} sx={{ mb: 1 }} />
        <Box sx={{ display: "flex", gap: 0.5 }}>
          {[...Array(5)].map((_, index) => (
            <Skeleton key={index} variant="circular" width={20} height={20} />
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default TitleSkeleton;
