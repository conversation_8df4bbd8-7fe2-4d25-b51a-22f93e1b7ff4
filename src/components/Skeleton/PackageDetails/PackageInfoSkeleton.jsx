import React from "react";
import { Box, Skeleton } from "@mui/material";

const PackageInfoSkeleton = () => {
  return (
    <Box
      display="flex"
      width="100%"
      flexDirection={{ xs: "column", md: "row" }}
      justifyContent="space-between"
      alignItems="center"
      columnGap={2}
    >
      <Box
        sx={{
          width: { xs: "100%" },
          boxSizing: "border-box",
          pt: { xs: 3, md: 4 },
          pr: 2.5,
          overflow: "hidden",
          position: "relative",
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            gap: "16px",
          }}
        >
          {[...Array(6)].map((_, index) => (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: "8px",
                flex: "0 0 auto",
                width: "fit-content",
              }}
              key={index}
            >
              <Skeleton
                variant="rectangular"
                width={46}
                height={46}
                sx={{ borderRadius: 1 }}
              />
              <Box sx={{ display: "flex", flexDirection: "column" }}>
                <Skeleton
                  variant="text"
                  width={60}
                  height={16}
                  sx={{ mb: 0.5 }}
                />
                <Skeleton variant="text" width={100} height={20} />
              </Box>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default PackageInfoSkeleton;
