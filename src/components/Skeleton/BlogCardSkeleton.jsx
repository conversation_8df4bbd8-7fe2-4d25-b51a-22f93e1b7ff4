import React from "react";
import { Box, Skeleton } from "@mui/material";

const BlogCardSkeleton = () => {
  return (
    <Box
      sx={{
        backgroundColor: "transparent",
        borderRadius: "12px",
        overflow: "hidden",
        boxSizing: "border-box",
        width: "100%",
        height: "100%",
      }}
    >
      {/* Top Image Skeleton */}
      <Box
        sx={{
          position: "relative",
          width: "100%",
          height: "300px",
          overflow: "hidden",
          borderRadius: "12px",
        }}
      >
        <Skeleton
          variant="rectangular"
          width="100%"
          height="100%"
          sx={{ borderRadius: "12px" }}
        />

        {/* Category Badge Skeleton */}
        <Box
          sx={{
            position: "absolute",
            top: "20px",
            left: "20px",
          }}
        >
          <Skeleton
            variant="rectangular"
            width={80}
            height={26}
            sx={{ borderRadius: "18px" }}
          />
        </Box>
      </Box>

      <Box sx={{ pt: "16px" }}>
        {/* Timestamp and Author Skeleton */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: "10px",
            marginBottom: "8px",
          }}
        >
          <Skeleton variant="text" width={100} height={20} />

          {/* Separation Line Skeleton */}
          <Skeleton
            variant="rectangular"
            width={1}
            height={26}
            sx={{ borderRadius: "50%" }}
          />

          <Skeleton variant="text" width={80} height={20} />
        </Box>

        {/* Title Skeleton */}
        <Box sx={{ display: "flex", flexDirection: "column", gap: 0.5 }}>
          <Skeleton variant="text" width="100%" height={27} />
          <Skeleton variant="text" width="80%" height={27} />
        </Box>
      </Box>
    </Box>
  );
};

export default BlogCardSkeleton;
