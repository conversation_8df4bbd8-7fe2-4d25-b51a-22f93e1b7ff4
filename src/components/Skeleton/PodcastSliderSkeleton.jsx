import React from "react";
import { Box, Skeleton } from "@mui/material";

const PodcastSliderSkeleton = () => {
  return (
    <Box sx={{ width: "100%" }}>
      {/* Swiper Container */}
      <Box
        sx={{
          display: "flex",
          gap: 3,
          overflow: "hidden",
          paddingBottom: "16px",
        }}
      >
        {/* Generate 5 skeleton cards to match the max slidesPerView */}
        {[...Array(5)].map((_, index) => (
          <Box
            key={index}
            sx={{
              minWidth: { xs: "260px", sm: "280px", md: "300px" },
              height: "354px",
              borderRadius: "24px 24px 0px 0px",
              overflow: "hidden",
              background: "#FFF9F9",
              boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.05)",
              flexShrink: 0,
              boxSizing: "border-box",
            }}
          >
            {/* Episode Image Skeleton */}
            <Skeleton
              variant="rectangular"
              width="100%"
              height="252px"
              sx={{ borderRadius: 0, boxSizing: "border-box" }}
            />

            {/* Episode Content Skeleton */}
            <Box
              sx={{
                padding: "16px",
                height: "102px",
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                boxSizing: "border-box",
              }}
            >
              {/* Episode Type Skeleton */}
              <Skeleton
                variant="text"
                width="60px"
                height={16}
                sx={{
                  borderRadius: "4px",
                  backgroundColor: "#FF3951",
                  opacity: 0.3,
                  boxSizing: "border-box",
                }}
              />

              {/* Play Icon Wrapper */}
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  boxSizing: "border-box",
                }}
              >
                {/* Episode Title Skeleton */}
                <Skeleton
                  variant="text"
                  width="80%"
                  height={18}
                  sx={{
                    borderRadius: "4px",
                    backgroundColor: "#1E3A8A",
                    opacity: 0.3,
                    boxSizing: "border-box",
                  }}
                />

                {/* Play Icon Skeleton */}
                <Skeleton
                  variant="circular"
                  width={32}
                  height={32}
                  sx={{
                    backgroundColor: "#FF3951",
                    opacity: 0.3,
                    boxSizing: "border-box",
                  }}
                />
              </Box>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default PodcastSliderSkeleton;
