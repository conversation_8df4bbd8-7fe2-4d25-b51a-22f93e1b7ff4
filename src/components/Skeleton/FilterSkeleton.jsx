import React from "react";
import { Box, Skeleton } from "@mui/material";

const FilterSkeleton = () => {
  return (
    <Box
      sx={{
        display: "flex",
      }}
    >
      <Box
        sx={{
          fontFamily: "Roboto",
          fontSize: "20px",
          fontWeight: "500",
          lineHeight: "32px",
          color: "#1E3A8A",
          width: {
            xs: "100%",
            md: "100px",
          },
        }}
      >
        Filters
      </Box>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: {
            xs: "flex-start",
            md: "flex-end",
          },
          gap: 1.5,
          width: {
            xs: "100%",
            md: "calc(100% - 100px)",
          },
          flexWrap: "wrap",
        }}
      >
        {[...Array(10)].map((_, index) => (
          <Box
            key={index}
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              minWidth: 120,
            }}
          >
            <Skeleton
              variant="rectangular"
              width={126}
              height={36}
              sx={{ borderRadius: 0.5 }}
            />
            {/* <Skeleton variant="text" width={80} height={20} /> */}
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default FilterSkeleton;
