import React from "react";
import { Box, Skeleton } from "@mui/material";

const DestinationListSkeleton = () => {
  return (
    <Box className="destionations-list-item">
      <Box className="destionations-list-item-img">
        <Skeleton
          variant="rectangular"
          width="100%"
          height="250px"
          sx={{ borderRadius: "8px 8px 0 0" }}
        />
        <Box className="destionations-list-item-img-text">
          <Skeleton
            variant="text"
            width="190px"
            height={16}
            sx={{ color: "white" }}
          />
        </Box>
      </Box>
      <Box className="destionations-list-item-text">
        <Box>
          <Skeleton variant="text" width="70%" height={24} sx={{ mb: 1 }} />
          <Skeleton variant="text" width="100%" height={16} sx={{ mb: 1 }} />
          <Skeleton variant="text" width="90%" height={16} sx={{ mb: 2 }} />
        </Box>
        <Box className="destionations-list-item-text-price-container">
          <Box className="destionations-list-item-text-price">
            <Skeleton variant="text" width="100px" height={16} sx={{ mb: 1 }} />
            <Skeleton variant="text" width="120px" height={20} />
          </Box>
          <Box>
            <Skeleton
              variant="rectangular"
              width={"140px"}
              height={"44px"}
              sx={{ borderRadius: "4px" }}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default DestinationListSkeleton;
