import React from "react";
import { Box, Skeleton } from "@mui/material";

const PackageSkeleton = () => {
  return (
    <Box className="package-list-container-item">
      <Box className="package-list-container-item-img">
        <Skeleton
          variant="rectangular"
          width="100%"
          height="100%"
          sx={{ borderRadius: "8px 8px 0 0" }}
        />
        <Box
          style={{
            width: "94px",
            height: "33px",
            background: "#F5F5F5",
            position: "absolute",
            top: "10px",
            left: "10px",
            borderRadius: "4px",
          }}
        />
      </Box>
      <Box className="package-list-container-item-text">
        <Box className="package-list-container-item-text-title-container">
          <Box>
            <Skeleton variant="text" width="100%" height={24} />
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1,
              }}
            >
              <Skeleton variant="text" width="40%" height={24} />
              {[1, 2, 3, 4, 5].map((item, index) => (
                <Skeleton
                  key={index}
                  variant="text"
                  width="16px"
                  height="24px"
                  sx={{ borderRadius: "4px" }}
                />
              ))}
            </Box>
          </Box>

          <Box className="package-divider" />
          <Skeleton variant="text" width="100px" height={16} sx={{ mb: 1 }} />
          <Box className="package-list-container-item-text-list">
            {[...Array(5)].map((_, index) => (
              <Box
                className="package-list-container-item-text-list-item"
                key={index}
                style={{ alignItems: "center" }}
              >
                <Skeleton
                  variant="circular"
                  width={8}
                  height={8}
                  sx={{ mr: 1 }}
                />
                <Skeleton variant="text" width="90%" height={16} />
              </Box>
            ))}
          </Box>
        </Box>
        <Box
          sx={{
            display: "flex",
            width: "100%",
            alignItems: "center",
            justifyContent: "space-between",
            boxSizing: "border-box",
            padding: "16px 27px",
            gap: 2,
          }}
        >
          <Box
            className="package-price"
            sx={{
              boxSizing: "border-box",
              alignItems: "center",
              justifyContent: "space-between",
              gap: "16px",
            }}
          >
            <Box>
              <Skeleton
                variant="rectangular"
                width={80}
                height={12}
                sx={{ borderRadius: "4px" }}
              />
            </Box>
            <Skeleton variant="text" width="100px" height={24} />
          </Box>
          <Skeleton
            variant="rectangular"
            width={184}
            height={40}
            sx={{ borderRadius: "4px" }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default PackageSkeleton;
