import React from "react";
import { Box } from "@mui/material";
import BlogCardSkeleton from "./BlogCardSkeleton";

const BlogGridSkeleton = ({ count = 8 }) => {
  return (
    <Box
      sx={{
        display: "grid",
        gridTemplateColumns: {
          xs: "1fr",
          sm: "repeat(2, 1fr)",
          md: "repeat(3, 1fr)",
          lg: "repeat(4, 1fr)",
        },
        rowGap: {
          xs: 3,
          md: 5,
        },
        columnGap: {
          xs: 3,
          md: 3,
        },
        width: "100%",
      }}
    >
      {[...Array(count)].map((_, index) => (
        <Box key={index}>
          <BlogCardSkeleton />
        </Box>
      ))}
    </Box>
  );
};

export default BlogGridSkeleton;
