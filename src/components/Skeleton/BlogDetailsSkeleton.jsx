import React from "react";
import { Box, Skeleton } from "@mui/material";

const BlogDetailsSkeleton = () => {
  return (
    <Box>
      {/* Header/Breadcrumb Skeleton */}
      <Box sx={{ pt: { xs: "80px", md: "116px" } }}>
        <Box
          sx={{
            backgroundColor: "#1E3A8A",
            padding: { xs: "8px 20px", md: "13px 80px" },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Skeleton variant="text" width={60} height={20} />
            <Skeleton variant="text" width={10} height={20} />
            <Skeleton variant="text" width={100} height={20} />
            <Skeleton variant="text" width={10} height={20} />
            <Skeleton variant="text" width={120} height={20} />
          </Box>
        </Box>
      </Box>

      {/* Hero Image Section */}
      <Box
        sx={{
          width: "100%",
          height: { xs: "300px", md: "600px" },
          position: "relative",
        }}
      >
        <Skeleton
          variant="rectangular"
          width="100%"
          height="100%"
          sx={{ borderRadius: 0 }}
        />

        {/* Title Overlay Skeleton */}
        <Box
          sx={{
            position: "absolute",
            bottom: 0,
            left: 0,
            width: "100%",
            height: "fit-content",
            background: "#0000001A",
            backdropFilter: "blur(10px)",
            p: {
              xs: "12px 16px",
              md: "28px 80px",
            },
            boxSizing: "border-box",
          }}
        >
          <Skeleton
            variant="text"
            width="80%"
            height={{ xs: 24, md: 32 }}
            sx={{ mb: 1.25, color: "white" }}
          />
          <Skeleton
            variant="text"
            width="40%"
            height={{ xs: 16, md: 20 }}
            sx={{ color: "white" }}
          />
        </Box>
      </Box>

      {/* Content Section Skeleton */}
      <Box
        sx={{
          width: "100%",
          height: "100%",
          px: { xs: "20px", md: "80px" },
          py: { xs: "30px", md: "60px" },
          boxSizing: "border-box",
        }}
      >
        {/* Content paragraphs */}
        {[...Array(8)].map((_, index) => (
          <Skeleton
            key={index}
            variant="text"
            width={index % 2 === 0 ? "100%" : "95%"}
            height={20}
            sx={{ mb: 1.5 }}
          />
        ))}
      </Box>

      {/* Author Section Skeleton */}
      <Box
        sx={{
          width: "100%",
          height: "100%",
          px: { xs: "20px", md: "80px" },
          boxSizing: "border-box",
          pb: { xs: "20px", md: "40px" },
        }}
      >
        <Box
          sx={{
            width: "100%",
            height: "100%",
            display: "flex",
            boxSizing: "border-box",
            borderTop: "1px solid #FF39510D",
            borderBottom: "1px solid #FF39510D",
            py: { xs: "16px", md: "20px" },
            gap: { xs: "8px", md: "30px" },
          }}
        >
          <Skeleton
            variant="circular"
            width={{ xs: "40px", md: "70px" }}
            height={{ xs: "40px", md: "70px" }}
          />
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: { xs: "8px" },
              flex: 1,
            }}
          >
            <Skeleton variant="text" width="60%" height={{ xs: 16, md: 20 }} />
            <Skeleton variant="text" width="100%" height={{ xs: 16, md: 20 }} />
            <Skeleton variant="text" width="80%" height={{ xs: 16, md: 20 }} />
          </Box>
        </Box>
      </Box>

      {/* Related Posts Section Skeleton */}
      <Box
        sx={{
          width: "100%",
          height: "100%",
          background: "linear-gradient(99.31deg, #FFFFFF 0.33%, #FEF7F4 100%)",
          pb: { xs: "40px", md: "40px" },
          px: { xs: "20px", md: "80px" },
          boxSizing: "border-box",
        }}
      >
        <Skeleton
          variant="text"
          width="200px"
          height={{ xs: 32, md: 40 }}
          sx={{
            mb: { xs: "20px", md: "40px" },
            textAlign: "center",
            mx: "auto",
            display: "block",
          }}
        />

        {/* Related posts grid skeleton */}
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: {
              xs: "1fr",
              sm: "repeat(2, 1fr)",
              md: "repeat(3, 1fr)",
            },
            gap: 3,
            mb: 3,
          }}
        >
          {[...Array(3)].map((_, index) => (
            <Box key={index}>
              <Skeleton
                variant="rectangular"
                width="100%"
                height={200}
                sx={{ borderRadius: "12px", mb: 2 }}
              />
              <Skeleton variant="text" width="80%" height={20} sx={{ mb: 1 }} />
              <Skeleton variant="text" width="60%" height={16} sx={{ mb: 1 }} />
              <Skeleton variant="text" width="40%" height={16} />
            </Box>
          ))}
        </Box>

        {/* Navigation buttons skeleton */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            gap: 2,
          }}
        >
          <Skeleton
            variant="rectangular"
            width={34}
            height={34}
            sx={{ borderRadius: "50%" }}
          />
          <Skeleton
            variant="rectangular"
            width={34}
            height={34}
            sx={{ borderRadius: "50%" }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default BlogDetailsSkeleton;
