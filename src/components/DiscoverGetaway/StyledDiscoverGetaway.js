import { styled } from "@mui/material/styles";
import discoverGetawayBg from '../../assets/png/PerfectGatewayBg.png';

const StyledDiscoverGetaway = styled("div")(
  ({ theme }) => `
  padding: 20px 0;
  background-image: url(${discoverGetawayBg});
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  overflow: hidden;

  .content-wrapper {
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 24px;
    text-align: center;
  }

  .title {
    font-family: 'Lora', serif;
    font-size: 32px;
    font-weight: 700;
    color: ${theme.palette.secondary.main};
    margin-bottom: 16px;
    line-height: 45px;
  }

  .description {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    color: ${theme.palette.custom.subText};
    max-width: 700px;
    margin: 0 auto;
    line-height: 150%;
    letter-spacing: 0.05em;
  }

    .navigation-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    z-index: 10;
  }

  .nav-button {
    width: 34px;
    height: 34px;
    border-radius: 50%;
    border: 1px solid #E0E0E0;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0px 0px 4px 0px #0000001F;

    svg {
      font-size: 23px;
      color: ${theme.palette.custom.lightred};
    }
  }

  .explore-btn {
    margin-top: 25px;
    background: ${theme.palette.custom.lightred};
    color: white;
    padding: 8px 32px;
    border-radius: 4px;
    text-transform: none;
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0.05em;
    text-transform: capitalize;
    margin-bottom: 60px;
    display: inline-flex;
    align-items: center;
    width: fit-content;
    transition: all 0.3s ease;
    box-shadow: none;

    .arrow-icon {
      font-size: 20px;
      margin-left: -4px;
      opacity: 0;
      transform: translateX(-8px);
      transition: all 0.3s ease;
      width: 0;
      overflow: hidden;
    }

    &:hover {
      background: ${theme.palette.custom.lightred};
      padding-right: 28px;
      width: auto;
      box-shadow: none;

      .arrow-icon {
        opacity: 1;
        transform: translateX(0);
        margin-left: 4px;
        width: 20px;
      }
    }
  }

`
);

export default StyledDiscoverGetaway;
