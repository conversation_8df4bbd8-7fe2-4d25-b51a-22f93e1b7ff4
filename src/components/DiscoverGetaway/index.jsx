/* eslint-disable react-hooks/exhaustive-deps */
import React from "react";
import { Box, Typography, Button } from "@mui/material";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/effect-coverflow";
import StyledDiscoverGetaway from "./StyledDiscoverGetaway";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
// import PanoramaSlider from "../PanoramaSlider";
import { useNavigate } from "react-router-dom";
import ROUTE_PATH from "../../constants/route";
import PackageSlider from "../PackageSlider";
import { DiscoverBackground } from "../common/SvgIcon";

const DiscoverGetaway = () => {
  const navigate = useNavigate();
  return (
    <StyledDiscoverGetaway>
      <Box className="content-wrapper" sx={{ position: "relative" }}>
        <DiscoverBackground
          sx={{
            width: "100%",
            height: "fit-content",
            position: "absolute",
            top: "0px",
            left: "20px",
          }}
        />
        <Typography className="title">Discover Your Perfect Getaway</Typography>
        <Typography className="description">
          Escape the everyday grind with our curated holiday packages designed
          for every kind of traveler. Whether you're dreaming of sun-soaked
          beaches, mountain retreats, cultural explorations, or luxury stays,
          we've got the perfect itinerary for you.
        </Typography>
        <Button
          variant="contained"
          className="explore-btn"
          onClick={() => navigate(ROUTE_PATH.EXPLORE)}
        >
          Explore All
          <ArrowForwardIcon className="arrow-icon" />
        </Button>

        {/* <Box><PanoramaSlider /></Box> */}
        <Box>
          <PackageSlider />
        </Box>
      </Box>
    </StyledDiscoverGetaway>
  );
};

export default DiscoverGetaway;
