import { styled } from "@mui/material/styles";
import optimizedTravelBg from '../../assets/svg/optimizedTravelBg.svg';

const StyledOptimizedTravel = styled("div")(
  ({ theme }) => `
  padding: 60px 0;
  width: 100%;
  background-image: url(${optimizedTravelBg});
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  overflow: hidden;
  position: relative;

  @media (max-width: 1200px) {
    margin-bottom: 80px;
  }


  .content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    text-align: center;
    position: relative;
    z-index: 1;
  }

  .title {
    font-family: '<PERSON>ra', serif;
    font-size: 28px;
    font-weight: 700;
    color: ${theme.palette.secondary.main};
    margin-bottom: 8px;
    line-height: 40px;

    .highlight {
      background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .description {
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: ${theme.palette.custom.subText};
    max-width: 700px;
    margin: 0 auto;
    line-height: 150%;
    letter-spacing: 0.05em;
  }

  .main-content {
    margin-top: 40px;
  }

  .features-left, .features-right {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1;
  }

  .feature-tag {
    position: absolute;
    background: white;
    padding: 12px 24px;
    border-radius: 30px;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.05);
    width: fit-content;
    cursor: pointer;
    transition: all 0.3s ease;
    // border: 1px solid ${theme.palette.custom.lightred};
    display: flex;
    align-items: center;
    gap: 5px;


    p {
      font-family: 'Roboto', sans-serif;
      font-size: 11px;
      font-weight: 400;
      color: ${theme.palette.custom.lightBlack};
      line-height: 100%;
      letter-spacing: -0.02em;
    }
  }

  .features-left {
    position: absolute;
    left: 200px;
    top: 47%;
    transform: translateY(-50%);
    width: 300px;
    height: 100%;

    .feature-tag {
      border: none;
      position: relative;
      
      &:before {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        border-radius: 30px;
        background: linear-gradient(90deg, #FF3951 0%, #5530F9 125.89%);
        z-index: -1;
      }
    }

    .feature-tag {
      position: absolute;

      &:nth-of-type(1) {
        right: -55px;
        top: 20px;
      }

      &:nth-of-type(2) {
        left: -100px;
        top: 30%;
      }

      &:nth-of-type(3) {
        right: 0px;
        top: 47%;
      }

      &:nth-of-type(4) {
        left: 0px;
        top: 80%;
      }
    }
  }

  .features-right {
    position: absolute;
    top: 50%;
    left: 49%;
    transform: translateY(-50%);
    width: 300px;
    height: 100%;

    .feature-tag {
      position: absolute;

      &:before {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        border-radius: 30px;
        background: linear-gradient(90deg, #FF3951 0%, #5530F9 125.89%);
        z-index: -1;
      }

      &:nth-of-type(1) {
        right: 19px;
        top: 122px;
      }

      &:nth-of-type(2) {
        right: -170px;
        top: 65%;
      }

      &:nth-of-type(3) {
        right: 80px;
        top: 83%;
      }
    }
  }

  .robot-container {
    position: relative;
    flex: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 40px;

    img {
      width: 180px;
      height: auto;
    }

    .chat-bubble {
      position: absolute;
      top: -15px;
      right: 200px;
      background: white;
      padding: 16px;
      border-radius: 12px;
      box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.05);
      max-width: 300px;
      text-align: left;

      &:before {
        content: '';
        position: absolute;
        bottom: -9px;
        left: 17px;
        transform: translateX(-50%);
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-top: 10px solid white;
      }

      p {
        font-family: 'Roboto', sans-serif;
        font-weight: 400;
        font-size: 11px;
        color: ${theme.palette.custom.subText};
        line-height: 150%;
        letter-spacing: 0.05em;

        span {
          color: ${theme.palette.custom.lightred};
          font-weight: 600;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .robot-image {
    position: relative;
    z-index: 1;
    margin-top: 35px;
  }

  @media (max-width: 1024px) {
     .chat-bubble {
      padding: 14px !important;
      right: 95px !important;
     }
      .features-left {
        left: 99px !important;
      }
  }
  
  @media (max-width: 768px) {
     .description {
      font-size: 14px;
      margin-bottom: 32px;
    }
    .chat-bubble {
      padding: 9px !important;
      right: -20px !important;
    }
    .features-left {
    left: 122px;
    top: 111%;
    .feature-tag {
     &:nth-of-type(1) {
       right: 112px;
        top: -128px;
      }

      &:nth-of-type(2) {
        top: -30%;
        left: -99px;
      }

      &:nth-of-type(3) {
        right: 150px;
        top: -5%;
        min-width: 100px;
      }

      &:nth-of-type(4) {
        left: -100px;
        top: 22%;
      }
    }
   }
    .features-right {
      top: 80%;
    
    .feature-tag {

      &:nth-of-type(1) {
        right: 37px;
        top: 65px;
      }

      &:nth-of-type(2) {
        right: -56px;
        top: 50%;
      }

      &:nth-of-type(3) {
        right: 109px;
        top: 70%;
      }
    }  
  }

  @media (max-width: 600px) {
  .main-content {
    margin-left: 25px !important;
  }
   .content-wrapper {
     text-align: left;
   }
   .title {
     font-size: 24px;
     line-height: 35px;
   }  
      .robot-container {
    width: fit-content;
    position: relative;
    height: 350px;

    .robot-image {
     position: absolute;
     left: -19px;
     top: 34px;
     width: 160px;
    } 
  }
  .chat-bubble {
    top: -15px !important;
    right: -254px !important;
    max-width: 200px !important;
  }  
  .feature-tag {
    padding: 12px 12px !important;
  }  
  .features-left {
    left: 122px;
    top: 111%;
    .feature-tag {
     &:nth-of-type(1) {
        right: 158px;
        top: -16px;
      }

      &:nth-of-type(2) {
        top: 10%;
        left: -138px;
      }

      &:nth-of-type(3) {
        right: 184px;
        top: 23%;
        min-width: 100px;
      }

      &:nth-of-type(4) {
        left: -135px;
        top: 34%;
      }
    }
   }
    .features-right {
      top: 80%;
    
    .feature-tag {

      &:nth-of-type(1) {
        right: 19px;
        top: 145px
      }

      &:nth-of-type(2) {
        right: 21px;
        top: 64%;
      }

      &:nth-of-type(3) {
        right: 125px;
        top: 76%;
      }
    }  
  }
`
); 

export default StyledOptimizedTravel;
