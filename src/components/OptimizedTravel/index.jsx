import React from "react";
import { Box, Typography } from "@mui/material";
import StyledOptimizedTravel from "./StyledOptimizedTravel";
import aiRobot from "../../assets/svg/aiRobot.svg";
import multipleStartIcon from "../../assets/svg/multipleStarIcon.svg";

const OptimizedTravel = () => {
  const features = [
    "Hassle-free Booking",
    "Effortless Travel Planning",
    "Chat & Go Travel",
    "Your Travel Buddy",
    "On-the-go Assistant",
    "Plan in Seconds",
    "Travel Simplified",
  ];

  return (
    <StyledOptimizedTravel>
      <Box className='content-wrapper'>
        <Typography className='title'>
          Optimized Travel Solutions,{" "}
          <span className='highlight'>Powered by AI.</span>
        </Typography>
        <Typography className='description'>
          Ready to make your travel experience smooth and stress-free? With my
          advanced capabilities, we can help you plan the perfect trip, from
          booking flights and accommodations to finding the best local
          attractions.
        </Typography>

        <Box className='main-content'>
          <Box className='robot-container'>
            <img src={aiRobot} alt='AI Travel Assistant' className='robot-image' />
            <Box className='chat-bubble'>
              <Typography>Hi there! 👋</Typography>
              <Typography>
                I'm <span>Zippy</span>, your travel assistant. I'm here to make your journey smooth, stress-free, and full of adventure. Let's go!
              </Typography>
            </Box>
            <Box className='features-left'>
              {features.slice(0, 4).map((feature, index) => (
                <Box key={index} className='feature-tag'>
                  <img src={multipleStartIcon} alt={feature.title} style={{width: "10px", height: "13px"}} />
                  <Typography className="feature-text">{feature}</Typography>
                </Box>
              ))}
            </Box>
            <Box className='features-right'>
              {features.slice(4).map((feature, index) => (
                <Box key={index} className='feature-tag'>
                  <img src={multipleStartIcon} alt={feature.title} style={{width: "10px", height: "13px"}} />
                  <Typography>{feature}</Typography>
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>
    </StyledOptimizedTravel>
  );
};

export default OptimizedTravel;
