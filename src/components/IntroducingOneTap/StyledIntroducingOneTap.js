import { styled } from "@mui/material/styles";
import OneTapBg from '../../assets/svg/OneTagBg.svg';

const StyledIntroducingOneTap = styled("div")(
  ({ theme }) => `
  margin-top: 40px;
  width: 100%;
  height: 588px;
  overflow: hidden;

  .content-wrapper {
    margin: 0 auto;
    // padding: 45px 24px 0;
    text-align: center;
    z-index: 1;
    height: 100%;
    position: relative;
  }

  .background-wrapper {
    width: 100%;
    height: 100%;
    background-image: url(${OneTapBg});
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    position: relative;
  }

  .pre-title {
    font-family: 'Lora', serif;
    font-size: 40px;
    font-weight: 500;
    line-height: 56px;
    color: #000000;
    padding-top: 40px;
  }

  .main-title {
    font-family: '<PERSON>ra', serif;
    font-size: 40px;
    font-weight: 600;
    color: ${theme.palette.custom.lightred};
    line-height: 56px;

    sup {
      font-family: inherit;
      font-size: 16px !important;
      position: relative;
      top: -10px;
      color: ${theme.palette.secondary.main};
      font-weight: inherit;
    }
  }

  .description {
    max-width: 580px;
    font-family: 'Roboto', sans-serif;
    font-size: 20px;
    font-weight: 400;
    color: ${theme.palette.custom.subText};
    margin: 0 auto 38px;
    line-height: 28px;
  }

  .mockups-container {
    padding-top: 20px;
    display: flex;
    justify-content: center;
    align-items: baseline;
    gap: 60px;
    margin-top: 40px;
    height: 200px;
    position: absolute;
    // top: 38%;
    bottom: 0px;
    right: 16%;
    z-index: 2;
    overflow: hidden;
  }

.phone-mockup {
  position: relative;
  width: 280px;
  transition: all 0.3s ease;
  z-index: 2;

  .all-phone-icon {
    position: relative;
    z-index: 2;
    width: 100%;
    height: auto;
    border-radius: 24px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  &.left {
    transform: translateX(40px) scale(0.85);
    opacity: 0.8;
  }

  &.center {
    z-index: 3;
    transform: scale(1.1);
  }

  &.right {
    transform: translateX(-40px) scale(0.85);
    opacity: 0.8;
  }
}

.left-rectangle {
  position: absolute;
  top: -3px;
  left: -3px;
  width: 100px;
  height: 300px;
  background-color: ${theme.palette.custom.lightred};
  border-radius: 24px 0 0 0;
  z-index: 1;
}

.center-rectangle {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 300px;
  background: linear-gradient(90deg, #FF3951 45.45%, #5530F9 145.45%);
  border-radius: 24px 24px;
  z-index: 1;
}

.right-rectangle {
  position: absolute;
  top: -3px;
  right: -3px;
  width: 100px;
  height: 300px;
  background-color: ${theme.palette.custom.lightred};
  border-radius: 0 24px 0 0;
  z-index: 1;
}

.phone-mockup-icon2 {
  position: absolute;
  top: 0;
  left: -16px;
  transform: translate(-50%, -50%);
  z-index: 2;
}
.phone-mockup-icon3 {
  position: absolute;
  top: -28px;
  right: -46px;
  z-index: 2;
}

@media (max-width: 1024px) {
  height: 266px;
  .mockups-container {
    display: none;
  }
  .content-wrapper {
    height: 40vh;
  }
  .info-card {
    height: 322px;
  }  
  .button-wrapper {
    left: 30%;
    bottom: 22px;
  }  
}



  @media (max-width: 768px) {
    background-size: 180% auto;
    height: 200px;
    
    
    .content-wrapper {
      height: 40vh;
      padding: 0 !important;
    }

    .pre-title {
      font-size: 24px;
      line-height: 30px;
      font-weight: 500;
    }

    .main-title {
      margin-top: 5px;
      font-size: 24px;
      line-height: 30px;
      font-weight: 500;
    }

    .description {
      margin-top: 10px;
      font-size: 16px;
      line-height: 22px;
      max-width: 450px;
    }

    .mockups-container {
      display: none;
    }
      
    .subscribe-button-wrapper {
      width: 100%;
      align-items: center;
      justify-content: center;
      display: flex;
    }

    .subscribe-button {
      width: 50% !important;
    }
  }

  @media (max-width: 600px) {
    .content-wrapper {
      height: 33vh;
      padding: 45px 24px 0;
    }
  }

  @media (max-width: 450px) {
    height: 144px;
    margin-top: 20px;
    .content-wrapper {
      height: 30vh;
      padding: 45px 24px 0;
    }
    .pre-title {
      font-size: 15px;
      line-height: 26px;
      padding-top: 20px;
    }  
    .main-title {
      font-size: 15px;
      line-height: 14px;
      padding-top: 0px;
      margin-top: 0px;
      sup {
        font-size: 10px;
        line-height: 10px;
        top: -5px;
      }
    }  
    .description {
      font-size: 12px;
      line-height: 18px;
      margin-top: 5px;
      max-width: 230px;
      margin-top: 20px;
    }

  }
`
);

export default StyledIntroducingOneTap; 