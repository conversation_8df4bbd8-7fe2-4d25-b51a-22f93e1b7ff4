import React from 'react';
import { Box, Typography } from '@mui/material';
// import StyledIntroducingOneTap from './StyledIntroducingOneTap';
// import leftBg from '../../assets/svg/OneTapLeftBg.svg';
// import centerBg from '../../assets/svg/OneTapCenterBg.svg';
// import phoneMockupIcon from '../../assets/svg/phoneMockupIcon.svg';
// import phoneMockupIcon2 from '../../assets/svg/phoneMockupIcon2.svg';
import OneTapBg from "../../assets/svg/OneTagBg.svg";

const IntroducingOneTap = () => {
  return (
    <Box
      sx={{
        mt: 2.25,
        display: "flex",
        flexDirection: "column",
        justifyContent: { xs: "flex-end", md: "flex-end" },
        backgroundRepeat: "no-repeat",
        backgroundPosition: "bottom center",
        backgroundSize: "100% 100%",
        position: "relative",
        backgroundImage: `url(${OneTapBg})`,
        width: "100%",
        minHeight: { xs: "288px", md: "588px" },
        height: "100%",
        boxSizing: "border-box",
      }}
    >
      <Typography
        sx={{
          fontSize: { xs: "24px", md: "40px" },
          fontWeight: "500",
          color: "#000000",
          lineHeight: { xs: "32px", md: "56px" },
          textAlign: "center",
          fontFamily: "Lora",
          pt: 7.5,
        }}
      >
        Introducing
      </Typography>
      <Typography
        sx={{
          fontSize: { xs: "24px", md: "40px" },
          fontWeight: "600",
          color: "#FF3951",
          lineHeight: { xs: "32px", md: "56px" },
          fontFamily: "Lora",
          textAlign: "center",
        }}
      >
        ZUUMM One-TAP<sup style={{ color: "#1E3A8A" }}>®</sup>
      </Typography>
      <Typography
        sx={{
          fontSize: { xs: "16px", md: "20px" },
          fontWeight: "400",
          color: "#333333",
          lineHeight: { xs: "24px", md: "28px" },
          fontFamily: "Roboto",
          textAlign: "center",
          maxWidth: "700px",
          pb: { xs: 4, md: 0 },
          px: { xs: 2, md: 0 },
          mx: "auto",
          mt: 1.5,
        }}
      >
        Our Infrastructure-as-a-Service (IaaS) platform that gives you an
        instant plug-and-play travel business, powered by AI.
      </Typography>
      <Box
        sx={{
          width: "100%",
          height: "fit-content",
          boxSizing: "border-box",
          position: "relative",
          display: { xs: "none", md: "flex" },
          justifyContent: "center",
          alignItems: "flex-end",
          pt: 4.5,

          // mt: "auto",
        }}
      >
        <Box
          component={"img"}
          src={"/static/partner/laptopPartner.png"}
          sx={{
            mx: "auto",
            zIndex: 1,
            width: { xs: "300px", lg: "486px" },
            aspectRatio: "486 / 320",
          }}
        />
        <Box
          component={"img"}
          src={"/static/partner/mobilePartner.png"}
          sx={{
            position: "absolute",
            zIndex: 2,
            bottom: 0,
            right: { xs: "calc(50% + 100px)", lg: "calc(50% + 178px)" },
            width: { xs: "100px", lg: "150px" },
            aspectRatio: "150 / 277",
          }}
        />
        <Box
          component={"img"}
          src={"/static/partner/tabletPartner.png"}
          sx={{
            position: "absolute",
            zIndex: 2,
            bottom: "15px",
            left: { xs: "calc(50% + 88px)", lg: "calc(50% + 138px)" },
            width: { xs: "250px", lg: "394px" },
            aspectRatio: "394 / 285",
          }}
        />
      </Box>
    </Box>
  );
};

export default IntroducingOneTap; 