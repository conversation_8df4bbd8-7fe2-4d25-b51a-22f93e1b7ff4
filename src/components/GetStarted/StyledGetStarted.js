import { styled } from "@mui/material/styles";
import newsletterBg from '../../assets/png/newletterBg.png';

const StyledGetStarted = styled("div")(
  ({ theme }) => `
  padding: 80px 0;
  background: #FFFFFF;

  @media (max-width: 600px) {
    padding: 20px 0px;
  }

  .content-wrapper {
    max-width: 90%;
    margin: 0 auto;
    padding: 0 24px;
    text-align: center;
  }

  .title {
    font-family: 'Lora', serif;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 16px;
    color: ${theme.palette.secondary.main};
    line-height: 56px;

    .highlight {
      background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .subtitle {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 150%;
    color: ${theme.palette.custom.subText};
    margin-bottom: 28px;
  }

  .action-cards {
    display: flex;
    justify-content: space-around;
    margin-bottom: 50px;

  }

  .action-card {
    width: 25%;
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform 0.3s ease;
    box-shadow: 0px 4px 4px 0px #0000000D;
    border-radius: 16px;
    
    .card-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
      border-radius: 16px 16px 0px 0px;
      margin-bottom: 16px;
    }

    .card-title {
      font-family: 'Lora', serif;
      font-size: 20px;
      font-weight: 600;
      line-height: 100%;
      padding: 10px 0px 40px;
      color: ${theme.palette.secondary.main};
    }
  }

  .newsletter-section {
    background: #FFF5F6 url(${newsletterBg});
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 24px;
    padding: 48px;
    text-align: center;
    width: 88%;
    margin: 0 auto;
  }

  .newsletter-title, .newsletter-subtitle {
    font-family: 'Lora', serif;
    font-size: 24px;
    font-weight: 700;
    color: ${theme.palette.secondary.main};
    line-height: 40px;
  }

  .subscribe-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
    max-width: 600px;
    margin: 32px auto 0;
    position: relative;

    .MuiOutlinedInput-root {
      padding-right: 16px;
    }

    .subscribe-button {
      height: 44px;
      background: ${theme.palette.custom.lightred};
      border-radius: 4px;
      font-family: 'Roboto', sans-serif;
      font-weight: 500;
      font-size: 16px;
      text-transform: none;
      white-space: nowrap;
      padding: 8px 40px;
      margin: 0;
      width: 100%;
    }

    .email-input {
      .MuiOutlinedInput-root {
        background: #FFFFFF;
        border-radius: 8px;
        padding-right: 8px;
        
        &:hover .MuiOutlinedInput-notchedOutline {
          border-color: #E0E0E0;
        }

        &.Mui-focused .MuiOutlinedInput-notchedOutline {
          border-color: #E0E0E0;
          border-width: 1px;
        }

        .MuiOutlinedInput-input {
          padding: 16px;
        }

        .MuiOutlinedInput-notchedOutline {
          border-color: #E0E0E0;
        }
      }
    }
  }

  @media (max-width: 1024px) {
    .action-cards {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 768px) {
    .title {
      font-size: 24px;
      line-height: 30px;
      margin-bottom: 0px;
    }

    .action-cards {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 20px;
    }
    .action-card {
      width: 60%;
    }
    .email-input {
      width: 100%;
      
      .MuiOutlinedInput-root {
        .MuiOutlinedInput-input {
          padding: 16px !important;
        }
      }
    }

    .newsletter-section {
      padding: 32px 24px;
    }

    .newsletter-title, .newsletter-subtitle {
      font-size: 16px;
      line-height: 22px;
    }

    .subscribe-form {
      .subscribe-button {
        width: 100%;
      }
    }
  }
  @media (max-width: 600px) {
    .action-card {
      width: 100%;
    }
  }
`
);

export default StyledGetStarted; 