import React, { useState } from 'react';
import { Box, Typography, TextField, useMediaQuery } from "@mui/material";
import { useTheme } from '@mui/material/styles';
import StyledGetStarted from './StyledGetStarted';
import startPlanImage from '../../assets/svg/getStartedTodayCardImg.svg';
import scheduleDemoImage from "../../assets/svg/getStartedDemo.svg";
import contactSalesImage from "../../assets/svg/getStartedSales.svg";
import { ToastNotifyError, ToastNotifySuccess } from '../Toast/ToastNotify';
import { fetchJoinNewsletter } from '../../store/reducers/Home';
import { useDispatch } from 'react-redux';
import { useNavigate } from "react-router-dom";
import PrimaryButton from "../common/Button/PrimaryButton";

const GetStarted = ({ handleScheduleDemo = () => {} }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleEmailChange = (e) => {
    setEmail(e.target.value);
  };

  const handleJoinNewsletter = async () => {
    // Trim whitespace
    const trimmedEmail = email.trim();

    // Validation checks
    if (!trimmedEmail) {
      ToastNotifyError("Please enter your email address to join");
      return;
    }

    if (!validateEmail(trimmedEmail)) {
      ToastNotifyError("Please enter a valid email address");
      return;
    }

    setIsLoading(true);

    try {
      const response = await dispatch(
        fetchJoinNewsletter({ email: trimmedEmail })
      );
      const { message, status } = response?.payload?.data;
      if (status === "success") {
        ToastNotifySuccess(message);
        setEmail("");
      } else {
        ToastNotifyError(message);
      }
    } catch (error) {
      ToastNotifyError("Failed to join newsletter. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      handleJoinNewsletter();
    }
  };

  const handleContactSales = () => {
    const subject = encodeURIComponent("Sales Inquiry - Zuumm AI");

    const mailtoLink = `mailto:<EMAIL>?subject=${subject}&bcc=<EMAIL>`;
    window.open(mailtoLink, "_blank");
  };

  const actionCards = [
    {
      image: startPlanImage,
      title: "Start Your Plan",
      onClick: () => {
        navigate("/explore");
      },
    },
    {
      image: scheduleDemoImage,
      title: "Schedule a Demo",
      onClick: handleScheduleDemo,
    },
    {
      image: contactSalesImage,
      title: "Contact Sales",
      onClick: handleContactSales,
    },
  ];

  const handleSubscribe = (e) => {
    e.preventDefault();
    // Handle subscription logic here
  };

  return (
    <StyledGetStarted>
      <Box className="content-wrapper">
        <Typography className="title">
          <span className="highlight">Get Started</span> Today
        </Typography>
        <Typography className="subtitle">
          No setup fees. No technical expertise required.
        </Typography>

        <Box className="action-cards">
          {actionCards.map((card, index) => (
            <Box
              key={index}
              className="action-card"
              onClick={card?.onClick}
              sx={{
                cursor: "pointer",
              }}
            >
              <Box
                component="img"
                src={card.image}
                alt={card.title}
                className="card-image"
              />
              <Typography className="card-title">{card.title}</Typography>
            </Box>
          ))}
        </Box>

        <Box className="newsletter-section">
          <Typography className="newsletter-title">
            Subscribe to our newsletter for the
          </Typography>
          <Typography className="newsletter-subtitle">
            latest updates and tips.
          </Typography>

          <Box
            component="form"
            onSubmit={handleSubscribe}
            className="subscribe-form"
          >
            <TextField
              placeholder="Enter your Email"
              variant="outlined"
              fullWidth
              className="email-input"
              value={email}
              onChange={handleEmailChange}
              onKeyPress={handleKeyPress}
              disabled={isLoading}
              InputProps={
                !isMobile
                  ? {
                      endAdornment: (
                        <PrimaryButton
                          type="submit"
                          onClick={handleJoinNewsletter}
                          sx={{
                            width: "50% !important",
                          }}
                          disabled={isLoading}
                        >
                          {isLoading ? "Subscribing..." : "Subscribe Now"}
                        </PrimaryButton>
                      ),
                    }
                  : undefined
              }
            />
            {isMobile && (
              <Box className="subscribe-button-wrapper">
                <PrimaryButton
                  type="submit"
                  value={email}
                  onChange={handleEmailChange}
                  onKeyPress={handleKeyPress}
                  sx={{
                    width: "50% !important",
                  }}
                >
                  Subscribe Now
                </PrimaryButton>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
    </StyledGetStarted>
  );
};

export default GetStarted; 