import { styled } from "@mui/material/styles";

const StyledHeader = styled("div")(
  ({ theme, isScrolled, isSticky }) => `
  position: ${isSticky ? "fixed" : "absolute"};
  top: ${isScrolled ? "0" : "24px"};
  left: 0;
  right: 0;
  z-index: 1000;
  transition: top 0.3s ease;
  width: 100%;
  .header-wrap {
    position: relative;
    width: 88%;
    height: 80px;
    margin-left: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 30px;
    padding-right: 54px;
    background: white;
    box-shadow: 0px -4px 12px 0px rgba(255, 57, 81, 0.01),
                0px 4px 12px 0px rgba(255, 57, 81, 0.07);
    overflow: visible;
    isolation: isolate;
  }

  .header-wrap > * {
    position: relative;
    z-index: 2;
  }

  .logo-animation {
    display: block;
    transform: scale(2);
  }

  .talk-to-expert-button {
      position: relative;
      background: #FF3951;
      color: white;
      font-size: 14px;
      font-weight: 600;
      padding: 6px 30px;
      font-family: 'Roboto', sans-serif;
      border-radius: 4px;
      text-transform: none;
      font-weight: 500;
      line-height: 100%;
      letter-spacing: 0.05em;
      height: 44px;
      cursor: pointer;
      z-index: 1;
      box-shadow: none !important;
  }

.talk-to-expert-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
  background-size: 100% 200%;
  background-position: left center;
  transition: background-position 4s ease, opacity 0.5s ease;
  z-index: -1;
  opacity: 0;
  border-radius: 4px;
  box-shadow: none !important;
}

.talk-to-expert-button:hover::before {
  background-position: right center;
  opacity: 1;
  border-radius: 4px;
  box-shadow: none !important;
}

  .header-wrap::before {
    content: "";
    position: absolute;
    top: 4px;
    left: -32px;
    width: 80px;
    height: 90%;
    transform: skewX(-20deg) rotate(90deg);
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    box-shadow: 0px 4px 12px 0px rgba(255, 57, 81, 0.07);
    z-index: 3;
    background-color: white;
  }

  .logo-header {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding-left: 0px;
    position: relative;
    z-index: 5;
    overflow: hidden;

    .company-name {
      height: 20px;
      z-index: 1001;
    }
  }

  .nav-links {
    display: flex;
    gap: 32px;
    align-items: center;

    a {
      font-family: 'Roboto', sans-serif;
      text-align: center;
      font-weight: 500;
      cursor: pointer;
      text-decoration: none;
      color: ${theme.palette.secondary.main};
      font-size: 14px;
      line-height: 24px;
      transition: color 0.2s ease;

      &.active {
        color: ${theme.palette.custom.lightred};
        font-weight: 600;
      }
    }
  }

  .mobile-menu-button {
    display: none;
    padding: 8px;
    color: #FF3951;

    svg {
      font-size: 24px;
    }
  }

  .btn {
    padding-right: 120px;
    position: relative;
    z-index: 1;
    
    button {
      background: #FF3951;
      color: white;
      font-size: 16px;
      font-weight: 600;
      padding: 12px 24px;
      border-radius: 8px;
      border: none;
      cursor: pointer;
      height: 48px;
      text-transform: none;
      transition: background-color 0.2s ease;
      &:hover {
        background: #E62E5C;
      }
    }
  }

  // Mobile styles
  @media (max-width: 768px) {
    top: 0;
    background: white;

    .talk-to-expert-button {
      display: none;
    }

    .header-wrap {
      width: 95%;
      height: 56px;
      margin: 0;
      padding: 0 16px;
      box-shadow: none;
      border-bottom: 1px solid rgba(0, 0, 0, 0.08);
      box-shadow: 0px 4px 12px 0px #FF395112;
    }

    .header-wrap::before {
      display: none;
    }

    .logo-header {
      padding: 0;
      
      .company-name {
        height: 18px;
      }

      .logo-animation {
        display: none;
      }
    }

    .desktop-nav {
      display: none;
    }

    .mobile-menu-button {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: -8px;
    }

    .mobile-menu {
      .MuiDrawer-paper.MuiPaper-root {
        width: 90%;
        background: white;
        padding: 16px;
        right: 0;
        border-radius: 0;
        box-shadow: -4px 0px 16px rgba(0, 0, 0, 0.08);
      }

      .close-button {
        position: absolute;
        right: 12px;
        top: 16px;
        color: #FF3951;
      }

      .mobile-nav-links {
        display: flex;
        flex-direction: column;
        gap: 0px !important;
        margin-top: 56px;
        padding: 0 16px;

        a {
          font-family: 'Roboto', sans-serif;
          font-size: 16px;
          color: #1E3A8A;
          text-decoration: none !important;
          font-weight: 500;
          line-height: 100%;
          padding: 8px 0;

          &.active {
            color: #FF3951;
            font-weight: 600;
          }
        }
      }
    }
  }
    @media (max-width: 480px) {
      .header-wrap {
        width: 90%;
      }
    }
`
);

export default StyledHeader;

