import React, { useState } from "react";
import { Ava<PERSON>, Typography, Menu, MenuItem, Box } from "@mui/material";
import { getInitials } from "../../utils/helper";
import { useLogout } from "../../hooks/useLogout";
import ConfirmationModal from "../common/ConfirmationModal";
import { useNavigate } from "react-router-dom";

const UserMenu = ({ name }) => {
  const navigate = useNavigate();
  const [userMenuAnchor, setUserMenuAnchor] = useState(null);
  const { logout } = useLogout();
  const [openLogoutModal, setOpenLogoutModal] = useState(false);

  const handleConfirm = () => {
    logout();
    setOpenLogoutModal(false);
    handleUserMenuClose();
  };

  const menuItems = [
    {
      label: "My Account",
      icon: "/static/menu-drop/avatar.png",
      onClick: () => {
        console.log("My Profile");
      },
    },
    {
      label: "Affiliate",
      icon: "/static/menu-drop/affiliate.png",
      onClick: () => {
        navigate("/affiliate");
      },
    },
    {
      label: "My Trips",
      icon: "/static/menu-drop/trip.png",
      onClick: () => {
        console.log("My Bookings");
      },
    },
    {
      label: "Saved",
      icon: "/static/menu-drop/saved.png",
      onClick: () => {
        console.log("Settings");
      },
    },
    {
      label: "Logout",
      icon: "/static/menu-drop/logout.png",
      onClick: () => {
        setOpenLogoutModal(true);
      },
    },
  ];

  const handleUserMenuOpen = (event) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  return (
    <>
      <Box
        onClick={handleUserMenuOpen}
        sx={{
          cursor: "pointer",
          display: "flex",
          alignItems: "center",
          gap: 1,
          padding: "4px 4px",
          borderRadius: "25px",
          textTransform: "none",
          borderColor: "#FF3951",
          color: "#FF3951",
          "&:hover": {
            borderColor: "#FF3951",
            backgroundColor: "#FF39510D",
          },
        }}
      >
        <Avatar
          sx={{
            width: { xs: 28, md: 34 },
            height: { xs: 28, md: 34 },
            background: "#FF39510D",
            fontSize: "14px",
            color: "#FF3951",
          }}
        >
          {getInitials(name || "")}
        </Avatar>
        <Typography
          variant="body2"
          sx={{
            fontWeight: 600,
            fontSize: "14px",
            lineHeight: "16px",
            fontFamily: "Roboto",
            color: "#333333",
            maxWidth: "150px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          {name}
        </Typography>
        <Box
          component={"img"}
          src={"/static/menu-drop/toggle-drop.png"}
          sx={{ width: "24px", height: "24px" }}
          alt="menu-drop"
          ml={0.5}
          mr={1}
        />
      </Box>
      <Menu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={handleUserMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 170,
            boxShadow: "0px 0px 8px 0px #0000000D",
            borderRadius: "10px",
            border: "1px solid #FF39510D",
          },
        }}
        MenuListProps={{
          sx: {
            padding: 0,
          },
        }}
      >
        {menuItems.map((item, index) => (
          <MenuItem
            sx={{
              p: 1.5,
              borderBottom:
                index !== menuItems.length - 1 &&
                "1px solid rgba(51, 51, 51, 0.05)",
              display: "flex",
              alignItems: "center",
              gap: 0.5,
              "&:hover": {
                backgroundColor: "#FF39510D",
              },
            }}
            onClick={item?.onClick}
            key={item.label}
          >
            <Box
              component={"img"}
              src={item.icon}
              alt={item.label}
              sx={{ width: "20px", height: "20px" }}
            />
            <Typography
              variant="body2"
              sx={{
                fontSize: "14px",
                lineHeight: "16px",
                fontWeight: 400,
                fontFamily: "Roboto",
                color: "#333333",
              }}
            >
              {item.label}
            </Typography>
          </MenuItem>
        ))}
      </Menu>

      <ConfirmationModal
        open={openLogoutModal}
        onClose={() => setOpenLogoutModal(false)}
        onConfirm={handleConfirm}
        title="Logout"
        description="Are you sure you want to logout?"
        confirmText="Yes, Logout"
        cancelText="Cancel"
      />
    </>
  );
};

export default UserMenu;
