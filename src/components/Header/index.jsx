/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Avatar,
  Typography,
} from "@mui/material";
import { NavLink, useLocation } from "react-router-dom";
import StyledHeader from "./StyledHeader";
import <PERSON><PERSON> from "lottie-react";
import animationData from "../../assets/animations/logo.json";
import logo from "../../assets/svg/logo.svg";
import { useNavigate } from "react-router-dom";
import hamburgerIcon from "../../assets/svg/hamburerIcon.svg";
import CloseIcon from "@mui/icons-material/Close";
import ROUTE_PATH from "../../constants/route";
import { useSelector } from "react-redux";
import { useLogout } from "../../hooks/useLogout";
import UserMenu from "./UserMenu";
import { getInitials } from "../../utils/helper";
import ConfirmationModal from "../common/ConfirmationModal";
import OnboardingModal from "../OnboardingModal";

const Header = ({ isSticky = true }) => {
  const { userDetails } = useSelector((state) => state.authentication);
  const { logout } = useLogout();

  const [openLogoutModal, setOpenLogoutModal] = useState(false);
  const [openOnboardingModal, setOpenOnboardingModal] = useState(false);

  const handleOpenOnboardingModal = () => {
    setOpenOnboardingModal(true);
  };

  const handleCloseOnboardingModal = () => {
    setOpenOnboardingModal(false);
  };

  const handleConfirm = () => {
    logout();
    setOpenLogoutModal(false);
    toggleMobileMenu();
  };

  const navigate = useNavigate();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();
  const isLogin = userDetails?.user ? true : false;

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsScrolled(scrollPosition > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    setTimeout(() => {
      if (
        !openOnboardingModal &&
        !isLogin &&
        !sessionStorage.getItem("hasVisited")
      ) {
        const isVisited = sessionStorage.getItem("hasVisited");
        if (!isVisited) {
          setOpenOnboardingModal(true);
          sessionStorage.setItem("hasVisited", "true");
        }
      }
    }, 5000);
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <StyledHeader isScrolled={isScrolled} isSticky={isSticky}>
      <Box className="header-wrap">
        <Box className="logo-header">
          <img
            src={logo}
            alt="logo"
            className="company-name"
            onClick={() => {
              navigate("/");
              window.scrollTo({
                top: 0,
                behavior: "smooth",
              });
            }}
          />
          <Box className="logo-animation">
            <Lottie
              animationData={animationData}
              loop
              autoplay
              style={{ height: 79, width: 79 }}
            />
          </Box>
        </Box>

        {/* Desktop Navigation */}
        <Box className="nav-links desktop-nav">
          <NavLink
            to={ROUTE_PATH.EXPLORE}
            className={() =>
              [
                "/explore",
                "/explore-destination",
                "/explore-package",
                "/package-details",
              ].some((path) => location.pathname.startsWith(path))
                ? "active"
                : ""
            }
          >
            Explore
          </NavLink>
          <NavLink to="/for-partners">For Partners</NavLink>
          <NavLink to="/wander-notes">Wander Notes</NavLink>
          <NavLink to="/corporate-travel">Corporate Travel</NavLink>
          <NavLink to="/about-us">About Us</NavLink>
        </Box>
        <Box sx={{ display: { xs: "none", md: "flex" } }}>
          {isLogin ? (
            <UserMenu name={userDetails?.user?.name || ""} />
          ) : (
            <Button
              variant="contained"
              color="primary"
              className="talk-to-expert-button"
              onClick={handleOpenOnboardingModal}
            >
              Login/Sign-up
            </Button>
          )}
        </Box>

        {/* Mobile Menu Button */}
        <IconButton
          className="mobile-menu-button"
          onClick={toggleMobileMenu}
          sx={{ display: { md: "none" } }}
        >
          <img src={hamburgerIcon} alt="hamburger" />
        </IconButton>

        {/* Mobile Navigation Drawer */}
        <Drawer
          anchor="top"
          open={isMobileMenuOpen}
          onClose={toggleMobileMenu}
          className="mobile-menu"
          PaperProps={{
            sx: {
              backgroundColor: "white", // example
              width: "100%",
            },
          }}
        >
          <Box
            className="mobile-menu-content"
            style={{
              padding: "25px",
              height: "100dvh",
              boxSizing: "border-box",
            }}
          >
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                height: "100%",
                width: "100%",
                boxSizing: "border-box",
              }}
            >
              <Box className="mobile-menu-header">
                {/* <Box className='mobile-menu-logo'> */}
                <img src={logo} alt="logo" />
                {/* </Box> */}
                <Box className="mobile-menu-close-button">
                  <IconButton
                    className="close-button"
                    onClick={toggleMobileMenu}
                  >
                    <CloseIcon sx={{ color: "#FF3951" }} />
                  </IconButton>
                </Box>
              </Box>
              {isLogin && (
                <>
                  <hr className="mobile-menu-divider" />
                  <Box
                    sx={{ display: "flex", alignItems: "center", gap: "10px" }}
                  >
                    <Avatar
                      sx={{
                        width: 60,
                        height: 60,
                        background: "#FF39510D",
                        fontSize: "20px",
                        color: "#FF3951",
                      }}
                    >
                      {getInitials(userDetails?.user?.name || "")}
                    </Avatar>
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "5px",
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 600,
                          fontSize: "14px",
                          lineHeight: "16px",
                          fontFamily: "Roboto",
                          color: "#1E3A8A",
                        }}
                      >
                        {userDetails?.user?.name || ""}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 600,
                          fontSize: "14px",
                          lineHeight: "16px",
                          fontFamily: "Roboto",
                          color: "#333333",
                        }}
                      >
                        {userDetails?.user?.email || ""}
                      </Typography>
                    </Box>
                  </Box>
                </>
              )}

              <hr className="mobile-menu-divider" />

              <Box
                className="mobile-nav-links"
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "0px",
                  "& a": {
                    textDecoration: "none",
                    color: "#1E3A8A",
                    fontFamily: "Roboto",
                    fontSize: "16px",
                    fontWeight: "500",
                    lineHeight: "100%",
                  },
                }}
              >
                <NavLink to="/explore" onClick={toggleMobileMenu}>
                  Explore
                </NavLink>
                <hr className="mobile-menu-divider" />
                <NavLink to="/for-partners" onClick={toggleMobileMenu}>
                  For Partners
                </NavLink>
                <hr className="mobile-menu-divider" />
                <NavLink to="/wander-notes" onClick={toggleMobileMenu}>
                  Wander Notes
                </NavLink>
                <hr className="mobile-menu-divider" />
                <NavLink to="/corporate-travel" onClick={toggleMobileMenu}>
                  Corporate Travel
                </NavLink>
                <hr className="mobile-menu-divider" />
                <NavLink to="/about-us" onClick={toggleMobileMenu}>
                  About Us
                </NavLink>
                <hr className="mobile-menu-divider" />
              </Box>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  marginTop: "20px",
                }}
              >
                {!isLogin && (
                  <Button
                    className="talk-to-expert-button-mobile"
                    onClick={handleOpenOnboardingModal}
                  >
                    Login/Sign-up
                  </Button>
                )}
              </Box>
              {isLogin && (
                <Box sx={{ marginTop: "auto" }}>
                  <hr className="mobile-menu-divider" />
                  <Box
                    onClick={() => {
                      setOpenLogoutModal(true);
                    }}
                    sx={{
                      fontFamily: "Roboto",
                      fontSize: "16px",
                      fontWeight: "500",
                      lineHeight: "100%",
                      color: "#FF3951",
                      cursor: "pointer",
                      display: "flex",
                    }}
                  >
                    Logout
                  </Box>
                </Box>
              )}
            </Box>
          </Box>
        </Drawer>
      </Box>

      {!isLogin && openOnboardingModal && (
        <OnboardingModal
          open={openOnboardingModal}
          onClose={handleCloseOnboardingModal}
        />
      )}

      {openLogoutModal && (
        <ConfirmationModal
          open={openLogoutModal}
          onClose={() => setOpenLogoutModal(false)}
          onConfirm={handleConfirm}
          title="Logout"
          description="Are you sure you want to logout?"
          confirmText="Yes, Logout"
          cancelText="Cancel"
        />
      )}
    </StyledHeader>
  );
};

export default Header;
