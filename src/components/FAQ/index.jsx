/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { Box, Typography, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';
import StyledFAQ from './StyledFAQ';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import { fetchFaq } from '../../store/reducers/Faq';
import { useDispatch } from 'react-redux';
import { ToastNotifyError } from '../Toast/ToastNotify';

const FAQ = () => {
  const [expanded, setExpanded] = useState(false);
  const [faqs, setFaqs] = useState([]);
  const dispatch = useDispatch();

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  const getFaqs = async () => {
    try{  
      const response = await dispatch(fetchFaq());
      const { data, status, message } = response?.payload?.data;
      if(status === "success"){
        setFaqs(data);
      } else {
        ToastNotifyError(message);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getFaqs();
  }, []);

  return (
    <StyledFAQ>
      <Box className="content-wrapper">
        <Typography className="title">Frequently Asked Questions</Typography>
        <Typography className="subtitle">
          Get answers to common questions about ZUUMM's implementation and
          features
        </Typography>

        <Box className="faq-container">
          {faqs.map((faq) => (
            <Accordion
              key={faq.id}
              expanded={expanded === faq.id}
              onChange={handleChange(faq.id)}
              sx={{
                boxShadow: "0px 4px 8px 0px #1E3A8A0F",
                borderRadius: "8px",
                "&:before": {
                  display: "none",
                },
              }}
            >
              <AccordionSummary
                expandIcon={expanded === faq.id ? <RemoveIcon /> : <AddIcon />}
                className="accordion-summary"
              >
                <Typography className="question">{faq.question}</Typography>
              </AccordionSummary>
              <AccordionDetails className="accordion-details">
                <Typography className="answer">{faq.answer}</Typography>
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      </Box>
    </StyledFAQ>
  );
};

export default FAQ; 