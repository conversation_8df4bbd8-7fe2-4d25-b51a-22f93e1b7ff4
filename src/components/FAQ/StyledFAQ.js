import { styled } from "@mui/material/styles";

const StyledFAQ = styled("div")(
  ({ theme }) => `
  padding: 50px 0;
  background: ${theme.palette.custom.white};
  width: 100%;

  .content-wrapper {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 24px;
  }

  .title {
    font-family: 'Lora', serif;
    font-size: 32px;
    font-weight: 700;
    color: ${theme.palette.secondary.main};
    text-align: center;
    line-height: 45px;
    margin-bottom: 16px;
  }

  .subtitle {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 150%;
    color: ${theme.palette.custom.subText};
    text-align: center;
    margin-bottom: 48px;
  }

  .faq-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .faq-accordion {
    border-radius: 8px !important;
    box-shadow: 0px 10px 23px 0px #0000000F;
    border: none;
    background: transparent;

    &:before {
      display: none;
    }
  }

  .accordion-summary {
    min-height: 64px;
    padding: 0 24px;

    .MuiAccordionSummary-content {
      margin: 20px 0;
    }

    .MuiAccordionSummary-expandIconWrapper {
      color: ${theme.palette.custom.lightred};
    }

    &.Mui-expanded {
      background: ${theme.palette.custom.lightPink};
      border-radius: 8px 8px 0 0;
    }
  }

  .question {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: 500;
    line-height: 100%;
    color: ${theme.palette.custom.black};
  }

  .accordion-details {
    padding: 0px 24px 24px;
  }

  .answer {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: 400;
    color: ${theme.palette.custom.subText};
    line-height: 24px;
  }

  @media (max-width: 768px) {
    padding: 20px 0;

    .title {
      font-size: 28px;
      margin-bottom: 12px;
    }

    .subtitle {
      font-size: 14px;
      margin-bottom: 32px;
    }

    .faq-container {
      gap: 12px;
    }

    .accordion-summary {
      min-height: 56px;
      padding: 0 16px;
    }

    .question {
      font-size: 14px;
      line-height: 20px;
    }

    .accordion-details {
      padding: 12px 16px 16px;
    }

    .answer {
      font-size: 13px;
    }
  }

  @media (max-width: 600px) {
    padding: 0;
  }
`
);

export default StyledFAQ; 