import React from "react";
import { Box, Typography, Link } from "@mui/material";
import StyledFooter from "./StyledFooter";
import { ReactComponent as Logo } from "../../assets/svg/logo.svg";
import { ReactComponent as FooterTopIcon } from "../../assets/svg/footerTopIcon.svg";
import { ReactComponent as FacebookIcon } from "../../assets/svg/facebookIcon.svg";
import { ReactComponent as TwitterIcon } from "../../assets/svg/twitterIcon.svg";
import { ReactComponent as InstagramIcon } from "../../assets/svg/instagramIcon.svg";
import { ReactComponent as IIMBLogo } from "../../assets/svg/IIMBLogo.svg";
import { ReactComponent as StartupIndiaLogo } from "../../assets/svg/emblemLogo.svg";
import contact from "../../assets/svg/smallEmailIcon.svg";
import { ReactComponent as PlayStoreIcon } from '../../assets/svg/appleStoreLogo.svg';
import { ReactComponent as AppStoreIcon } from '../../assets/svg/googlePlayIcon.svg';
// import { ReactComponent as IIMBLogo } from '../../assets/images/iimb-logo.svg';
// import { ReactComponent as StartupIndiaLogo } from '../../assets/images/startup-india-logo.svg';

const Footer = () => {
  return (
    <StyledFooter>
      <Box sx={{ width: "100%" }}>
        <Box className="footer-content">
          <FooterTopIcon className="footer-top-icon" />
          <Box className="company-info">
            <Logo className="logo" />
            <Typography className="address">
              1005, Prestige Meridian 2,
              <br />
              MG Road, Bangalore-560001, India
            </Typography>
            <Box className="social-links">
              <Link href="https://facebook.com/zuummtravel" target="_blank">
                <FacebookIcon />
              </Link>
              <Link href="https://x.com/zuummtravel" target="_blank">
                <TwitterIcon />
              </Link>
              <Link href="https://instagram.com/zuummtravel" target="_blank">
                <InstagramIcon />
              </Link>
            </Box>
          </Box>

          <div className="quick-links">
            <Typography className="quick-link-title mobile-titles">
              Quick Links
            </Typography>
            <div className="quick-links-container">
              <Link href="/explore">Explore</Link>
              <Link href="/for-partners">For Partners</Link>
              <Link href="/wander-notes">Wander Notes</Link>
              <Link href="/corporate-travel">Corporate Travel</Link>
              <Link href="/about-us">About ZUUMM</Link>
              <Link href="/document-hub">Technical Documents</Link>
            </div>
          </div>

          <Box className="incubated-recognition">
            <Box className="incubated">
              <Typography
                className="quick-link-title mobile-titles"
                style={{ marginBottom: "10px" }}
              >
                Incubated at
              </Typography>
              <IIMBLogo className="incubator-logo" />
            </Box>

            <Box className="recognition">
              <Typography
                className="quick-link-title mobile-titles"
                style={{ marginBottom: "10px" }}
              >
                Recognized by
              </Typography>
              <StartupIndiaLogo className="recognition-logo" />
            </Box>
          </Box>

          {/* Contact Section */}
          <Box className="contact">
            <Typography className="quick-link-title">Contact US</Typography>
            <Box className="contact-container">
              <Box className="contact-icon">
                <img src={contact} alt="contact" />
              </Box>
              <Link
                href="mailto:<EMAIL>"
                style={{ marginBottom: "0px" }}
              >
                <EMAIL>
              </Link>
            </Box>

            <Box className="download-section">
              <Typography className="quick-link-title">Download App</Typography>
              <Box className="store-buttons">
                <Link
                  href="https://play.google.com"
                  target="_blank"
                  sx={{ textDecoration: "none" }}
                >
                  <Box className="store-button">
                    <AppStoreIcon className="store-icon" />
                    <Typography className="coming-soon">Coming Soon</Typography>
                  </Box>
                </Link>
                <Link
                  href="https://apps.apple.com"
                  target="_blank"
                  sx={{ textDecoration: "none" }}
                >
                  <Box className="store-button">
                    <PlayStoreIcon className="store-icon" />
                    <Typography className="coming-soon">Coming Soon</Typography>
                  </Box>
                </Link>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Footer Bottom */}
      <Box className="footer-bottom">
        <Typography>
          © 2025 ZUUMM by IncBuddy Technologies Pvt. Ltd. All Rights Reserved.
        </Typography>
        <Box className="legal-links">
          <Link href="/terms">Terms and Conditions,</Link>
          <Link href="/privacy">Privacy policy</Link>
        </Box>
      </Box>
    </StyledFooter>
  );
};

export default Footer;
