import { styled } from "@mui/material/styles";

const StyledFooter = styled("footer")(
  ({ theme }) => `
  background: ${theme.palette.custom.white};
  padding: 0px 0 0;
  width: 100%;
  overflow-x: hidden;

  .footer-content {
    position: relative;
    width: 100%;
    margin: 0px;
    padding: 60px 40px 40px;
    display: flex;
    justify-content: space-around;
    gap: 90px;
    background: #FEF7F4;
    overflow: hidden;
    box-sizing: border-box;
    flex-wrap: wrap;

    > * {
      padding: 0;
    }
  }

  .footer-top-icon {
    position: absolute;
    top: -27px;
    left: 0;
    width: 100%;
    height: 80px;
    object-fit: cover;
    padding: 0;
  }

  .company-info {
    .logo {
      width: 156px;
      height: auto;
      margin-bottom: 24px;
    }

    .address {
      font-family: 'Roboto', sans-serif;
      font-size: 16px;
      font-weight: 400;
      line-height: 25px;
      color: ${theme.palette.custom.subText};
      margin-bottom: 24px;
    }

    .social-links {
      display: flex;
      gap: 16px;

      a {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
      }
    }
  }

  .quick-links {
    .quick-link-title {
      font-family: 'Lora', serif;
      font-size: 24px;
      font-weight: 600;
      color: #202020;
      margin-bottom: 20px;
    }

    .quick-links-container {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    a {
      display: block;
      font-family: 'Roboto', sans-serif;
      font-size: 14px;
      font-weight: 500;
      line-height: 100%;
      letter-spacing: 0.05em;
      color: ${theme.palette.custom.subText};
      text-decoration: none;
      margin-bottom: 0;
    }
  }

  .incubated, .recognition {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .quick-link-title {
      font-family: 'Lora', serif !important;
      font-size: 24px !important;
      font-weight: 600 !important;
      color: #202020 !important;
      margin-bottom: 20px !important;
      text-align: center;
    }
  }

  .incubator-logo, .recognition-logo {
    max-width: 120px;
    height: auto;
  }

  .download-section {
    margin-top: 45px;

    .store-buttons {
      display: flex;
      gap: 16px;

      .store-icon {
        height: 40px;
        width: auto;
      }
    }
  }

  .contact {
    .quick-link-title {
      font-family: 'Lora', serif;
      font-size: 24px;
      font-weight: 600;
      color: #202020;
      margin-bottom: 10px;
    }
    a {
      font-family: 'Roboto', sans-serif;
      font-size: 16px;
      font-weight: 500;
      line-height: 100%;
      color: #2D64FF;
    } 
  }

  .contact-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: ${theme.palette.custom.white};
    border-radius: 50%;
  }
  
  .contact-container {
    display: flex;
    align-items: center;
    gap: 10px;
  }  

  .coming-soon {
    text-decoration: none;
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    font-weight: 700;
    line-height: 100%;
    background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
  }
  .store-button {
    text-align: center;
  }    

  .footer-bottom {
    width: 100%;
    margin-top: 0;
    padding: 20px 40px;
    border-top: 1px solid ${theme.palette.custom.border};
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    
    p {
      font-family: 'Roboto', sans-serif;
      font-weight: 600;
      font-size: 12px;
      color: ${theme.palette.custom.lightred};
      line-height: 100%;
    }

    .legal-links {
      display: flex;
      gap: 5px;

      a {
        font-family: 'Roboto', sans-serif;
        font-size: 12px;
        font-weight: 600;
        line-height: 100%;
        color: ${theme.palette.secondary.main};
        text-decoration: none;
      }
    }
  }

  @media (max-width: 1024px) {
    .footer-content {
      gap: 70px;
      justify-content: flex-start;
    }
  }

  @media (max-width: 768px) {
    .footer-content {
      gap: 70px;
    }
  }

  @media (max-width: 600px) {
    .footer-content {
      flex-direction: column;
      gap: 24px;
    }
    .mobile-titles {
      font-size: 18px;
      line-height: 22px;
      font-weight: 600;
      font-family: 'Lora', serif;
    }
    .incubated, .recognition {
      .quick-link-title {
        font-size: 18px !important;
        line-height: 24px !important;
        font-weight: 600 !important;
        font-family: 'Lora', serif !important;
      }
    }
    .incubated-recognition {
      display: flex;
      flex-direction: row !important;
      align-items: baseline !important;
      gap: 50px !important;
    }
    .footer-bottom {
      flex-direction: column;
      gap: 10px;
      text-align: center;
    }
    .quick-link-title {
      font-size: 18px !important;
      line-height: 24px !important;
      font-weight: 600 !important;
      font-family: 'Lora', serif !important;
    }  
    .quick-links {
      width: 100%;
      
      .quick-links-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, auto);
        gap: 20px;
        width: 100%;
      }
    }
    .footer-bottom p {
      font-size: 10px !important;
      font-weight: 400 !important;
    }  
    .legal-links {
      gap: 2px !important;
      a {
        font-size: 10px !important;
        font-weight: 500 !important;
        text-decoration: underline !important;
      }
    }
  }
`
);

export default StyledFooter; 