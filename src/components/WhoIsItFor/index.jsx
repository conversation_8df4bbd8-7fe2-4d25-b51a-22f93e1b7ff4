import React from "react";
import { <PERSON>, Typography, Button } from "@mui/material";
import StyledWhoIsItFor from "./StyledWhoIsItFor";
import StoreIcon from "../../assets/svg/travelAgencyIcon.svg";
import BusinessCenterIcon from "../../assets/svg/instituteIcon.svg";

const cardData = [
  {
    icon: BusinessCenterIcon,
    alt: "affiliate-icon",
    title: "For Affiliates:",
    subtitle: "Earn by Helping Others Travel Smarter",
    description:
      "Tap into our fully loaded AI travel platform — no coding, no inventory, no hassle. Get a branded app/web portal, backed by our team for sales and support. You bring in users, we do the rest. Perfect for freelancers, influencers, and gig workers looking to build recurring income.",
  },
  {
    icon: BusinessCenterIcon,
    alt: "enterprise-icon",
    title: "For Enterprises:",
    subtitle: "Empower Your Workforce with Seamless Travel Benefits",
    description:
      "Offer your employees exclusive travel deals and AI-powered planning through your own branded platform — with zero setup effort. Whether you're a Fortune 500 company, or a large enterprise, our plug-and-play solution lets you provide unforgettable holiday experiences as a perk or reward.",
  },
  {
    icon: StoreIcon,
    alt: "store-icon",
    title: "For Travel Agencies & Tour Operators:",
    subtitle: "Power Your Growth with the First AI-Driven Travel Platform",
    description:
      "Launch your own branded app or web portal, upload packages in minutes, and manage your business your way — we handle the tech. Whether you're streamlining operations or scaling up, our infrastructure is built to support you. The first Ai-driven APP that helps you compete with travel giants – all with zero upfront cost.",
  },
];

const WhoIsItFor = () => {
  return (
    <StyledWhoIsItFor>
      <Box className="content-wrapper">
        <Typography className="title">Who is it for?</Typography>
        <Typography className="description">
          Whether you're just starting out or already serving travelers, ZUUMM
          gives you the tools to grow.
          <br />
          From solo agents to large institutions — anyone can plug into our
          travel infrastructure and start selling.
        </Typography>

        <Box className="cards-container">
          {cardData.map((card, index) => (
            <Box className="info-card" key={index}>
              <Box className="icon-wrapper">
                <img src={card.icon} alt={card.alt} />
              </Box>
              <Typography className="card-title">
                {card.title}
                <br />
                {card.subtitle}
              </Typography>
              <Typography className="card-description">
                {card.description}
              </Typography>
              <Box className="button-wrapper">
                <Button variant="contained" className="action-button">
                  Choose A Plan
                </Button>
              </Box>
            </Box>
          ))}
        </Box>
      </Box>
    </StyledWhoIsItFor>
  );
};

export default WhoIsItFor;
