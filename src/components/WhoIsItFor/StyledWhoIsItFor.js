import { styled } from "@mui/material/styles";
import WhoIsItBg from "../../assets/svg/whoIsItBg.svg"

const StyledWhoIsItFor = styled("div")(
  ({ theme }) => `
  padding: 60px 0;
  background-image: url(${WhoIsItBg});
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  position: relative;

  @media (max-width: 600px) {
    padding: 15px 0px !important;
  }

  .content-wrapper {
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 24px;
    text-align: center;
  }

  .title {
    font-family: 'Lora', serif;
    font-size: 32px;
    font-weight: 700;
    line-height: 45px;
    color: ${theme.palette.secondary.main};
    margin-bottom: 24px;
  }

  .description {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: 400;
    color: ${theme.palette.custom.subText};
    max-width: 1027px;
    margin: 0 auto 48px;
    line-height: 150%;
    letter-spacing: 0.05em;
  }

  .cards-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
    margin-top: 40px;
  }

  .info-card {
    display: flex;
    flex-direction: column;
    background: #FFFFFF;
    border-radius: 16px;
    padding: 32px;
    text-align: left;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .icon-wrapper {
    width: 44px;
    height: 44px;
    margin-bottom: 24px;
  }

  .card-title {
    font-family: 'Lora', serif;
    font-size: 20px;
    font-weight: 600;
    color: ${theme.palette.secondary.main};
    margin-bottom: 8px;
    line-height: 30px;
  }

  .card-description {
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    font-weight: 400;
    color: ${theme.palette.custom.subText};
    margin-bottom: 20px;
    line-height: 18px;
  }

  .highlight-text {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: 500;
    color: ${theme.palette.custom.subText};
    margin-bottom: 32px;
    line-height: 24px;
  }
  .button-wrapper {
    margin-top: auto;
    display: flex;
    justify-content: center;
    align-items: center;
  }  

  .action-button {
    padding: 6px 30px;
    background: #FF3951;
    color: white;
    font-size: 14px;
    font-weight: 600;
    border-radius: 4px;
    text-transform: none;
    overflow: hidden;
    z-index: 1;
    cursor: pointer;
    position: relative;
    box-shadow: none !important;
    height: 44px;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
      background-size: 100% 200%;
      background-position: left center;
      transition: background-position 4s ease, opacity 0.5s ease;
      z-index: -1;
      opacity: 0;
      box-shadow: none !important;
    }

    &:hover::before {
      background-position: right center;
      opacity: 1;
      box-shadow: none !important;
    }
  }

  @media (max-width: 1024px) {
    padding: 60px 0;

    .cards-container {
      grid-template-columns: repeat(2, 1fr);
      gap: 24px;
    }

    .info-card {
      padding: 24px;
      height: 390px;
    }
    .button-wrapper {
      left: 30%;
    }
  }
  @media (max-width: 768px) {
  .info-card {
    height: 280px;
  }
  .button-wrapper {
    left: 38%;
    bottom: 22px;
  }
  }
  @media (max-width: 600px) {
    .content-wrapper {
      text-align: left;
    }
    .title {
      font-size: 24px;
    }
    .info-card {
      height: 410px
    } 
      
    .button-wrapper {
      bottom: 24px;
      left: 35%;
    }
    .action-button {
      padding: 8px 12px;
      font-size: 12px;
    }
    .description {
      font-size: 12px;
    }
  }
  @media (max-width: 768px) {
    .cards-container {
      grid-template-columns: repeat(1, 1fr);
    }

    .info-card {
      width: 90%;
    }

    .title {
      font-size: 24px;
      line-height: 30px;
    }

    .card-title {
      font-size: 20px;
    }
  }
`
);

export default StyledWhoIsItFor; 