import React from "react";
import { Breadcrumbs, Typography, Link, Box } from "@mui/material";
import { Link as RouterLink } from "react-router-dom";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";

const BreadCrumb = ({
  separator = <NavigateNextIcon fontSize="small" />,
  maxItems = 8,
  itemsAfterCollapse = 2,
  itemsBeforeCollapse = 2,
  crumbs,
}) => {
  const breadcrumbs = crumbs;

  // Don't render if no breadcrumbs or only home
  if (breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <Box>
      <Breadcrumbs
        separator={separator}
        maxItems={maxItems}
        itemsAfterCollapse={itemsAfterCollapse}
        itemsBeforeCollapse={itemsBeforeCollapse}
        sx={{
          "& .MuiBreadcrumbs-separator": {
            color: "#FFFFFF",
          },
          "& .MuiBreadcrumbs-li": {
            display: "flex",
            alignItems: "center",
          },
        }}
      >
        {breadcrumbs.map((breadcrumb, index) => {
          const isLast = breadcrumb.isLast || index === breadcrumbs.length - 1;

          if (isLast) {
            return (
              <Typography
                key={breadcrumb.path}
                color="#FFFFFF"
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 0.5,
                  fontWeight: 500,
                  fontSize: { xs: "12px", md: "16px" },
                  fontFamily: "Roboto",
                  textTransform: "capitalize",
                }}
              >
                {breadcrumb.label}
              </Typography>
            );
          }

          return (
            <Link
              key={breadcrumb.path}
              component={RouterLink}
              to={breadcrumb.path}
              underline="hover"
              color="inherit"
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 0.5,
                color: "#FFFFFF",
                fontSize: { xs: "12px", md: "16px" },
                fontFamily: "Roboto",
                "&:hover": {
                  color: "#FFFFFF",
                },
              }}
            >
              {breadcrumb.icon}
              {breadcrumb.label}
            </Link>
          );
        })}
      </Breadcrumbs>
    </Box>
  );
};

export default BreadCrumb;
