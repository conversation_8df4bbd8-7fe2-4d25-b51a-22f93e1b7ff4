import React from 'react';
import { CircularProgress, Backdrop } from '@mui/material';
import { styled } from '@mui/material/styles';
import { useSelector } from 'react-redux';
import { selectorHomeLoader } from '../../store/reducers/loader';

const StyledBackdrop = styled(Backdrop)(({ theme }) => ({
  zIndex: 9999,
  color: '#fff',
  backgroundColor: 'rgba(0, 0, 0, 0.7)',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  position: 'fixed',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
}));

const Loader = () => {
  const isLoading = useSelector(selectorHomeLoader);

  return (
    <StyledBackdrop open={isLoading}>
      <CircularProgress 
        color="primary"
        size={60}
        thickness={4}
        sx={{
          color: '#fff',
        }}
      />
    </StyledBackdrop>
  );
};

export default Loader; 