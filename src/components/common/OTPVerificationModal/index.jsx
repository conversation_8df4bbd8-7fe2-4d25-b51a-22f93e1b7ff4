import React, { useState, useRef, useEffect } from 'react';
import { Box, Button, Modal, Typography } from '@mui/material';
import StyledOTPVerificationModal from './StyledOTPVerificationModal';
import circleCrossIcon from '../../../assets/svg/circleCrossIcon.svg';

const OTPVerificationModal = ({ open, onClose, phoneNumber, onVerify, handleResendOTP, mobileOtpVerifiedStatus, emailOtpVerifiedStatus }) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [countdown, setCountdown] = useState(0);
  const [canResend, setCanResend] = useState(true);
  const inputRefs = useRef([]);

  useEffect(() => {
    if (open) {
      // Multiple attempts to focus with different timing
      const focusFirstInput = () => {
        if (inputRefs.current[0]) {
          inputRefs.current[0].focus();
          inputRefs.current[0].select();
          
          // Force keyboard to open on mobile
          if (inputRefs.current[0].type === 'tel') {
            inputRefs.current[0].click();
          }
          
          return true;
        }
        return false;
      };

      // Try immediately
      if (!focusFirstInput()) {
        // Try after a short delay
        setTimeout(() => {
          if (!focusFirstInput()) {
            // Try after a longer delay
            setTimeout(() => {
              focusFirstInput();
            }, 200);
          }
        }, 50);
      }
    }
  }, [open]);

  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    } else if (countdown === 0 && !canResend) {
      setCanResend(true);
    }
    return () => clearTimeout(timer);
  }, [countdown, canResend]);

  const handleChange = (index, value) => {
    if (value.length <= 1 && /^[0-9]*$/.test(value)) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      // Move to next input if value is entered
      if (value && index < 5) {
        inputRefs.current[index + 1].focus();
      }
    }
  };

  const handleKeyDown = (index, e) => {
    // Move to previous input on backspace if current input is empty
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1].focus();
    }
  };

  const handleVerify = () => {
    const otpString = otp.join('');
    onVerify(otpString);
  };

  const handleResendOTPWithClear = async () => {
    if (!canResend) return;
    
    try {

      if (mobileOtpVerifiedStatus || emailOtpVerifiedStatus) {
        setOtp(['', '', '', '', '', '']);
        if (inputRefs.current[0]) {
          inputRefs.current[0].focus();
        }
        setCountdown(60);
        setCanResend(false);
        handleResendOTP();
      }
    } catch (error) {
      console.error('Resend OTP failed:', error);
    }
  };

  const handleModalKeyDown = (e) => {
    if (e.key === 'Enter' && !otp.some(digit => !digit)) {
      handleVerify();
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Modal open={open} onClose={null} BackdropProps={{
      sx: {
        backdropFilter: 'blur(3px)',
        onClick: () => null
      }
    }}>
      <StyledOTPVerificationModal>
        <Box className="otp-modal-content" onKeyDown={handleModalKeyDown}>
        <Box className="circle-cross-icon-container" onClick={() => {onClose(); setOtp(['', '', '', '', '', '']); setCanResend(true);}}>
        <img src={circleCrossIcon} alt="circleCrossIcon" className="circle-cross-icon" />          
        </Box>
          <Typography className="otp-title">
            Enter OTP
          </Typography>
          <Typography className="otp-subtitle">
            Sent to {phoneNumber}
          </Typography>
          
          <Box className="otp-input-container">
            {otp.map((digit, index) => (
              <input
                key={index}
                type="tel"
                inputMode="numeric"
                pattern="[0-9]*"
                maxLength={1}
                value={digit}
                placeholder="|"
                ref={el => inputRefs.current[index] = el}
                onChange={e => handleChange(index, e.target.value)}
                onKeyDown={e => handleKeyDown(index, e)}
                className="otp-input"
                autoFocus={index === 0}
                autoComplete="one-time-code"
              />
            ))}
          </Box>
          <Box className="otp-input-container" >
          <Typography 
            className={`resend-otp ${!canResend ? 'disabled' : ''}`}
            onClick={handleResendOTPWithClear}
            sx={{
              cursor: canResend ? 'pointer' : 'default',
              '&:hover': canResend ? {
                textDecoration: 'underline'
              } : {
                textDecoration: 'none'
              }
            }}
          >
            {canResend 
              ? 'Resend OTP' 
              : <span style={{ fontFamily: 'Roboto', fontSize: '12px', fontWeight: '500', color: '#706E75', textDecoration: 'none' }}>Didn't receive code? <span style={{ color: '#333333' }}>Resend in {formatTime(countdown)}</span></span>
            }
          </Typography>
          </Box>
          <Button 
            className="verify-button"
            onClick={handleVerify}
            disabled={otp.some(digit => !digit)}
          >
            Verify
          </Button>
        </Box>
      </StyledOTPVerificationModal>
    </Modal>
  );
};

export default OTPVerificationModal; 