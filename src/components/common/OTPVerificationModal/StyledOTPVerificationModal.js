import { styled } from '@mui/material/styles';

const StyledOTPVerificationModal = styled('div')(({ theme }) => `
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 16px;
  padding: 24px;
  outline: none;
  min-width: 320px;
  @media (max-width: 480px) {
    width: 80%;
    min-width: 0px;
  }

  .otp-modal-content {
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .circle-cross-icon-container {
    position: absolute;
    top: 0;
    right: 0;
  }

  .otp-title {
    font-family: 'Roboto', sans-serif;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #000000;
    text-align: center;
    line-height: 28px;
  }

  .otp-subtitle {
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-size: 16px;
    color: #6B6B6B;
    margin-bottom: 24px;
    line-height: 28px;
    text-align: center;
    word-break: break-all;
  }

  .otp-input-container {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    justify-content: center;
  }

  .otp-input {
    width: 40px;
    height: 40px;
    border: 1px solid rgba(255, 57, 81, 0.05);
    border-radius: 10px;
    text-align: center;
    outline: none;
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    line-height: 100%;
    font-size: 16px;
    color: ${theme.palette.custom.placeholder};
    position: relative;
    background: transparent;

    &::placeholder {
      color: #706E75;
      opacity: 1;
    }

    &:focus {
      border-color: #DB0000;
    }

    &::-webkit-inner-spin-button,
    &::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }
  .otp-input-container {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    justify-content: flex-end;
  }

  .resend-otp {
    width: fit-content;
    color: #0083E7;
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-size: 12px;
    line-height: 100%;
    cursor: pointer;
    margin-bottom: 24px;
    text-decoration: none;
    text-align: right;
    cursor: pointer;

    // &:hover {
    //   text-decoration: underline;
    // }
  }

  .verify-button {
    background-color: #FF3951;
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    text-transform: none;
    font-family: 'Roboto', sans-serif;
    font-weight: 500;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0.05em;
    width: fit-content;
    margin: 0 auto;

    &.Mui-disabled {
      background-color: #E0E0E0;
      color: #666;
    }
  }

  @media (max-width: 480px) {
    .otp-input-container {
      gap: 3px;
    }
      .otp-input {
      width: 35px;
      height: 35px;
    }
  }
  @media (max-width: 375px) {
    .otp-input-container {
      gap: 4px;
    }
    .otp-input {
      width: 35px;
      height: 35px;
    }
  }
`); 

export default StyledOTPVerificationModal;
