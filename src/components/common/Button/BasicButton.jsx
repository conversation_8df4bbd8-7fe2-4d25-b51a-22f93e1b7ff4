import React from "react";
import { Box } from "@mui/material";

const BasicButton = ({
  children,
  color = "#0083E7",
  fontSize = "16px",
  lineHeight = "19px",
  onClick,
  disabled = false,
  sx = {},
  ...props
}) => {
  return (
    <Box
      component="button"
      onClick={onClick}
      disabled={disabled}
      sx={{
        background: "none",
        border: "none",
        padding: 0,
        cursor: disabled ? "not-allowed" : "pointer",
        fontFamily: "Roboto",
        fontWeight: 500,
        fontSize: fontSize,
        lineHeight: lineHeight,
        color: color,
        textDecoration: "none",
        transition: "text-decoration 0.2s ease",
        "&:hover": {
          textDecoration: disabled ? "none" : "underline",
        },
        "&:focus": {
          outline: "none",
        },
        ...sx,
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

export default BasicButton;
