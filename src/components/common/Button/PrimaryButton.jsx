import React from "react";
import { Button } from "@mui/material";

const PrimaryButton = ({
  children,
  onClick = () => {},
  background = "#FF3951",
  hoverBackground = "#FF3951",
  gradientBackground = "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
  color = "#FFFFFF",
  fontSize = "16px",
  fontWeight = 500,
  fontFamily = "Roboto",
  lineHeight = "19px",
  px = 5,
  py = "12.5px",
  borderRadius = "4px",
  width = { xs: "100%", sm: "fit-content" },
  sx = {},
  ...props
}) => {
  return (
    <Button
      variant="contained"
      sx={{
        width: width,
        background: background,
        color: color,
        fontFamily: fontFamily,
        fontWeight: fontWeight,
        fontSize: fontSize,
        lineHeight: lineHeight,
        px: px,
        py: py,
        borderRadius: borderRadius,
        textTransform: "none",
        transition: "all 0.3s ease",
        position: "relative",
        overflow: "hidden",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          height: "100%",
          width: "100%",
          background: gradientBackground,
          backgroundSize: "100% 200%",
          backgroundPosition: "left center",
          transition: "background-position 4s ease, opacity 0.5s ease",
          zIndex: -1,
          opacity: 0,
        },
        "&:hover": {
          background: hoverBackground,
          transform: "scale(1.02)",
          boxShadow: "none",
          "&::before": {
            opacity: 1,
            backgroundPosition: "left bottom",
          },
        },
        boxShadow: "none",
        ...sx,
      }}
      onClick={onClick}
      {...props}
    >
      {children}
    </Button>
  );
};

export default PrimaryButton;
