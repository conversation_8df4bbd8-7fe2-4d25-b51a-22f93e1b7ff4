import React from "react";
import { Button } from "@mui/material";

const SecondaryButton = ({
  children,
  onClick = () => {},
  borderColor = "#FF3951",
  hoverBackgroundColor = "#ff395021",
  color = "#FF3951",
  fontSize = "16px",
  fontWeight = 500,
  fontFamily = "Roboto",
  lineHeight = "19px",
  px = "38px",
  py = "11.5px",
  borderRadius = "4px",
  width = { xs: "100%", sm: "fit-content" },
  sx = {},
  ...props
}) => {
  return (
    <Button
      variant="outlined"
      sx={{
        width: width,
        border: `1px solid ${borderColor}`,
        color: color,
        fontFamily: fontFamily,
        fontWeight: fontWeight,
        lineHeight: lineHeight,
        fontSize: fontSize,
        px: px,
        py: py,
        borderRadius: borderRadius,
        textTransform: "none",
        transition: "all 0.3s ease",
        "&:hover": {
          backgroundColor: hoverBackgroundColor,
        },
        ...sx,
      }}
      onClick={onClick}
      {...props}
    >
      {children}
    </Button>
  );
};

export default SecondaryButton;
