import React from 'react';
import { Select, FormControl, InputLabel, MenuItem, FormHelperText } from '@mui/material';
import StyledCustomSelect from './StyledCustomSelect';
import { SelectDropdownIcon } from "../SvgIcon";

const CustomSelect = ({
  label,
  value,
  onChange,
  options,
  error,
  helperText,
  required = false,
  placeholder,
  ...props
}) => {
  const menuProps = {
    // sx: {
    //   "& .MuiPaper-root": {
    //     borderRadius: "10px !important",
    //   },
    // },
    // slotProps: {
    //   paper: {
    //     style: {
    //       borderRadius: "10px !important",
    //     },
    //   },
    // },
    PaperProps: {
      style: {
        maxHeight: 250,
      },
    },
    anchorOrigin: {
      vertical: "bottom",
      horizontal: "left",
    },
    transformOrigin: {
      vertical: "top",
      horizontal: "left",
    },
  };

  return (
    <StyledCustomSelect>
      <FormControl fullWidth error={error}>
        <InputLabel className="custom-label" required={required}>
          {label}
        </InputLabel>
        <Select
          value={value}
          onChange={onChange}
          label={label}
          className="custom-select"
          MenuProps={menuProps}
          IconComponent={SelectDropdownIcon}
          {...props}
        >
          {placeholder && (
            <MenuItem value="" disabled>
              {placeholder}
            </MenuItem>
          )}
          {options.map((option) => (
            <MenuItem
              key={option.value}
              value={option.value}
              title={option.label}
            >
              {option.label}
            </MenuItem>
          ))}
        </Select>
        <FormHelperText error={error}>
          {error ? helperText : helperText || ""}
        </FormHelperText>
      </FormControl>
    </StyledCustomSelect>
  );
};

export default CustomSelect; 