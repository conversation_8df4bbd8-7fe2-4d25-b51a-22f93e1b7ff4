import React from 'react';
import { FormControl, InputLabel, Select, MenuItem, FormHelperText } from '@mui/material';
import StyledCustomSelect from './StyledCustomSelect';
import dropdownIcon from '../../../assets/svg/dropdownIcon.svg';

const CustomSelect = ({
  label,
  value,
  onChange,
  options,
  error,
  helperText,
  required,
  disabled,
  placeholder,
  name,
  customHeight,
  customMarginBottom,
  customBG,
  customFontSize,
  customFontWeight,
  disableLabel = false,
  hidePlaceholderAsValue = false,
  ...props
}) => {
  const [selectOpen, setSelectOpen] = React.useState(false);

  return (
    <StyledCustomSelect
      customHeight={customHeight}
      customMarginBottom={customMarginBottom}
      customBG={customBG}
      customFontSize={customFontSize}
      customFontWeight={customFontWeight}
    >
      <FormControl fullWidth error={error}>
        {disableLabel ? null : (
          <InputLabel className="custom-label" required={required}>
            {label}
          </InputLabel>
        )}
        <Select
          value={value}
          onChange={onChange}
          className={`custom-select ${selectOpen ? "select-open" : ""}`}
          label={label}
          disabled={disabled}
          name={name}
          open={selectOpen}
          onOpen={() => setSelectOpen(true)}
          onClose={() => setSelectOpen(false)}
          IconComponent={() => (
            <img
              src={dropdownIcon}
              alt="dropdown"
              style={{
                width: "12px",
                height: "9px",
                marginRight: "10px",
                cursor: "pointer",
              }}
              onClick={() => setSelectOpen(true)}
            />
          )}
          MenuProps={{
            PaperProps: {
              sx: {
                borderRadius: "0px 0px 10px 10px",
                boxShadow: "rgba(0, 0, 0, 0.05) 0px 1px 2px 0px",
                background: "#fff",
                mt: 0,
                maxHeight: 200,
                overflowY: "auto",
                minWidth: "fit-content",
                width: "auto",
                border: "1px solid #FF39510D !important",
              },
              className: "custom-dropdown-box",
            },
            anchorOrigin: {
              vertical: "bottom",
              horizontal: "left",
            },
            transformOrigin: {
              vertical: "top",
              horizontal: "left",
            },
          }}
          sx={
            selectOpen
              ? {
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderBottom: "none !important",
                    outline: "none !important",
                  },
                  "& .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline":
                    {
                      borderColor: "#FF39510D",
                      background: "transparent !important",
                      outline: "none !important",
                    },
                  "& .MuiOutlinedInput-root:hover": {
                    background: "transparent !important",
                    outline: "none !important",
                  },
                }
              : {}
          }
          {...props}
        >
          {placeholder && (
            <MenuItem
              value=""
              disabled
              sx={{
                display: hidePlaceholderAsValue ? "none" : "block",
              }}
            >
              {placeholder}
            </MenuItem>
          )}
          {options.map((option, idx) => (
            <MenuItem
              key={option.value}
              value={option.value}
              sx={{
                borderBottom:
                  idx !== options.length - 1 ? "1px solid #F6F6F6" : "none",
                fontSize: "14px !important",
                lineHeight: "16px !important",
                fontWeight: 400,
                color: "#706E75",
                padding: "14px 18px",
                background: "transparent",
                "&:hover": {
                  background: "transparent",
                },
                "&.Mui-selected": {
                  background: "#FFF9F9",
                },
                "&.Mui-selected:hover": {
                  background: "#FFF9F9",
                },
              }}
            >
              {option.label}
            </MenuItem>
          ))}
        </Select>
        {helperText && <FormHelperText>{helperText}</FormHelperText>}
      </FormControl>
    </StyledCustomSelect>
  );
};

// CustomSelect.propTypes = {
//   label: PropTypes.string.isRequired,
//   value: PropTypes.any,
//   onChange: PropTypes.func.isRequired,
//   options: PropTypes.arrayOf(
//     PropTypes.shape({
//       value: PropTypes.any.isRequired,
//       label: PropTypes.string.isRequired,
//     })
//   ).isRequired,
//   error: PropTypes.bool,
//   helperText: PropTypes.string,
//   required: PropTypes.bool,
//   disabled: PropTypes.bool,
//   placeholder: PropTypes.string,
//   name: PropTypes.string,
// };

// CustomSelect.defaultProps = {
//   error: false,
//   required: false,
//   disabled: false,
//   value: '',
// };

export default CustomSelect; 