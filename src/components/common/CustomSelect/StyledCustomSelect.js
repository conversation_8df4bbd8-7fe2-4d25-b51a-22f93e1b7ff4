import { styled } from '@mui/material/styles';

const StyledCustomSelect = styled("div")(
  ({
    theme,
    customHeight,
    customMarginBottom,
    customBG,
    customFontSize,
    customFontWeight,
  }) => `
  .MuiFormControl-root {
    margin-bottom: ${customMarginBottom || "0px"};
    height: ${customHeight || "auto"};
  }

  .custom-label {
    font-family: 'Roboto', sans-serif;
    color: ${theme.palette.custom.placeholder};
    font-size: 14px;
    transform: translate(14px, 12px) scale(1);
    background-color: ${theme.palette.custom.white};
    padding: 0px 4px !important;

    &.MuiInputLabel-shrink {
      transform: translate(14px, -9px) scale(0.75);
    }

    & .MuiFormLabel-asterisk {
      color: ${theme.palette.custom.lightred};
    }

    &.Mui-focused {
      color: ${theme.palette.custom.placeholder};
      
    }
    
    &.Mui-error {
      color: ${theme.palette.custom.lightred};
    }
  }


  .custom-select {
    font-family: 'Roboto', sans-serif;
    font-size: ${customFontSize || "16px"};
    font-weight: ${customFontWeight || "400"};
    line-height: 100%;
    width: 100%;
    border-radius: 10px;
    
    background-color: ${customBG || theme.palette.custom.white};
    
    .MuiOutlinedInput-notchedOutline {
      border-color: rgba(255, 57, 81, 0.05) !important;
      border-radius: 10px !important;
      transition: all 0.3s ease;
    }

    .MuiSelect-select {
      padding: 8px 14px;
      min-height: 20px !important;
      height: 30px;
      display: flex;
      align-items: center;
      width: 100%;
    }

    &.Mui-focused .MuiOutlinedInput-notchedOutline {
      border-color: rgba(255, 57, 81, 0.05) !important;
    }

    &.Mui-error .MuiOutlinedInput-notchedOutline {
      border-color: ${theme.palette.custom.lightred};
    }
  }

  .MuiFormHelperText-root {
    font-family: 'Roboto', sans-serif;
    font-size: 10px;
    margin-top: 6px;
    margin-left: 2px;
    line-height: 10px;

    &.Mui-error {
      color: ${theme.palette.custom.lightred};
    }
  }

  .MuiSelect-icon {
    color: ${theme.palette.custom.placeholder};
    top: calc(50% - 10px);
  }

  .MuiMenuItem-root {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 16px;
    min-height: 36px;
  }
`
);

export default StyledCustomSelect; 