import React from "react";
import { Modal, Box, Typography, IconButton } from "@mui/material";
import { CloseOutline } from "../SvgIcon";
import SecondaryButton from "../Button/SecondaryButton";
import PrimaryButton from "../Button/PrimaryButton";

const ConfirmationModal = ({
  open,
  onClose,
  onConfirm,
  title = "Confirm Action",
  description = "Are you sure you want to proceed with this action?",
  confirmText = "Yes",
  cancelText = "Cancel",
}) => {
  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
  };

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      BackdropProps={{
        sx: {
          backdropFilter: "blur(16px)",
          background: "rgba(51, 51, 51, 0.4)",
        },
      }}
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "white",
          borderRadius: "20px",
          maxWidth: "400px",
          width: "calc(100vw - 80px)",
          outline: "none",
          boxShadow: 24,
          p: 0,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            p: "12px 16px",
            borderBottom: "1px solid rgba(0, 0, 0, 0.1)",
          }}
        >
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: "600",
              fontSize: "20px",
              lineHeight: "24px",
              color: "#333333",
            }}
          >
            {title}
          </Typography>
          <IconButton
            onClick={handleClose}
            sx={{
              p: 0,
              "&:hover": {
                backgroundColor: "rgba(0, 0, 0, 0.04)",
              },
            }}
          >
            <CloseOutline />
          </IconButton>
        </Box>

        {/* Content */}
        <Box sx={{ p: "32px 16px" }}>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: "400",
              fontSize: "16px",
              lineHeight: "24px",
              color: "#333333",
              mb: 4,
              textAlign: "center",
            }}
          >
            {description}
          </Typography>

          {/* Buttons */}
          <Box
            sx={{
              display: "flex",
              gap: 2,
              justifyContent: "center",
            }}
          >
            <SecondaryButton
              px={{ xs: "16px", sm: "38.5px" }}
              py={{ xs: "12px", sm: "11.5px" }}
              fontSize={{ xs: "14px", sm: "16px" }}
              onClick={handleClose}
            >
              {cancelText}
            </SecondaryButton>
            <PrimaryButton
              px={{ xs: "16px", sm: "40px" }}
              py={{ xs: "12px", sm: "12.5px" }}
              fontSize={{ xs: "14px", sm: "16px" }}
              onClick={handleConfirm}
            >
              {confirmText}
            </PrimaryButton>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default ConfirmationModal;
