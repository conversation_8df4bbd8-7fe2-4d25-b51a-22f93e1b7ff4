import { Box } from "@mui/material";
import React from "react";

const FlightIcon = ({ width = "18px", height = "18px", ...props }) => {
  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M7.92561 17.8448L12.1183 10.6686L15.4921 10.6686C15.8682 10.6747 16.2425 10.6202 16.5996 10.506C17.1849 10.3319 17.6656 9.92221 17.9209 9.38069C18.0264 9.14861 18.0264 8.884 17.9209 8.6519C17.6656 8.11038 17.1849 7.70069 16.5996 7.52664C16.2425 7.41147 15.8682 7.35609 15.4921 7.36224L12.1183 7.36224L7.92561 0.187785C7.86338 0.0796538 7.74614 0.0128424 7.61899 0.0119624L6.79018 0.0119624C6.68467 0.0119624 6.58546 0.0594349 6.51962 0.140312C6.45198 0.217674 6.42492 0.32141 6.44657 0.421628L7.84986 7.43877L3.52993 7.76316L2.05268 5.06253C1.98955 4.94825 1.8669 4.87704 1.73433 4.87968L0.750402 4.8999C0.643081 4.90341 0.542974 4.95352 0.477136 5.03704C0.412203 5.12055 0.389654 5.22868 0.415811 5.32979L1.15443 8.09816L0.491562 8.33903C0.197563 8.44013 0.00094948 8.71001 4.91977e-05 9.01419C-0.00355952 9.31662 0.190342 9.58738 0.482534 9.68935L1.15443 9.93022L0.406785 12.7029C0.378827 12.8031 0.399568 12.9095 0.461797 12.993C0.524927 13.08 0.62774 13.1319 0.736864 13.1328L1.72079 13.1556L1.71989 13.1547C1.85336 13.1574 1.97602 13.0871 2.03824 12.9728L3.51549 10.2721L7.84973 10.5895L6.45094 17.5994C6.4275 17.7005 6.45275 17.806 6.51858 17.8869C6.58442 17.9678 6.68453 18.0153 6.79005 18.0161L7.61885 18.0161C7.74511 18.017 7.86249 17.9521 7.92561 17.8448Z"
        fill="#FF3951"
      />
    </Box>
  );
};

export default FlightIcon;
