import { Box } from "@mui/material";
import React from "react";

const Customize = ({ width = "50px", height = "48px", sx = {}, ...props }) => {
  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 50 48"
      fill="none"
      sx={{ ...sx }}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_5278_18202)">
        <rect
          width="50"
          height="48"
          fill="url(#paint0_linear_5278_18202)"
          fillOpacity="0.1"
        />
        <path
          d="M19.5833 13.0909C19.5833 12.4884 19.0983 12 18.5 12C17.9017 12 17.4167 12.4884 17.4167 13.0909V14.1818H13.0833C12.485 14.1818 12 14.6703 12 15.2727C12 15.8752 12.485 16.3637 13.0833 16.3637H17.4167V17.4546C17.4167 18.0571 17.9017 18.5455 18.5 18.5455C19.0983 18.5455 19.5833 18.0571 19.5833 17.4546V13.0909Z"
          fill="url(#paint1_linear_5278_18202)"
        />
        <path
          d="M12 32.7273C12 32.1248 12.485 31.6364 13.0833 31.6364H21.75V30.5455C21.75 29.943 22.235 29.4546 22.8333 29.4546C23.4317 29.4546 23.9167 29.943 23.9167 30.5455V34.9092C23.9167 35.5117 23.4317 36.0001 22.8333 36.0001C22.235 36.0001 21.75 35.5117 21.75 34.9092V33.8182H13.0833C12.485 33.8182 12 33.3298 12 32.7273Z"
          fill="url(#paint2_linear_5278_18202)"
        />
        <path
          d="M13.0833 22.9089C12.485 22.9089 12 23.3973 12 23.9998C12 24.6023 12.485 25.0907 13.0833 25.0907H30.4167V26.1816C30.4167 26.7841 30.9017 27.2725 31.5 27.2725C32.0983 27.2725 32.5833 26.7841 32.5833 26.1816V21.818C32.5833 21.2155 32.0983 20.7271 31.5 20.7271C30.9017 20.7271 30.4167 21.2155 30.4167 21.818V22.9089H13.0833Z"
          fill="url(#paint3_linear_5278_18202)"
        />
        <path
          d="M21.75 14.1816H36.9167C37.515 14.1816 38 14.6701 38 15.2726C38 15.875 37.515 16.3635 36.9167 16.3635H21.75V14.1816Z"
          fill="url(#paint4_linear_5278_18202)"
        />
        <path
          d="M38.0002 32.7274C38.0002 32.1249 37.5152 31.6365 36.9168 31.6365H26.0835V33.8183H36.9168C37.5152 33.8183 38.0002 33.3299 38.0002 32.7274Z"
          fill="url(#paint5_linear_5278_18202)"
        />
        <path
          d="M34.75 22.9089H36.9167C37.515 22.9089 38 23.3973 38 23.9999C38 24.6024 37.515 25.0908 36.9167 25.0908H34.75V22.9089Z"
          fill="url(#paint6_linear_5278_18202)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_5278_18202"
          x1="0"
          y1="24"
          x2="50"
          y2="24"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF3951" />
          <stop offset="1" stopColor="#5530F9" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_5278_18202"
          x1="12"
          y1="15.2727"
          x2="19.5833"
          y2="15.2727"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF3951" />
          <stop offset="1" stopColor="#5530F9" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_5278_18202"
          x1="12"
          y1="32.7273"
          x2="23.9167"
          y2="32.7273"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF3951" />
          <stop offset="1" stopColor="#5530F9" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_5278_18202"
          x1="12"
          y1="23.9998"
          x2="32.5833"
          y2="23.9998"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF3951" />
          <stop offset="1" stopColor="#5530F9" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_5278_18202"
          x1="21.75"
          y1="15.2726"
          x2="38"
          y2="15.2726"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF3951" />
          <stop offset="1" stopColor="#5530F9" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_5278_18202"
          x1="26.0835"
          y1="32.7274"
          x2="38.0002"
          y2="32.7274"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF3951" />
          <stop offset="1" stopColor="#5530F9" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_5278_18202"
          x1="34.75"
          y1="23.9999"
          x2="38"
          y2="23.9999"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF3951" />
          <stop offset="1" stopColor="#5530F9" />
        </linearGradient>
        <clipPath id="clip0_5278_18202">
          <path
            d="M0 12C0 5.37258 5.37258 0 12 0H50V48H12C5.37258 48 0 42.6274 0 36V12Z"
            fill="white"
          />
        </clipPath>
      </defs>
    </Box>
  );
};

export default Customize;
