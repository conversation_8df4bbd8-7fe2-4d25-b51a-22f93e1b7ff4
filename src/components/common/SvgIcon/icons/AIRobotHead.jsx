import { Box } from "@mui/material";
import React from "react";

const AIRobotHead = ({ width = "40px", height = "40px", ...props }) => {
  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle
        cx="20"
        cy="20"
        r="20"
        fill="url(#paint0_linear_4278_40788)"
        fill-opacity="0.1"
      />
      <path
        d="M23.8076 29.3061H16.1631C15.6642 29.3061 15.2598 28.901 15.2598 28.4012V28.2688C15.2598 27.769 15.6641 27.3639 16.1631 27.3639H23.8076C24.3065 27.3639 24.7109 27.7689 24.7109 28.2688V28.4012C24.7108 28.901 24.3065 29.3061 23.8076 29.3061Z"
        fill="black"
      />
      <path
        d="M20.3123 14.9504H19.6523V11.6075H20.3123V14.9504Z"
        fill="black"
      />
      <path
        d="M9.88847 24.8993C8.8464 24.6515 8.20279 23.6046 8.45092 22.5609C8.69905 21.5173 9.74495 20.872 10.787 21.1198C11.8291 21.3675 12.4727 22.4144 12.2246 23.4581C11.9764 24.5018 10.9305 25.147 9.88847 24.8993Z"
        fill="url(#paint1_linear_4278_40788)"
      />
      <path
        d="M29.4858 24.7744C28.4412 24.5374 27.7869 23.4973 28.0241 22.4512C28.2614 21.405 29.3005 20.749 30.3451 20.9859C31.3896 21.2229 32.044 22.263 31.8067 23.3091C31.5694 24.3553 30.5303 25.0113 29.4858 24.7744Z"
        fill="url(#paint2_linear_4278_40788)"
      />
      <path
        d="M20.3685 14.2255C20.3685 14.2255 28.2535 14.1306 29.5017 20.9718C30.75 27.8132 24.0817 27.9409 22.1639 28.0705C21.2942 28.1292 19.4336 28.1353 17.7919 28.1068C14.157 28.0435 11.5887 27.3389 10.7147 23.9608C10.6128 23.5669 10.544 23.1625 10.5183 22.751C10.2871 19.0485 12.7891 14.0433 20.3685 14.2255Z"
        fill="url(#paint3_linear_4278_40788)"
      />
      <path
        d="M14.6468 24.6453L26.1022 24.8245C27.3029 24.8433 28.2915 23.8834 28.3103 22.6806C28.329 21.4778 27.3709 20.4875 26.1701 20.4688L14.7148 20.2897C13.5141 20.2709 12.5255 21.2308 12.5068 22.4335C12.4879 23.6362 13.4461 24.6265 14.6468 24.6453Z"
        fill="black"
      />
      <path
        d="M23.7268 22.4431C23.8009 22.8219 24.1675 23.0688 24.5456 22.9946C24.9238 22.9204 25.1703 22.5531 25.0962 22.1743C25.0221 21.7955 24.6554 21.5486 24.2772 21.6228C23.8992 21.697 23.6527 22.0643 23.7268 22.4431Z"
        fill="white"
      />
      <path
        d="M15.7229 22.3153C15.797 22.6941 16.1636 22.941 16.5417 22.8668C16.9199 22.7926 17.1664 22.4253 17.0923 22.0465C17.0182 21.6677 16.6516 21.4208 16.2735 21.495C15.8953 21.5691 15.6488 21.9363 15.7229 22.3153Z"
        fill="white"
      />
      <path
        d="M19.2138 12.3114C18.789 11.8859 18.789 11.196 19.2138 10.7705C19.6386 10.3449 20.3273 10.3449 20.7521 10.7705C21.1769 11.196 21.1769 11.8859 20.7521 12.3114C20.3273 12.737 19.6386 12.737 19.2138 12.3114Z"
        fill="url(#paint4_linear_4278_40788)"
      />
      <path
        d="M21.7192 22.1207C21.6673 22.1263 21.6931 22.3708 21.5409 22.6958C21.4672 22.8572 21.3447 23.0336 21.1705 23.1836C20.9975 23.3336 20.7672 23.4487 20.5149 23.475C20.2619 23.495 20.0146 23.4236 19.8174 23.3074C19.6189 23.1917 19.4664 23.0404 19.3648 22.895C19.1563 22.603 19.1375 22.358 19.0855 22.3618C19.0652 22.3622 19.044 22.424 19.044 22.5396C19.0432 22.655 19.0767 22.8238 19.1709 23.0117C19.2641 23.1993 19.4267 23.4002 19.6608 23.5577C19.8923 23.7149 20.2066 23.8218 20.544 23.7942C20.8808 23.7601 21.1703 23.5979 21.3697 23.4013C21.5715 23.204 21.695 22.977 21.7527 22.7756C21.8114 22.5738 21.8138 22.4016 21.7922 22.2882C21.7712 22.1743 21.7392 22.1174 21.7192 22.1207Z"
        fill="white"
      />
      <path
        d="M11.222 25.3109C11.2335 25.3043 11.0922 25.0893 10.9562 24.7014C10.8166 24.3156 10.6948 23.757 10.6364 23.1324C10.5741 22.5073 10.5835 21.9368 10.5995 21.5257C10.6091 21.3199 10.6187 21.1534 10.6279 21.0385C10.6361 20.9235 10.6386 20.8595 10.6342 20.859C10.6298 20.8585 10.6189 20.9214 10.6031 21.036C10.5862 21.1504 10.5697 21.317 10.5543 21.5232C10.5266 21.9354 10.5099 22.5091 10.5726 23.1386C10.6313 23.7677 10.7614 24.331 10.9136 24.7172C10.9876 24.9111 11.0639 25.0614 11.1223 25.1612C11.183 25.2601 11.2182 25.3133 11.222 25.3109Z"
        fill="black"
      />
      <path
        d="M29.4838 20.7484C29.4796 20.7498 29.4938 20.8125 29.5228 20.9241C29.538 20.9798 29.5557 21.0481 29.5718 21.1285C29.5885 21.2086 29.6119 21.299 29.6271 21.4009C29.6656 21.6032 29.6955 21.847 29.7135 22.1183C29.7275 22.3899 29.7273 22.6893 29.701 23.0021C29.6719 23.3148 29.6195 23.6096 29.5579 23.8746C29.4925 24.1385 29.4202 24.3732 29.3468 24.5656C29.3139 24.6632 29.275 24.748 29.2444 24.824C29.2144 24.9003 29.1849 24.9643 29.1602 25.0164C29.1121 25.1212 29.0871 25.1804 29.091 25.1825C29.095 25.1846 29.1273 25.1294 29.1826 25.0275C29.2109 24.9769 29.2438 24.914 29.2771 24.8387C29.311 24.7636 29.3531 24.6795 29.3889 24.582C29.4683 24.39 29.5459 24.1548 29.6152 23.8887C29.6806 23.622 29.7354 23.3239 29.7648 23.0079C29.7913 22.6916 29.7898 22.3888 29.7724 22.1146C29.751 21.8407 29.716 21.5952 29.6716 21.3924C29.6533 21.2902 29.6268 21.1999 29.6066 21.1201C29.5871 21.0401 29.5657 20.9724 29.5467 20.9175C29.5102 20.807 29.4881 20.747 29.4838 20.7484Z"
        fill="black"
      />
      <path
        d="M21.3659 14.7374C21.3659 14.7385 21.3755 14.7401 21.3943 14.7422C21.4169 14.7443 21.4443 14.7467 21.4776 14.7497C21.5508 14.7573 21.6593 14.7595 21.7978 14.7763C22.0754 14.8015 22.4763 14.8563 22.9615 14.9694C23.4467 15.0818 24.0142 15.2639 24.6116 15.5413C25.2089 15.8175 25.832 16.1964 26.4196 16.6783C27.0036 17.165 27.4932 17.7061 27.8774 18.2411C28.2625 18.7761 28.5488 19.2998 28.7509 19.7557C28.954 20.2115 29.0836 20.5952 29.161 20.8636C29.2037 20.9967 29.2265 21.1029 29.2478 21.1735C29.2572 21.2057 29.2648 21.2322 29.271 21.254C29.2767 21.2721 29.28 21.2812 29.2811 21.2809C29.2822 21.2807 29.2809 21.2711 29.2774 21.2525C29.2728 21.2302 29.2672 21.2032 29.2603 21.1704C29.243 21.0989 29.2241 20.9914 29.185 20.8568C29.1147 20.5856 28.9911 20.1981 28.7922 19.7374C28.5942 19.2767 28.3104 18.7473 27.9252 18.2067C27.541 17.666 27.0489 17.1194 26.4601 16.629C25.8678 16.1432 25.2389 15.763 24.6361 15.4877C24.0332 15.2115 23.4605 15.0327 22.9714 14.9254C22.4823 14.8173 22.0788 14.7691 21.7997 14.7514C21.6605 14.7385 21.5515 14.7402 21.478 14.7368C21.4445 14.7363 21.4169 14.7359 21.3943 14.7355C21.3756 14.7357 21.3659 14.7363 21.3659 14.7374Z"
        fill="white"
      />
      <path
        d="M29.0816 24.2144C29.089 24.2175 29.1365 24.1255 29.1937 23.9685C29.2509 23.8118 29.3144 23.5888 29.3541 23.3365C29.3933 23.0841 29.401 22.8522 29.3944 22.6855C29.3878 22.5186 29.3706 22.4165 29.3627 22.4171C29.3425 22.418 29.3704 22.8292 29.2908 23.3265C29.2148 23.8244 29.0628 24.2074 29.0816 24.2144Z"
        fill="white"
      />
      <path
        d="M31.1492 23.5783C31.1523 23.5809 31.1771 23.5584 31.2157 23.5119C31.2544 23.4657 31.3048 23.3934 31.3547 23.2969C31.455 23.1062 31.542 22.8031 31.4981 22.4694C31.4536 22.1358 31.2907 21.8658 31.1443 21.7079C31.071 21.6277 31.0036 21.5711 30.9542 21.5365C30.9048 21.5018 30.8749 21.4865 30.8727 21.4897C30.8656 21.499 30.9763 21.5741 31.11 21.7374C31.2433 21.8982 31.3923 22.1597 31.4347 22.4778C31.4765 22.7958 31.4009 23.0872 31.314 23.2773C31.2274 23.4697 31.14 23.5713 31.1492 23.5783Z"
        fill="white"
      />
      <path
        d="M19.9099 10.7814C19.9126 10.7935 20.0221 10.7531 20.1991 10.7743C20.3716 10.7924 20.6198 10.8921 20.767 11.1185C20.9113 11.3469 20.8984 11.6144 20.8431 11.7791C20.7886 11.9493 20.7064 12.0321 20.7162 12.0396C20.7189 12.0422 20.7418 12.0245 20.7756 11.9851C20.8096 11.9458 20.8514 11.8817 20.8857 11.7943C20.9553 11.6243 20.9792 11.3353 20.8208 11.0839C20.6586 10.8351 20.3865 10.7366 20.2031 10.7292C20.1095 10.7242 20.0339 10.7355 19.9841 10.7502C19.9342 10.7645 19.9087 10.7779 19.9099 10.7814Z"
        fill="white"
      />
      <path
        d="M9.20075 22.085C9.21414 22.0995 9.40537 21.9167 9.69418 21.7782C9.98163 21.6366 10.2433 21.5993 10.2402 21.5799C10.2407 21.5641 9.96566 21.5736 9.66628 21.7206C9.3658 21.865 9.18796 22.0755 9.20075 22.085Z"
        fill="white"
      />
      <path
        d="M16.5412 19.8887C16.5412 19.9064 18.0501 19.9207 19.911 19.9207C21.7727 19.9207 23.2812 19.9063 23.2812 19.8887C23.2812 19.871 21.7727 19.8567 19.911 19.8567C18.0501 19.8566 16.5412 19.871 16.5412 19.8887Z"
        fill="white"
      />
      <path
        d="M22.292 27.5863C22.2918 27.5885 22.3114 27.5916 22.349 27.5955C22.3941 27.5995 22.4483 27.6041 22.5134 27.6097C22.5848 27.6158 22.6723 27.6215 22.7742 27.6231C22.876 27.6253 22.992 27.6311 23.1202 27.6272C23.3766 27.6266 23.6819 27.6089 24.0193 27.5731C24.3561 27.5337 24.7249 27.4739 25.1064 27.3853C25.4875 27.294 25.8442 27.1826 26.1633 27.0673C26.4813 26.9485 26.7623 26.8278 26.992 26.7137C27.1085 26.66 27.2097 26.6029 27.2998 26.5555C27.3902 26.5086 27.4659 26.4644 27.5271 26.427C27.5829 26.3929 27.6293 26.3644 27.668 26.3408C27.6998 26.3205 27.7159 26.3089 27.7148 26.3071C27.7137 26.3051 27.6954 26.313 27.6615 26.3297C27.6213 26.3504 27.573 26.3753 27.5151 26.4052C27.4522 26.4392 27.375 26.48 27.2833 26.5237C27.192 26.5679 27.0899 26.622 26.9727 26.6728C26.7416 26.7812 26.4604 26.8968 26.1433 27.0117C25.8251 27.1233 25.4703 27.2322 25.0918 27.3228C24.7128 27.4109 24.3469 27.4721 24.0125 27.5144C23.6776 27.5532 23.3744 27.5753 23.1193 27.5819C22.9918 27.5888 22.8763 27.5861 22.7749 27.5873C22.6734 27.5891 22.5862 27.587 22.5148 27.5848C22.4495 27.584 22.3952 27.5832 22.3501 27.5827C22.312 27.5828 22.2921 27.5841 22.292 27.5863Z"
        fill="white"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4278_40788"
          x1="0"
          y1="20"
          x2="50.3546"
          y2="20"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FF3951" />
          <stop offset="1" stop-color="#5530F9" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_4278_40788"
          x1="12.2246"
          y1="23.4581"
          x2="8.45092"
          y2="22.5609"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FF3951" />
          <stop offset="1" stop-color="#5530F9" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_4278_40788"
          x1="31.8067"
          y1="23.3091"
          x2="28.0241"
          y2="22.4512"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FF3951" />
          <stop offset="1" stop-color="#5530F9" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_4278_40788"
          x1="29.6562"
          y1="21.172"
          x2="10.5038"
          y2="21.172"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FF3951" />
          <stop offset="1" stop-color="#5530F9" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_4278_40788"
          x1="20.7521"
          y1="12.3114"
          x2="19.2111"
          y2="10.7731"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FF3951" />
          <stop offset="1" stop-color="#5530F9" />
        </linearGradient>
      </defs>
    </Box>
  );
};

export default AIRobotHead;
