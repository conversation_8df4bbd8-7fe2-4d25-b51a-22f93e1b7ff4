import { Box } from "@mui/material";
import React from "react";

const ArrowRight = ({ width = "16px", height = "16px", sx = {}, ...props }) => {
  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 16 16"
      fill="none"
      sx={{ ...sx }}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M6 11.9998C6 11.9998 9.99999 9.05391 10 7.99984C10 6.94578 6 3.99988 6 3.99988"
        stroke="#1E3A8A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Box>
  );
};

export default ArrowRight;
