import { Box } from "@mui/material";
import React from "react";

const EditIcon = ({
  width = "14px",
  height = "14px",
  color = "#FF3951",
  disabled = false,
  sx = {},
  ...props
}) => {
  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 14 14"
      fill="none"
      sx={
        !disabled
          ? {
              cursor: "pointer",
              transition: "transform 0.2s ease-in-out",
              "&:hover": {
                transform: "scale(1.1)",
              },
              ...sx,
            }
          : {
              opacity: 0.5,
              cursor: "not-allowed",
              ...sx,
            }
      }
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_5363_10846)">
        <path
          d="M6.41699 2.3335H2.33366C2.02424 2.3335 1.72749 2.45641 1.5087 2.6752C1.28991 2.894 1.16699 3.19074 1.16699 3.50016V11.6668C1.16699 11.9762 1.28991 12.273 1.5087 12.4918C1.72749 12.7106 2.02424 12.8335 2.33366 12.8335H10.5003C10.8097 12.8335 11.1065 12.7106 11.3253 12.4918C11.5441 12.273 11.667 11.9762 11.667 11.6668V7.5835"
          stroke={disabled ? "#D1D5DB" : color}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10.792 1.45814C11.0241 1.22608 11.3388 1.0957 11.667 1.0957C11.9952 1.0957 12.3099 1.22608 12.542 1.45814C12.7741 1.6902 12.9044 2.00495 12.9044 2.33314C12.9044 2.66133 12.7741 2.97608 12.542 3.20814L7.00033 8.74981L4.66699 9.33314L5.25033 6.99981L10.792 1.45814Z"
          stroke={disabled ? "#D1D5DB" : color}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_5363_10846">
          <rect width="14" height="14" fill="white" />
        </clipPath>
      </defs>
    </Box>
  );
};

export default EditIcon;
