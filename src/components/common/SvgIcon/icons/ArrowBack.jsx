import { Box } from "@mui/material";
import React from "react";

const ArrowBack = ({
  width = "34px",
  height = "34px",
  sx = {},
  disabled = false,
  ...props
}) => {
  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 42 42"
      fill="none"
      sx={{
        cursor: disabled ? "not-allowed" : "pointer",
        opacity: disabled ? 0.5 : 1,
        transition: "all 0.2s ease-in-out",
        "&:hover": !disabled && {
          transform: "scale(1.05)",
          filter: "drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15))",
        },
        ...sx,
      }}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g filter="url(#filter0_d_256_2730)">
        <circle cx="21" cy="21" r="16.5" fill="white" stroke="white" />
        <path
          d="M30 22C30.5523 22 31 21.5523 31 21C31 20.4477 30.5523 20 30 20L30 22ZM12.2929 20.2929C11.9024 20.6834 11.9024 21.3166 12.2929 21.7071L18.6569 28.0711C19.0474 28.4616 19.6805 28.4616 20.0711 28.0711C20.4616 27.6805 20.4616 27.0474 20.0711 26.6569L14.4142 21L20.0711 15.3431C20.4616 14.9526 20.4616 14.3195 20.0711 13.9289C19.6805 13.5384 19.0474 13.5384 18.6569 13.9289L12.2929 20.2929ZM30 21L30 20L13 20L13 21L13 22L30 22L30 21Z"
          fill={disabled ? "#CCCCCC" : "#FF3951"}
        />
      </g>
      <defs>
        <filter
          id="filter0_d_256_2730"
          x="0"
          y="0"
          width="42"
          height="42"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_256_2730"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_256_2730"
            result="shape"
          />
        </filter>
      </defs>
    </Box>
  );
};

export default ArrowBack;
