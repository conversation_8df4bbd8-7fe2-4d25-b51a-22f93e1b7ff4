import React from "react";

const UserRead = ({
  width = "24",
  height = "24",
  color = "#FF3951",
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M16.0975 8.00171H6.40609C4.85013 8.00171 3.56091 9.15756 3.40532 10.7135L3.00521 14.4034C2.96076 14.759 3.20526 15.0924 3.56091 15.1813C3.96101 15.2702 4.49449 15.3369 5.02796 15.4036L5.36138 21.2718C5.3836 21.7608 5.80593 22.1387 6.29495 22.1387H9.14012C9.62914 22.1387 10.0515 21.7608 10.0737 21.2718L10.5627 11.2025C10.5627 10.6913 10.985 10.2912 11.4741 10.2912H15.5862C15.7641 10.2912 15.9419 10.18 16.0086 10.0022L16.5198 8.62409C16.6309 8.3129 16.4087 8.00171 16.0975 8.00171Z"
        fill={color}
      />
      <path
        d="M7.71761 7.17912C9.424 7.17912 10.8073 5.79583 10.8073 4.08944C10.8073 2.38305 9.424 0.999756 7.71761 0.999756C6.01123 0.999756 4.62793 2.38305 4.62793 4.08944C4.62793 5.79583 6.01123 7.17912 7.71761 7.17912Z"
        fill={color}
      />
      <path
        d="M17.3867 14.0699H20.4097C20.7653 14.0699 21.0765 13.7809 21.0765 13.403C21.0765 13.0252 20.7876 12.7362 20.4097 12.7362H17.3867C17.031 12.7362 16.7198 13.0252 16.7198 13.403C16.7198 13.7809 17.031 14.0699 17.3867 14.0699Z"
        fill={color}
      />
      <path
        d="M14.6527 12.1584L13.6302 13.2476L13.319 12.9142C13.0745 12.6474 12.6521 12.6252 12.3854 12.8697C12.1187 13.1142 12.0964 13.5366 12.341 13.8033L13.1412 14.648C13.2745 14.7813 13.4301 14.848 13.6302 14.848C13.808 14.848 13.9858 14.7813 14.1192 14.648L15.6307 13.0253C15.8752 12.7586 15.853 12.3362 15.5862 12.0917C15.3195 11.8695 14.8972 11.8917 14.6527 12.1584Z"
        fill={color}
      />
      <path
        d="M20.4097 16.2928H17.3867C17.031 16.2928 16.7198 16.5818 16.7198 16.9597C16.7198 17.3153 17.0088 17.6265 17.3867 17.6265H20.4097C20.7653 17.6265 21.0765 17.3376 21.0765 16.9597C21.0543 16.5818 20.7653 16.2928 20.4097 16.2928Z"
        fill={color}
      />
      <path
        d="M14.6527 15.6919L13.6302 16.781L13.319 16.4476C13.0745 16.1809 12.6521 16.1587 12.3854 16.4032C12.1187 16.6477 12.0964 17.07 12.341 17.3367L13.1412 18.1814C13.2745 18.3148 13.4301 18.3815 13.6302 18.3815C13.808 18.3815 13.9858 18.3148 14.1192 18.1814L15.6307 16.5588C15.8752 16.292 15.853 15.8697 15.5862 15.6252C15.3195 15.4029 14.8972 15.4251 14.6527 15.6919Z"
        fill={color}
      />
      <path
        d="M20.4097 19.8268H17.3867C17.031 19.8268 16.7198 20.1157 16.7198 20.4936C16.7198 20.8493 17.0088 21.1605 17.3867 21.1605H20.4097C20.7653 21.1605 21.0765 20.8715 21.0765 20.4936C21.0543 20.1157 20.7653 19.8268 20.4097 19.8268Z"
        fill={color}
      />
      <path
        d="M14.6527 19.2269L13.6302 20.316L13.319 20.0048C13.0745 19.7381 12.6521 19.7159 12.3854 19.9604C12.1187 20.2049 12.0964 20.6272 12.341 20.894L13.1412 21.7386C13.2745 21.872 13.4301 21.9387 13.6302 21.9387C13.808 21.9387 13.9858 21.872 14.1192 21.7386L15.6307 20.116C15.8752 19.8492 15.853 19.4269 15.5862 19.1824C15.3195 18.9601 14.8972 18.9601 14.6527 19.2269Z"
        fill={color}
      />
    </svg>
  );
};

export default UserRead;
