import React from "react";

const PropertyRules = ({
  width = "24",
  height = "24",
  color = "#FF3951",
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M15.9996 7.25244V12.4546H8.66663V7.25244L12.3326 3.86475L15.9996 7.25244Z"
        fill={color}
        stroke={color}
      />
      <path
        d="M17.2347 6.20422L17.1273 6.32336L12.6722 2.21301L12.3334 1.90051L11.9945 2.21301L7.53845 6.32336L7.43103 6.20422L12.3334 1.67981L17.2347 6.20422Z"
        fill={color}
        stroke={color}
      />
      <path
        d="M20.1377 15.0347V15.1841H4.5V15.0347H20.1377Z"
        fill={color}
        stroke={color}
      />
      <path
        d="M20.1377 18.1956V18.345H4.5V18.1956H20.1377Z"
        fill={color}
        stroke={color}
      />
      <path
        d="M20.1666 22.822V22.9714H11.1666V22.822H20.1666Z"
        fill={color}
        stroke={color}
      />
    </svg>
  );
};

export default PropertyRules;
