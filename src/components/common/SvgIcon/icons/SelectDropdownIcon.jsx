import { Box } from "@mui/material";
import React from "react";

const SelectDropdownIcon = ({
  width = "24px",
  height = "24px",
  sx = {},
  ...props
}) => {
  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      sx={sx}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M19 10L12 17L5 10"
        stroke="#333333"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Box>
  );
};

export default SelectDropdownIcon;
