import { Box } from "@mui/material";
import React from "react";

const UpgradeBooking = ({
  width = "40px",
  height = "40px",
  active = false,
  sx = {},
  ...props
}) => {
  const fillColor = active ? "#1E3A8A" : "#333333";

  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      sx={
        active
          ? {
              cursor: "pointer",
              transition: "transform 0.2s ease-in-out",
              "&:hover": { transform: "scale(1.1)" },
              ...sx,
            }
          : { ...sx }
      }
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle cx="20" cy="20" r="20" fill="white" />
      <path
        d="M22.4192 19.677L22.1369 19.9996C21.5724 20.685 20.645 21.0883 19.637 21.0883C18.6289 21.0883 17.6612 20.685 17.0967 19.9996L16.8145 19.677L16.8548 19.4351C17.5806 20.2012 18.5483 20.7254 19.5967 20.7254C20.6853 20.7254 21.6127 20.2012 22.3385 19.4351L22.4192 19.677Z"
        fill={fillColor}
      />
      <path
        d="M16.4921 21.8544C17.7017 23.3463 18.3469 25.0399 18.3469 26.7334V29.9994H10.4035C10.1615 29.9994 10.0002 29.7978 10.0002 29.5962V25.6044C10.0002 22.5399 14.9598 21.8141 16.4517 21.6528V21.7738L16.4921 21.8544Z"
        fill={fillColor}
      />
      <path
        d="M29.3145 25.5641V29.5559C29.3145 29.7979 29.1129 29.9592 28.9112 29.9592H20.8872V26.6931C20.8872 24.9996 21.5324 23.3061 22.7823 21.8142L22.863 21.6932V21.6125C24.3549 21.7738 29.3145 22.4996 29.3145 25.5641Z"
        fill={fillColor}
      />
      <path
        d="M22.4595 20.2417C21.1289 21.8949 18.1451 21.8546 16.8145 20.2417V21.5723C18.0241 23.0239 18.7902 24.7981 18.7902 26.6932V29.9593H20.4837V26.6932C20.4837 24.7981 21.2499 23.0239 22.4595 21.5723V20.2417ZM19.637 28.7899C19.3547 28.7899 19.1128 28.548 19.1128 28.2657C19.1128 27.9835 19.3547 27.7416 19.637 27.7416C19.9192 27.7416 20.1612 27.9835 20.1612 28.2657C20.1612 28.548 19.9192 28.7899 19.637 28.7899ZM19.637 26.6125C19.3547 26.6125 19.1128 26.3706 19.1128 26.0884C19.1128 25.8061 19.3547 25.5642 19.637 25.5642C19.9192 25.5642 20.1612 25.8061 20.1612 26.0884C20.1612 26.3706 19.9192 26.6125 19.637 26.6125ZM19.637 24.4352C19.3547 24.4352 19.1128 24.1932 19.1128 23.911C19.1128 23.6287 19.3547 23.3868 19.637 23.3868C19.9192 23.3868 20.1612 23.6287 20.1612 23.911C20.1612 24.1932 19.9192 24.4352 19.637 24.4352Z"
        fill={fillColor}
      />
      <path
        d="M24.1932 16.3304C24.0722 16.8546 23.7093 17.1772 23.3867 17.0965C22.7416 18.8707 21.3303 20.3223 19.6368 20.3223C17.9433 20.3223 16.532 18.8707 15.8869 17.0965C15.5643 17.1772 15.2417 16.8546 15.0804 16.3304C14.9595 15.8062 15.0804 15.3627 15.403 15.282C15.4433 15.282 15.524 15.282 15.5643 15.282C15.5643 15.2417 15.5643 15.2014 15.5643 15.1611C15.5643 14.9998 15.5643 14.7982 15.6046 14.6369L15.7659 14.5966C17.0562 14.3143 18.3465 14.1934 19.6771 14.1934C21.0077 14.1934 22.298 14.3143 23.5883 14.5966L23.7496 14.6369C23.7899 14.7982 23.7899 14.9595 23.7899 15.1611C23.7899 15.2014 23.7899 15.2417 23.7899 15.282C23.8303 15.282 23.9109 15.282 23.9512 15.282C24.1932 15.3627 24.3141 15.8466 24.1932 16.3304Z"
        fill={fillColor}
      />
      <path
        d="M23.6287 11.1693V12.5806C22.3384 12.2983 21.0078 12.1774 19.6369 12.1774C18.2659 12.1774 16.9353 12.2983 15.645 12.5806V11.1693C15.645 11.008 15.766 10.8468 15.887 10.7661C17.0966 10.2823 18.3466 10 19.6369 10C20.8869 10 22.1772 10.2419 23.3868 10.7661C23.5481 10.8468 23.6287 11.008 23.6287 11.1693Z"
        fill={fillColor}
      />
      <path
        d="M23.5481 13.9111L23.6287 13.9514V14.2336C22.2981 13.9514 20.9675 13.8304 19.6369 13.8304C18.3063 13.8304 16.9353 13.9917 15.645 14.2336V13.9111L15.7257 13.8707C17.016 13.5885 18.3466 13.4675 19.6369 13.4675C20.9675 13.5079 22.2578 13.6288 23.5481 13.9111Z"
        fill={fillColor}
      />
      <path
        d="M15.645 13.0241V13.5483C16.9756 13.266 18.3063 13.1451 19.6369 13.1451C20.9675 13.1451 22.3384 13.3063 23.6287 13.5483V13.0241C22.2981 12.7418 20.9675 12.6209 19.6369 12.6209C18.3063 12.5806 16.9353 12.7418 15.645 13.0241Z"
        fill={fillColor}
      />
    </Box>
  );
};

export default UpgradeBooking;
