import { Box } from "@mui/material";
import React from "react";

const Location = ({
  width = "46px",
  height = "46px",
  active = false,
  sx = {},
  ...props
}) => {
  const fillColor = active ? "#1E3A8A" : "#333333";

  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 46 46"
      fill="none"
      sx={
        active
          ? {
              cursor: "pointer",
              transition: "transform 0.2s ease-in-out",
              "&:hover": { transform: "scale(1.1)" },
              ...sx,
            }
          : { ...sx }
      }
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle cx="23" cy="23" r="23" fill="white" />
      <path
        d="M23 11C18.8946 11 15.556 14.3519 15.556 18.4705C15.556 18.9261 15.5741 19.4624 15.6874 19.8855C16.1057 22.3263 17.3917 24.326 18.8008 25.824C20.217 27.3294 21.7489 28.3347 22.754 28.812C22.9095 28.8867 23.0903 28.8867 23.2458 28.812C24.251 28.3347 25.783 27.3294 27.199 25.824C28.608 24.3258 29.8941 22.3262 30.3124 19.8855C30.4257 19.4624 30.4438 18.9261 30.4438 18.4705C30.4438 14.3519 27.1054 11 23 11ZM23 15.0415C24.8863 15.0415 26.4278 16.5831 26.4278 18.4705C26.4278 20.3568 24.8862 21.8983 23 21.8983C21.1138 21.8983 19.5722 20.3567 19.5722 18.4705C19.5722 16.583 21.1138 15.0415 23 15.0415ZM23 16.1841C21.7308 16.1841 20.7148 17.2014 20.7148 18.4706C20.7148 19.7398 21.7308 20.7558 23 20.7558C24.2692 20.7558 25.2852 19.7397 25.2852 18.4706C25.2852 17.2014 24.2692 16.1841 23 16.1841ZM19.5722 30.4284C17.4437 30.4284 15.6526 31.8832 15.145 33.8561H11.5725C11.2568 33.8561 11 34.1129 11 34.4275C11 34.7432 11.2567 35 11.5725 35H34.4275C34.7432 35 35 34.7433 35 34.4275C35 34.1129 34.7433 33.8561 34.4275 33.8561H30.855C30.3476 31.8831 28.5565 30.4284 26.4278 30.4284H19.5722Z"
        fill={fillColor}
      />
    </Box>
  );
};

export default Location;
