import { Box } from "@mui/material";
import React from "react";

const Flight = ({
  width = "40px",
  height = "40px",
  active = false,
  sx = {},
  ...props
}) => {
  const fillColor = active ? "#1E3A8A" : "#333333";

  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      sx={
        active
          ? {
              cursor: "pointer",
              transition: "transform 0.2s ease-in-out",
              "&:hover": { transform: "scale(1.1)" },
              ...sx,
            }
          : { ...sx }
      }
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle cx="20" cy="20" r="20" fill="white" />
      <path
        d="M27.5784 14.3442L21.6082 16.8545L15.6379 14.7345C15.487 14.686 15.3371 14.686 15.2117 14.7345L14.0574 15.1001C13.7566 15.1972 13.7066 15.5875 13.9575 15.7579L18.0464 18.3415L12.9298 20.4862L10.2204 18.7071C10.0696 18.609 9.86869 18.5605 9.69328 18.609L9.41696 18.6576C9.09066 18.7309 8.91528 19.0479 9.0407 19.3401L10.4203 22.533C10.9475 23.7515 12.6279 23.8486 13.8822 23.3126L20.5793 20.8272L19.3251 27.5537C19.2752 27.8707 19.626 28.1144 19.9023 27.944L20.8802 27.3595C21.0056 27.2862 21.081 27.1891 21.131 27.0673L24.9435 19.0987L31.7418 15.6869C32.0181 15.5403 32.0926 15.1748 31.8672 14.9558C30.8393 13.9306 29.0081 13.7355 27.5783 14.3447L27.5784 14.3442Z"
        fill={fillColor}
      />
    </Box>
  );
};

export default Flight;
