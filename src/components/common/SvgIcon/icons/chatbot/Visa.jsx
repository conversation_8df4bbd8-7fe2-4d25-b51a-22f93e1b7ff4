import { Box } from "@mui/material";
import React from "react";

const Visa = ({
  width = "40px",
  height = "40px",
  active = false,
  sx = {},
  ...props
}) => {
  const fillColor = active ? "#1E3A8A" : "#333333";

  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      sx={
        active
          ? {
              cursor: "pointer",
              transition: "transform 0.2s ease-in-out",
              "&:hover": { transform: "scale(1.1)" },
              ...sx,
            }
          : { ...sx }
      }
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle cx="20" cy="20" r="20" fill="white" />
      <path
        d="M21.0253 28.8642C20.5784 28.7133 20.2782 28.3096 20.2773 27.8569V27.4996H12.4239C12.22 27.4961 12.0574 27.3371 12.0574 27.1424C12.0574 26.9486 12.2201 26.7896 12.4239 26.7852H20.4791C20.7446 26.3431 21.2383 26.0698 21.7731 26.0707H22.9438C23.0429 25.7001 23.2954 25.3831 23.6432 25.1928V23.3031C22.9428 22.7583 22.5352 21.9429 22.5324 21.0811C22.5296 20.2184 22.9316 19.4003 23.6282 18.852C23.6899 18.8029 23.7554 18.7564 23.8227 18.7127V13.1872C23.8208 12.596 23.3197 12.1173 22.7007 12.1155H22.1846V11.1331C22.1799 10.7893 22.0134 10.4651 21.732 10.2499C21.4496 10.0355 21.0822 9.95335 20.7297 10.0257L11.0063 12.1262C10.4369 12.1843 10.0049 12.6406 10.0002 13.1872V28.9286C10.0021 29.5198 10.5033 29.9985 11.1223 30.0003H21.2272C21.0467 29.6485 20.9767 29.2536 21.0253 28.8642ZM19.9033 26.0706H13.92C13.7153 26.068 13.5507 25.909 13.5507 25.7134C13.5507 25.5187 13.7153 25.3598 13.92 25.3562H19.9033C20.108 25.3598 20.2717 25.5187 20.2717 25.7134C20.2717 25.909 20.1081 26.068 19.9033 26.0706ZM20.8907 10.7222C21.0216 10.6945 21.159 10.7249 21.2647 10.8044C21.3703 10.8838 21.4339 11.0044 21.4367 11.133V12.1154H14.4362L20.8907 10.7222ZM21.3993 19.1876C21.3974 20.7183 20.5419 22.1321 19.1543 22.8966C17.7658 23.6611 16.0567 23.6611 14.6691 22.8966C13.2805 22.1321 12.425 20.7184 12.4241 19.1876C12.6718 13.5014 21.1525 13.5032 21.3993 19.1876Z"
        fill={fillColor}
      />
      <path
        d="M18.7217 22.3099C19.796 21.7437 20.5104 20.7104 20.6329 19.5449H19.5258C19.5052 20.5166 19.2285 21.4668 18.7217 22.3099Z"
        fill={fillColor}
      />
      <path
        d="M20.6329 18.8298C20.5085 17.6652 19.7942 16.6328 18.7217 16.0657C19.2275 16.9078 19.5043 17.859 19.5258 18.8298H20.6329Z"
        fill={fillColor}
      />
      <path
        d="M18.7789 19.5449H17.2866V22.6849C18.072 22.3705 18.6966 21.0845 18.7789 19.5449Z"
        fill={fillColor}
      />
      <path
        d="M15.1007 22.3099C14.5948 21.4669 14.3181 20.5166 14.2975 19.5449H13.1904C13.3129 20.7104 14.0273 21.7437 15.1007 22.3099Z"
        fill={fillColor}
      />
      <path
        d="M15.1007 16.0657C14.0291 16.6337 13.3148 17.6661 13.1904 18.8307H14.2975C14.319 17.8599 14.5948 16.9088 15.1007 16.0657Z"
        fill={fillColor}
      />
      <path
        d="M16.5373 22.6861V19.5461H15.0459C15.1272 21.0858 15.7518 22.3717 16.5373 22.6861Z"
        fill={fillColor}
      />
      <path
        d="M16.5382 18.8305V15.6914C15.7528 16.0093 15.1282 17.2954 15.0459 18.8314L16.5382 18.8305Z"
        fill={fillColor}
      />
      <path
        d="M17.2866 15.6914V18.8305H18.7789C18.6966 17.2944 18.0721 16.0083 17.2866 15.6914Z"
        fill={fillColor}
      />
      <path
        d="M29.2519 26.7862H27.3819V26.4289C27.3809 26.0351 27.0472 25.7162 26.6339 25.7145V23.1003C26.6385 22.9878 26.7021 22.8842 26.8022 22.8253C27.5315 22.3359 27.8887 21.4812 27.7129 20.6471C27.5371 19.8121 26.862 19.1565 25.9906 18.9743C25.3239 18.8404 24.6292 18.9985 24.099 19.4066C23.5501 19.8326 23.2425 20.4801 23.2697 21.1543C23.2977 21.8286 23.6558 22.452 24.2374 22.836C24.33 22.8932 24.387 22.9905 24.3908 23.0968V25.7152V25.7143C23.9775 25.7161 23.6437 26.035 23.6427 26.4288V26.786H21.7727C21.3603 26.7878 21.0256 27.1066 21.0247 27.5005V27.8577C21.0256 28.0551 21.192 28.215 21.3987 28.215H29.626C29.8326 28.215 29.999 28.0551 30 27.8577V27.5005C29.999 27.1066 29.6643 26.7879 29.2519 26.7862Z"
        fill={fillColor}
      />
      <path
        d="M21.7734 29.2855C21.7744 29.6793 22.1082 29.999 22.5215 29.9999H28.5047C28.917 29.999 29.2518 29.6793 29.2527 29.2855V28.9282H21.7734V29.2855Z"
        fill={fillColor}
      />
    </Box>
  );
};

export default Visa;
