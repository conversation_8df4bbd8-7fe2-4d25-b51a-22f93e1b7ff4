import { Box } from "@mui/material";
import React from "react";

const Copy = ({
  width = "24px",
  height = "24px",
  active = false,
  sx = {},
  ...props
}) => {
  const strokeColor = active ? "#1E3A8A" : "#0083E7";
  const rectFill = active ? "#1E3A8A" : "#FFF9F9";

  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      sx={
        active
          ? {
              cursor: "pointer",
              transition: "transform 0.2s ease-in-out",
              "&:hover": { transform: "scale(1.1)" },
              ...sx,
            }
          : { ...sx }
      }
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect width="24" height="24" rx="4" fill={rectFill} />
      <path
        d="M14.8 12.63V15.57C14.8 18.02 13.82 19 11.37 19H8.43C5.98 19 5 18.02 5 15.57V12.63C5 10.18 5.98 9.19995 8.43 9.19995H11.37C13.82 9.19995 14.8 10.18 14.8 12.63Z"
        stroke={strokeColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.0002 8.43V11.37C19.0002 13.82 18.0202 14.8 15.5702 14.8H14.8002V12.63C14.8002 10.18 13.8202 9.2 11.3702 9.2H9.2002V8.43C9.2002 5.98 10.1802 5 12.6302 5H15.5702C18.0202 5 19.0002 5.98 19.0002 8.43Z"
        stroke={strokeColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Box>
  );
};

export default Copy;
