import { Box } from "@mui/material";
import React from "react";

const Send = ({
  width = "40px",
  height = "40px",
  active = false,
  secondary = false,
  sx = {},
  ...props
}) => {
  const isSecondary = secondary;

  if (isSecondary) {
    const strokeColor = active ? "#FF3951" : "#b4b3b6";
    // Secondary variant - red background with white stroke
    return (
      <Box
        component={"svg"}
        width={width}
        height={height}
        viewBox="0 0 25 26"
        fill="none"
        sx={{
          cursor: "pointer",
          transition: "transform 0.2s ease-in-out",
          ...sx,
        }}
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          d="M0 13C0 6.09644 5.59644 0.5 12.5 0.5C19.4036 0.5 25 6.09644 25 13C25 19.9036 19.4036 25.5 12.5 25.5C5.59644 25.5 0 19.9036 0 13Z"
          fill={strokeColor}
        />
        <path
          d="M11.5716 8.30128L16.773 10.902C19.1063 12.0686 19.1063 13.9766 16.773 15.1433L11.5716 17.744C8.07162 19.494 6.64367 18.06 8.39367 14.566L8.92232 13.5148C9.056 13.2475 9.056 12.8039 8.92232 12.5365L8.39367 11.4792C6.64367 7.9853 8.0777 6.55127 11.5716 8.30128Z"
          stroke="white"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M9.09863 13.0229H12.3799"
          stroke="white"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </Box>
    );
  }

  // Primary variant - original design
  const strokeColor = active ? "white" : "#706E75";
  const rectFill = active ? "url(#paint0_linear_4278_40356)" : "#F6F6F6";

  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      sx={
        active
          ? {
              cursor: "pointer",
              transition: "transform 0.2s ease-in-out",
              "&:hover": { transform: "scale(1.05)" },
              ...sx,
            }
          : { ...sx }
      }
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect width="40" height="40" rx="20" fill={rectFill} />
      <path
        d="M15.0634 13.1052L24.3512 12.5477C28.5177 12.2976 30.0441 14.9414 27.7442 18.4246L22.6175 26.1894C19.1678 31.4143 16.0419 30.5696 15.6717 24.3282L15.5632 22.4487C15.5346 21.9713 15.1797 21.3567 14.7806 21.0932L13.2022 20.051C7.98223 16.6097 8.82205 13.4755 15.0634 13.1052Z"
        stroke={strokeColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.4137 21.6259L19.9603 19.0009"
        stroke={strokeColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      {active && (
        <defs>
          <linearGradient
            id="paint0_linear_4278_40356"
            x1="0"
            y1="20"
            x2="40"
            y2="20"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#FF3951" />
            <stop offset="1" stopColor="#5530F9" />
          </linearGradient>
        </defs>
      )}
    </Box>
  );
};

export default Send;
