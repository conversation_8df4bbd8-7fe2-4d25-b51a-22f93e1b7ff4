import { Box } from "@mui/material";
import React from "react";

const Sub = ({
  width = "16px",
  height = "16px",
  active = false,
  disabled = false,
  sx = {},
  ...props
}) => {
  let fillColor = "#706E75";
  if (disabled) {
    fillColor = "#D1D5DB"; // light gray for disabled
  } else if (active) {
    fillColor = "#1E3A8A";
  }

  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 16 16"
      fill="none"
      sx={{
        cursor: disabled ? "not-allowed" : "pointer",
        opacity: disabled ? 0.5 : 1,
        transition: "transform 0.2s ease-in-out",
        pointerEvents: disabled ? "none" : "auto",
        "&:hover": !disabled ? { transform: "scale(1.1)" } : {},
        ...sx,
      }}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect width="16" height="16" fill="#F9F9F9" />
      <path
        d="M10.6663 8.5H5.33301C5.05967 8.5 4.83301 8.27333 4.83301 8C4.83301 7.72667 5.05967 7.5 5.33301 7.5H10.6663C10.9397 7.5 11.1663 7.72667 11.1663 8C11.1663 8.27333 10.9397 8.5 10.6663 8.5Z"
        fill={fillColor}
      />
    </Box>
  );
};

export default Sub;
