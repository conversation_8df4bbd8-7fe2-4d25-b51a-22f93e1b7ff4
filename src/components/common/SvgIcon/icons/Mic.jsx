import { Box } from "@mui/material";
import React from "react";

const Mic = ({
  width = "24px",
  height = "24px",
  active = false,
  sx = {},
  ...props
}) => {
  const fillColor = active ? "#1E3A8A" : "#706E75";

  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      sx={
        active
          ? {
              cursor: "pointer",
              transition: "transform 0.2s ease-in-out",
              "&:hover": { transform: "scale(1.1)" },
              ...sx,
            }
          : { ...sx }
      }
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M19.12 9.11999C18.73 9.11999 18.42 9.42999 18.42 9.81999V11.4C18.42 14.94 15.54 17.82 12 17.82C8.45999 17.82 5.57999 14.94 5.57999 11.4V9.80999C5.57999 9.41999 5.26999 9.10999 4.87999 9.10999C4.48999 9.10999 4.17999 9.41999 4.17999 9.80999V11.39C4.17999 15.46 7.30999 18.81 11.3 19.17V21.3C11.3 21.69 11.61 22 12 22C12.39 22 12.7 21.69 12.7 21.3V19.17C16.68 18.82 19.82 15.46 19.82 11.39V9.80999C19.81 9.42999 19.5 9.11999 19.12 9.11999Z"
        fill={fillColor}
      />
      <path
        d="M12 2C9.56 2 7.58 3.98 7.58 6.42V11.54C7.58 13.98 9.56 15.96 12 15.96C14.44 15.96 16.42 13.98 16.42 11.54V6.42C16.42 3.98 14.44 2 12 2ZM13.31 8.95C13.24 9.21 13.01 9.38 12.75 9.38C12.7 9.38 12.65 9.37 12.6 9.36C12.21 9.25 11.8 9.25 11.41 9.36C11.09 9.45 10.78 9.26 10.7 8.95C10.61 8.64 10.8 8.32 11.11 8.24C11.7 8.08 12.32 8.08 12.91 8.24C13.21 8.32 13.39 8.64 13.31 8.95ZM13.84 7.01C13.75 7.25 13.53 7.39 13.29 7.39C13.22 7.39 13.16 7.38 13.09 7.36C12.39 7.1 11.61 7.1 10.91 7.36C10.61 7.47 10.27 7.31 10.16 7.01C10.05 6.71 10.21 6.37 10.51 6.27C11.47 5.92 12.53 5.92 13.49 6.27C13.79 6.38 13.95 6.71 13.84 7.01Z"
        fill={fillColor}
      />
    </Box>
  );
};

export default Mic;
