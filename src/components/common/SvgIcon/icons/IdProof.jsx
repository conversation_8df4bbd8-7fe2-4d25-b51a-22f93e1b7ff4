import React from "react";

const IdProof = ({
  width = "24",
  height = "24",
  color = "#FF3951",
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M5.76015 11.93C6.32572 12.4211 7.1278 12.4802 7.74896 12.1052C7.75089 12.1039 7.75282 12.1026 7.75475 12.1013C7.83573 12.0518 7.91253 11.9937 7.98644 11.9294C8.40226 11.5672 8.67026 11.0084 8.67026 10.3815C8.67026 9.29211 7.864 8.40552 6.87297 8.40552C5.88194 8.40552 5.07568 9.29211 5.07568 10.3815C5.07568 11.0091 5.34401 11.5679 5.76015 11.93Z"
        fill={color}
      />
      <path
        d="M8.28942 12.5842C8.28363 12.5884 8.2772 12.5913 8.27174 12.5955C8.17791 12.6646 8.07861 12.725 7.97642 12.7802C7.96871 12.7844 7.961 12.7883 7.95297 12.7925C7.62584 12.9641 7.26014 13.063 6.87292 13.063C6.48602 13.063 6.12129 12.9644 5.79448 12.7934C5.78806 12.7902 5.78131 12.7867 5.77488 12.7831C5.67077 12.7275 5.56954 12.6662 5.47442 12.5958C5.4696 12.5923 5.46446 12.59 5.45964 12.5865C4.47118 13.046 3.81885 14.1061 3.81885 15.2858V16.5214H9.93503V15.2858C9.93471 14.1039 9.28077 13.0428 8.28942 12.5842Z"
        fill={color}
      />
      <path
        d="M21.2364 5.2162H14.5447C14.5447 3.99412 13.5762 2.99988 12.3859 2.99988H11.5376C10.3473 2.99988 9.37875 3.99412 9.37875 5.2162H2.68704C2.30817 5.2162 2 5.53273 2 5.92156V19.0045C2 19.3934 2.30817 19.7099 2.68704 19.7099H21.2364C21.6153 19.7099 21.9235 19.3934 21.9235 19.0045V5.92156C21.9235 5.53273 21.6153 5.2162 21.2364 5.2162ZM11.5376 3.70523H12.3859C13.1973 3.70523 13.8577 4.38295 13.8577 5.2162H10.0658C10.0658 4.38295 10.7262 3.70523 11.5376 3.70523ZM10.6217 16.8737C10.6217 17.0687 10.4678 17.2265 10.2782 17.2265H3.47498C3.28538 17.2265 3.13146 17.0687 3.13146 16.8737V15.2856C3.13146 13.9234 3.8426 12.6885 4.94225 12.0657C4.59648 11.6049 4.38857 11.0194 4.38857 10.3815C4.38857 8.90301 5.50299 7.70021 6.87289 7.70021C8.24279 7.70021 9.35722 8.90301 9.35722 10.3815C9.35722 11.0178 9.14995 11.6023 8.80579 12.0625C9.90865 12.6846 10.622 13.9208 10.622 15.2859L10.6217 16.8737ZM20.5764 15.8855H13.7179C13.5283 15.8855 13.3744 15.7278 13.3744 15.5327C13.3744 15.3376 13.5283 15.1799 13.7179 15.1799H20.5761C20.7657 15.1799 20.9196 15.3376 20.9196 15.5327C20.9199 15.7278 20.766 15.8855 20.5764 15.8855ZM20.5764 12.816H13.7179C13.5283 12.816 13.3744 12.6583 13.3744 12.4632C13.3744 12.2681 13.5283 12.1104 13.7179 12.1104H20.5761C20.7657 12.1104 20.9196 12.2681 20.9196 12.4632C20.9199 12.6583 20.766 12.816 20.5764 12.816ZM20.5764 9.74622H13.7179C13.5283 9.74622 13.3744 9.58844 13.3744 9.39338C13.3744 9.19832 13.5283 9.04054 13.7179 9.04054H20.5761C20.7657 9.04054 20.9196 9.19832 20.9196 9.39338C20.9199 9.58844 20.766 9.74622 20.5764 9.74622Z"
        fill={color}
      />
    </svg>
  );
};

export default IdProof;
