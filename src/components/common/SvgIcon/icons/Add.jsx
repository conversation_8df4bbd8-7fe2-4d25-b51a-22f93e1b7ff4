import { Box } from "@mui/material";
import React from "react";

const Add = ({
  width = "16px",
  height = "16px",
  disabled = false,
  type = "primary",
  sx = {},
  ...props
}) => {
  let bgColor = "#F9F9F9";
  let fillColor = "#1E3A8A";

  if (disabled) {
    fillColor = "#D1D5DB"; // light gray for disabled
    bgColor = "#F9F9F9";
  } else if (type === "primary") {
    fillColor = "#F9F9F9";
    bgColor = "#1E3A8A";
  } else if (type === "secondary") {
    fillColor = "#1E3A8A";
    bgColor = "#F9F9F9";
  }

  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 16 16"
      fill="none"
      sx={
        !disabled
          ? {
              cursor: "pointer",
              transition: "transform 0.2s ease-in-out",
              "&:hover": {
                transform: "scale(1.1)",
              },
              ...sx,
            }
          : { ...sx }
      }
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect
        width="16"
        height="16"
        fill={bgColor}
        rx={type === "primary" ? "2" : "0"}
      />
      <path
        d="M10.6673 8.5H5.33398C5.06065 8.5 4.83398 8.27333 4.83398 8C4.83398 7.72667 5.06065 7.5 5.33398 7.5H10.6673C10.9407 7.5 11.1673 7.72667 11.1673 8C11.1673 8.27333 10.9407 8.5 10.6673 8.5Z"
        fill={fillColor}
      />
      <path
        d="M8 11.1668C7.72667 11.1668 7.5 10.9402 7.5 10.6668V5.3335C7.5 5.06016 7.72667 4.8335 8 4.8335C8.27333 4.8335 8.5 5.06016 8.5 5.3335V10.6668C8.5 10.9402 8.27333 11.1668 8 11.1668Z"
        fill={fillColor}
      />
    </Box>
  );
};

export default Add;
