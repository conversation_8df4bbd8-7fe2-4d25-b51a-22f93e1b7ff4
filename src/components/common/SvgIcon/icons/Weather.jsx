import { Box } from "@mui/material";
import React from "react";

export const RainThunder = ({
  width = "128px",
  height = "128px",
  sx = {},
  ...props
}) => {
  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 128 128"
      fill="none"
      sx={sx}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M89.2916 58.3905H82.2735C80.9695 58.3905 79.8148 59.2329 79.4167 60.4747L72.8768 80.876C72.2562 82.8121 73.7005 84.7918 75.7336 84.7918H79.3155C80.4605 84.7918 81.3717 85.7511 81.3129 86.8946L80.204 108.431C80.1763 108.969 80.9008 109.165 81.1476 108.686L96.5345 78.8757C97.5651 76.879 96.1156 74.4997 93.8686 74.4997H90.8735C89.5096 74.4997 88.5458 73.1644 88.9753 71.8699L92.139 62.3353C92.7833 60.3934 91.3376 58.3905 89.2916 58.3905Z"
        fill="#FFD400"
      />
      <path
        d="M98.1803 82.9112L100.241 78.9196C102.062 75.3909 99.5004 71.1862 95.5293 71.1862H92.6932L95.7252 62.0484C96.8639 58.6167 94.309 55.0769 90.6932 55.0769H81.5351C79.2305 55.0769 77.1899 56.5657 76.4864 58.7603L69.2978 81.1852C69.1122 81.7641 69.0311 82.3452 69.0418 82.9112L33.1834 82.9112C19.275 82.9112 8 71.6362 8 57.7278C8 43.8194 19.275 32.5444 33.1834 32.5444C36.1992 32.5444 39.0912 33.0745 41.7712 34.0466C47.059 21.1128 59.7671 12 74.6036 12C94.1852 12 110.059 27.874 110.059 47.4556C110.059 48.3182 110.028 49.1737 109.968 50.0208C115.889 52.7474 120 58.7339 120 65.6805C120 75.1967 112.285 82.9112 102.769 82.9112H98.1803Z"
        fill="#8f8f9261"
      />
      <path
        d="M68.4334 109.963L64.9943 102.793L61.5546 109.964C61.2134 110.562 61.018 111.256 61.018 111.997C61.018 114.234 62.7983 116.047 64.9943 116.047C67.1904 116.047 68.9706 114.234 68.9706 111.997C68.9706 111.256 68.775 110.561 68.4334 109.963Z"
        fill="#64D2FF"
      />
      <path
        d="M56.505 96.7097L53.0653 89.5384L49.6261 96.7088C49.2846 97.3066 49.0889 98.0015 49.0889 98.7429C49.0889 100.98 50.8692 102.793 53.0653 102.793C55.2613 102.793 57.0416 100.98 57.0416 98.7429C57.0416 98.0018 56.8462 97.3073 56.505 96.7097Z"
        fill="#64D2FF"
      />
      <path
        d="M44.5759 109.964L41.1364 102.793L37.6971 109.963C37.3556 110.561 37.16 111.256 37.16 111.997C37.16 114.234 38.9403 116.047 41.1363 116.047C43.3324 116.047 45.1127 114.234 45.1127 111.997C45.1127 111.256 44.9172 110.561 44.5759 109.964Z"
        fill="#64D2FF"
      />
      <path
        d="M32.6466 96.7091L29.2073 89.5384L25.7677 96.7094C25.4264 97.307 25.231 98.0017 25.231 98.7429C25.231 100.98 27.0112 102.793 29.2073 102.793C31.4034 102.793 33.1836 100.98 33.1836 98.7429C33.1836 98.0016 32.9881 97.3068 32.6466 96.7091Z"
        fill="#64D2FF"
      />
    </Box>
  );
};

export const Rainy = ({
  width = "128px",
  height = "128px",
  sx = {},
  ...props
}) => {
  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 128 128"
      fill="none"
      sx={sx}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M33.1834 81.9112C19.275 81.9112 8 70.6362 8 56.7278C8 42.8194 19.275 31.5444 33.1834 31.5444C36.1992 31.5444 39.0912 32.0745 41.7712 33.0466C47.059 20.1128 59.767 11 74.6035 11C94.1851 11 110.059 26.874 110.059 46.4556C110.059 47.3182 110.028 48.1737 109.968 49.0208C115.889 51.7474 120 57.7339 120 64.6805C120 74.1968 112.286 81.9112 102.769 81.9112H33.1834Z"
        fill="#8f8f9261"
      />
      <path
        d="M92.9542 95.7094L89.5147 88.5385L86.0751 95.7096C85.7339 96.3072 85.5384 97.0018 85.5384 97.7429C85.5384 99.9797 87.3187 101.793 89.5147 101.793C91.7108 101.793 93.4911 99.9797 93.4911 97.7429C93.4911 97.0017 93.2956 96.307 92.9542 95.7094Z"
        fill="#64D2FF"
      />
      <path
        d="M81.0254 110.29L77.5858 103.118L74.1464 110.289C73.805 110.887 73.6094 111.581 73.6094 112.323C73.6094 114.56 75.3897 116.373 77.5857 116.373C79.7818 116.373 81.5621 114.56 81.5621 112.323C81.5621 111.582 81.3666 110.887 81.0254 110.29Z"
        fill="#64D2FF"
      />
      <path
        d="M69.0963 95.7096L65.6568 88.5385L62.2173 95.7094C61.8759 96.307 61.6804 97.0017 61.6804 97.7429C61.6804 99.9797 63.4607 101.793 65.6567 101.793C67.8528 101.793 69.6331 99.9797 69.6331 97.7429C69.6331 97.0018 69.4376 96.3071 69.0963 95.7096Z"
        fill="#64D2FF"
      />
      <path
        d="M57.8299 110.289L54.3905 103.118L50.9509 110.289C50.6096 110.887 50.4141 111.582 50.4141 112.323C50.4141 114.56 52.1944 116.373 54.3905 116.373C56.5865 116.373 58.3668 114.56 58.3668 112.323C58.3668 111.582 58.1713 110.887 57.8299 110.289Z"
        fill="#64D2FF"
      />
      <path
        d="M45.9012 95.7097L42.4615 88.5385L39.0221 95.7092C38.6807 96.3069 38.4851 97.0017 38.4851 97.7429C38.4851 99.9797 40.2654 101.793 42.4615 101.793C44.6575 101.793 46.4378 99.9797 46.4378 97.7429C46.4378 97.0019 46.2424 96.3073 45.9012 95.7097Z"
        fill="#64D2FF"
      />
      <path
        d="M33.972 110.289L30.5325 103.118L27.093 110.289C26.7516 110.887 26.5562 111.582 26.5562 112.323C26.5562 114.56 28.3364 116.373 30.5325 116.373C32.7286 116.373 34.5088 114.56 34.5088 112.323C34.5088 111.582 34.3133 110.887 33.972 110.289Z"
        fill="#64D2FF"
      />
    </Box>
  );
};

export const Snowy = ({
  width = "128px",
  height = "128px",
  sx = {},
  ...props
}) => {
  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 128 128"
      fill="none"
      sx={sx}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M33.1834 81.9112C19.275 81.9112 8 70.6362 8 56.7278C8 42.8194 19.275 31.5444 33.1834 31.5444C36.1992 31.5444 39.0912 32.0745 41.7712 33.0466C47.059 20.1128 59.767 11 74.6035 11C94.1851 11 110.059 26.874 110.059 46.4556C110.059 47.3182 110.028 48.1737 109.968 49.0208C115.889 51.7474 120 57.7339 120 64.6805C120 74.1968 112.286 81.9112 102.769 81.9112H33.1834Z"
        fill="#8f8f9261"
      />
      <path
        d="M92.9542 95.7094L89.5147 88.5385L86.0751 95.7096C85.7339 96.3072 85.5384 97.0018 85.5384 97.7429C85.5384 99.9797 87.3187 101.793 89.5147 101.793C91.7108 101.793 93.4911 99.9797 93.4911 97.7429C93.4911 97.0017 93.2956 96.307 92.9542 95.7094Z"
        fill="#64D2FF"
      />
      <path
        d="M81.0254 110.29L77.5858 103.118L74.1464 110.289C73.805 110.887 73.6094 111.581 73.6094 112.323C73.6094 114.56 75.3897 116.373 77.5857 116.373C79.7818 116.373 81.5621 114.56 81.5621 112.323C81.5621 111.582 81.3666 110.887 81.0254 110.29Z"
        fill="#64D2FF"
      />
      <path
        d="M69.0963 95.7096L65.6568 88.5385L62.2173 95.7094C61.8759 96.307 61.6804 97.0017 61.6804 97.7429C61.6804 99.9797 63.4607 101.793 65.6567 101.793C67.8528 101.793 69.6331 99.9797 69.6331 97.7429C69.6331 97.0018 69.4376 96.3071 69.0963 95.7096Z"
        fill="#64D2FF"
      />
      <path
        d="M57.8299 110.289L54.3905 103.118L50.9509 110.289C50.6096 110.887 50.4141 111.582 50.4141 112.323C50.4141 114.56 52.1944 116.373 54.3905 116.373C56.5865 116.373 58.3668 114.56 58.3668 112.323C58.3668 111.582 58.1713 110.887 57.8299 110.289Z"
        fill="#64D2FF"
      />
      <path
        d="M45.9012 95.7097L42.4615 88.5385L39.0221 95.7092C38.6807 96.3069 38.4851 97.0017 38.4851 97.7429C38.4851 99.9797 40.2654 101.793 42.4615 101.793C44.6575 101.793 46.4378 99.9797 46.4378 97.7429C46.4378 97.0019 46.2424 96.3073 45.9012 95.7097Z"
        fill="#64D2FF"
      />
      <path
        d="M33.972 110.289L30.5325 103.118L27.093 110.289C26.7516 110.887 26.5562 111.582 26.5562 112.323C26.5562 114.56 28.3364 116.373 30.5325 116.373C32.7286 116.373 34.5088 114.56 34.5088 112.323C34.5088 111.582 34.3133 110.887 33.972 110.289Z"
        fill="#64D2FF"
      />
    </Box>
  );
};

export const Sunny = ({
  width = "128px",
  height = "128px",
  sx = {},
  ...props
}) => {
  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 128 128"
      fill="none"
      sx={sx}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M111.955 66.5117C110.637 66.4438 109.63 65.3198 109.63 64C109.63 62.6802 110.637 61.5562 111.955 61.4883L117.005 61.2278C118.617 61.1446 120 62.3851 120 64C120 65.6149 118.617 66.8553 117.005 66.7722L111.955 66.5117ZM103.521 41.1787C104.182 42.3208 105.618 42.7936 106.793 42.1936L111.289 39.8982C112.732 39.1616 113.309 37.3865 112.497 35.9845C111.686 34.5825 109.864 34.2057 108.507 35.0892L104.276 37.8426C103.17 38.5623 102.861 40.0366 103.521 41.1787ZM86.8213 24.4786C87.9634 25.1395 89.4377 24.8305 90.1574 23.7245L92.9108 19.4933C93.7942 18.1356 93.4175 16.3141 92.0155 15.5028C90.6135 14.6915 88.8384 15.2684 88.1018 16.711L85.8064 21.2071C85.2064 22.3824 85.6792 23.8176 86.8213 24.4786ZM64 18.3704C65.3198 18.3704 66.4438 17.3633 66.5117 16.0453L66.7722 10.9954C66.8553 9.38262 65.6149 8 64 8C62.3851 8 61.1446 9.38262 61.2278 10.9954L61.4883 16.0453C61.5562 17.3633 62.6802 18.3704 64 18.3704ZM41.1787 24.4786C42.3208 23.8176 42.7936 22.3824 42.1936 21.2071L39.8982 16.711C39.1616 15.2684 37.3865 14.6915 35.9845 15.5028C34.5825 16.3141 34.2057 18.1356 35.0892 19.4933L37.8426 23.7245C38.5623 24.8305 40.0366 25.1395 41.1787 24.4786ZM24.4786 41.1787C25.1395 40.0366 24.8305 38.5623 23.7245 37.8426L19.4933 35.0892C18.1356 34.2058 16.3141 34.5825 15.5028 35.9845C14.6915 37.3865 15.2684 39.1616 16.711 39.8982L21.2071 42.1936C22.3824 42.7936 23.8176 42.3208 24.4786 41.1787ZM16.0453 61.4883C17.3633 61.5562 18.3704 62.6802 18.3704 64C18.3704 65.3198 17.3633 66.4438 16.0453 66.5117L10.9954 66.7722C9.38262 66.8554 8 65.6149 8 64C8 62.3851 9.38262 61.1446 10.9954 61.2278L16.0453 61.4883ZM24.4786 86.8213C23.8176 85.6792 22.3824 85.2064 21.2071 85.8064L16.711 88.1018C15.2684 88.8384 14.6915 90.6135 15.5028 92.0155C16.3141 93.4175 18.1356 93.7943 19.4933 92.9108L23.7245 90.1574C24.8305 89.4377 25.1395 87.9634 24.4786 86.8213ZM41.1787 103.521C40.0366 102.861 38.5623 103.17 37.8426 104.276L35.0892 108.507C34.2058 109.864 34.5825 111.686 35.9845 112.497C37.3865 113.309 39.1616 112.732 39.8982 111.289L42.1936 106.793C42.7936 105.618 42.3208 104.182 41.1787 103.521ZM64 109.63C62.6802 109.63 61.5562 110.637 61.4883 111.955L61.2278 117.005C61.1446 118.617 62.3851 120 64 120C65.6149 120 66.8554 118.617 66.7722 117.005L66.5117 111.955C66.4438 110.637 65.3198 109.63 64 109.63ZM86.8213 103.521C85.6792 104.182 85.2064 105.618 85.8064 106.793L88.1018 111.289C88.8384 112.732 90.6135 113.309 92.0155 112.497C93.4175 111.686 93.7943 109.864 92.9108 108.507L90.1574 104.276C89.4377 103.17 87.9634 102.861 86.8213 103.521ZM103.521 86.8213C102.861 87.9634 103.17 89.4377 104.276 90.1574L108.507 92.9108C109.864 93.7942 111.686 93.4175 112.497 92.0155C113.309 90.6135 112.732 88.8384 111.289 88.1018L106.793 85.8064C105.618 85.2064 104.182 85.6792 103.521 86.8213Z"
        fill="#FFD400"
      />
      <path
        d="M102.37 64C102.37 85.1914 85.1911 102.37 63.9998 102.37C42.8084 102.37 25.6294 85.1914 25.6294 64C25.6294 42.8086 42.8084 25.6296 63.9998 25.6296C85.1911 25.6296 102.37 42.8086 102.37 64Z"
        fill="#FFD400"
      />
    </Box>
  );
};

export const PartiallyCloudy = ({
  width = "128px",
  height = "128px",
  sx = {},
  ...props
}) => {
  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 128 128"
      fill="none"
      sx={sx}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M63.2655 28.5539C64.0663 29.0173 65.1 28.8007 65.6046 28.0252L67.5351 25.0585C68.1546 24.1066 67.8904 22.8294 66.9074 22.2606C65.9244 21.6917 64.6798 22.0962 64.1634 23.1077L62.5539 26.2601C62.1332 27.0842 62.4647 28.0905 63.2655 28.5539Z"
        fill="#FFD400"
      />
      <path
        d="M47.2644 24.2712C48.1897 24.2712 48.9778 23.5651 49.0255 22.6409L49.2081 19.1002C49.2664 17.9694 48.3967 17 47.2644 17C46.1321 17 45.2623 17.9694 45.3207 19.1002L45.5033 22.6409C45.5509 23.5651 46.339 24.2712 47.2644 24.2712Z"
        fill="#FFD400"
      />
      <path
        d="M31.2632 28.5539C32.064 28.0905 32.3955 27.0842 31.9748 26.2602L30.3654 23.1077C29.8489 22.0962 28.6043 21.6917 27.6213 22.2606C26.6383 22.8294 26.3741 24.1066 26.9936 25.0585L28.9241 28.0252C29.4287 28.8007 30.4624 29.0173 31.2632 28.5539Z"
        fill="#FFD400"
      />
      <path
        d="M19.5539 40.2632C20.0173 39.4624 19.8007 38.4287 19.0252 37.9241L16.0585 35.9936C15.1066 35.3741 13.8294 35.6383 13.2606 36.6213C12.6917 37.6043 13.0962 38.8489 14.1077 39.3654L17.2601 40.9748C18.0842 41.3955 19.0905 41.064 19.5539 40.2632Z"
        fill="#FFD400"
      />
      <path
        d="M13.6409 54.5033C14.5651 54.5509 15.2712 55.339 15.2712 56.2644C15.2712 57.1897 14.5651 57.9778 13.6409 58.0255L10.1002 58.2081C8.96943 58.2664 8 57.3967 8 56.2644C8 55.1321 8.96942 54.2623 10.1002 54.3207L13.6409 54.5033Z"
        fill="#FFD400"
      />
      <path
        d="M65.4965 36.4809C60.7028 32.0609 54.2989 29.361 47.2644 29.361C32.4061 29.361 20.361 41.4061 20.361 56.2644C20.361 57.3502 20.4253 58.421 20.5504 59.4731C24.8985 56.6299 30.0953 54.977 35.6782 54.977C37.9422 54.977 40.146 55.2495 42.257 55.7644C46.9301 46.4848 55.3308 39.3996 65.4965 36.4809Z"
        fill="#FFD400"
      />
      <path
        d="M35.678 107.115C22.1693 107.115 11.2183 96.1639 11.2183 82.6552C11.2183 69.1464 22.1693 58.1954 35.678 58.1954C38.6071 58.1954 41.416 58.7103 44.0191 59.6544C49.1548 47.0924 61.4978 38.2414 75.9079 38.2414C94.9268 38.2414 110.345 53.6593 110.345 72.6782C110.345 73.516 110.315 74.3468 110.256 75.1697C116.007 77.8179 120 83.6323 120 90.3793C120 99.6221 112.507 107.115 103.264 107.115H35.678Z"
        fill="#8f8f9261"
      />
    </Box>
  );
};

export const getWeatherIcon = (
  weatherText,
  width = "128px",
  height = "128px"
) => {
  if (!weatherText) return null;

  const text = weatherText.toLowerCase();

  if (
    text.includes("sunny") ||
    text.includes("clear") ||
    text.includes("fair")
  ) {
    return <Sunny width={width} height={height} />;
  } else if (
    text.includes("partly cloudy") ||
    text.includes("mostly sunny") ||
    text.includes("partly sunny")
  ) {
    return <PartiallyCloudy width={width} height={height} />;
  } else if (
    text.includes("cloudy") ||
    text.includes("mostly cloudy") ||
    text.includes("overcast")
  ) {
    return <PartiallyCloudy width={width} height={height} />;
  } else if (
    text.includes("rain") ||
    text.includes("showers") ||
    text.includes("drizzle")
  ) {
    return <Rainy width={width} height={height} />;
  } else if (
    text.includes("thunder") ||
    text.includes("storm") ||
    text.includes("tstorm")
  ) {
    return <RainThunder width={width} height={height} />;
  } else if (
    text.includes("snow") ||
    text.includes("sleet") ||
    text.includes("flurries") ||
    text.includes("blizzard") ||
    text.includes("wintry mix") ||
    text.includes("icy")
  ) {
    return <Snowy width={width} height={height} />;
  } else if (text.includes("hail")) {
    return <Rainy width={width} height={height} />;
  } else if (
    text.includes("fog") ||
    text.includes("mist") ||
    text.includes("haze") ||
    text.includes("smoke") ||
    text.includes("dust")
  ) {
    return <PartiallyCloudy width={width} height={height} />;
  } else if (text.includes("windy")) {
    return <PartiallyCloudy width={width} height={height} />;
  } else {
    return <Sunny width={width} height={height} />;
  }
};
