import { Box } from "@mui/material";
import React from "react";

const HotelBlack = ({
  width = "46px",
  height = "46px",
  active = false,
  sx = {},
  ...props
}) => {
  const fillColor = active ? "#1E3A8A" : "#333333";

  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 46 46"
      fill="none"
      sx={
        active
          ? {
              cursor: "pointer",
              transition: "transform 0.2s ease-in-out",
              "&:hover": { transform: "scale(1.1)" },
              ...sx,
            }
          : { ...sx }
      }
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle cx="23" cy="23" r="23" fill="white" />
      <path d="M31.4902 17.1284H14V18.6276H31.4902V17.1284Z" fill={fillColor} />
      <path d="M31.4902 31.8696H14V33.3688H31.4902V31.8696Z" fill={fillColor} />
      <path
        d="M15.2461 18.377V32.1192H30.7374V18.377H15.2461ZM21.9923 19.1265H23.9912V21.6251H21.9923V19.1265ZM21.9923 23.3742H23.9912V25.6229H21.9923V23.3742ZM19.2439 29.8705H17.4948V27.6218H19.2439V29.8705ZM19.2439 25.6229H17.4948V23.3742H19.2439V25.6229ZM19.2439 21.6251H17.4948V19.1265H19.2439V21.6251ZM24.7408 31.8694H21.2427V28.0805C21.2427 28.0805 22.7419 26.0746 24.7408 28.0805V31.8694ZM28.7385 29.8705H26.9895V27.6218H28.7385V29.8705ZM28.7385 25.6229H26.9895V23.3742H28.7385V25.6229ZM28.7385 21.6251H26.9895V19.1265H28.7385V21.6251Z"
        fill={fillColor}
      />
      <path
        d="M32.9873 25.4975C32.9873 25.9805 32.5958 26.3721 32.1128 26.3721C31.6298 26.3721 31.2383 25.9805 31.2383 25.4975V20.2505C31.2383 19.7675 31.6298 19.376 32.1128 19.376C32.5958 19.376 32.9873 19.7675 32.9873 20.2505V25.4975Z"
        fill={fillColor}
      />
      <path
        d="M22.8203 12L16.8789 16.1279H28.8752L22.8203 12Z"
        fill={fillColor}
      />
    </Box>
  );
};

export default HotelBlack;
