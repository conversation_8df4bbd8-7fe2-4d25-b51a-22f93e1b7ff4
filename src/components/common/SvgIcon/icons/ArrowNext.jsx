import { Box } from "@mui/material";
import React from "react";

const ArrowNext = ({
  width = "34px",
  height = "34px",
  sx = {},
  disabled = false,
  ...props
}) => {
  return (
    <Box
      component={"svg"}
      width={width}
      height={height}
      viewBox="0 0 42 42"
      fill="none"
      sx={{
        cursor: disabled ? "not-allowed" : "pointer",
        opacity: disabled ? 0.5 : 1,
        transition: "all 0.2s ease-in-out",
        "&:hover": !disabled && {
          transform: "scale(1.05)",
          filter: "drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15))",
        },
        ...sx,
      }}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g filter="url(#filter0_d_256_2727)">
        <circle
          cx="21"
          cy="21"
          r="16.5"
          transform="rotate(180 21 21)"
          fill="white"
          stroke="white"
        />
        <path
          d="M12 20C11.4477 20 11 20.4477 11 21C11 21.5523 11.4477 22 12 22L12 20ZM29.7071 21.7071C30.0976 21.3166 30.0976 20.6834 29.7071 20.2929L23.3431 13.9289C22.9526 13.5384 22.3195 13.5384 21.9289 13.9289C21.5384 14.3195 21.5384 14.9526 21.9289 15.3431L27.5858 21L21.9289 26.6569C21.5384 27.0474 21.5384 27.6805 21.9289 28.0711C22.3195 28.4616 22.9526 28.4616 23.3431 28.0711L29.7071 21.7071ZM12 21L12 22L29 22L29 21L29 20L12 20L12 21Z"
          fill={disabled ? "#CCCCCC" : "#FF3951"}
        />
      </g>
      <defs>
        <filter
          id="filter0_d_256_2727"
          x="0"
          y="0"
          width="42"
          height="42"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_256_2727"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_256_2727"
            result="shape"
          />
        </filter>
      </defs>
    </Box>
  );
};

export default ArrowNext;
