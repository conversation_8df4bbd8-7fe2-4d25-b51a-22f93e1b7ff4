import { styled } from "@mui/material/styles";

const StyledColorPickerPopup = styled("div")(
  ({ theme }) => `

  .dialog-content {
    padding: 0px;
    background-color: transparent;
    
    .color-picker-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 250px;
      min-width: 250px;

    }
  }

  .dialog-actions {
    background-color: ${theme.palette.custom.white};
    gap:8px;
    padding: 0px;
    padding-bottom: 10px;
    padding-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;


    .cancel-button {
      color: ${theme.palette.text.secondary};
      text-transform: none;
      font-weight: 500;
      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }

    .confirm-button {
      background: #FF3951;
      color: white;
      text-transform: none;
      &:hover {
        background: linear-gradient(90deg, rgba(255, 57, 81, 0.1) 0%, rgba(85, 48, 249, 0.1) 125.89%);
      }
    }
  }

  /* Override react-color-palette styles for better integration */
  .rcp {
    width: 100%;
    max-width: 300px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 0px !important;
  }

  .rcp__body {
    padding: 0;
  }

  .rcp-saturation {
    height: 200px;
    border-radius: 0px !important;
  }

  .rcp__hue {
    border-radius: 0px !important;
  }

  .rcp__alpha {
    border-radius: 0px !important;
  }
`
);

export default StyledColorPickerPopup;
