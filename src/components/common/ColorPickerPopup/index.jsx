import React from "react";
import {
  Box,
  Dialog,
  DialogContent,
  DialogActions,
  Button,
} from "@mui/material";
import { ColorPicker, useColor } from "react-color-palette";
import "react-color-palette/css";
import StyledColorPickerPopup from "./StyledColorPickerPopup";

const ColorPickerPopup = ({
  open,
  onClose,
  onColorChange,
  initialColor = "#000000",
  title = "Choose Color",
}) => {
  const [color, setColor] = useColor(initialColor);

  const handleConfirm = () => {
    onColorChange(color.hex);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      sx={{
        "& .MuiDialog-paper": {
          maxWidth: "fit-content",
        },
      }}
      PaperProps={{
        style: {
          borderRadius: "4px",
          padding: "0",
        },
      }}
    >
      <StyledColorPickerPopup>
        <DialogContent className="dialog-content">
          <Box className="color-picker-container">
            <ColorPicker
              width={150}
              height={150}
              color={color}
              onChange={setColor}
              hideHSV
              dark
              hideInput={["rgb", "hsv"]}
            />
          </Box>
        </DialogContent>

        <DialogActions className="dialog-actions">
          <Button
            onClick={handleCancel}
            variant="outlined"
            sx={{
              border: "1px solid #FF3951",
              color: "#FF3951",
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              px: "22px",
              py: "7px",
              borderRadius: "8px",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FFFFFF",
              },
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            variant="contained"
            sx={{
              background: "linear-gradient(90deg, #FF3951 0%, #FF3951 100%)",
              color: "#FFFFFF",
              fontFamily: "Roboto",
              fontWeight: 500,
              fontSize: "14px",
              px: 3,
              py: "8px",
              borderRadius: "8px",
              transition: "all 0.3s ease",
              textTransform: "none",
              "&:hover": {
                background: "linear-gradient(90deg, #FF3951 0%, #5530F9 100%)",
              },
            }}
          >
            Confirm
          </Button>
        </DialogActions>
      </StyledColorPickerPopup>
    </Dialog>
  );
};

export default ColorPickerPopup;
