import { FormControlLabel, Radio, Typography } from "@mui/material";

const PrimaryRadio = ({ value, label, sx, ...props }) => {
  return (
    <FormControlLabel
      value={value}
      {...props}
      control={
        <Radio
          sx={{
            padding: "0px",
            marginRight: "4px",
            color: "#4CAF50",
            "&.Mui-checked": {
              color: "#4CAF50",
            },
          }}
        />
      }
      label={
        <Typography
          sx={{
            fontFamily: "Roboto",
            fontWeight: 400,
            fontStyle: "Regular",
            fontSize: "12px",
            color: "#000000",
            cursor: "pointer",
          }}
        >
          {label}
        </Typography>
      }
      sx={{
        backgroundColor: "#fff",
        borderRadius: "4px",
        padding: "9px 8px",
        margin: "0px",
        boxSizing: "border-box",
        ...sx,
      }}
    />
  );
};

export default PrimaryRadio;
