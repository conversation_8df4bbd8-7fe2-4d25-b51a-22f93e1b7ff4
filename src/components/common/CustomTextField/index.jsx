import React from 'react';
import { TextField, Box } from '@mui/material';
import StyledCustomTextField from './StyledCustomTextField';

const CustomTextField = ({ 
  label,
  placeholder,
  value,
  onChange,
  error,
  helperText,
  fullWidth = true,
  required = false,
  startAdornment,
  endAdornment,
  colorPreview,
  ...props 
}) => {
  return (
    <StyledCustomTextField>
      <TextField
        label={label}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        error={error}
        helperText={helperText}
        fullWidth={fullWidth}
        required={false}
        InputLabelProps={{
          className: 'custom-label',
          required: required
        }}
        variant="outlined"
        InputProps={{
          className: 'custom-input',
          startAdornment: startAdornment || (colorPreview && (
            <Box 
              className="color-preview-box"
              style={{ backgroundColor: colorPreview }}
            />
          )),
          endAdornment: endAdornment,
          ...props.InputProps
        }}
        {...props}
      />
    </StyledCustomTextField>
  );
};

export default CustomTextField; 