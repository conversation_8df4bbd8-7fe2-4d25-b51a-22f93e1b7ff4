import { styled } from '@mui/material/styles';

const StyledCustomTextField = styled("div")(
  ({ theme }) => `
  .MuiTextField-root {
    margin-bottom: 16px;
    height: 68px;
  }

  .custom-label {
    font-family: 'Roboto', sans-serif;
    color: ${theme.palette.custom.placeholder};
    font-size: 14px;
    transform: translate(14px, 12px) scale(1);
    background-color: ${theme.palette.custom.white};
    padding: 0px 4px !important;

    &.MuiInputLabel-shrink {
      transform: translate(14px, -9px) scale(0.75);
    }

    & .MuiFormLabel-asterisk {
      color: ${theme.palette.custom.lightred};
    }

    &.Mui-focused {
      color: ${theme.palette.custom.placeholder};
    }
    
    &.Mui-error {
      color: ${theme.palette.custom.lightred};
    }
  }

  .custom-input {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    background-color: ${theme.palette.custom.white};
    border-radius: 10px !important;
    
    .MuiOutlinedInput-notchedOutline {
      border-color: rgba(255, 57, 81, 0.05) !important;
      border-radius: 10px !important;
      transition: all 0.3s ease;
    }

    .MuiInputBase-input {
      padding: 8px 14px;
      height: 30px;
    }

    &.Mui-focused .MuiOutlinedInput-notchedOutline {
      border-color: rgba(255, 57, 81, 0.05) !important;
    }

    &.Mui-error .MuiOutlinedInput-notchedOutline {
      border-color: ${theme.palette.custom.lightred};
    }

    .color-preview-box {
      width: 24px;
      height: 20px;
    }
  }

  .MuiFormHelperText-root {
    font-family: 'Roboto', sans-serif;
    font-size: 10px;
    margin-top: 6px;
    margin-left: 2px;
    line-height: 10px;

    &.Mui-error {
      color: ${theme.palette.custom.lightred};
    }
  }

  /* Override autofill background colors globally */
  .MuiInputBase-input:-webkit-autofill,
  .MuiInputBase-input:-webkit-autofill:hover,
  .MuiInputBase-input:-webkit-autofill:focus,
  .MuiInputBase-input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px ${theme.palette.custom.white} inset !important;
    -webkit-text-fill-color: inherit !important;
    transition: background-color 5000s ease-in-out 0s;
  }
  }
`
);

export default StyledCustomTextField; 