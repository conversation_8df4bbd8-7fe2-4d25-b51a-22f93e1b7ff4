import React, { useState } from 'react';
import { Tooltip, Popover, IconButton, useTheme, useMediaQuery } from '@mui/material';
import tooltipIcon from '../../assets/svg/tooltipIcon.svg';

const ResponsiveTooltipPopover = ({ title, icon, placement = 'top' }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleClick = (event) => {
    if (isMobile) {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  if (isMobile) {
    return (
      <>
        <IconButton
          onClick={handleClick}
          size="small"
          sx={{ padding: 0 }}
        >
          <img src={tooltipIcon} alt='tooltip' sx={{ fontSize: '24px' }} />
        </IconButton>
        <Popover
          open={open}
          anchorEl={anchorEl}
          onClose={handleClose}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
          PaperProps={{
            sx: {
              padding: '8px 12px',
              maxWidth: '200px',
              fontSize: '12px',
              backgroundColor: 'white',
              color: '#706E75',
              borderRadius: '4px',
            }
          }}
        >
          {title}
        </Popover>
      </>
    );
  }

  return (
    <Tooltip
      title={title}
      placement={placement}
      arrow
    >
      <IconButton
        size="small"
        sx={{ padding: 0 }}
      >
        <img src={tooltipIcon} alt='tooltip' sx={{ fontSize: '24px' }} />
      </IconButton>
    </Tooltip>
  );
};

export default ResponsiveTooltipPopover; 