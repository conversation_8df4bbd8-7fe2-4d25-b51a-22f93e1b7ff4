import { styled } from "@mui/material/styles";

const StyledWhyPartner = styled("div")(
  ({ theme }) => `
  padding: 80px 0;
  background: #FFFFFF;
  position: relative;
  display: block;
  width: 100%;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 40%;
    height: 100%;
    background-color: #FFF9F9;
    border-radius: 50% 0px 0px 0px;
    z-index: 0;
  }

  .content-wrapper {
    position: relative;
    z-index: 1;
    max-width: 100%;
    margin: 0 auto;
    padding: 0 24px;
    text-align: center;
  }

  .title {
    font-family: '<PERSON>ra', serif;
    font-size: 32px;
    font-weight: 700;
    color: ${theme.palette.secondary.main};
    margin-bottom: 10px;
    line-height: 56px;

    .highlight {
      background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .description {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: 400;
    color: ${theme.palette.custom.subText};
    max-width: 900px;
    margin: 0 auto 48px;
    line-height: 150%;
    letter-spacing: 0.05em;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    margin-top: 48px;
  }

  .feature-card {
    width: 245px;
    height: auto;
    background: #FFFFFF;
    border-radius: 16px;
    padding: 18px;
    text-align: left;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: relative;
    box-shadow: 0px 4px 4px 0px #0000000D;
    background-image: var(--border-gradient);
    background-origin: border-box;
    border: 2px solid transparent;
    background-clip: padding-box, border-box;
  }

  .icon-wrapper {
    width: 44px;
    height: 44px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .feature-title {
    font-family: 'Lora', serif;
    font-size: 16px;
    font-weight: 600;
    color: ${theme.palette.secondary.main};
    line-height: 100%;
    margin-bottom: 5px;
  }

  .feature-description {
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    font-weight: 400;
    color: ${theme.palette.custom.subText};
    line-height: 20px;
  }

  @media (max-width: 1024px) {
    .features-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }
  }

  @media (max-width: 768px) {
    padding: 60px 0;

    .title {
      font-size: 28px;
      line-height: 36px;
    }

    .features-grid {
      grid-template-columns: 1fr;
      gap: 16px;
      justify-items: center;
    }

    .feature-card {
      padding: 20px;
      width: 300px;
      align-items: center;
      text-align: center;
    }
  }
`
);

export default StyledWhyPartner; 