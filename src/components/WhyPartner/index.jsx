import React from 'react';
import { Box, Typography } from '@mui/material';
import StyledWhyPartner from './StyledWhyPartner';
import infrastructureIcon from '../../assets/svg/infrastructureIcon.svg';
import aiAutomationIcon from '../../assets/svg/aiAutomationIcon.svg';
import readyToSellIcon from '../../assets/svg/readyToSellIcon.svg';
import privateLabelIcon from '../../assets/svg/privateLabelIcon.svg';

const WhyPartner = () => {
  const features = [
    {
      icon: infrastructureIcon,
      title: 'Infrastructure-as-a-Service (IaaS)',
      description: 'Get your own hosted travel platform with One-TAP™ — manage bookings, payments, and customer interactions with zero development effort.',
      borderColor: 'linear-gradient(white, white), linear-gradient(106.95deg, #00AF31 1.02%, #FFFFFF 67.12%)'
    },
    {
      icon: aiAutomationIcon,
      title: 'AI-Powered Automation',
      description: 'Use advanced AI to generate itineraries, handle customer queries instantly, and optimize packages based on real-time user behavior.',
      borderColor: 'linear-gradient(white, white), linear-gradient(108.1deg, #FF3951 0%, #FFFFFF 73.68%)'
    },
    {
      icon: readyToSellIcon,
      title: 'Ready-to-Sell Holiday Packages',
      description: "Don't have inventory? No problem. Instantly access ZUUMM's catalog of curated, high-converting holiday packages — ready to sell.",
      borderColor: 'linear-gradient(white, white), linear-gradient(111.38deg, #AF06BC 2.67%, #FFFFFF 71.82%)'
    },
    {
      icon: privateLabelIcon,
      title: 'White-label Branding',
      description: 'Operate under your own brand with fully white-labeled web, mobile, and chat interfaces — all prebuilt and ready to launch.',
      borderColor: 'linear-gradient(white, white), linear-gradient(113.27deg, #FF8418 0%, #FFFFFF 66.2%)'
    }
  ];

  return (
    <StyledWhyPartner>
      <Box className="content-wrapper">
        <Typography className="title">
          Why <span className="highlight">Partner with ZUUMM?</span>
        </Typography>
        <Typography className="description">
          ZUUMM empowers you to launch or scale your travel business effortlessly with a powerful AI-first platform and zero upfront investment. Here's what you get:
        </Typography>

        <Box className="features-grid">
          {features.map((feature, index) => (
            <Box 
              key={index} 
              className="feature-card"
              sx={{ 
                '--border-gradient': feature.borderColor
              }}
            >
              <Box className="icon-wrapper">
                <img src={feature.icon} alt={feature.title} />
              </Box>
              <Typography className="feature-title">
                {feature.title}
              </Typography>
              <Typography className="feature-description">
                {feature.description}
              </Typography>
            </Box>
          ))}
        </Box>
      </Box>
    </StyledWhyPartner>
  );
};

export default WhyPartner; 