import React, { useEffect, useRef, useState } from "react";
import { Box, CircularProgress, Typography } from "@mui/material";
import { Loader } from "@googlemaps/js-api-loader";

const GoogleMap = ({
  address,
  lat,
  lng,
  height = "236px",
  width = "100%",
  zoom = 15,
  mapTypeId = "roadmap",
}) => {
  const mapRef = useRef(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!address && (!lat || !lng)) {
      setError("No address or coordinates provided");
      setLoading(false);
      return;
    }

    const initMap = async () => {
      try {
        const loader = new Loader({
          apiKey: process.env.REACT_APP_GOOGLE_MAPS_API_KEY,
          version: "weekly",
          libraries: ["places"],
        });

        const google = await loader.load();

        // If coordinates are provided, use them directly
        if (lat && lng) {
          const location = { lat: parseFloat(lat), lng: parseFloat(lng) };

          const map = new google.maps.Map(mapRef.current, {
            center: location,
            zoom: zoom,
            mapTypeId: mapTypeId,
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false,
            zoomControl: true,
            styles: [
              {
                featureType: "poi",
                elementType: "labels",
                stylers: [{ visibility: "off" }],
              },
            ],
          });

          // Add a marker for the location
          new google.maps.Marker({
            position: location,
            map: map,
            title: address || `Location (${lat}, ${lng})`,
          });

          setLoading(false);
        } else if (address) {
          // Fall back to geocoding if no coordinates provided
          const geocoder = new google.maps.Geocoder();

          // Geocode the address to get coordinates
          geocoder.geocode({ address }, (results, status) => {
            if (status === "OK") {
              const location = results[0].geometry.location;

              const map = new google.maps.Map(mapRef.current, {
                center: location,
                zoom: zoom,
                mapTypeId: mapTypeId,
                mapTypeControl: false,
                streetViewControl: false,
                fullscreenControl: false,
                zoomControl: true,
                styles: [
                  {
                    featureType: "poi",
                    elementType: "labels",
                    stylers: [{ visibility: "off" }],
                  },
                ],
              });

              // Add a marker for the location
              new google.maps.Marker({
                position: location,
                map: map,
                title: address,
              });

              setLoading(false);
            } else {
              setError("Could not find the address on the map");
              setLoading(false);
            }
          });
        }
      } catch (err) {
        setError("Failed to load Google Maps");
        setLoading(false);
        console.error("Google Maps loading error:", err);
      }
    };

    initMap();
  }, [address, lat, lng, zoom, mapTypeId]);

  if (error) {
    return (
      <Box
        sx={{
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: "#f5f5f5",
          border: "1px solid #ddd",
          borderRadius: "4px",
        }}
      >
        <Typography color="error" variant="body2">
          {error}
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ position: "relative", height, width }}>
      {loading && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "#f5f5f5",
            zIndex: 1,
          }}
        >
          <CircularProgress />
        </Box>
      )}
      <Box
        ref={mapRef}
        sx={{
          height: "100%",
          width: "100%",
          borderRadius: "4px",
          overflow: "hidden",
        }}
      />
    </Box>
  );
};

export default GoogleMap;
