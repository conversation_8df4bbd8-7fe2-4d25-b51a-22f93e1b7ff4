import React from 'react';
import { Box, Typography } from '@mui/material';
import StyledGlobalImpact from './StyledGlobalImpact';

const GlobalImpact = () => {
  const stats = [
    {
      value: '55K',
      label: 'Hours saved'
    },
    {
      value: '100K+',
      label: "Traveler's assisted"
    },
    {
      value: '25K+',
      label: 'Flights, hotels, and packages booked'
    },
    {
      value: '$200K',
      label: 'Saved for travelers'
    }
  ];

  return (
    <StyledGlobalImpact>
      <Box className="content-wrapper">
        <Typography className="title">
          Global Impact Dashboard
        </Typography>
        <Typography className="subtitle">
          Real-time visualization of ZUUMM's impact on global travel
        </Typography>

        <Box className="stats-container">
          {stats.map((stat, index) => (
            <Box key={index} className="stat-item">
              <Typography className="stat-value">
                {stat.value}
              </Typography>
              <Typography className="stat-label">
                {stat.label}
              </Typography>
            </Box>
          ))}
        </Box>
      </Box>
    </StyledGlobalImpact>
  );
};

export default GlobalImpact; 