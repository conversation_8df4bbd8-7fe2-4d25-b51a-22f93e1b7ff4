import { styled } from "@mui/material/styles";

const StyledGlobalImpact = styled("div")(
  ({ theme }) => `
  padding: 60px 0;
  background: #FFFFFF;

  .content-wrapper {
    width: 90%;
    margin: 0 auto;
    padding: 40px 20px;
    text-align: center;
    box-shadow: 0px 12px 35px 0px #64748C1F;
    border-radius: 10px;
  }

  .title {
    font-family: 'Lora', serif;
    font-size: 28px;
    font-weight: 700;
    color: ${theme.palette.secondary.main};
    margin-bottom: 4px;
    line-height: 40px;
  }

  .subtitle {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    color: ${theme.palette.custom.subText};
    margin-bottom: 24px;
    line-height: 150%;
  }

  .stats-container {
    display: flex;
    justify-content: space-between;
    gap: 0px;
    flex-wrap: wrap;
  }

  .stat-item {
    flex: 1;
    min-width: 200px;
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  .stat-value {
    font-family: 'Roboto', sans-serif;
    font-size: 34px;
    font-weight: 500;
    color: ${theme.palette.custom.lightred};
    line-height: 100%;
  }

  .stat-label {
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    font-weight: 600;
    color: ${theme.palette.custom.subText};
    text-align: center;
    line-height: 100%;
  }

  @media (max-width: 768px) {
    padding: 40px 0;
    .content-wrapper {
    width: 80%;
}

    .title {
      font-size: 24px;
      line-height: 36px;
    }

    .subtitle {
      font-size: 14px;
      margin-bottom: 32px;
    }

    .stats-container {
      gap: 16px;
      justify-content: center;
    }

    .stat-item {
      min-width: 45%;
      padding: 16px;
    }

    .stat-value {
      font-size: 28px;
    }

    .stat-label {
      font-size: 14px;
    }
  }

  @media (max-width: 480px) {
    .stat-item {
      min-width: 100%;
    }
  }
`
); 

export default StyledGlobalImpact;