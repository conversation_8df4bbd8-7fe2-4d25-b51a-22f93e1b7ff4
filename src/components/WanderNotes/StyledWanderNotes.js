import { styled } from "@mui/material/styles";

const StyledWanderNotes = styled("div")(
  ({ theme }) => `
  padding: 20px 0 40px 0;
  background: #FFFFFF;

  .content-wrapper {
    max-width: 97%;
    margin: 0 auto;
    padding: 0 24px;
  }

  .title {
    font-family: 'Lora', serif;
    font-size: 28px;
    font-weight: 700;
    color: ${theme.palette.secondary.main};
    text-align: center;
    margin-bottom: 40px;
    line-height: 40px;
  }

  .articles-grid {
    margin-top: 32px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 20px;
  }

  .article-card {
    width: 30%;
    background: #FFFFFF;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    cursor: pointer;
    margin: 0 10px;
  }

  .image-wrapper {
    position: relative;
    width: 100%;
    height: 300px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
      border-radius: 12px;
    }

    .category-tag {
      position: absolute;
      top: 16px;
      left: 16px;
      background: #FFFFFF;
      border-radius: 20px;
      padding: 6px 12px;

      p {
        font-family: 'Inter', sans-serif;
        font-size: 14px;
        font-weight: 500;
        line-height: 26px;
        color: ${theme.palette.secondary.main};
        margin: 0px;
      }
    }
  }

  .article-info {
    padding: 24px 0px;

    .meta-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .date, .author {
        font-family: 'Roboto', sans-serif;
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
        color: ${theme.palette.custom.darkBlue3};
      }
    .separator {
        width: 1px;
        height: 16px;
        background-color: #E7E6E6;
    }
    }

    .article-title {
      font-family: 'Roboto', sans-serif;
      font-size: 16px;
      font-weight: 500;
      color: ${theme.palette.custom.darkBlue3};
      line-height: 27px;
      margin: 0px;
      
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  @media (max-width: 600px) {
   padding: 30px 0;
    .article-card {
      width: 94% !important;
    }
  }
  @media (max-width: 768px) {
    padding: 30px 0;

    .title {
      font-size: 28px;
      margin-bottom: 32px;
    }

    .image-wrapper {
      height: 200px;
    }

    .article-info {
      padding: 14px 14px 14px 0px;

      .article-title {
        font-size: 15px;
        line-height: 20px;
      }
    }
  }
`
);

export default StyledWanderNotes;
