/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react";
import { Box, Typography, Skeleton } from "@mui/material";
import StyledWanderNotes from "./StyledWanderNotes";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { fetchBlogPosts } from "../../store/reducers/Blog/apiThunk";
import generateCloudFrontURL from "../../utils";
import { ToastNotifyError } from "../Toast/ToastNotify";
import BlogCard from "../../pages/Blogs/BlogCard";

const WanderNotes = () => {
  const navigate = useNavigate();
  const [blogPosts, setBlogPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const dispatch = useDispatch();

  // Material-UI Skeleton component for blog cards
  const ShimmerBlogCard = () => (
    <Box
      sx={{
        backgroundColor: "transparent",
        borderRadius: "12px",
        overflow: "hidden",
        boxSizing: "border-box",
        width: "100%",
        height: "100%",
      }}
    >
      {/* Top Image Skeleton */}
      <Box
        sx={{
          position: "relative",
          width: "100%",
          height: "300px",
          overflow: "hidden",
          borderRadius: "12px",
        }}
      >
        <Skeleton
          variant="rectangular"
          width="100%"
          height="100%"
          sx={{ borderRadius: "12px" }}
        />

        {/* Category Badge Skeleton */}
        <Box
          sx={{
            position: "absolute",
            top: "20px",
            left: "20px",
          }}
        >
          <Skeleton
            variant="rectangular"
            width={80}
            height={26}
            sx={{ borderRadius: "18px" }}
          />
        </Box>
      </Box>

      <Box sx={{ pt: "16px" }}>
        {/* Timestamp and Author Skeleton */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: "10px",
            marginBottom: "8px",
          }}
        >
          <Skeleton variant="text" width={100} height={20} />

          {/* Separation Line Skeleton */}
          <Skeleton
            variant="rectangular"
            width={1}
            height={26}
            sx={{ borderRadius: "50%" }}
          />

          <Skeleton variant="text" width={80} height={20} />
        </Box>

        {/* Title Skeleton */}
        <Box sx={{ display: "flex", flexDirection: "column", gap: 0.5 }}>
          <Skeleton variant="text" width="100%" height={27} />
          <Skeleton variant="text" width="80%" height={27} />
        </Box>
      </Box>
    </Box>
  );

  const getRelatedBlogPosts = async () => {
    try {
      setLoading(true);
      const res = await dispatch(fetchBlogPosts());
      const { data, message, status } = res?.payload?.data;
      if (status === "success") {
        const formattedData = data?.blogs?.map((blog) => ({
          ...blog,
          banner: generateCloudFrontURL({
            bucket: process.env.REACT_APP_BUCKET_NAME,
            key: blog.banner,
            width: 410,
            height: 300,
            fit: "cover",
            actual: false,
          }),
        }));
        setBlogPosts(formattedData);
      } else {
        ToastNotifyError(message);
      }
    } catch (err) {
      console.log(err);
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 2000);
    }
  };

  useEffect(() => {
    getRelatedBlogPosts();
  }, []);

  const handleCardClick = (blogId) => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
    navigate(`/wander-notes/${blogId}`);
  };

  if (blogPosts?.length === 0 && !loading) {
    return null;
  }

  return (
    <StyledWanderNotes>
      <Box className="content-wrapper">
        <Typography className="title">Wander Notes</Typography>

        <Box>
          <Swiper
            modules={[Navigation]}
            spaceBetween={24}
            slidesPerView={1}
            navigation={{
              nextEl: ".swiper-button-next-blog",
              prevEl: ".swiper-button-prev-blog",
            }}
            breakpoints={{
              640: {
                slidesPerView: 2,
                spaceBetween: 24,
              },
              768: {
                slidesPerView: 3,
                spaceBetween: 24,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 24,
              },
            }}
            style={{
              width: "100%",
              paddingBottom: "16px",
            }}
          >
            {loading
              ? // Render Material-UI skeleton cards while loading
                [...Array(6)].map((_, index) => (
                  <SwiperSlide key={`shimmer-${index}`}>
                    <ShimmerBlogCard />
                  </SwiperSlide>
                ))
              : // Render actual blog cards when loaded
                blogPosts?.map((blog) => (
                  <SwiperSlide key={blog.external_id}>
                    <BlogCard
                      image={blog.banner}
                      title={blog.seo_title}
                      author={blog.writer?.name}
                      timestamp={blog.published_at}
                      category={blog.tags?.length > 0 ? blog.tags[0].name : ""}
                      onClick={() => handleCardClick(blog.slug)}
                      isHover={false}
                    />
                  </SwiperSlide>
                ))}
          </Swiper>

          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              gap: 2,
            }}
          >
            <img
              className="swiper-button-prev-blog"
              src={"/static/common/slidePrev.png"}
              alt="prev"
              style={{ cursor: "pointer", width: "34px", height: "34px" }}
            />
            <img
              className="swiper-button-next-blog"
              src={"/static/common/slideNext.png"}
              alt="next"
              style={{ cursor: "pointer", width: "34px", height: "34px" }}
            />
          </Box>
        </Box>
      </Box>
    </StyledWanderNotes>
  );
};

export default WanderNotes;
