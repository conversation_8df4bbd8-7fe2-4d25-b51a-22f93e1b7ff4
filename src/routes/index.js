import { createBrowserRouter } from "react-router-dom";
import Home from "../pages/Home";
import PublicLayout from "../layout/publicLayout";
import ROUTE_PATH from "../constants/route";
import ForPartners from "../pages/ForPartners";
import BrandOnboarding from "../pages/BrandOnboarding";
import Explore from "../pages/Explore";
import SelectedDestinationPage from "../pages/Explore/SelectedDestinationPage";
import PackageDetails from "../pages/PackageDetails";
import SelectedPakagePage from "../pages/Explore/SelectedPakagePage";
import AboutZuumm from "../pages/AboutZuumm";
import DocumentHub from "../pages/DocumentHub";
import Terms from "../pages/Terms";
import Privacy from "../pages/Privacy";
import CorporateTravel from "../pages/CorporateTravel";
import Blogs from "../pages/Blogs";
import BlogDetails from "../pages/BlogDetails";
// import AiChat from "../pages/AiChat";
import AffiliatePage from "../pages/Affiliate";
import ProtectedRoutes from "./ProtectedRoutes";
import ChatBot from "../pages/ChatBot";

const routes = createBrowserRouter([
  {
    path: ROUTE_PATH.HOME,
    element: (
      <PublicLayout>
        <Home />
      </PublicLayout>
    ),
  },
  {
    path: ROUTE_PATH.FOR_PARTNERS,
    element: (
      <PublicLayout>
        <ForPartners />
      </PublicLayout>
    ),
  },
  {
    path: ROUTE_PATH.BRAND_ONBOARDING,
    element: <BrandOnboarding />,
  },
  {
    path: ROUTE_PATH.EXPLORE,
    element: (
      <PublicLayout>
        <Explore />
      </PublicLayout>
    ),
  },
  {
    path: ROUTE_PATH.SELECTED_DESTINATION,
    element: (
      <PublicLayout stickyHeader={false}>
        <SelectedDestinationPage />
      </PublicLayout>
    ),
  },
  {
    path: ROUTE_PATH.SELECTED_PACKAGE,
    element: (
      <PublicLayout stickyHeader={false}>
        <SelectedPakagePage />
      </PublicLayout>
    ),
  },
  {
    path: ROUTE_PATH.PACKAGE_DETAILS,
    element: (
      <>
        <PackageDetails />
      </>
    ),
  },
  {
    path: ROUTE_PATH.ABOUT_ZUUMM,
    element: <AboutZuumm />,
  },
  {
    path: ROUTE_PATH.DOCUMENT_HUB,
    element: <DocumentHub />,
  },
  {
    path: ROUTE_PATH.TERMS,
    element: <Terms />,
  },
  {
    path: ROUTE_PATH.PRIVACY,
    element: <Privacy />,
  },
  {
    path: ROUTE_PATH.CORPORATE_TRAVEL,
    element: (
      <PublicLayout>
        <CorporateTravel />
      </PublicLayout>
    ),
  },
  {
    path: ROUTE_PATH.BLOGS,
    element: (
      <PublicLayout>
        <Blogs />
      </PublicLayout>
    ),
  },
  {
    path: ROUTE_PATH.BLOG_DETAILS,
    element: (
      <PublicLayout>
        <BlogDetails />
      </PublicLayout>
    ),
  },
  {
    path: ROUTE_PATH.AI_CHAT,
    element: (
      <ProtectedRoutes>
        <ChatBot />
      </ProtectedRoutes>
    ),
  },
  // {
  //   path: ROUTE_PATH.CHAT_BOT,
  //   element: <ChatBot />,
  // },
  {
    path: ROUTE_PATH.AFFILIATE,
    element: (
      <ProtectedRoutes>
        <AffiliatePage />
      </ProtectedRoutes>
    ),
  },
]);

export default routes;