import React, { useEffect } from "react";
import { Navigate } from "react-router-dom";
import { useSelector } from "react-redux";
import ROUTE_PATH from "../constants/route";

const ProtectedRoutes = ({ children }) => {
  const { userDetails } = useSelector((state) => state.authentication);

  // Check for token in localStorage
  const tokenFromStorage = localStorage.getItem("zuumm_token");

  // Check if user is authenticated (either from Redux state or localStorage)
  const isAuthenticated = userDetails?.token || tokenFromStorage;

  useEffect(() => {
    // If we have a token in localStorage but not in Redux state,
    // we could optionally dispatch an action to restore the auth state
    if (tokenFromStorage && !userDetails?.token) {
      // You might want to dispatch an action here to restore user details
      // dispatch(restoreUserSession(tokenFromStorage));
    }
  }, [tokenFromStorage, userDetails?.token]);

  // If not authenticated, redirect to home page
  if (!isAuthenticated) {
    return <Navigate to={ROUTE_PATH.HOME} replace />;
  }

  // If authenticated, render the protected content
  return children;
};

export default ProtectedRoutes;
