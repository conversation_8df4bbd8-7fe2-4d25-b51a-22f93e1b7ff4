import routes from './routes';
import { RouterProvider } from "react-router-dom";
import "./App.css";
import { ToastContainer } from "react-toastify";
// import TawkToWidget from "./components/TawkToWidget";
import UserlikeWidget from "./components/UserlikeWidget";
import { GoogleOAuthProvider } from "@react-oauth/google";
// import Loader from "./components/common/Loader";

function App() {
  return (
    <GoogleOAuthProvider clientId={process.env.REACT_APP_GOOGLE_CLIENT_ID}>
      <RouterProvider router={routes} />
      <ToastContainer />
      <UserlikeWidget />
      {/* <TawkToWidget /> */}
      {/* <Loader /> */}
    </GoogleOAuthProvider>
  );
}

export default App;