<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Sign-In Callback</title>
</head>
<body>
    <script>
        // Parse the URL hash to get the access token and ID token
        const hash = window.location.hash.substring(1);
        const params = new URLSearchParams(hash);
        const accessToken = params.get('access_token');
        const idToken = params.get('id_token');
        const error = params.get('error');

        if (accessToken) {
            // Send success message to parent window
            window.opener.postMessage({
                type: 'GOOGLE_SIGNIN_SUCCESS',
                access_token: accessToken,
                id_token: idToken
            }, window.location.origin);
        } else if (error) {
            // Send error message to parent window
            window.opener.postMessage({
                type: 'GOOGLE_SIGNIN_ERROR',
                error: error
            }, window.location.origin);
        } else {
            // Send error message if no token or error found
            window.opener.postMessage({
                type: 'GOOGLE_SIGNIN_ERROR',
                error: 'No access token received'
            }, window.location.origin);
        }

        // Close the popup
        window.close();
    </script>
</body>
</html> 