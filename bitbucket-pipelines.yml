image: node:18
# Workflow  Configuration 
pipelines:
  branches:
    development:
      - step:
          name: Step 1 - Dev Build
          script:
            - npm install
            - npm run build
            - cd build
          artifacts:
            - build/**
      - step:
          name: Step 2 - Deploy to S3 Dev Bucket
          script:
            # sync your files to S3
            - pipe: atlassian/aws-s3-deploy:0.4.4
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_REGION
                S3_BUCKET: $AWS_DEV_BUCKET_NAME
                LOCAL_PATH: ./build/
      - step:
          name: Step 3 - Invalidate Cloudfront cache
          script:
            - pipe: atlassian/aws-cloudfront-invalidate:0.1.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_REGION
                DISTRIBUTION_ID: $CLOUDFRONT_DEV_DIST_ID
    staging:
      - step:
          name: Step 1 - Stage Build
          script:
            - npm install
            - npm run build:stage
            - cd build
          artifacts:
            - build/**
      - step:
          name: Step 2 - Deploy to S3 Stage Bucket
          script:
            # sync your files to S3
            - pipe: atlassian/aws-s3-deploy:0.4.4
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_REGION
                S3_BUCKET: $AWS_STAGE_BUCKET_NAME
                LOCAL_PATH: ./build/
      - step:
          name: Step 3 - Invalidate Cloudfront cache
          script:
            - pipe: atlassian/aws-cloudfront-invalidate:0.1.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_REGION
                DISTRIBUTION_ID: $CLOUDFRONT_STAGE_DIST_ID
    main:
      - step:
          name: Step 1 - Prod Build
          script:
            - npm install
            - npm run build:prod
            - cd build
          artifacts:
            - build/**
      - step:
          name: Step 2 - Deploy to S3 Prod Bucket
          script:
            # sync your files to S3
            - pipe: atlassian/aws-s3-deploy:0.4.4
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_REGION
                S3_BUCKET: $AWS_PROD_BUCKET_NAME
                LOCAL_PATH: ./build/
      - step:
          name: Step 3 - Invalidate Cloudfront cache
          script:
            - pipe: atlassian/aws-cloudfront-invalidate:0.1.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_REGION
                DISTRIBUTION_ID: $CLOUDFRONT_PROD_DIST_ID
