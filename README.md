# Bitbucket Pipeline Configuration

This repository uses Bitbucket Pipelines for CI/CD with three deployment environments:

## Development Branch
When code is pushed to the `development` branch, it:
1. Builds the application using `npm run build`
   ```bash
   npm install
   npm start start:dev
   npm run build
   ```
2. Deploys built files to an AWS S3 development bucket using aws-s3-deploy pipe
3. Invalidates the CloudFront cache for development distribution using aws-cloudfront-invalidate pipe

## Staging Branch
When code is pushed to the `staging` branch, it:
1. Builds the application using `npm run build:stage`
   ```bash
   npm install
   npm start start:stage
   npm run build:stage
   ```
2. Deploys built files to an AWS S3 staging bucket using aws-s3-deploy pipe
3. Invalidates the CloudFront cache for staging distribution using aws-cloudfront-invalidate pipe

## Production Branch
When code is pushed to the `main` branch, it:
1. Builds the application using `npm run build:prod`
   ```bash
   npm install
   npm start start:prod
   npm run build:prod
   ```
2. Deploys built files to an AWS S3 production bucket using aws-s3-deploy pipe
3. Invalidates the CloudFront cache for production distribution using aws-cloudfront-invalidate pipe

Each environment uses separate S3 buckets and CloudFront distributions, configured through environment variables:
- Development: $AWS_DEV_BUCKET_NAME and $CLOUDFRONT_DEV_DIST_ID
- Staging: $AWS_STAGE_BUCKET_NAME and $CLOUDFRONT_STAGE_DIST_ID  
- Production: $AWS_PROD_BUCKET_NAME and $CLOUDFRONT_PROD_DIST_ID
