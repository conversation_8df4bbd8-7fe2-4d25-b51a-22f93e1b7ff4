{"name": "zuumm-ai-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@googlemaps/js-api-loader": "^1.16.10", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.8.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "countries-list": "^3.1.1", "framer-motion": "^12.16.0", "libphonenumber-js": "^1.12.8", "lottie-react": "^2.4.1", "moment": "^2.30.1", "react": "^19.1.0", "react-color-palette": "^7.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "react-scripts": "5.0.1", "react-slick": "^0.30.3", "react-speech-recognition": "^4.0.1", "react-toastify": "^11.0.5", "redux-persist": "^6.0.0", "slick-carousel": "^1.8.1", "styled-components": "^6.1.18", "swiper": "^11.2.8", "uuid": "^11.1.0", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"start": "env-cmd -f .env.development react-scripts start", "start:dev": "env-cmd -f .env.development react-scripts start", "start:stage": "env-cmd -f .env.staging react-scripts start", "start:prod": "env-cmd -f .env.production react-scripts start", "build": "env-cmd -f .env.development react-scripts build", "build:dev": "env-cmd -f .env.development react-scripts build", "build:stage": "env-cmd -f .env.staging react-scripts build", "build:prod": "env-cmd -f .env.production react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"env-cmd": "^10.1.0", "eslint": "^8.57.1", "prettier": "^3.5.3"}}